
services:
  php:
    hostname: cnat_app
    container_name: cnat_app
    profiles:
      - dev
    build:
      context: ./
      dockerfile: docker/php/Dockerfile
      target: php-dev
      args:
        USER_ID: ${USER_ID-1000}
        GROUP_ID: ${GROUP_ID-1000}
        APP_BASE_DIR: ${APP_BASE_DIR-./}
        # To authenticate to private registry either use username / password, or <PERSON><PERSON><PERSON> key/secret / etc
        # Please add Composer Auth JSON Object
        COMPOSER_AUTH: "{}"
    restart: always
    environment:
        PHP_INDEX: app_dev.php
        PHP_FPM_HOST: php
        XDEBUG_CLIENT_HOST: ${XDEBUG_CLIENT_HOST-host.docker.internal}
        TZ: Europe/Amsterdam
        COMPOSER_MEMORY_LIMIT: -1
        COMPOSER_ALLOW_SUPERUSER: 1
        XDEBUG_MODE: debug
        PHP_IDE_CONFIG: "serverName=${CAT_HOSTNAME_DOTENV}"
        SERVICETOOL_HOSTNAME_DOTENV: ${SERVICETOOL_HOSTNAME_DOTENV}
        SERVICETOOL_DOMAIN_DOTENV: ${SERVICETOOL_DOMAIN_DOTENV}
        CAT_HOSTNAME_DOTENV: ${CAT_HOSTNAME_DOTENV}
        CAT_DOMAIN_DOTENV: ${CAT_DOMAIN_DOTENV}
        # For Xdebuger to work, it needs the docker host ID
        # - in Mac AND Windows, `host.docker.internal` resolve to Docker host IP (When using Docker Desktop)
        # More information: https://xdebug.org/docs/all_settings#XDEBUG_CONFIG
        XDEBUG_CONFIG: "client_host=${XDEBUG_CLIENT_HOST-host.docker.internal} xdebug.discover_client_host=true"
        # To authenticate to private registry either use username / password, or Oauth key/secret / etc
        # Please add Composer Auth JSON Object
        COMPOSER_AUTH: "{}"
        SSH_AUTH_SOCK: /ssh-agent
        SYMFONY_IDE: "phpstorm://open?file=%f&line=%%l&/var/www/app/>${PWD}/"
    volumes:
      - .:/var/www/app
      - ~/.ssh:/home/<USER>/.ssh
    extra_hosts:
        - "host.docker.internal:host-gateway"
    # depends_on:
    #   database:
    #     condition: service_healthy

  web:
    hostname: cnat_web
    container_name: cnat_web
    profiles:
      - dev
    build:
      context: .
      dockerfile: docker/web/Dockerfile
      target: web-dev
      args:
        USER_ID: ${USER_ID-1000}
        GROUP_ID: ${GROUP_ID-1000}
        APP_BASE_DIR: ${APP_BASE_DIR:-./}
        SSH_AUTH_SOCK: /ssh-agent
    restart: always
    ports:
      - "8080"
      - "8443"
    environment:
      PHP_INDEX: app_dev.php
      PHP_FPM_HOST: cnat_app
      PHP_FPM_PORT: ${PHP_PORT-9000}
      VIRTUAL_HOST: servicetool.loc,cat.loc,test-tool.loc,cat-test.loc
      VIRTUAL_PORT: 8443
      VIRTUAL_PROTO: https
      CERT_NAME: self-signed
      TZ: Europe/Amsterdam
    networks:
      default:
        aliases:
          - servicetool.loc
          - cat.loc
          - test-tool.loc
          - cat-test.loc
    volumes:
      - ./web:/var/www/app/web
    # depends_on:
    #   php:
    #     condition: service_healthy

  php-prod:
    profiles:
      - prod
    build:
      context: ./
      dockerfile: docker/php/Dockerfile
      target: php-prod
      args:
        USER_ID: ${USER_ID-1000}
        GROUP_ID: ${GROUP_ID-1000}
        # To authenticate to private registry either use username / password, or Oauth key/secret / etc
        # Please add Composer Auth JSON Object
        COMPOSER_AUTH: "{}"
    restart: always
    environment:
        PHP_INDEX: app.php
        PHP_FPM_HOST: php-prod
        TZ: Europe/Amsterdam
        PHP_IDE_CONFIG: "serverName=${CAT_HOSTNAME_DOTENV}"
        SERVICETOOL_HOSTNAME_DOTENV: ${SERVICETOOL_HOSTNAME_DOTENV}
        SERVICETOOL_DOMAIN_DOTENV: ${SERVICETOOL_DOMAIN_DOTENV}
        CAT_HOSTNAME_DOTENV: ${CAT_HOSTNAME_DOTENV}
        CAT_DOMAIN_DOTENV: ${CAT_DOMAIN_DOTENV}
        # To authenticate to private registry either use username / password, or Oauth key/secret / etc
        # Please add Composer Auth JSON Object
        COMPOSER_AUTH: "{}"
        COMPOSER_MEMORY_LIMIT: -1
        COMPOSER_ALLOW_SUPERUSER: 1
    volumes:
      - ./app/config/parameters.yml:/var/www/app/app/config/parameters.yml:cached
    # depends_on:
    #   database:
    #     condition: service_healthy

  web-prod:
    profiles:
      - prod
    build:
      context: .
      dockerfile: docker/web/Dockerfile
      target: web-prod
      args:
        USER_ID: ${USER_ID-1000}
        GROUP_ID: ${GROUP_ID-1000}
        APP_BASE_DIR: ${APP_BASE_DIR:-./}
        SSH_AUTH_SOCK: /ssh-agent
    restart: always
    ports:
      - "8080"
      - "8443"
    environment:
      PHP_INDEX: app.php
      PHP_FPM_HOST: php-prod
      PHP_FPM_PORT: ${PHP_PORT-9000}
      VIRTUAL_HOST: servicetool.loc,cat.loc,test-tool.loc,cat-test.loc
      VIRTUAL_PORT: 8443
      VIRTUAL_PROTO: https
      CERT_NAME: self-signed
      TZ: Europe/Amsterdam
    networks:
      default:
        aliases:
          - servicetool.loc
          - cat.loc
          - test-tool.loc
          - cat-test.loc
    # depends_on:
    #   php-prod:
    #     condition: service_healthy

  php-image:
    profiles:
      - image
    image: ghcr.io/skrepr/cameranu-admin-tool/php:pr-8
    restart: always
    environment:
        PHP_INDEX: app.php
        PHP_FPM_HOST: php-prod
        ELASTIC_HOST: elasticsearch
        TZ: Europe/Amsterdam
        PHP_IDE_CONFIG: "serverName=${CAT_HOSTNAME_DOTENV}"
        SERVICETOOL_HOSTNAME_DOTENV: ${SERVICETOOL_HOSTNAME_DOTENV}
        SERVICETOOL_DOMAIN_DOTENV: ${SERVICETOOL_DOMAIN_DOTENV}
        CAT_HOSTNAME_DOTENV: ${CAT_HOSTNAME_DOTENV}
        CAT_DOMAIN_DOTENV: ${CAT_DOMAIN_DOTENV}
        # To authenticate to private registry either use username / password, or Oauth key/secret / etc
        # Please add Composer Auth JSON Object
        COMPOSER_AUTH: "{}"
        COMPOSER_MEMORY_LIMIT: -1
        COMPOSER_ALLOW_SUPERUSER: 1
    volumes:
      - ./app/config/parameters.yml:/var/www/app/app/config/parameters.yml:cached
    # depends_on:
    #   database:
    #     condition: service_healthy

  web-image:
    profiles:
      - image
    image: ghcr.io/skrepr/cameranu-admin-tool/web:pr-8
    restart: always
    ports:
      - "8080:8080"
      - "8443:8443"
    environment:
      PHP_INDEX: app.php
      PHP_FPM_HOST: php-image
      PHP_FPM_PORT: ${PHP_PORT-9000}
      VIRTUAL_HOST: servicetool.loc,cat.loc,test-tool.loc,cat-test.loc
      VIRTUAL_PORT: 8080
      TZ: Europe/Amsterdam
      SERVICETOOL_HOSTNAME_DOTENV: ${SERVICETOOL_HOSTNAME_DOTENV}
      SERVICETOOL_DOMAIN_DOTENV: ${SERVICETOOL_DOMAIN_DOTENV}
      CAT_HOSTNAME_DOTENV: ${CAT_HOSTNAME_DOTENV}
      CAT_DOMAIN_DOTENV: ${CAT_DOMAIN_DOTENV}
    networks:
      default:
        aliases:
          - servicetool.loc
          - cat.loc
          - test-tool.loc
          - cat-test.loc

  memcached:
    image: memcached:latest
    container_name: cnat_memcached
    hostname: cnat_memcached
    environment:
      - TZ=Europe/Amsterdam
    expose:
      - 11211

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.5
    container_name: cnat_elasticsearch
    hostname: cnat_elasticsearch
    environment:
      node.name: cnat_elasticsearch
      bootstrap.memory_lock: "true"
      discovery.type: single-node
      ES_JAVA_OPTS: "-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - esdata1:/usr/share/elasticsearch/data
      - ./docker/elasticsearch/config/extra:/usr/share/elasticsearch/config/extra:r
    ports:
      - 9200:9200

  rabbitmq:
    container_name: cnat_rabmq
    hostname: cnat_rabmq
    image: bitnami/rabbitmq:latest
    ports:
      - 15672:15672
      - 5672:5672
    environment:
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
      - RABBITMQ_USERNAME=${RABBITMQ_USERNAME}
      - RABBITMQ_MANAGEMENT_ALLOW_WEB_ACCESS=true
      - TZ=Europe/Amsterdam

  nginx-proxy:
    profiles:
      - proxy
    image: nginxproxy/nginx-proxy:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/tmp/docker.sock:ro
      - ./docker/web/etc/certs:/etc/nginx/certs:ro
      - ./docker/proxy/etc/nginx/conf.d/custom.conf:/etc/nginx/conf.d/custom.conf

volumes:
  esdata1:
    driver: local

networks:
  default:
    external: true
    name: nginx-proxy
