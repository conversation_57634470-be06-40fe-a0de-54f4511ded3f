#!/bin/sh
set -eu

# NOTE: rebuild docker image without cache after change

# ----------------------------------------------------------------------------------------------------------------------

echo "► Starting Production Entrypoint..."

# ----------------------------------------------------------------------------------------------------------------------

echo "► Expanding Dotenv files with Environment Variables..."
for f in $(find . -name ".env*"); do cat $f | envsubst "$(env | sed -e 's/=.*//' -e 's/^/\$/g')" > "$f.tmp"; mv "$f.tmp" "$f"; done

# ----------------------------------------------------------------------------------------------------------------------

# Run Entrypoint and pass CMD to it (Don't forget exec)
exec entrypoint-base "${@}"
