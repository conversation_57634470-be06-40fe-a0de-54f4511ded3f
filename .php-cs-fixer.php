<?php

$finder = PhpCsFixer\Finder::create()
    ->in('src')
;

return (new PhpCsFixer\Config())
    ->setRules([
        '@PSR12' => true,
        'array_syntax' => true,
        'no_unused_imports' => true,
        'blank_line_before_statement' => ['statements' => ['return']],
        'no_extra_blank_lines' => true,
        'ordered_imports' => true,
        'trailing_comma_in_multiline' => true,
        'ternary_to_null_coalescing' => true,
        'no_trailing_comma_in_singleline_array' => true,
        'cast_spaces' => ['space' => 'single'],
        'lowercase_cast' => true,
        'single_quote' => true,
    ])
    ->setFinder($finder)
;
