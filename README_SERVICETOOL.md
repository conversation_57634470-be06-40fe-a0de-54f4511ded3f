CameraNU.nl Servicetool
=======================

Na de checkout moet je een aantal acties uitvoeren in de root van je project:

	setfacl -R -m u:"apache":rwX -m u:`whoami`:rwX var/cache var/logs
	setfacl -dR -m u:"apache":rwX -m u:`whoami`:rwX var/cache var/logs
	composer install

Composer zal je vragen een aantal instellingen aan te passen, deze spreken voor zich.
Let wel op dat je de secret wel aanpast. Dit moet een lange random string zijn.

Als je wilt kijken of het project goed werkt in de productieomgeving kun je het volgende
doen in de root van je project:

	// De cache legen:
	php-c bin/console cache:clear --env=prod
	// Alle css en javscript combineren:
	php-c bin/console assetic:dump --env=prod --no-debug

Als je bijlages wilt uploaden in je dev-omgeving moet je de web/uploads/attachments schrijfbaar
maken voor de apache gebruiker.

Assets
=======================
Na de checkout dien je (in ieder geval voor de Docker setup) de assets te genereren, zodat deze als statische
content kan worden geserveerd. Dit doe je door de volgende commando's uit te voeren:

	bin/console assets:install
	bin/console assetic:dump

Of binnen de Docker setup:

	docker exec -it cnat_phpfpm bin/console assets:install
	docker exec -it cnat_phpfpm bin/console assetic:dump