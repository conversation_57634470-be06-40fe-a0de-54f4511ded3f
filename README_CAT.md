CameraNU.nl Administration Tool
=======================

Na de checkout moet je een aantal acties uitvoeren in de root van je project:

    mkdir var
	setfacl -R -m u:"apache":rwX -m u:`whoami`:rwX var/cache var/logs
	setfacl -dR -m u:"apache":rwX -m u:`whoami`:rwX var/cache var/logs
	composer install

Kopieer ook het `.env.dist` bestand naar `.env`. De default waardes kun je in eerste instantie laten staan.

Composer zal je vragen een aantal instellingen aan te passen, deze spreken voor zich.
Let wel op dat je de secret wel aanpast. Dit moet een lange random string zijn.

Als je wilt kijken of het project goed werkt in de productieomgeving kun je het volgende
doen in de root van je project:

	// De cache legen:
	php-c bin/console cache:clear --env=prod
	// Alle css en javscript combineren:
	php-c bin/console assetic:dump --env=prod --no-debug

Assets
=======================
Na de checkout dien je (in ieder geval voor de Docker setup) de assets te genereren, zodat deze als statische
content kan worden geserveerd. Dit doe je door de volgende commando's uit te voeren:

	bin/console assets:install
	bin/console assetic:dump

Of binnen de Docker setup:

	docker exec -it cnat_phpfpm bin/console assets:install
	docker exec -it cnat_phpfpm bin/console assetic:dump

Docker
=======================

Om de applicatie in (Docker) containers te starten, heb je allereerst een Docker installatie nodig. Docker kun je
downloaden op https://www.docker.com/get-docker en kies voor de Community Edition (CE). In de CE heb je ook direct
de beschikking over docker-compose, die je nodig zult hebben om de applicatie te starten in containers.

Eenmaal Docker gedownload en geïnstalleerd, zul je een Docker ID moeten aanmaken op https://hub.docker.com om
gebruik te kunnen maken van de Docker HUB waar de containers vandaan gedownload zullen gaan worden. Na het aanmaken
van het Docker ID kun je hiermee inloggen op je eigen Docker installatie en ben je klaar om met containers te gaan
werken.

De git checkout die je hebt gedaan van de applicatie is voorzien van een aantal extra bestanden om containers te
kunnen starten en te vullen met de juiste instellingen die nodig zijn voor een juiste werking van de applicatie.

Om direct van start kunnen kan je het commando uitvoeren om het op te starten zoals hieronder staat:
    
    ./start.sh

De virtualhost van de applicatie is opgebouwd in site.conf, de PHP settings staan in

	php.ini

uitgebreide instellingen voor (in dit geval) de PHP-FPM container staan in de

	Dockerfile

die in de docker-compose.yml file worden gekoppeld aan de juiste container. Mocht je meer fruitigheden nodig hebben voor
een van de andere containers, zoals in de Dockerfile beschreven staan, moet je hiervoor een extra Dockerfile met een
andere naam maken en deze koppelen aan de container d.m.v. de "build" optie:

	- build: ./Dockerfile-special

Let wel dat hiermee dan de mogelijkheid tot het gebruik van een standaard image komt te vervallen.
Verder is er nog sprake van het automatisch invoegen van het SSL certificaat.

What's next? Omdat je als het ware begint met een schone lei, moeten de container images gedownload worden en daarop de
instellingen worden toegepast. Om dit te doen, kun je dit aan docker-compose overlaten:

	docker-compose build

Dit zorgt ervoor dat de images klaar staan om gebruikt te gaan worden. Om de containers te starten, doe je:

	docker-compose up

of

	docker-compose up -d

als je docker-compose als daemon wilt laten draaien. Het voordeel van alleen docker-compose up is dat je meteen de
access log en error log van alle containers voorbij ziet komen in je scherm.
Om de containers te stoppen, doe je:

	docker-compose down

of

	Crtl + C

Bij deze laatste actie kan het voorkomen dat je een foutmelding ziet en kun je met

	docker-stats

zien dat er nog containers actief zijn en draaien. Als dat het geval is, kun je alsnog

	docker-compose down

doen en zal Docker zorgen dat de containers gestopt worden. Let wel: de containers worden alleen maar gestopt en niet
verwijderd. Mocht je dat willen, kun je doen:

	docker ps -a
	docker kill [(eerste twee karakters van het) container ID]
	docker rm [(eerste twee karakters van het) container ID]

Docker stats geeft waardevolle informatie over de containers, zoals bijvoorbeeld het geheugengebruik van een container.

Composer
-------
Na het opzetten van de Docker containers moet je nog een `composer install` doen binnen de PHP-FPM
container, waardoor de vendor directory gevuld of bijgewerkt wordt met de juiste informatie.
Dit kun je op de commandline doen door het volgende uit te voeren in de root van het project:

	docker exec -it cnat_phpfpm composer install

Of, als dat niet het gewenste resultaat geeft, kun je inloggen op de container met

	docker exec -it cnat_phpfpm bash

en het volgende doen:

	cd /data/www && composer install

of, als je al in de `/data/www` directory zit, waar de CAT applicatie zich bevindt:

	composer install

Labels printen
-------
Vanwege de architectuur van Docker is het niet zonder meer mogelijk om een extra service
binnen een container te starten. Deze extra service is nodig om (labels) te kunnen printen
vanuit de container. De printer definitie is te vinden in de `docker/cups` directory en
wordt in zijn geheel naar de container gekopieerd. Na het builden en starten van de container
moet CUPS nog handmatig gestart worden en dit kun je als volgt doen:

	docker exec -it cat_phpfpm /etc/init.d/cups start

Daarmee wordt CUPS gestart en kun printen op printers die in het `printers.conf` bestand
gedefinieerd zijn. Let hierbij vooral op de locatie en het IP-adres die bij de printers
genoemd staan om ervoor te zorgen dat je niet zomaar ergens een productieproces verstoort
door allerlei testdocumenten te printen.
