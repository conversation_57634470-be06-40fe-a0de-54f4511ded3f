<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;

return RectorConfig::configure()
    ->withPaths([
        __DIR__ . '/src/CatBundle/Entity',
        __DIR__ . '/src/ContentBundle/Entity',
        __DIR__ . '/src/FrontpageBundle/Entity',
        __DIR__ . '/src/Servicetool/ServiceBundle/Entity',
        __DIR__ . '/src/StatisticsBundle/Entity',
        __DIR__ . '/src/Webdsign/GlobalBundle/Entity',
        __DIR__ . '/src/WebsiteBundle/Entity',
    ])
    ->withAttributesSets(symfony: true, doctrine: true);
