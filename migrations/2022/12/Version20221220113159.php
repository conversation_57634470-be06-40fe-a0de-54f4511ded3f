<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20221220113159 extends AbstractMigration
{
    public const EAN_TRIMMED_INDEX = 'artikelen_ean_naam_IDX';

    public function getDescription() : string
    {
        return 'CAT-4068 virtuele kolom voor ean trimming';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("alter table `cameranu`.`artikelen_ean` add column if not exists `ean_trimmed` varchar(32) generated always as (trim(leading '0' from ean)) stored");
        $this->addSql(sprintf("create index `%s` USING BTREE ON `cameranu`.`artikelen_ean` (`naam`,`ean_trimmed`)", self::EAN_TRIMMED_INDEX));
    }

    public function down(Schema $schema) : void
    {
        $this->addSql(sprintf("alter table `cameranu`.`artikelen_ean` drop index `%s`", self::EAN_TRIMMED_INDEX));
        $this->addSql("alter table `cameranu`.`artikelen_ean` drop column if exists `ean_trimmed`");
    }
}
