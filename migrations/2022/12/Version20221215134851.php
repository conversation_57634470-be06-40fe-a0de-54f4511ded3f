<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221215134851 extends AbstractMigration
{
    private const IDX = 'courier_lists_products_stock_internalInvoiceLineId_IDX';
    public function getDescription(): string
    {
        return 'CAT-4074 add index for internalInvoiceLineId to courier_lists_products_stock.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`courier_lists_products_stock` ADD INDEX IF NOT EXISTS ' . self::IDX . ' (internalInvoiceLineId ASC)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`courier_lists_products_stock` DROP INDEX IF EXISTS ' . self::IDX);
    }
}
