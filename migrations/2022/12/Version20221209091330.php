<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221209091330 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-4069 tags_content_agenda table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `cameranu`.`tags_content_agenda` (
            id INT auto_increment NOT NULL,
            tagId INT NOT NULL,
            contentAgendaId INT NOT NULL,
            CONSTRAINT tags_content_agenda_PK PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`tags_content_agenda`;');
    }
}
