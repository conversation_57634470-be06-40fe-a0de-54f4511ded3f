<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221228083734 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-4096 add location to stockAnalysis';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`stock_locations_history` ADD COLUMN IF NOT EXISTS `location` varchar(255) DEFAULT \'\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`stock_locations_history` DROP COLUMN IF EXISTS `location`');
    }
}
