<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Domain;
use Webdsign\GlobalBundle\Entity\InternalInvoiceStockLocationInformation;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221215142825 extends AbstractMigration
{
    private const ROTTERDAM_NOORD_IBAN = '******************';
    private const ROTTERDAM_NOORD_BIC = 'ABNANL2A';

    public function getDescription() : string
    {
        return 'CAT-4081 Bankrekeningnummer op factuur Rotterdam Noord wijzigen';
    }

    public function up(Schema $schema) : void
    {
        $domain = [
            'id' => Domain::CAMERANU_ROTTERDAM_PRO,
            'iban' => self::ROTTERDAM_NOORD_IBAN,
        ];

        $internalInvoiceStockLocationInformation = [
            'id' => InternalInvoiceStockLocationInformation::INTERNAL_INVOICE_STOCK_LOCATION_INFORMATION_ROTTERDAM_PRO_ID,
            'iban' => self::ROTTERDAM_NOORD_IBAN,
            'bic' => self::ROTTERDAM_NOORD_BIC,
        ];

        $this->addSql("UPDATE `cameranu`.`domeinen` SET `iban` = :iban WHERE `id` = :id", $domain);
        $this->addSql("UPDATE `cameranu`.`internal_invoice_stock_location_information` SET `sepaIban` = :iban , `sepaBic` = :bic  WHERE `id` = :id", $internalInvoiceStockLocationInformation);
    }

    public function down(Schema $schema) : void
    {
        $domain = [
            'id' => Domain::CAMERANU_ROTTERDAM_PRO,
            'iban' => '',
        ];

        $internalInvoiceStockLocationInformation = [
            'id' => InternalInvoiceStockLocationInformation::INTERNAL_INVOICE_STOCK_LOCATION_INFORMATION_ROTTERDAM_PRO_ID,
            'iban' => '',
            'bic' => '',
        ];

        $this->addSql("UPDATE `cameranu`.`domeinen` SET `iban` = :iban WHERE `id` = :id", $domain);
        $this->addSql("UPDATE `cameranu`.`internal_invoice_stock_location_information` SET `sepaIban` = :iban , `sepaBic` = :bic  WHERE `id` = :id", $internalInvoiceStockLocationInformation);
    }
}
