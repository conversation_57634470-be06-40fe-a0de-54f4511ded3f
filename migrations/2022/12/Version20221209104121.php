<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221209104121 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-4069 events pageblock type toevoegen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
            VALUES (NOW(), NOW(), \'events\', 60, \'Content (Rebranding)\')');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = \'events\'');
    }
}
