<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use RuntimeException;

final class Version20240122135413 extends AbstractMigration
{
    public const BACKUP_TABLE = 'migration_backups';

    private const CHUNK_SIZE = 1000;

    public function getDescription(): string
    {
        return 'CAM-3683 - GA4 session_id als int meesturen';
    }

    public function up(Schema $schema): void
    {
        // select from table rows which are not numeric
        $query = 'SELECT * FROM `order_analytics_info` WHERE `session_id` REGEXP \'[a-z]+\'';
        $result = $this
            ->connection
            ->executeQuery($query)
            ->fetchAllAssociative();

        // move the values to the backup table
        $inserted = $this
            ->connection
            ->insert(self::BACKUP_TABLE, [
                'version' => self::class,
                'payload' => gzencode(json_encode($result, JSON_THROW_ON_ERROR))
            ]);
        if ($inserted !== 1) {
            throw new RuntimeException('Insert of backup failed, rows affected: ' . $inserted);
        }
        $rowsBackedUp = count($result);
        $this->write(sprintf('Finished backing up %s rows (%s)', $rowsBackedUp, self::class));

        // set the session_id of those rows to 0
        $rowIds = array_map(static fn (array $row) => $row['id'], $result);
        foreach (array_chunk($rowIds, self::CHUNK_SIZE) as $chunk) {
            $updateStmt = 'UPDATE `order_analytics_info` SET `session_id` = 0 WHERE `id` in ('
                . implode(',', $chunk)
                . ')';
            $affected = $this
                ->connection
                ->executeStatement($updateStmt);
            if ($affected !== count($chunk)) {
                throw new RuntimeException('Updating row failed, rows affected: ' . $affected);
            }
        }

        // update the column
        $this
            ->connection
            ->executeStatement('ALTER TABLE `order_analytics_info` MODIFY `session_id` INTEGER');
    }

    public function down(Schema $schema): void
    {
        $backupRow = $this
            ->connection
            ->executeQuery('SELECT * FROM `migration_backups` WHERE version = ?', [self::class])
            ->fetchAssociative();
        $backupContent = json_decode(gzdecode($backupRow['payload']), true, 512, JSON_THROW_ON_ERROR);

        // Change the column to varchar
        $this
            ->connection
            ->executeStatement('ALTER TABLE `order_analytics_info` MODIFY `session_id` VARCHAR(255)');

        // restore the values of the zeroed rows
        // Thanks to: https://stackoverflow.com/a/34866431/3767105 for batch update SQL
        foreach (array_chunk($backupContent, self::CHUNK_SIZE) as $chunk) {
            $values = array_map(
                static fn (array $row) => sprintf("(%s, '%s')", $row['id'], $row['session_id']),
                $chunk
            );
            // This will fail if no values is there for the id, but that's what we want
            $q = 'INSERT INTO `order_analytics_info` (id, session_id) VALUES '
                . implode(',', $values)
                . ' ON DUPLICATE KEY UPDATE `session_id`=VALUES(session_id)';
            $this->connection->executeStatement($q);
        }

        // remove the rows from backup table
        $this->connection->delete(self::BACKUP_TABLE, ['version' => self::class]);
    }
}
