<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240126110754 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2887 - Leverdatum (bekend bij leverancier) benoemen in productfeed voor advertising';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`) VALUES (?, ?, ?)', [
            'availability_date',
            ProductFeed::ADCHIEVE,
            'availabilityDate',
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `feed_id` = ? AND `key` = ?', [
            ProductFeed::ADCHIEVE,
            'availabilityDate',
        ]);
    }
}
