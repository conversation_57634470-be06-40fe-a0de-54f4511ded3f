<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240119095324 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3669 Add thumbnail_image to events table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`events` ADD COLUMN IF NOT EXISTS `thumbnail_image` VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`events` DROP COLUMN IF EXISTS `thumbnail_image`');
    }
}
