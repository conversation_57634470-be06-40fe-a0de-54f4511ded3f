<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240102093722 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-3043 Update minimal stock menu item resource';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu_urk`.`xs_resources`
            SET `shortDescription` = \'script-minimum-stock\'
            WHERE `resourceId` = 358;
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu_urk`.`xs_resources`
            SET `shortDescription` = \'script-/php/minimale_voorraad.php\'
            WHERE `resourceId` = 358;
        ');
    }
}
