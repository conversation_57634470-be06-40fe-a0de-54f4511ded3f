<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240109122639 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-3671 Eventbrite velden toevoegen aan evenementen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`event_products` ADD COLUMN IF NOT EXISTS `external_link` VARCHAR(255) NULL');
        $this->addSql('ALTER TABLE `cameranu`.`event_products` ADD COLUMN IF NOT EXISTS `external_name` VARCHAR(255) NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`event_products` DROP COLUMN IF EXISTS `external_link`');
        $this->addSql('ALTER TABLE `cameranu`.`event_products` DROP COLUMN IF EXISTS `external_name`');
    }
}
