<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240125084727 extends AbstractMigration
{
    private const DISCOUNTSET_POSITIONS_TABLE = 'discountset_positions';
    private const SPECS_SPECIFICATIONS_TABLE = 'specs_specifications';
    private const SPEC_PROFILE_ID = 'spec_profile_id';
    private const SPECS_SPECIFICATIONS_ID = 'spec_id';
    private const SPECS_ARTICLE_SPEC_VALUE = 'spec_value';
    // Needs to be globally unique
    private const SPECS_SPECIFICATIONS_ID_FK = 'FK_specs_specifications_id_discountset_positions';

    public function getDescription(): string
    {
        return 'CAM-2384 - Specifiekere accessoire kunnen selecteren combodeal';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::DISCOUNTSET_POSITIONS_TABLE);
        $table->addColumn(
            self::SPECS_SPECIFICATIONS_ID,
            Types::INTEGER,
            [
                'notnull' => false,
                // Should be true but needs to match with referenced column
                // 'unsigned' => true,
            ]
        );
        $table->addForeignKeyConstraint(
            self::SPECS_SPECIFICATIONS_TABLE,
            [self::SPECS_SPECIFICATIONS_ID],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                // For InnoDB with Mysql 5.5 this actually shows up as no action, but we like explicit
                // See http://download.nust.na/pub6/mysql/doc/refman/5.5/en/innodb-foreign-key-constraints.html
                'onDelete' => 'RESTRICT'
            ],
            self::SPECS_SPECIFICATIONS_ID_FK
        );
        $table->addColumn(
            self::SPECS_ARTICLE_SPEC_VALUE,
            Types::STRING,
            [
                // Matches the value from specs_article_specifications value column
                'length' => 750,
                'notnull' => false,
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::DISCOUNTSET_POSITIONS_TABLE);
        $table->removeForeignKey(self::SPECS_SPECIFICATIONS_ID_FK);
        $table->dropColumn(self::SPECS_SPECIFICATIONS_ID);
        $table->dropColumn(self::SPECS_ARTICLE_SPEC_VALUE);
    }
}
