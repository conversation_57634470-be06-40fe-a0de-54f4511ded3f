<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240812091123 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4240 add table for excluded tags and add columns for specs and options for discountcodes';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `condition_tags_discounts_excluded` (
          `tagId` int(11) NOT NULL,
          `itemId` int(11) NOT NULL,
          PRIMARY KEY (`tagId`,`itemId`),
          KEY `itemId` (`itemId`),
          KEY `tagId` (`tagId`),
          CONSTRAINT `condition_tags_discounts_excluded_ibfk_2` FOREIGN KEY (`tagId`) REFERENCES `tags` (`tagId`) ON DELETE CASCADE ON UPDATE CASCADE,
          CONSTRAINT `condition_tags_discounts_excluded_ibfk_3` FOREIGN KEY (`itemId`) REFERENCES `discountcodes` (`discountId`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');

        $discountCodeTable = $schema->getTable('discountcodes');
        $discountCodeTable->addColumn('options', Types::TEXT, [
            'notnull' => false,
        ]);

        $discountCodeTable->addColumn('all_tags', Types::BOOLEAN, [
            'notnull' => true,
            'default' => false,
        ]);

        $discountCodeTable->addColumn('all_specs', Types::BOOLEAN, [
            'notnull' => true,
            'default' => false,
        ]);

        $discountCodeTable->addIndex(['all_specs'], 'idx_all_tags');
        $discountCodeTable->addIndex(['all_tags'], 'idx_all_specs');

        $discountCodeBatchTable = $schema->getTable('discount_code_batches');
        $discountCodeBatchTable->addColumn('options', Types::TEXT, [
            'notnull' => false,
        ]);

        $discountCodeBatchTable->addColumn('all_tags', Types::BOOLEAN, [
            'notnull' => true,
            'default' => false,
        ]);

        $discountCodeBatchTable->addColumn('all_specs', Types::BOOLEAN, [
            'notnull' => true,
            'default' => false,
        ]);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('condition_tags_discounts_excluded');

        $discountCodeTable = $schema->getTable('discountcodes');
        $discountCodeTable->dropColumn('options');
        $discountCodeTable->dropColumn('all_tags');
        $discountCodeTable->dropColumn('all_specs');

        $discountCodeBatchTable = $schema->getTable('discount_code_batches');
        $discountCodeBatchTable->dropColumn('options');
        $discountCodeBatchTable->dropColumn('all_tags');
        $discountCodeBatchTable->dropColumn('all_specs');
    }
}
