<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Origin;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240819095311 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2368 - Herkomsten archiveren';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`bestelling_herkomst` ADD COLUMN `status` enum(\'active\',\'archived\') DEFAULT \'active\'');

        $this->addSql('UPDATE `cameranu`.`bestelling_herkomst` SET `status` = \'archived\' WHERE `id` IN (
            ' . Origin::AMSTERDAM_PRO . ',
            ' . Origin::AMSTERDAM_PRINTSHOP . ',
            ' . Origin::ROTTERDAM_PRO . '
        )');
    }

    public function down(Schema $schema): void
    {
        $originTable = $schema->getTable('bestelling_herkomst');
        $originTable->dropColumn('status');
    }
}
