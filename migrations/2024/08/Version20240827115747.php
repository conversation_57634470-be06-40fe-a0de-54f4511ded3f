<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240827115747 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.discountsets');
        $table->addColumn('has_price_range', Types::BOOLEAN)
            ->setDefault(false);
        $table->addColumn('price_range_min', Types::FLOAT)
            ->setNotnull(false);
        $table->addColumn('price_range_max', Types::FLOAT)
            ->setNotnull(false);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.discountsets');
        $table->dropColumn('has_price_range', Types::BOOLEAN);
        $table->dropColumn('price_range_min', Types::FLOAT);
        $table->dropColumn('price_range_max', Types::FLOAT);
    }
}
