<?php

declare(strict_types=1);

namespace App\Migrations;

use <PERSON>B<PERSON>le\MigrationHelper\ManagesForeignKeys;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240806101756 extends AbstractMigration
{
    use ManagesForeignKeys;

    public function getDescription(): string
    {
        return 'CAM-4192 Table for discountCode batches';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `discount_code_batches` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `name` varchar(255) NOT NULL,
              `description` varchar(255) NOT NULL,
              `information` varchar(255)  NOT NULL,
              `discount` varchar(255) NOT NULL,
              `start_date` datetime,
              `end_date` datetime,
              `ledger_code` varchar(255) NOT NULL,
              `available_codes` int(11) NOT NULL DEFAULT 1,
              `minimal_amount`  double DEFAULT NULL,
              `only_one_per_product` tinyint(1) NOT NULL DEFAULT 1,
              `type` enum(\'siteWide\',\'selection\') NOT NULL,
              `origin` enum(\'CameraNU.nl\',\'AutoGeneratedClang\', \'Sovendus\') NOT NULL DEFAULT \'CameraNU.nl\',
              `prefix` varchar(255) NOT NULL,
              `total_codes` int(11) NOT NULL,
              `archived` tinyint(1) NOT NULL DEFAULT 0,
              PRIMARY KEY (`id`),
              KEY `idx_start_date` (`start_date`),
              KEY `idx_end_date` (`end_date`),
              KEY `idx_only_one_per_product` (`only_one_per_product`),
              KEY `idx_type` (`type`),
              KEY `idx_origin` (`origin`),
              KEY `idx_archived` (`archived`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

        $table = $schema->getTable('discountcodes');
        $table->addColumn('batch_id', Types::INTEGER, [
            'notnull' => false,
            'unsigned' => true,
        ]);

        $table->addForeignKeyConstraint(
            'discount_code_batches',
            ['batch_id'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE'],
            'fk_discount_code_batch'
        );

        $table->addIndex(['origin'], 'idx_origin');
        $table->addIndex(['siteWide'], 'idx_site_wide');
        $table->addIndex(['conditions'], 'idx_conditions');
        $table->addIndex(['only_one_per_product'], 'idx_only_one_per_product');
        $table->addIndex(['startDate'], 'idx_start_date');
        $table->addIndex(['endDate'], 'idx_end_date');
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('discountcodes');
        $table->dropColumn('batch_id');
        $table->dropIndex('idx_origin');
        $table->dropIndex('idx_site_wide');
        $table->dropIndex('idx_conditions');
        $table->dropIndex('idx_only_one_per_product');
        $table->dropIndex('idx_start_date');
        $table->dropIndex('idx_end_date');
        $table->dropIndex(self::getIndexNameByColumnName('discountcodes', 'batch_id'));
        $table->removeForeignKey('fk_discount_code_batch');

        $schema->dropTable('discount_code_batches');
    }
}
