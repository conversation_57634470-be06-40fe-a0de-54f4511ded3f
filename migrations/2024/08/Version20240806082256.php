<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240806082256 extends AbstractMigration
{
    private const TABLE_NAME = 'pages';
    private const NAME_COLUMN = 'structured_data_name';
    private const DESCRIPTION_COLUMN = 'structured_data_description';
    private const CATEGORY_COLUMN = 'structured_data_category';
    private const MENTION_COLUMN = 'structured_data_mention';

    public function getDescription(): string
    {
        return 'CAM-5074 - Velden toevoegen structured data page builder';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->addColumn(
            self::NAME_COLUMN,
            Types::STRING,
            [
                'notnull' => false,
                'default' => null,
            ]
        );
        $table->addColumn(
            self::DESCRIPTION_COLUMN,
            Types::TEXT,
            [
                'notnull' => false,
                'default' => null,
            ]
        );
        $table->addColumn(
            self::CATEGORY_COLUMN,
            Types::STRING,
            [
                'notnull' => false,
                'default' => null,
            ]
        );
        $table->addColumn(
            self::MENTION_COLUMN,
            Types::STRING,
            [
                'notnull' => false,
                'default' => null,
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->dropColumn(self::NAME_COLUMN);
        $table->dropColumn(self::DESCRIPTION_COLUMN);
        $table->dropColumn(self::CATEGORY_COLUMN);
        $table->dropColumn(self::MENTION_COLUMN);
    }
}
