<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240809131946 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4905 Add extra statuses and timeline steps for quotes';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cameranu.quotes MODIFY COLUMN status ENUM(\'draft\',\'open\',\'sent\',\'cancelled\',\'done\',\'products_requested\',\'requests_handled\')');

        $this->addSql('ALTER TABLE cameranu.quote_steps ADD COLUMN `pos` INT(10) UNSIGNED NOT NULL');
        $this->addSql('UPDATE cameranu.quote_steps SET `pos` = id');
        $this->addSql('ALTER TABLE cameranu.quote_steps ADD UNIQUE KEY quote_steps_pos_IDX (pos) USING BTREE');
        $this->addSql('UPDATE cameranu.quote_steps SET pos = pos + 2 WHERE pos > 1 ORDER BY `pos` DESC');
        $this->addSql('INSERT INTO cameranu.quote_steps VALUES (9, \'Productaanvragen verwerkt\', \'["all"]\', 2), (10, \'Offerte-aanmaak afgerond\', \'["all"]\', 3)');
        $this->addSql('UPDATE cameranu.quote_steps SET show_on_status = \'["draft", "open", "sent", "done", "cancelled"]\' WHERE id = 2');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cameranu.quotes MODIFY COLUMN status ENUM(\'draft\', \'open\', \'sent\', \'cancelled\', \'done\')');
        $this->addSql('ALTER TABLE cameranu.quote_steps DROP INDEX quote_steps_pos_IDX');
        $this->addSql('ALTER TABLE cameranu.quote_steps DROP COLUMN pos');
        $this->addSql('UPDATE cameranu.quote_steps SET show_on_status = \'["all"]\' WHERE id = 2');
        $this->addSql('DELETE FROM cameranu.quote_steps WHERE id IN (9, 10)');
    }
}
