<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240816063443 extends AbstractMigration
{
    private const REFUND_REF_COLUMN_NAME = 'psp_refund_reference';

    public function getDescription(): string
    {
        return 'CAM-4678 Add refund reference to order payment table';
    }

    public function up(Schema $schema): void
    {
        $orderPaymentsTable = $schema->getTable('bestelling_betaling');
        $orderPaymentsTable->addColumn(
            self::REFUND_REF_COLUMN_NAME,
            Types::STRING,
            [
                'length' => 255,
            ]
        );

        $orderPaymentsTable->addIndex([self::REFUND_REF_COLUMN_NAME]);
    }

    public function down(Schema $schema): void
    {
        $orderPaymentsTable = $schema->getTable('bestelling_betaling');
        $orderPaymentsTable->dropColumn(self::REFUND_REF_COLUMN_NAME);
    }
}
