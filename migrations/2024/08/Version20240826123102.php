<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240826123102 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5025 Add position column to discountsets table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`discountsets` ADD COLUMN `position` int(11) DEFAULT NULL');
        $this->addSql(<<<SQL
            UPDATE
            `cameranu`.`discountsets` d1
            JOIN (
                SELECT
                    id,
                    @row := @row + 1 AS `position`
                FROM
                    `cameranu`.`discountsets`
                    CROSS JOIN (
                        SELECT
                            @row := 0
                    ) r
                ORDER BY
                    id DESC
            ) d2 ON d1.id = d2.id
        SET
            d1.`position` = d2.`position`
        SQL);
        $this->addSql('ALTER TABLE `cameranu`.`discountsets` MODIFY COLUMN `position` int(11) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $schema->getTable('cameranu.discountsets')
            ->dropColumn('position');
    }
}
