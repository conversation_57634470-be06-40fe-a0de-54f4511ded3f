<?php

declare(strict_types=1);

namespace App\Migrations;

use <PERSON>B<PERSON>le\Enum\InstantQuote\ExplanationDesign;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240805135720 extends AbstractMigration
{
    private const TABLE_NAME = 'quote_questions';

    public function getDescription(): string
    {
        return 'CAM-4749 - CAT - 2nd hand – meer informatie bij vragen beantwoorden';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`' . self::TABLE_NAME . '` ADD COLUMN IF NOT EXISTS `explanation_design` enum(
                "' . ExplanationDesign::HORIZONTAL_IMAGE_LIST->value . '",
                "' . ExplanationDesign::VERTICAL_IMAGE_LIST->value . '",
                "' . ExplanationDesign::TEXT->value . '"
            ) DEFAULT NULL');

        $schema
            ->getTable(self::TABLE_NAME)
            ->addColumn('explanation_title', Types::STRING, [
                'notnull' => false
            ]);

        $schema
            ->getTable(self::TABLE_NAME)
            ->addColumn('explanation_description', Types::TEXT, [
                'notnull' => false
            ]);

        $imagesTable = $schema->createTable('cameranu.quote_questions_explanation_image');
        $imagesTable->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
        ]);
        $imagesTable->addColumn('position', Types::INTEGER, [
            'unsigned' => true,
        ]);
        $imagesTable->addColumn('quote_question_id', Types::INTEGER, [
            'unsigned' => true,
        ]);
        $imagesTable->addColumn('title', Types::TEXT, [
            'length' => 255,
        ]);
        $imagesTable->addColumn('description', Types::TEXT, [
            'notnull' => false,
        ]);
        $imagesTable->addColumn('image', Types::TEXT);
        $imagesTable->addColumn('image_description', Types::TEXT, [
            'length' => 255,
        ]);

        $imagesTable->setPrimaryKey(['id']);
        $imagesTable->addForeignKeyConstraint(
            'quote_questions',
            ['quote_question_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->dropColumn('explanation_design');
        $table->dropColumn('explanation_title');
        $table->dropColumn('explanation_description');
        $table->dropColumn('explanation_image');
        $table->dropColumn('explanation_image_list');

        $schema->dropTable('cameranu.quote_questions_explanation_image');
    }
}
