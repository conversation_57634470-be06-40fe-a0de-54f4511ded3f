<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240821143642 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5065 add parking folder for CBS backorders';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'INSERT INTO parkeren (id, domain_id, pos, omschrijving, info, flags) VALUES (2323, 1, 3209, "Backorders CBS", "", 112)'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM parkeren WHERE omschrijving = "Backorders CBS"');
    }
}
