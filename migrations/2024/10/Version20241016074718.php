<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241016074718 extends AbstractMigration
{

    public const PROMOTIONS_TABLE_NAME = 'cameranu.discountset_promotions';

    public function getDescription(): string
    {
        return 'CAM-5329 Add discountset promotions';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable(self::PROMOTIONS_TABLE_NAME);

        $table->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true
        ]);
        $table->addColumn('name', Types::STRING);
        $table->addColumn('active', Types::BOOLEAN, [
            'default' => false,
        ]);

        $table->addColumn('active_from', Types::DATETIME_IMMUTABLE);
        $table->addColumn('active_to', Types::DATETIME_IMMUTABLE);
        $table->addColumn('type', Types::STRING, [
            'length' => 64
        ]);

        $table->addColumn('discounts', Types::JSON);

        $table->setPrimaryKey(['id']);

        $table->addIndex([
            'active',
            'active_from',
            'active_to',
        ]);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::PROMOTIONS_TABLE_NAME);
    }
}
