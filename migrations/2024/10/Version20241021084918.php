<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241021084918 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5470 - Migration voor herstellen lostShopcart functionaliteit';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('shopcart_lost');
        $table->addColumn('parent_shopcart_id', Types::INTEGER, [
            'length' => 11,
            'notnull' => false,
            'default' => null,
        ]);
        $table->addIndex(['parent_shopcart_id'], 'parent_shopcart_id');

        $table = $schema->getTable('shopcart');
        $table->addColumn('hash', Types::STRING, [
            'length' => 32,
            'notnull' => false,
            'default' => null,
        ]);
        $table->addIndex(['hash'], 'hash');
    }

    public function down(Schema $schema): void
    {
        $schema->getTable('shopcart_lost')->dropIndex('parent_shopcart_id');
        $schema->getTable('shopcart_lost')->dropColumn('parent_shopcart_id');
        $schema->getTable('shopcart')->dropIndex('hash');
        $schema->getTable('shopcart')->dropColumn('hash');
    }
}
