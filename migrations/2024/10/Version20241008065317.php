<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20241008065317 extends AbstractMigration
{
    private const string TABLE_STOCK_SCAN_LIST = 'cameranu.stock_scan_list';
    private const string TABLE_LOCATION_COMPARTMENTS = 'cameranu.locatieVakken';
    private const string TABLE_LOCATION_COMPARTMENT_NODES = 'cameranu.locatieVakkenNodes';
    private const string TABLE_USERS = 'cameranu_urk.users';
    private const string FK_LOCATION_COMPARTMENT = 'fk_location_compartment';
    private const string FK_LOCATION_COMPARTMENT_NODE = 'fk_location_compartment_node';
    private const string FK_CREATED_BY = 'fk_created_by';
    private const string FK_UPDATED_BY = 'fk_updated_by';

    public function getDescription(): string
    {
        return 'CAM-2458 - change inventory entities';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_STOCK_SCAN_LIST);

        $table->addColumn('location_compartment_id', Types::INTEGER, [
            'notnull' => false,
        ]);
        $table->addColumn('location_compartment_node_id', Types::INTEGER, [
            'notnull' => false,
        ]);
        $table->addColumn('list_type', 'stock_scan_list_type_enum', [
            'notnull' => false,
        ]);
        $table->addColumn('status', 'stock_scan_list_status_enum', [
            'notnull' => false,
        ]);
        $table->addColumn('updated_by', Types::INTEGER, [
            'notnull' => false,
        ]);
        $table->addForeignKeyConstraint(
            self::TABLE_LOCATION_COMPARTMENTS,
            ['location_compartment_id'],
            ['id'],
            [
                'onDelete' => 'SET NULL',
                'onUpdate' => 'CASCADE',
            ],
            self::FK_LOCATION_COMPARTMENT,
        );
        $table->addForeignKeyConstraint(
            self::TABLE_LOCATION_COMPARTMENT_NODES,
            ['location_compartment_node_id'],
            ['id'],
            [
                'onDelete' => 'SET NULL',
                'onUpdate' => 'CASCADE',
            ],
            self::FK_LOCATION_COMPARTMENT_NODE,
        );
        $table->addForeignKeyConstraint(
            self::TABLE_USERS,
            ['created_by'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ],
            self::FK_CREATED_BY,
        );
        $table->addForeignKeyConstraint(
            self::TABLE_USERS,
            ['updated_by'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ],
            self::FK_UPDATED_BY,
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_STOCK_SCAN_LIST);

        $table->dropColumn('location_compartment_id');
        $table->dropColumn('location_compartment_node_id');
        $table->dropColumn('list_type');
        $table->dropColumn('status');
        $table->dropColumn('updated_by');
        $table->removeForeignKey(self::FK_LOCATION_COMPARTMENT);
        $table->removeForeignKey(self::FK_LOCATION_COMPARTMENT_NODE);
        $table->removeForeignKey(self::FK_CREATED_BY);
        $table->removeForeignKey(self::FK_UPDATED_BY);
    }
}
