<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241004090420 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5249 - add product id to discountsets table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.discountsets');

        $table->modifyColumn('spec_profile_id', ['notnull' => false]);
        $table->addColumn('product_id', Types::INTEGER, ['notnull' => false]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.discountsets');

        $table->modifyColumn('spec_profile_id', ['notnull' => true]);
        $table->dropColumn('product_id');
    }
}
