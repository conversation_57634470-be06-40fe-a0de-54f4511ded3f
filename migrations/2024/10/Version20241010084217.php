<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20241010084217 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5396 update QuoteQuestionOption cascade rules';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`quote_question_options`
                DROP FOREIGN KEY `quote_question_options_question_id_fk`;
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`quote_question_options`
                ADD CONSTRAINT `quote_question_options_question_id_fk`
                FOREIGN KEY (`question_id`) REFERENCES `quote_questions` (`id`)
                ON DELETE CASCADE
                ON UPDATE CASCADE;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`quote_question_options`
                DROP FOREIGN KEY `quote_question_options_question_id_fk`;
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`quote_question_options`
                ADD CONSTRAINT `quote_question_options_question_id_fk`
                FOREIGN KEY (`question_id`) REFERENCES `quote_questions` (`id`)
                ON DELETE RESTRICT
                ON UPDATE RESTRICT;
        ');
    }
}
