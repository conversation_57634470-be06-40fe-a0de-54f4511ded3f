<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\MailingTypes;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241007074635 extends AbstractMigration
{
    private const MAILING_TYPES_TABLE_NAME = 'mailing_types';
    private const WEBUSER_MAILING_TYPES_TABEL_NAME = 'webuser_mailing_types';
    private const NAME = 'Post';
    private const NAME_EN = 'Mail';
    private const SQUEEZELY_KEY = 'custom_mailpref_post';
    private const MAIL_POST = MailingTypes::MAIL_POST;

    public function getDescription(): string
    {
        return 'CAM-5340 - Briefpost opt-out';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            "INSERT INTO ". self::MAILING_TYPES_TABLE_NAME ."
            (`id`,`name`, `name_en`, `active`, `default_for_new_user`, `mailing_category_id`, `squeezely_key`) VALUES
            (". self::MAIL_POST .", '". self::NAME ."', '". SELF::NAME_EN ."', 1, 1, 2, '". SELF::SQUEEZELY_KEY ."')
        ");

        $this->addSql('
            INSERT IGNORE INTO `'. self::WEBUSER_MAILING_TYPES_TABEL_NAME .'` (`webuser_id`, `mailing_type_id`)
            (SELECT id, '. self::MAIL_POST .' FROM webusers);
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            'DELETE FROM '. self::MAILING_TYPES_TABLE_NAME .'
            WHERE id = '. self::MAIL_POST .'
            AND name = "'. SELF::NAME .'"
            AND name_en = "'. SELF::NAME_EN .'"
            AND squeezely_key = "'. SELF::SQUEEZELY_KEY .'"
        ');

        $this->addSql('
            DELETE FROM '. self::WEBUSER_MAILING_TYPES_TABEL_NAME .'
            WHERE mailing_type_id = '. self::MAIL_POST );
    }
}
