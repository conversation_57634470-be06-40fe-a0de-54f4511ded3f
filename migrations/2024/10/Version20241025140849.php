<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241025140849 extends AbstractMigration
{
    private const QUOTE_PRODUCTS_TABLE = 'quote_products';


    public function getDescription(): string
    {
        return 'CAM-5323 - Accessoires gaan mee in de -5% berekening';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable($this::QUOTE_PRODUCTS_TABLE)->addColumn(
            'missing_accessory_total_customer',
            'float',
            [
                'notnull' => false,
                'default' => null
            ]
        );
        $schema->getTable($this::QUOTE_PRODUCTS_TABLE)->addColumn(
            'missing_accessory_total_definitive',
            'float',
            [
                'notnull' => false,
                'default' => null
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $schema->getTable($this::QUOTE_PRODUCTS_TABLE)->dropColumn('missing_accessory_total_customer');
        $schema->getTable($this::QUOTE_PRODUCTS_TABLE)->dropColumn('missing_accessory_total_definitive');
    }
}
