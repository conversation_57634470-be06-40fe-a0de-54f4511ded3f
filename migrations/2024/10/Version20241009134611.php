<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\PaymentType;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241009134611 extends AbstractMigration
{
    private const array PIN_TERMINALS = [
        [
            'payment_type_id' => PaymentType::UTRECHT_A920_1,
            'new_terminal_id' => 'term_2X6JqzkhUqqTmbbcx7kwH',
            'old_terminal_id' => 'term_tKBQhSyG6ucVK88dtvfkH',
        ],
        [
            'payment_type_id' => PaymentType::UTRECHT_A920_2,
            'new_terminal_id' => 'term_pk9td9VAqnpFzFNkn8kwH',
            'old_terminal_id' => 'term_uXhDhujVFN3yhavbawfkH',
        ],
        [
            'payment_type_id' => PaymentType::UTRECHT_A920_3,
            'new_terminal_id' => 'term_d6deEfScokXGRzBnGHVwH',
            'old_terminal_id' => 'term_KehFcZ8STBbsg3N66YtnH',
        ],
        [
            'payment_type_id' => PaymentType::UTRECHT_A35_1,
            'new_terminal_id' => 'term_FZhxP6rvNYaqoRTB98kwH',
            'old_terminal_id' => 'term_epYRwVhhvrUVBdSExwfkH',
        ]
    ];

    public function getDescription(): string
    {
        return 'CAM-5404 - Utrecht Terminals updaten';
    }

    public function up(Schema $schema): void
    {
        foreach (self::PIN_TERMINALS as $terminal) {
            $this->addSql('UPDATE `cameranu`.`pin_terminals` SET `terminal_id` = \'' . $terminal['new_terminal_id'] . '\' WHERE `payment_type_id` = ' . $terminal['payment_type_id']);
        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::PIN_TERMINALS as $terminal) {
            $this->addSql('UPDATE `cameranu`.`pin_terminals` SET `terminal_id` = \'' . $terminal['old_terminal_id'] . '\' WHERE `payment_type_id` = ' . $terminal['payment_type_id']);
        }
    }
}
