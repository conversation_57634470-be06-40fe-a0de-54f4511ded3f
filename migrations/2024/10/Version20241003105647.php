<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241003105647 extends AbstractMigration
{
    private const EVENT_PRODUCTS_TABLE = 'event_products';

    public function getDescription(): string
    {
        return 'CAM-4732 - Programmalijst naar eventdetails verhuizen';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::EVENT_PRODUCTS_TABLE)->addColumn(
            'program_list_event',
            'text',
            [
                'notnull' => false,
                'length' => '65535'
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::EVENT_PRODUCTS_TABLE)->dropColumn('program_list_event');
    }
}
