<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241022150210 extends AbstractMigration
{
    private const array IDS = [
        2431,
        2432,
        2433,
        2434,
        2435,
        2436,
        2437,
        2438,
    ];

    public function getDescription(): string
    {
        return 'CAM-4784 - Ship from store Parkeermappen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `parkeren` (`id`, `domain_id`, `pos`, `omschrijving`, `info`, `flags`)
            VALUES
                (\'2431\', \'1\', \'670\', \'------------------------------\', X\'\', \'0\'),
                (\'2432\', \'1\', \'671\', \'Ship from store URK\', X\'\', \'40\'),
                (\'2433\', \'1\', \'672\', \'Ship from store AMS\', X\'\', \'40\'),
                (\'2434\', \'1\', \'673\', \'Ship from store APL\', X\'\', \'40\'),
                (\'2435\', \'1\', \'674\', \'Ship from store GRO\', X\'\', \'40\'),
                (\'2436\', \'1\', \'675\', \'Ship from store ROT\', X\'\', \'40\'),
                (\'2437\', \'1\', \'676\', \'Ship from store ANT\', X\'\', \'40\'),
                (\'2438\', \'1\', \'677\', \'Ship from store UTR\', X\'\', \'40\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `parkeren` WHERE `id` in ( ' . implode(',', self::IDS) . ' )');
    }
}
