<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241014151010 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5333 add tables for discount batch conditions';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE  `cameranu`.`condition_tags_discount_batch` (
            `tagId` int(11) NOT NULL,
            `itemId` int(11) unsigned NOT NULL,
            PRIMARY KEY (`tagId`, `itemId`),
            FOREIGN KEY (`tagId`) REFERENCES `cameranu`.`tags`(`tagId`) ON UPDATE CASCADE ON DELETE CASCADE,
            FOREIGN KEY (`itemId`) REFERENCES `cameranu`.`discount_code_batches`(`id`) ON UPDATE CASCADE ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');

        $this->addSql('CREATE TABLE  `cameranu`.`condition_tags_discount_batch_excluded` (
            `tagId` int(11) NOT NULL,
            `itemId` int(11) unsigned NOT NULL,
            PRIMARY KEY (`tagId`,`itemId`),
            FOREIGN KEY (`tagId`) REFERENCES `cameranu`.`tags`(`tagId`) ON UPDATE CASCADE ON DELETE CASCADE,
            FOREIGN KEY (`itemId`) REFERENCES `cameranu`.`discount_code_batches`(`id`) ON UPDATE CASCADE ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');

        $this->addSql('CREATE TABLE  `cameranu`.`condition_products_discount_batch` (
            `productId` int(11) NOT NULL,
            `itemId` int(11) unsigned NOT NULL,
            PRIMARY KEY (`productId`,`itemId`),
            FOREIGN KEY (`productId`) REFERENCES `cameranu`.`artikelen`(`id`) ON UPDATE CASCADE ON DELETE CASCADE,
            FOREIGN KEY (`itemId`) REFERENCES `cameranu`.`discount_code_batches`(`id`) ON UPDATE CASCADE ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');

    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('condition_tags_discount_batch');
        $schema->dropTable('condition_tags_discount_batch_excluded');
        $schema->dropTable('condition_products_discount_batch');
    }
}
