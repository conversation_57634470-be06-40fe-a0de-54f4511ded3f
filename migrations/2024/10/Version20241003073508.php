<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241003073508 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5231 - Opschonen + keuzeoptie toevoegen additionele tool';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO fl_margin_sub (`fl_margin_id`,`description`) VALUES
                (1,"Additionele productpremie"),
                (4,"Additionele productpremie"),
                (7,"Additionele productpremie"),
                (1,"Additionele marketingbijdrage"),
                (4,"Store support")
        ');

    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM fl_margin_sub WHERE fl_margin_id = 1 AND description = "Additionele productpremie"');
        $this->addSql('DELETE FROM fl_margin_sub WHERE fl_margin_id = 4 AND description = "Additionele productpremie"');
        $this->addSql('DELETE FROM fl_margin_sub WHERE fl_margin_id = 7 AND description = "Additionele productpremie"');
        $this->addSql('DELETE FROM fl_margin_sub WHERE fl_margin_id = 1 AND description = "Additionele marketingbijdrage"');
        $this->addSql('DELETE FROM fl_margin_sub WHERE fl_margin_id = 4 AND description = "Store support"');
    }
}
