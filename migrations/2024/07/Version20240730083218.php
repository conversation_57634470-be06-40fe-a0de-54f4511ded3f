<?php

declare(strict_types=1);

namespace App\Migrations;

use <PERSON><PERSON><PERSON>le\MigrationHelper\UrkMigration;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\DBAL\Types\Types;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240730083218 extends UrkMigration
{
    public const TABLE = 'service_mailboxes';
    public const COLUMN = 'importable_with_imap';

    public function getDescription(): string
    {
        return 'CAM-5054 Add option to import mailbox with the IMAP importer';
    }

    /**
     * @throws SchemaException
     */
    public function upForUrk(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->addColumn(self::COLUMN, Types::BOOLEAN, ['default' =>  true]);
    }

    /**
     * @throws SchemaException
     */
    public function downForUrk(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->dropColumn(self::COLUMN);
    }
}
