<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240731085141 extends AbstractMigration
{
    private const TABLE_NAME = 'calculated_purchase_prices';
    private const COLUMN_NAME = 'is_preferred';

    public function getDescription(): string
    {
        return 'CAM-4758 add option to calculated_purchase_prices table to mark price as preferred';
    }

    public function up(Schema $schema): void
    {
        $schema
            ->getTable(self::TABLE_NAME)
            ->addColumn(
                self::COLUMN_NAME,
                Types::BOOLEAN,
                [
                    'notnull' => true,
                    'default' => false
                ]
            );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->dropColumn(self::COLUMN_NAME);
    }
}
