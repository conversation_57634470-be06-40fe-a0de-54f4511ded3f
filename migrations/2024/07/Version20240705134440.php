<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240705134440 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4881 add indexes to table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('artikelen_prijzen_achteraf_log');
        $table->addIndex(['article_id'], 'article_id_idx');
        $table->addIndex(['date_start'], 'date_start_idx');
        $table->addIndex(['date_end'], 'date_end_idx');
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('artikelen_prijzen_achteraf_log');
        $table->dropIndex('article_id_idx');
        $table->dropIndex('date_start_idx');
        $table->dropIndex('date_end_idx');
    }
}
