<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240723123230 extends AbstractMigration
{
    private const TABLE = 'leveranciers_verzamel';

    public function getDescription(): string
    {
        return 'CAM-4821 add packingSlipCluster option to supplierGroups';
    }

    public function up(Schema $schema): void
    {
        $schema
            ->getTable(self::TABLE)
            ->addColumn(
                'cluster_packing_slips',
                Types::BOOLEAN,
                [
                    'notnull' => true,
                    'default' => false
                ]
            );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->dropColumn('cluster_packing_slips');
    }
}
