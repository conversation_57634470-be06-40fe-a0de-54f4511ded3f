<?php

declare(strict_types=1);

namespace App\Migrations;

use <PERSON>B<PERSON>le\MigrationHelper\ManagesForeignKeys;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240703122542 extends AbstractMigration
{
    use ManagesForeignKeys;

    public function getDescription(): string
    {
        return 'CAM-2958 add some columns and relations for PostNL labels and quotes';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('postnl');
        $table->addColumn(
            'quote_id',
            Types::INTEGER, [
                'unsigned' => true,
                'notnull' => false,
            ]
        );

        $table->addForeignKeyConstraint(
            'cameranu.quotes',
            ['quote_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_quote_postnl_quotes'
        );

        $table->addColumn('file_path', Types::STRING, [
            'notnull' => false,
        ]);

        $externalCommunicationTable = $schema->getTable('external_communication');
        $externalCommunicationTable->addColumn(
            'quote_id',
            Types::INTEGER, [
                'unsigned' => true,
                'notnull' => false,
            ]
        );

        $externalCommunicationTable->addForeignKeyConstraint(
            'cameranu.quotes',
            ['quote_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_quote_external_communication_quotes'
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('postnl');
        $table->removeForeignKey('fk_quote_postnl_quotes');
        $fkIndex = self::getIndexNameByColumnName('postnl', 'quote_id');
        $table->dropIndex($fkIndex);
        $table->dropColumn('quote_id');
        $table->dropColumn('file_path');

        $externalCommunicationTable = $schema->getTable('external_communication');
        $externalCommunicationTable->removeForeignKey('fk_quote_external_communication_quotes');
        $fkIndex = self::getIndexNameByColumnName('external_communication', 'quote_id');
        $externalCommunicationTable->dropIndex($fkIndex);
        $externalCommunicationTable->dropColumn('quote_id');
    }
}
