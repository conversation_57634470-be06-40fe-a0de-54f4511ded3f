<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240722134606 extends AbstractMigration
{
    private const QUOTE_PRODUCTS_TABLE = 'quote_products';

    public function getDescription(): string
    {
        return 'CAM-4958 add extra discount columns to quote_products tables';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::QUOTE_PRODUCTS_TABLE);
        $table->addColumn('discount_customer', Types::FLOAT, ['notnull' => true, 'default' => 0]);
        $table->addColumn('discount_definitive', Types::FLOAT, ['notnull' => true, 'default' => 0]);
        $table->addColumn('discount_percentage', Types::INTEGER, ['notnull' => true, 'default' => 0]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::QUOTE_PRODUCTS_TABLE);
        $table->dropColumn('discount_customer');
        $table->dropColumn('discount_definitive');
        $table->dropColumn('discount_percentage');
    }
}
