<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240716133122 extends AbstractMigration
{
    private const TABLE = 'second_hand_state_content';
    private const BULLET_POINTS = [
        2 => [
            'Geen gebruikssporen',
        ],
        5 => [
            'Vrijwel geen gebruikssporen',
        ],
        8 => [
            'Hier en daar wat lichte krasjes of beschadigingen',
        ],
        11 => [
            'Zichtbare gebruikssporen',
        ],
        14 => [
            'Duidelijke beschadigingen',
        ],
        17 => [
            'Geen gebruikssporen',
            'Alle accessoires inbegrepen',
            'Minder dan 5.000 clicks',
        ],
        20 => [
            'Vrijwel geen gebruikssporen',
            'Alle accessoires inbegrepen',
            'Minder dan 25.000 clicks',
            ],
        23 => [
            'Hier en daar wat lichte krasjes of beschadigingen',
            'Alle accessoires inbegrepen',
            'Minder dan 75.000 clicks',
        ],
        26 => [
            'Zichtbare gebruikssporen',
            'Alle accessoires inbegrepen',
            'Minder dan 150.000 clicks',
        ],
        29 => [
            'Duidelijke beschadigingen',
            'Alle accessoires inbegrepen',
            'Minder dan 250.000 clicks',
        ],
        32 => [
            'Geen gebruikssporen',
            'Alle accessoires inbegrepen',
        ],
        35 => [
            'Vrijwel geen gebruikssporen',
            'Alle accessoires inbegrepen',
        ],
        38 => [
            'Hier en daar wat lichte krasjes of beschadigingen',
            'Alle accessoires inbegrepen',
        ],
        41 => [
            'Zichtbare gebruikssporen',
            'Alle accessoires inbegrepen',
        ],
        44 => [
            'Duidelijke beschadigingen',
            'Alle accessoires inbegrepen',
        ],
        47 => [
            'Geen gebruikssporen',
            'Alle accessoires inbegrepen',
        ],
        50 => [
            'Vrijwel geen gebruikssporen',
            'Alle accessoires inbegrepen',
        ],
        53 => [
            'Hier en daar wat lichte krasjes of beschadigingen',
            'Alle accessoires inbegrepen',
        ],
        56 => [
            'Lichte gebruikssporen',
            'Alle accessoires inbegrepen',
        ],
        59 => [
            'Duidelijke gebruikssporen',
            'Alle accessoires inbegrepen',
        ],
    ];

    public function getDescription(): string
    {
        return 'CAM-4970 add bullet points to the state content table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `' . self::TABLE . '` ADD COLUMN `bullet_points` TEXT DEFAULT NULL');

        $queryString = 'UPDATE `' . self::TABLE . '` SET `bullet_points` = :bullet_points WHERE `id` = :id';
        foreach (self::BULLET_POINTS as $id =>  $bulletPoints) {
            $this->addSql($queryString, [
                'bullet_points' => json_encode($bulletPoints),
                'id' => $id,
            ]);
        }
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->dropColumn('bullet_points');
    }
}
