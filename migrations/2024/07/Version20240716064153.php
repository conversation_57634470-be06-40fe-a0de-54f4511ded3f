<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240716064153 extends AbstractMigration
{
    private const string TABLE_PICK_LIST = 'pick_list';
    private const string TABLE_PICK_LIST_LINK = 'pick_list_link';
    private const string TABLE_PICK_LIST_ENTRY = 'pick_list_entry';
    private const string TABLE_PICK_LIST_CART = 'pick_list_cart';
    private const string TABLE_PICK_LIST_PICK_LIST_CART = 'pick_list_pick_list_cart';

    public function getDescription(): string
    {
        return 'CAM-4798 entities voor picklijsten';
    }

    public function up(Schema $schema): void
    {
        $pickListTable = $schema->createTable(self::TABLE_PICK_LIST);
        $pickListTable->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true
        ]);
        $pickListTable->addColumn('created_at', Types::DATETIME_IMMUTABLE);
        $pickListTable->addColumn('finished_at', Types::DATETIME_IMMUTABLE, ['notnull' => false]);
        $pickListTable->addColumn('created_by', Types::INTEGER);
        $pickListTable->addColumn('owned_by', Types::INTEGER, ['notnull' => false]);

        $pickListTable->setPrimaryKey(['id']);

        $pickListTable->addForeignKeyConstraint(
            'cameranu.users',
            ['created_by'],
            ['id'],
            [],
            'fk_pick_list_created_by'
        );

        $pickListTable->addForeignKeyConstraint(
            'cameranu.users',
            ['owned_by'],
            ['id'],
            [],
            'fk_pick_list_owned_by'
        );

        $pickListLinkTable = $schema->createTable(self::TABLE_PICK_LIST_LINK);
        $pickListLinkTable->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true
        ]);
        $pickListLinkTable->addColumn('pick_list_id', Types::INTEGER, ['unsigned' => true]);
        $pickListLinkTable->addColumn('parking_folder_id', Types::INTEGER, ['notnull' => false]);
        $pickListLinkTable->addColumn('courier_list_id', Types::INTEGER, ['notnull' => false]);

        $pickListLinkTable->setPrimaryKey(['id']);

        $pickListLinkTable->addForeignKeyConstraint(
            'cameranu.' . self::TABLE_PICK_LIST,
            ['pick_list_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_pick_list_link_pick_list'
        );

        $pickListLinkTable->addForeignKeyConstraint(
            'cameranu.parkeren',
            ['parking_folder_id'],
            ['id'],
            [],
            'fk_pick_list_link_parking_folder_id'
        );

        $pickListLinkTable->addForeignKeyConstraint(
            'cameranu.courier_lists',
            ['courier_list_id'],
            ['id'],
            [],
            'fk_pick_list_link_courier_list_id'
        );

        $pickListEntryTable = $schema->createTable(self::TABLE_PICK_LIST_ENTRY);
        $pickListEntryTable->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true
        ]);
        $pickListEntryTable->addColumn('pick_list_link_id', Types::INTEGER, ['unsigned' => true]);
        $pickListEntryTable->addColumn('picked_at', Types::DATETIME_IMMUTABLE, ['notnull' => false]);
        $pickListEntryTable->addColumn('picked_by', Types::INTEGER, ['notnull' => false]);
        $pickListEntryTable->addColumn('marked_as_problem_at', Types::DATETIME_IMMUTABLE, ['notnull' => false]);
        $pickListEntryTable->addColumn('order_id', Types::INTEGER, ['notnull' => false]);
        $pickListEntryTable->addColumn('product_id', Types::INTEGER);
        $pickListEntryTable->addColumn('stock_id', Types::INTEGER, ['notnull' => false]);
        $pickListEntryTable->addColumn('location_compartment_node_id', Types::INTEGER, ['notnull' => false]);

        $pickListEntryTable->setPrimaryKey(['id']);

        $pickListEntryTable->addForeignKeyConstraint(
            'cameranu.' . self::TABLE_PICK_LIST_LINK,
            ['pick_list_link_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_pick_list_entry_pick_list_link'
        );

        $pickListEntryTable->addForeignKeyConstraint(
            'cameranu.users',
            ['picked_by'],
            ['id'],
            [],
            'fk_pick_list_entry_picked_by'
        );

        $pickListEntryTable->addForeignKeyConstraint(
            'cameranu.bestelling_naw',
            ['order_id'],
            ['id'],
            [],
            'fk_pick_list_entry_order'
        );

        $pickListEntryTable->addForeignKeyConstraint(
            'cameranu.artikelen',
            ['product_id'],
            ['id'],
            [],
            'fk_pick_list_entry_product'
        );

        $pickListEntryTable->addForeignKeyConstraint(
            'cameranu.voorraad',
            ['stock_id'],
            ['id'],
            [],
            'fk_pick_list_entry_stock'
        );

        $pickListEntryTable->addForeignKeyConstraint(
            'cameranu.locatieVakkenNodes',
            ['location_compartment_node_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'SET NULL',
            ],
            'fk_pick_list_entry_location_compartment_node'
        );

        $pickListCartTable = $schema->createTable(self::TABLE_PICK_LIST_CART);
        $pickListCartTable->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true
        ]);
        $pickListCartTable->addColumn('name', Types::STRING);
        $pickListCartTable->addColumn('active', Types::BOOLEAN);
        $pickListCartTable->addColumn('exclusive', Types::BOOLEAN);

        $pickListCartTable->setPrimaryKey(['id']);

        $pickListPickListCartTable = $schema->createTable(self::TABLE_PICK_LIST_PICK_LIST_CART);
        $pickListPickListCartTable->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true
        ]);

        $pickListPickListCartTable->addColumn('pick_list_id', Types::INTEGER, ['unsigned' => true]);
        $pickListPickListCartTable->addColumn('pick_list_cart_id', Types::INTEGER, ['unsigned' => true]);

        $pickListPickListCartTable->setPrimaryKey(['id']);

        $pickListPickListCartTable->addForeignKeyConstraint(
            'cameranu.' . self::TABLE_PICK_LIST,
            ['pick_list_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_pick_list_pick_list_cart_pick_list'
        );

        $pickListPickListCartTable->addForeignKeyConstraint(
            'cameranu.' . self::TABLE_PICK_LIST_CART,
            ['pick_list_cart_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_pick_list_pick_list_cart_pick_list_cart'
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE_PICK_LIST);
        $schema->dropTable(self::TABLE_PICK_LIST_LINK);
        $schema->dropTable(self::TABLE_PICK_LIST_ENTRY);
        $schema->dropTable(self::TABLE_PICK_LIST_CART);
        $schema->dropTable(self::TABLE_PICK_LIST_PICK_LIST_CART);
    }
}
