<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240710134200 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4930 add some extra quoteProduct statuses';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE quote_products MODIFY COLUMN `status` ENUM(\'receive\',\'judge\',\'return\',\'recycle\',\'done\', \'returned\', \'recycled\') DEFAULT \'receive\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE quote_products MODIFY COLUMN `status` ENUM(\'receive\',\'judge\',\'return\',\'recycle\',\'done\') DEFAULT \'receive\'');
    }
}
