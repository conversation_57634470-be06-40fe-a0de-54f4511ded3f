<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\InstantQuote\QuoteStep;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240716130343 extends AbstractMigration
{
    private const STEPS = [
        QuoteStep::ID_PRODUCTS_JUDGED,
        QuoteStep::ID_CUSTOMER_AGREES,
        QuoteStep::ID_QUOTE_PAYMENT_DONE,
        QuoteStep::ID_QUOTE_DONE,
    ];

    public function getDescription(): string
    {
        return 'CAM-4957 - Producten verdwijnen uit offertes';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("ALTER TABLE `cameranu`.`quotes` CHANGE `status` `status` enum('draft', 'open', 'sent', 'cancelled', 'done') default 'draft'");
        $this->addSql('UPDATE `cameranu`.`quote_steps` SET `show_on_status` = "[\"draft\", \"open\", \"sent\", \"done\"]" WHERE `id` IN (' . implode(',', self::STEPS) . ')');
    }

    public function down(Schema $schema): void
    {
        $this->addSql("ALTER TABLE `cameranu`.`quotes` CHANGE `status` `status` enum('open', 'sent', 'cancelled', 'done') default 'open'");
        $this->addSql('UPDATE `cameranu`.`quote_steps` SET `show_on_status` = "[\"open\", \"sent\", \"done\"]" WHERE `id` IN (' . implode(',', self::STEPS) . ')');
    }
}
