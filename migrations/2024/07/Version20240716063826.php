<?php

declare(strict_types=1);

namespace App\Migrations;

use CatB<PERSON>le\MigrationHelper\ManagesForeignKeys;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240716063826 extends AbstractMigration
{
    use ManagesForeignKeys;

    public function getDescription(): string
    {
        return 'CAM-4798 voorraad koppelen aan locatievakken';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('voorraad');
        $table->addColumn('location_compartment_node_id', Types::INTEGER, ['notnull' => false]);

        $table->addForeignKeyConstraint(
            'cameranu.locatieVakkenNodes',
            ['location_compartment_node_id'],
            ['id'],
            ['onDelete' => 'SET NULL'],
            'fk_voorraad_locatievakkennodes',
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('voorraad');
        $table->dropColumn('location_compartment_node_id');
        $table->removeForeignKey('fk_voorraad_locatievakkennodes');
        $fkIndex = self::getIndexNameByColumnName('voorraad', 'location_compartment_node_id');
        $table->dropIndex($fkIndex);
    }
}
