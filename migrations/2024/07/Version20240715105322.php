<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240715105322 extends AbstractMigration
{
    private const FK_QUOTE_PRODUCT_QUOTE_PRODUCT_REQUEST = 'quote_products_quote_product_request_id_fk';

    public function getDescription(): string
    {
        return 'CAM-4905 Add QuoteProduct -> QuoteProductRequest one to one relation';
    }

    public function up(Schema $schema): void
    {
        $quoteProductTable = $schema->getTable('quote_products');
        $quoteProductTable->addColumn(
            'quote_product_request_id',
            Types::INTEGER,
            [
                'unsigned' => true,
                'notnull' => false,
            ],
        );

        $quoteProductTable->addForeignKeyConstraint(
            'cameranu.quote_product_requests',
            ['quote_product_request_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'SET NULL',
            ],
            self::FK_QUOTE_PRODUCT_QUOTE_PRODUCT_REQUEST
        );
    }

    public function down(Schema $schema): void
    {
        $quoteProductTable = $schema->getTable('quote_products');
        $quoteProductTable->removeForeignKey(self::FK_QUOTE_PRODUCT_QUOTE_PRODUCT_REQUEST);
        $quoteProductTable->dropColumn('quote_product_request_id');
    }
}
