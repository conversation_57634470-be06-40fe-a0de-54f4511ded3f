<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240731063514 extends AbstractMigration
{
    private const TABLE_NAME = 'leveranciers';
    private const COLUMN_NAME = 'preferred';

    public function getDescription(): string
    {
        return 'CAM-4757 add option to supplier table to mark supplier as preferred';
    }

    public function up(Schema $schema): void
    {
        $schema
            ->getTable(self::TABLE_NAME)
            ->addColumn(
                self::COLUMN_NAME,
                Types::BOOLEAN,
                [
                    'notnull' => true,
                    'default' => false
                ]
            );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->dropColumn(self::COLUMN_NAME);
    }
}
