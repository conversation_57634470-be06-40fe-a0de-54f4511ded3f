<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\StockLocation;

final class Version20240716063931 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4798 nieuwe stock_location voor orderpicken in Urk';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(sprintf("
            INSERT INTO `cameranu`.`stock_locations`
                (id, parent, code, shop, description, color, available_for_dispatch, has_minimal_stock, domain_id, available_for_sections, has_transit_location, is_available_for_scan, origin_id, is_main_location, automatic_stock_supplier_id, is_available_for_rentals, show_in_minimum_stock_menu, minimum_stock_top_up_location, is_scannable_by_barcode, is_courier_location, use_transit_with_returns, is_return, ignore_in_calculation, winkelnaam)
             VALUES
                (%d, 'cameranu', '010P', NULL, 'Picking Urk', '#8532bd', 0, 0, 1, 0, 0, 0, NULL, 0, 0, 0, 0, NULL, 1, 0, 0, 0, 1, 'Urk')
        ", StockLocation::P_URK));
    }

    public function down(Schema $schema): void
    {
        $this->addSql(sprintf('DELETE FROM `cameranu`.`stock_locations` WHERE `id` = %d', StockLocation::P_URK));
    }
}
