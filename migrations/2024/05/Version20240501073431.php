<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240501073431 extends AbstractMigration
{
    private const TABLE = 'hoofdgroepen';
    private const COLUMN = 'secondhand_active';

    public function getDescription(): string
    {
        return 'CAM-4558 option to activate groups for quote questions';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->addColumn(self::COLUMN, Types::BOOLEAN, ['default' => false]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->dropColumn(self::COLUMN);
    }
}
