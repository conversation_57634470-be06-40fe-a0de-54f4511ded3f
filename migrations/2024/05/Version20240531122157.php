<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

final class Version20240531122157 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2449 - add new field (went_online) for Tweakwise feed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'INSERT INTO `productfeed_fields` (`name`, `feed_id`, `key`) VALUES ("went_online", ' . ProductFeed::TWEAKWISE_ID . ', "went_online")'
        );
        $this->addSql(
            'INSERT INTO `productfeed_fields` (`name`, `feed_id`, `key`) VALUES ("went_online", ' . ProductFeed::TWEAKWISE_TEST_ID . ', "went_online")'
        );
        // Prepare for FK
        $this->addSql(
            'ALTER TABLE `productfeed_fields` CHANGE `feed_id` `feed_id` INT(11)  UNSIGNED  NULL  DEFAULT NULL;'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM productfeed_fields WHERE `name` = "went_online"');
        $this->addSql('ALTER TABLE `productfeed_fields` CHANGE `feed_id` `feed_id` INT(11)  NULL  DEFAULT NULL;');
    }
}
