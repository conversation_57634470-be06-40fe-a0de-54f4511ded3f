<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240502101336 extends AbstractMigration
{
    public const PRODUCTS_MENU_ITEMS_TABLE = 'artikelen_menuItems';
    public const PRODUCTS_TABLE = 'artikelen';

    public const MENU_ITEMS_TABLE = 'menuItems';

    public const PRODUCT_TAGS_TABLE = 'tags_products';

    public const FK_PRODUCT_ID = 'FK_artikelen_menuItems_productId';
    public const FK_MENU_ITEM_ID = 'FK_artikelen_menuItems_menuItemId';
    public const FK_TAGS_PRODUCTS_ID = 'FK_tags_products_itemId';

    public function getDescription(): string
    {
        return 'CAM-4497 FKs on artikelen_menuItems';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE productId NOT IN (SELECT id FROM %s)',
                self::PRODUCTS_MENU_ITEMS_TABLE,
                self::PRODUCTS_TABLE
            )
        );
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE menuItemId NOT IN (SELECT menuItemId FROM %s)',
                self::PRODUCTS_MENU_ITEMS_TABLE,
                self::MENU_ITEMS_TABLE
            )
        );
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE itemId NOT IN (SELECT id FROM %s)',
                self::PRODUCT_TAGS_TABLE,
                self::PRODUCTS_TABLE
            )
        );
        $productsMenuItemsTable = $schema->getTable(self::PRODUCTS_MENU_ITEMS_TABLE);
        $productsMenuItemsTable->addForeignKeyConstraint(
            self::PRODUCTS_TABLE,
            ['productId'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE'],
            self::FK_PRODUCT_ID
        );
        $productsMenuItemsTable->addForeignKeyConstraint(
            self::MENU_ITEMS_TABLE,
            ['menuItemId'],
            ['menuItemId'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE'],
            self::FK_MENU_ITEM_ID
        );
        $productsTagsTable = $schema->getTable(self::PRODUCT_TAGS_TABLE);
        $productsTagsTable->addForeignKeyConstraint(
            self::PRODUCTS_TABLE,
            ['itemId'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE'],
            self::FK_TAGS_PRODUCTS_ID
        );
    }

    public function down(Schema $schema): void
    {
        $productsMenuItemsTable = $schema->getTable(self::PRODUCTS_MENU_ITEMS_TABLE);
        $productsMenuItemsTable->removeForeignKey(self::FK_PRODUCT_ID);
        $productsMenuItemsTable->removeForeignKey(self::FK_MENU_ITEM_ID);

        $productsTagsTable = $schema->getTable(self::PRODUCT_TAGS_TABLE);
        $productsTagsTable->removeForeignKey(self::FK_TAGS_PRODUCTS_ID);
    }
}
