<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240528135704 extends AbstractMigration
{
    private const TABLE = 'quotes';

    public function getDescription(): string
    {
        return 'Add userDeliveryMethod and dropoffLocation to quote table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`quotes` ADD COLUMN `user_delivery_method` enum(\'deposit\',\'postnl_label\') DEFAULT NULL');
        $this->addSql('ALTER TABLE `cameranu`.`quotes` ADD COLUMN `dropoff_location` int(11) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`quotes` DROP COLUMN `user_delivery_method`');
        $this->addSql('ALTER TABLE `cameranu`.`quotes` DROP COLUMN `dropoff_location`');
    }
}
