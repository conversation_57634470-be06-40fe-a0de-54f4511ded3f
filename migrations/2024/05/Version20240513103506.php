<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240513103506 extends AbstractMigration
{
    private const TABLE_NAME = 'article_original';

    public function getDescription(): string
    {
        return 'CAM-4587 add primary key to article_original and add relations to product table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);

        $table->addColumn('id', 'integer', [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true,
        ]);


        $table->addForeignKeyConstraint(
            'cameranu.artikelen', ['original_product_id'], ['id'], [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ],
            'parent_product_ibfk'
        );

        $table->addForeignKeyConstraint(
            'cameranu.artikelen', ['child_product_id'], ['id'], [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ],
            'child_product_ibfk',
        );

        $table->addIndex(['type'], 'idx_type');
        $table->setPrimaryKey(['id']);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);
        $table->dropColumn('id');
        $table->removeForeignKey('parent_product_ibfk');
        $table->removeForeignKey('child_product_ibfk');
        $table->dropIndex('idx_type');

        $parentIndexName = self::getIndexNameByColumnName('original_product_id');
        $table->dropIndex($parentIndexName);

        $childIndexName = self::getIndexNameByColumnName('child_product_id');
        $table->dropIndex($childIndexName);
    }

    private function getIndexNameByColumnName(string $columnName): string
    {
        $columnNames = array_merge([self::TABLE_NAME], [$columnName]);
        $hash = implode('', array_map(static function ($column): string {
            return dechex(crc32($column));
        }, $columnNames));

        return strtoupper(substr('idx_' . $hash, 0, 63));
    }
}
