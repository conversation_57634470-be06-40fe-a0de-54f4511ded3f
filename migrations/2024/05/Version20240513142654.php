<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240513142654 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4608 ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`betalingsmogelijkheden` (`id`, `pos`, `omschrijving`, `goedhart`, `flags`, `betaalwijze_id`)
            VALUES
                (464, 464, \'LOANER 1\', \'pin_rot\', 1, 3),
                (465, 465, \'LOANER 2\', \'pin_rot\', 1, 3);
        ');
        $this->addSql('
            INSERT INTO `cameranu`.`pin_terminals` (`payment_type_id`, `terminal_id`, `psp`, `enabled`)
            VALUES
                (464, \'term_KehFcZ8STBbsg3N66YtnH\', \'mollie\', 1),
                (465, \'term_t4EBdB9qwusFQ4ByBYtnH\', \'mollie\', 1);
        ');
        $this->addSql('
            INSERT INTO `betalingsmogelijkheden_herkomst` (`betalingsmogelijkheden_id`, `herkomst_id`)
            VALUES
                (464, 363),
                (465, 363);
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`pin_terminals` WHERE `payment_type_id` in (464, 465);');
        $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden` WHERE `id` in (464, 465);');
        $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden_herkomst` WHERE `betalingsmogelijkheden_id` in (464, 465);');
    }
}
