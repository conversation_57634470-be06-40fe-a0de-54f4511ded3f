<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240524073758 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4680 Landkeuze toevoegen aan event registraties';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`event_registrations` ADD COLUMN `country` varchar(12) DEFAULT NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`event_registrations` DROP COLUMN `country`;
        ');
    }
}
