<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\MigrationHelper\ManagesForeignKeys;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240531122248 extends AbstractMigration
{
    use ManagesForeignKeys;

    private const TABLE = 'productfeed_fields';

    private const FOREIGN_TABLE = 'productfeeds';

    private const FK_NAME = 'fk_product_feed_fields_feed_id';
    private const FK_COLUMN = 'feed_id';

    public function getDescription(): string
    {
        return 'CAM-2449 extra - FKs to productfeed_fields table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->addForeignKeyConstraint(
            self::FOREIGN_TABLE,
            [self::FK_COLUMN],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE'
            ],
            self::FK_NAME
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->removeForeignKey(self::FK_NAME);
        $fkIndex = self::getIndexNameByColumnName(self::TABLE, self::FK_COLUMN);
        $table->dropIndex($fkIndex);
    }
}
