<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240924084200 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5070 - Btw op grootboekrekeningen uitsluiten';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $schema->getTable('cameranu.codes')
            ->addColumn('vat_allowed', Types::BOOLEAN, ['notnull' => false, 'default' => true]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $schema->getTable('cameranu.codes')
            ->dropColumn('vat_allowed');
    }
}
