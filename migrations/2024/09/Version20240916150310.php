<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Constant\ShippingMethod;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\Domain;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\Parking;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\PaymentType;
use Webdsign\GlobalBundle\Entity\PinTerminal;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\Supplier;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240916150310 extends AbstractMigration
{
    private const array DOMAINS = [
        Domain::CAMERANU_UTRECHT => [
            'customer_id' => Customer::ID_STORE_UTR,
            'domeinnaam' => 'Cameranu Utrecht',
            'adres' => 'Parijsboulevard 201',
            'pcplaats' => '3541CS Utrecht',
            'telefoon' => '',
            'fax' => '',
            'email' => '',
            'kvknr' => '********',
            'btwnr' => 'NL8134.80.711.B.01',
            'bankrekening' => '44.58.43.209  (ABN Amro)',
            'iban' => '******************',
            'bic' => 'ABNANL2A',
            'maildomeinnaam' => '',
            'adres_header' => '',
            'adres_footer' => '',
            'factuurnummer' => 0,//Gaat via Urk
            'schadenummer' => 0,
            'tntVolgnr' => 0,
            'flagValue' => 0,
            'flags' => 0,
        ],
    ];

    private const array ORIGINS = [
        Origin::UTRECHT => [
            'source' => 'Winkel Utrecht',
            'country_id' => '394',
            'parent' => 'cameranu_utrecht',
            'code' => 0,
            'flags_verzend' => 0,
            'color' => '#5636A4',
            'logo' => null,
            'preferredLanguage' => 'nl',
            'retour_settlement' => 0,
            'physical_location' => 1,
            'is_cameranu' => 1,
            'description' => 'Cameranu - Utrecht',
            'pie_chart_color_code' => '#5636a3',
            'status' => 'active',
            'winkelnaam' => 'Utrecht',
        ],
    ];

    private const array OPTIONS_STORE = [
        'has_minimal_stock' => 1,
        'has_transit_location' => 1,
        'available_for_sections' => 1,
        'is_available_for_scan' => 1,
        'is_available_for_rentals' => 1,
        'is_main_location' => 1,
        'automatic_stock_supplier_id' => Supplier::ID_AUTOMATIC_STOCK_SUPPLIER_UTR,
        'show_in_minimum_stock_menu' => 1,
        'is_return' => 0,
        'is_scannable_by_barcode' => 0,
        'is_courier_location' => 0,
        'ignore_in_calculation' => 0,
        'location_node_color' => 'green',
        'winkelnaam' => 'Utrecht',
    ];

    private const array OPTIONS_TRANSIT = [
        'has_minimal_stock' => 0,
        'has_transit_location' => 0,
        'available_for_sections' => 0,
        'is_available_for_scan' => 0,
        'is_available_for_rentals' => 0,
        'is_main_location' => 0,
        'automatic_stock_supplier_id' => Supplier::ID_AUTOMATIC_STOCK_SUPPLIER_UTR,
        'show_in_minimum_stock_menu' => 0,
        'is_return' => 0,
        'is_scannable_by_barcode' => 1,
        'is_courier_location' => 0,
        'ignore_in_calculation' => 0,
        'location_node_color' => 'green',
        'winkelnaam' => 'Utrecht',
    ];

    private const array OPTIONS_COURIER = [
        'has_minimal_stock' => 0,
        'has_transit_location' => 0,
        'available_for_sections' => 0,
        'is_available_for_rentals' => 0,
        'is_available_for_scan' => 0,
        'is_main_location' => 0,
        'automatic_stock_supplier_id' => Supplier::ID_AUTOMATIC_STOCK_SUPPLIER_UTR,
        'show_in_minimum_stock_menu' => 0,
        'is_return' => 0,
        'is_scannable_by_barcode' => 0,
        'is_courier_location' => 1,
        'ignore_in_calculation' => 1,
        'location_node_color' => 'green',
        'winkelnaam' => 'Utrecht',
    ];

    private const array OPTIONS_RETURN = [
        'has_minimal_stock' => 0,
        'has_transit_location' => 0,
        'available_for_sections' => 0,
        'is_available_for_rentals' => 0,
        'is_available_for_scan' => 0,
        'is_main_location' => 0,
        'automatic_stock_supplier_id' => Supplier::ID_AUTOMATIC_STOCK_SUPPLIER_UTR,
        'show_in_minimum_stock_menu' => 0,
        'is_return' => 1,
        'is_scannable_by_barcode' => 0,
        'is_courier_location' => 0,
        'ignore_in_calculation' => 0,
        'location_node_color' => 'green',
        'winkelnaam' => 'Utrecht',
    ];

    private const array OPTIONS_RENTAL = [
        'has_minimal_stock' => 0,
        'has_transit_location' => 0,
        'available_for_sections' => 0,
        'is_available_for_scan' => 0,
        'is_available_for_rentals' => 0,
        'is_main_location' => 0,
        'automatic_stock_supplier_id' => 0,
        'show_in_minimum_stock_menu' => 0,
        'is_return' => 0,
        'is_scannable_by_barcode' => 0,
        'is_courier_location' => 0,
        'ignore_in_calculation' => 0,
        'location_node_color' => 'green',
        'winkelnaam' => 'Utrecht',
    ];
    private const array OPTIONS_DEFAULT = [
        'has_minimal_stock' => 0,
        'has_transit_location' => 0,
        'available_for_sections' => 0,
        'is_available_for_scan' => 0,
        'is_available_for_rentals' => 0,
        'is_main_location' => 0,
        'automatic_stock_supplier_id' => Supplier::ID_AUTOMATIC_STOCK_SUPPLIER_UTR,
        'show_in_minimum_stock_menu' => 0,
        'is_return' => 0,
        'is_scannable_by_barcode' => 0,
        'is_courier_location' => 0,
        'ignore_in_calculation' => 1,
        'location_node_color' => 'green',
        'winkelnaam' => 'Utrecht',
    ];

    private const array NEW_LOCATIONS = [
        [
            'id' => StockLocation::UTR_STORE,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_STORE,
            'shop' => 'utrecht',
            'color' => '#5636a3',
            'description' => 'Winkel Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => Origin::UTRECHT,
            'options' => self::OPTIONS_STORE,
        ],
        [
            'id' => StockLocation::TRANSIT_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_TRANSIT,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'Transit Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_TRANSIT,
        ],
        [
            'id' => StockLocation::COURIER_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_COURIER,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'Koerier Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_COURIER,
        ],
        [
            'id' => StockLocation::RETURN_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_RETURN,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'Retouren Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_RETURN,
        ],
        [
            'id' => StockLocation::RENTAL_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_RENTAL,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'Verhuur Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_RENTAL,
        ],
        [
            'id' => StockLocation::B_STOCK_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_B_STOCK,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'B-voorraad Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_DEFAULT,
        ],
        [
            'id' => StockLocation::DEMO_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_DEMO,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'Demo Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_DEFAULT,
        ],
        [
            'id' => StockLocation::Z_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_Z,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'Zoeklocatie Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_DEFAULT,
        ],
        [
            'id' => StockLocation::X_STOCK_UTR,
            'parent' => StockLocation::PARENT_UTR,
            'code' => StockLocation::CODE_UTR_X,
            'shop' => null,
            'color' => '#5636a3',
            'description' => 'X-voorraad Utrecht',
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'origin_id' => null,
            'options' => self::OPTIONS_DEFAULT,
        ],
    ];

    private const array PAYMENT_METHODS = [
        [
            'id' => PaymentType::CASH_UTR,
            'pos' => 470,
            'description' => 'Contant UTR',
            'goedhart' => 'contant_utr',
            'flags' => PaymentType::FLAG_ACTIVE,
            'betaalwijze_id' => PaymentMethod::PIN_CASH,
        ],
        [
            'id' => PaymentType::BANKTRANSFER_UTR,
            'pos' => 471,
            'description' => 'Per bank UTR',
            'goedhart' => 'perbank_utr',
            'flags' => PaymentType::FLAG_ACTIVE,
            'betaalwijze_id' => PaymentMethod::BANKTRANSFER,
        ],
        [
            'id' => PaymentType::TO_BE_RECEIVED_UTR,
            'pos' => 472,
            'description' => 'Nog te ontvangen UTR',
            'goedhart' => 'te_ontvangen_utr',
            'flags' => PaymentType::FLAG_ACTIVE,
            'betaalwijze_id' => null,
        ],
        [
            'id' => PaymentType::UTRECHT_A920_1,
            'pos' => 473,
            'description' => 'UTRECHT A920 1',
            'goedhart' => 'pin_utr',
            'flags' => PaymentType::FLAG_ACTIVE,
            'betaalwijze_id' => PaymentMethod::PIN_CASH,
        ],
        [
            'id' => PaymentType::UTRECHT_A920_2,
            'pos' => 474,
            'description' => 'UTRECHT A920 2',
            'goedhart' => 'pin_utr',
            'flags' => PaymentType::FLAG_ACTIVE,
            'betaalwijze_id' => PaymentMethod::PIN_CASH,
        ],
        [
            'id' => PaymentType::UTRECHT_A920_3,
            'pos' => 475,
            'description' => 'UTRECHT A920 3',
            'goedhart' => 'pin_utr',
            'flags' => PaymentType::FLAG_ACTIVE,
            'betaalwijze_id' => PaymentMethod::PIN_CASH,
        ],
        [
            'id' => PaymentType::UTRECHT_A35_1,
            'pos' => 476,
            'description' => 'UTRECHT A35 1',
            'goedhart' => 'pin_utr',
            'flags' => PaymentType::FLAG_ACTIVE,
            'betaalwijze_id' => PaymentMethod::PIN_CASH,
        ],
    ];

    private const array PIN_TERMINALS = [
        [
            'payment_type_id' => PaymentType::UTRECHT_A920_1,
            'terminal_id' => 'term_tKBQhSyG6ucVK88dtvfkH',
            'psp' => 'mollie',
            'enabled' => true,
        ],
        [
            'payment_type_id' => PaymentType::UTRECHT_A920_2,
            'terminal_id' => 'term_uXhDhujVFN3yhavbawfkH',
            'psp' => 'mollie',
            'enabled' => true,
        ],
        [
            'payment_type_id' => PaymentType::UTRECHT_A920_3,
            'terminal_id' => 'term_KehFcZ8STBbsg3N66YtnH',
            'psp' => 'mollie',
            'enabled' => true,
        ],
        [
            'payment_type_id' => PaymentType::UTRECHT_A35_1,
            'terminal_id' => 'term_epYRwVhhvrUVBdSExwfkH',
            'psp' => 'mollie',
            'enabled' => true,
        ]
    ];

    private const array ORIGIN_PROFILES = [
        [
            'origin_id' => Origin::UTRECHT,
            'name' => 'Winkel Utrecht',
            'pay_methods' => [
                PaymentType::CASH_UTR,
                PaymentType::BANKTRANSFER_UTR,
                PaymentType::TO_BE_RECEIVED_UTR,
                PaymentType::UTRECHT_A920_1,
                PaymentType::UTRECHT_A920_2,
                PaymentType::UTRECHT_A920_3,
                PaymentType::UTRECHT_A35_1,
            ],
        ],
    ];

    private const array PARKING_FOLDERS = [
        [
            'id' => Parking::UTR_TODAY,
            'domain_id' => Domain::CAMERANU,
            'pos' => 115,
            'description' => 'UTR klaargelegd',
            'info' => '',
            'flags' => Parking::FLAG_ALLOW_PARKING + Parking::FLAG_EXPORT_TO_ZOHO,
        ],
        [
            'id' => Parking::UTR_LATER,
            'domain_id' => Domain::CAMERANU,
            'pos' => 116,
            'description' => 'UTR later afhalen',
            'info' => '',
            'flags' => Parking::FLAG_ALLOW_PARKING + Parking::FLAG_NOT_PUSHING_ON_ORIGIN_STOCK + Parking::FLAG_EXPORT_TO_ZOHO,
        ],
        [
            'id' => Parking::BACKORDER_UTR,
            'domain_id' => Domain::CAMERANU_UTRECHT,
            'pos' => 3209,
            'description' => 'Backorder UTR',
            'info' => '',
            'flags' => Parking::FLAG_ALLOW_PARKING + Parking::FLAG_BACKORDER_CHECK + Parking::FLAG_NOT_PUSHING_ON_ORIGIN_STOCK + Parking::FLAG_EXPORT_TO_ZOHO,
        ],
        [
            'id' => Parking::RETURN_PAYMENT_UTRECHT,
            'domain_id' => Domain::CAMERANU,
            'pos' => 4041,
            'description' => 'RET. BETALING Utrecht',
            'info' => '',
            'flags' => Parking::FLAG_ALLOW_PARKING,
        ],
    ];

    private const array PICKUP_METHODS = [
        [
            'id' => ShippingMethod::UTR_TODAY,
            'naam' => '102w_vandaag_afhalen',
            'verzendwijze' => 'Vandaag afhalen in Utrecht',
            'verzendwijze_kort' => 'UTR vandaag afhalen',
            'pos' => 36,
            'flags' => ShippingMethod::PICKUP_UTR_TODAY_FLAGS,
            'change_origin' => Origin::UTRECHT,
            'disable_other' => null,
            'translations' => [
                [
                    'parent' => ShippingMethod::UTR_TODAY,
                    'language' => 'nl',
                    'info' => '(vandaag afhalen)',
                    'kort' => 'Vandaag afhalen in onze winkel in Utrecht',
                    'verzendwijze' => 'Utrecht',
                ],
                [
                    'parent' => ShippingMethod::UTR_TODAY,
                    'language' => 'en',
                    'info' => '(pick up today)',
                    'kort' => 'Pick up today at our store in Utrecht',
                    'verzendwijze' => 'Utrecht',
                ],
            ],
        ],
        [
            'id' => ShippingMethod::UTR_LATER,
            'naam' => '102w_later_afhalen',
            'verzendwijze' => 'Volgende werkdag afhalen in Utrecht',
            'verzendwijze_kort' => 'UTR later afhalen',
            'pos' => 37,
            'flags' => ShippingMethod::PICKUP_UTR_LATER_FLAGS,
            'change_origin' => Origin::UTRECHT,
            'disable_other' => ShippingMethod::UTR_TODAY,
            'translations' => [
                [
                    'parent' => ShippingMethod::UTR_LATER,
                    'language' => 'nl',
                    'info' => '(volgende werkdag)',
                    'kort' => 'Volgende werkdag afhalen in onze winkel in Utrecht',
                    'verzendwijze' => 'Utrecht',
                ],
                [
                    'parent' => ShippingMethod::UTR_LATER,
                    'language' => 'en',
                    'info' => '(next business day)',
                    'kort' => 'Pick up next business day at our store in Utrecht',
                    'verzendwijze' => 'Utrecht',
                ],
            ],
        ],
    ];

    public function getDescription() : string
    {
        return 'CAM-5102 - Inserts for new Store Utrecht';
    }

    public function up(Schema $schema): void
    {
        foreach (self::DOMAINS as $id => $domain) {
            $this->addSql(
                '
                INSERT INTO `cameranu`.`domeinen`
                    (
                        `id`,
                        `customer_id`,
                        `domeinnaam`,
                        `adres`,
                        `pcplaats`,
                        `telefoon`,
                        `fax`,
                        `email`,
                        `kvknr`,
                        `btwnr`,
                        `bankrekening`,
                        `iban`,
                        `bic`,
                        `maildomeinnaam`,
                        `adres_header`,
                        `adres_footer`,
                        `factuurnummer`,
                        `schadenummer`,
                        `tntVolgnr`,
                        `flagValue`,
                        `flags`
                    ) VALUES (
                        ' . $id . ',
                        ' . (empty($origin['customer_id']) ? 'NULL' : '"' . $origin['customer_id'] . '"') . ',
                        "' . $domain['domeinnaam'] . '",
                        "' . $domain['adres'] . '",
                        "' . $domain['pcplaats'] . '",
                        "' . $domain['telefoon'] . '",
                        "' . $domain['fax'] . '",
                        "' . $domain['email'] . '",
                        "' . $domain['kvknr'] . '",
                        "' . $domain['btwnr'] . '",
                        "' . $domain['bankrekening'] . '",
                        "' . $domain['iban'] . '",
                        "' . $domain['bic'] . '",
                        "' . $domain['maildomeinnaam'] . '",
                        "' . $domain['adres_header'] . '",
                        "' . $domain['adres_footer'] . '",
                        ' . $domain['factuurnummer'] . ',
                        ' . $domain['schadenummer'] . ',
                        ' . $domain['tntVolgnr'] . ',
                        ' . $domain['flagValue'] . ',
                        ' . $domain['flags'] . '
                    )
            ');
        }

        // Add origins
        foreach (self::ORIGINS as $id => $origin) {
            $this->addSql('
                INSERT INTO `cameranu`.`bestelling_herkomst`
                    (
                        `id`,
                        `country_id`,
                        `source`,
                        `parent`,
                        `code`,
                        `flags_verzend`,
                        `color`,
                        `logo`,
                        `preferredLanguage`,
                        `retour_settlement`,
                        `physical_location`,
                        `is_cameranu`,
                        `description`,
                        `pie_chart_color_code`
                    ) VALUES (
                        ' . $id .  ',
                        ' . $origin['country_id'] . ',
                        "' . $origin['source'] . '",
                        "' . $origin['parent'] . '",
                        ' . $origin['code'] . ',
                        ' . $origin['flags_verzend'] . ',
                        "' . $origin['color'] . '",
                        "NULL",
                        "' . $origin['preferredLanguage'] . '",
                        ' . $origin['retour_settlement'] . ',
                        ' . $origin['physical_location'] . ',
                        ' . $origin['is_cameranu'] . ',
                        "' . $origin['description'] . '",
                        "' . $origin['pie_chart_color_code'] . '"
                    )
            ');
        }

        //stockLocations
        foreach (self::NEW_LOCATIONS as $newLocation) {
            $this->addSql(
                '
                INSERT INTO `cameranu`.`stock_locations`
                (
                     id,
                     parent,
                     code,
                     shop,
                     description,
                     color,
                     available_for_dispatch,
                     has_minimal_stock,
                     domain_id,
                     available_for_sections,
                     has_transit_location,
                     is_available_for_scan,
                     origin_id,
                     is_main_location,
                     automatic_stock_supplier_id,
                     is_available_for_rentals,
                     show_in_minimum_stock_menu,
                     minimum_stock_top_up_location,
                     is_scannable_by_barcode,
                     is_courier_location,
                     use_transit_with_returns,
                     is_return,
                     ignore_in_calculation,
                     location_node_color,
                     winkelnaam
                ) VALUES (
                     ' . $newLocation['id'] . ',
                    "' . $newLocation['parent'] . '",
                    "' . $newLocation['code'] . '",
                    ' . (is_null($newLocation['shop']) ? 'NULL' : '"' . $newLocation['shop'] . '"'). ',
                    "' . $newLocation['description'] . '",
                    "' . $newLocation['color'] . '",
                     0,
                    ' . $newLocation['options']['has_minimal_stock'] . ',
                    ' . $newLocation['domain_id'] . ',
                    ' . $newLocation['options']['available_for_sections'] . ',
                    ' . $newLocation['options']['has_transit_location'] . ',
                    ' . $newLocation['options']['is_available_for_scan'] . ',
                    ' . (is_null($newLocation['origin_id']) ? 'NULL' : '"' . $newLocation['origin_id'] . '"') . ',
                    ' . $newLocation['options']['is_main_location'] . ',
                    ' . $newLocation['options']['automatic_stock_supplier_id'] . ',
                    ' . $newLocation['options']['is_available_for_rentals'] . ',
                    ' . $newLocation['options']['show_in_minimum_stock_menu'] . ',
                    NULL,
                    ' . $newLocation['options']['is_scannable_by_barcode'] . ',
                    ' . $newLocation['options']['is_courier_location'] . ',
                    0,
                    ' . $newLocation['options']['is_return'] . ',
                    ' . $newLocation['options']['ignore_in_calculation'] . ',
                    "' . $newLocation['options']['location_node_color'] . '",
                    "' . $newLocation['options']['winkelnaam'] . '"
                )
            ');
        }

        //add payment methods
        foreach (self::PAYMENT_METHODS as $paymentMethod) {
            $this->addSql('
                INSERT INTO `cameranu`.`betalingsmogelijkheden`
                (
                     `id`,
                     `pos`,
                     `omschrijving`,
                     `goedhart`,
                     `flags`,
                     `betaalwijze_id`
                ) VALUES (
                    ' . $paymentMethod['id'] . ',
                    ' . $paymentMethod['pos'] . ',
                    "' . $paymentMethod['description'] . '",
                    "' . $paymentMethod['goedhart'] . '",
                    ' . $paymentMethod['flags'] . ',
                    ' . (is_null($paymentMethod['betaalwijze_id']) ? 'NULL' : '"' . $paymentMethod['betaalwijze_id'] . '"') . '
                )
            ');

            //connect payment type to origin
            $this->addSql('
                INSERT INTO `cameranu`.`betalingsmogelijkheden_herkomst`
                (
                    `betalingsmogelijkheden_id`,
                    `herkomst_id`
                ) VALUES (
                      ' . $paymentMethod['id'] . ',
                      ' . Origin::UTRECHT . '
                )
            ');
        }

        foreach (self::PIN_TERMINALS as $terminal) {
            $this->addSql('
                INSERT INTO `cameranu`.`pin_terminals`
                (
                     `payment_type_id`,
                     `terminal_id`,
                     `psp`,
                     `enabled`
                ) VALUES (
                    ' . $terminal['payment_type_id'] . ',
                    \'' . $terminal['terminal_id'] . '\',
                    \'' . $terminal['psp'] . '\',
                    ' . ($terminal['enabled'] ? 1 : 0) . '
                )
            ');
        }

        //add origin_profiles
        foreach (self::ORIGIN_PROFILES as $originProfile) {
            $this->addSql('
                INSERT INTO `cameranu`.`origins_profiles`
                    (
                        `origin_id`,
                        `name`,
                        `pay_methods`
                    ) VALUES (
                        ' . $originProfile['origin_id'] . ',
                        "' . $originProfile['name'] . '",
                        "' . implode(', ' , $originProfile['pay_methods']) . '"
                    )
            ');
        }

        //Add parking folders
        foreach (self::PARKING_FOLDERS as $parkingFolder) {
            $this->addSql('
                INSERT INTO `cameranu`.`parkeren`
                (
                    `id`,
                    `domain_id`,
                    `pos`,
                    `omschrijving`,
                    `info`,
                    `flags`
                ) VALUES (
                    ' . $parkingFolder['id'] . ',
                    ' . $parkingFolder['domain_id'] . ',
                    ' . $parkingFolder['pos'] . ',
                    "' . $parkingFolder['description'] . '",
                    "' . $parkingFolder['info'] . '",
                    ' . $parkingFolder['flags'] . '
                )
            ');
        }

        foreach (self::PICKUP_METHODS as $pickupMethod) {
            $this->addSql('
                INSERT INTO `cameranu`.`verzendwijze`
                (
                    `id`,
                    `domain_id`,
                    `naam`,
                    `verzendwijze`,
                    `verzendwijze_kort`,
                    `pos`,
                    `flags`,
                    `type`,
                    `changeOrigin`,
                    `disableOther`
                ) VALUES (
                      ' . $pickupMethod['id'] . ',
                      1,
                      "' . $pickupMethod['naam'] . '",
                      "' . $pickupMethod['verzendwijze'] . '",
                      "' . $pickupMethod['verzendwijze_kort'] . '",
                      ' . $pickupMethod['pos'] . ',
                      ' . $pickupMethod['flags'] . ',
                      "pickup",
                      ' . $pickupMethod['change_origin'] . ',
                      ' . (is_null($pickupMethod['disable_other']) ? 'NULL' : '"' . $pickupMethod['disable_other'] . '"'). '
                )
            ');

            foreach ($pickupMethod['translations'] as $translation) {
                $this->addSql('
                    INSERT INTO `cameranu`.`verzendwijze2`
                        (
                            `parent_id`,
                            `verzendwijze`,
                            `verzendwijze_kort`,
                            `info`,
                            `taal`
                        ) VALUES (
                              ' . $translation['parent'] . ',
                              "' . $translation['verzendwijze'] . '",
                              "' . $translation['kort'] . '",
                              "' . $translation['info'] . '",
                              "' . $translation['language'] . '"
                        )
                    ');
            }
        }

        $this->addSql('
            UPDATE `cameranu`.`landcodes`
            SET flags_verzend = flags_verzend | 412316860416
            WHERE id IN (152, 394, 80, 300, 494, 22, 264)
        ');

        $this->addSql('
            UPDATE `cameranu`.`betaalwijze1`
            SET verzend_flags = verzend_flags | 412316860416
            WHERE id IN (112, 115, 121)
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu`.`betaalwijze1`
            SET verzend_flags = verzend_flags & ~412316860416
            WHERE id IN (112, 115, 121)
        ');

        $this->addSql('
            UPDATE `cameranu`.`landcodes`
            SET flags_verzend = flags_verzend & ~412316860416
            WHERE id IN (152, 394, 80, 300, 494, 22, 264)
        ');

        //Remove pickupMethods
        foreach (self::PICKUP_METHODS as $pickupMethod) {
            $this->addSql('DELETE FROM `cameranu`.`verzendwijze2` WHERE `parent_id` = ' . $pickupMethod['id']);
            $this->addSql('DELETE FROM `cameranu`.`verzendwijze` WHERE `id` = ' . $pickupMethod['id']);
        }

        //Remove parkingFolders
        $this->addSql('DELETE FROM `cameranu`.`parkeren` WHERE `domain_id` = ' . Domain::CAMERANU_UTRECHT);

        //Remove originProfiles
        $this->addSql('DELETE FROM `cameranu`.`origins_profiles` WHERE `origin_id` = ' . Origin::UTRECHT);

        //Remove paymentType relation
        $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden_herkomst` WHERE `herkomst_id` = ' . Origin::UTRECHT);

        //Remove pin terminals
        foreach (self::PIN_TERMINALS as $terminal) {
            $this->addSql('DELETE FROM `cameranu`.`pin_terminals` WHERE `payment_type_id` = ' . $terminal['payment_type_id']);
        }

        //Remove paymentMethods
        foreach (self::PAYMENT_METHODS as $paymentMethod) {
            $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden` WHERE `id` = ' . $paymentMethod['id']);
        }

        //Remove StockLocations
        foreach (self::NEW_LOCATIONS as $stockLocation) {
            $this->addSql('DELETE FROM `cameranu`.`stock_locations` WHERE `id` = ' . $stockLocation['id']);
        }

        // Remove domains
        foreach (self::DOMAINS as $id => $domain) {
            $this->addSql('DELETE FROM `cameranu`.`domeinen` WHERE `id` = ' . $id);
        }

        // Remove origins
        foreach (self::ORIGINS as $id => $origin) {
            $this->addSql('DELETE FROM `cameranu`.`bestelling_herkomst` WHERE `id` = ' . $id);
        }
    }
}
