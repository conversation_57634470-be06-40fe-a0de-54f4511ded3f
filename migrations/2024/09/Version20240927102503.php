<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240927102503 extends AbstractMigration
{
    private const string TABLE = 'leveranciers_verzamel';
    private const string COLUMN_CREATE_FROM_UBL = 'create_packing_slip_from_ubl';
    private const string COLUMN_TO_EMAIL = 'packing_slip_to_email';

    public function getDescription(): string
    {
        return 'CAM-5252 add createPackingSlipFromUbl option to supplierGroups';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->addColumn(
            self::COLUMN_CREATE_FROM_UBL,
            Types::BOOLEAN,
            [
                'notnull' => true,
                'default' => false
            ]
        );

        $table->addColumn(
            self::COLUMN_TO_EMAIL,
            Types::STRING,
            [
                'notnull' => false,
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->dropColumn(self::COLUMN_CREATE_FROM_UBL);
        $table->dropColumn(self::COLUMN_TO_EMAIL);
    }
}
