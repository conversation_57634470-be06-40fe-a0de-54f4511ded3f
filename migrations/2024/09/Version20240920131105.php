<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240920131105 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4794 - administratie kosten boolean toevoegen aan event product tabel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`event_products` ADD COLUMN `administration_costs` TINYINT(4) DEFAULT 0 AFTER `needs_payment`;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`event_products` DROP COLUMN `administration_costs`;');
    }
}
