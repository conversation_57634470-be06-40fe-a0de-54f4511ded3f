<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240918075213 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4219 - Add multiple column to discountset_positions table';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs

        $schema->getTable('cameranu.discountset_positions')
            ->addColumn('multiple', Types::BOOLEAN, ['notnull' => false, 'default' => false]);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $schema->getTable('cameranu.discountset_positions')
            ->dropColumn('multiple');
    }
}
