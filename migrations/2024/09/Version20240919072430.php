<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240919072430 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5310 - <PERSON><PERSON>an of factuur gemaild is tabel aanmaken';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `invoice_maillog` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `bestelling_id` int(11) NOT NULL,
                `sent_at` datetime NOT NULL DEFAULT current_timestamp(),
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `invoice_maillog`');
    }
}
