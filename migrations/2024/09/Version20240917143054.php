<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240917143054 extends AbstractMigration
{
    public const CATEGORY = 'Content (Rebranding)';
    public const TYPES = [
        110 => 'guidedSellingBanner'
    ];

    public function getDescription(): string
    {
        return 'CAM-5293 - Add guided selling banner pageblock';
    }

    public function up(Schema $schema): void
    {
        $timestamp = time();
        $sql = 'INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
                VALUES (:created, :modified, :type, :sort, :category)';

        foreach (self::TYPES as $sort => $type) {
            $this->addSql($sql, [
                'created' => $timestamp,
                'modified' => $timestamp,
                'type' => $type,
                'sort' => $sort,
                'category' => self::CATEGORY,
            ]);
        }
    }

    public function down(Schema $schema): void
    {
        $sql = 'DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = :type';

        foreach (self::TYPES as $type) {
            $this->addSql($sql, ['type' => $type]);
        }
    }
}
