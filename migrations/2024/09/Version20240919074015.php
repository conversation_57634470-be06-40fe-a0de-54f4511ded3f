<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240919074015 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5126 - Ingevulde colli anders opslaan';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `collis` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `parkerenId` int(11) NOT NULL,
                `bestellingId` int(11) NOT NULL,
                `colli` varchar(255) DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `collis`');
    }
}
