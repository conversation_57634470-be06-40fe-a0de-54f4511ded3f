<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240906144505 extends AbstractMigration
{
    private const TABLE_NAME = 'newsletter_registration';

    public function getDescription(): string
    {
        return 'CAM-4014 - Create newsletter_registration table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable(self::TABLE_NAME);

        $table->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true,
        ]);

        $table->setPrimaryKey(['id']);

        $table->addColumn('date', Types::DATETIME_MUTABLE, [
            'notnull' => true,
            'default' => 'CURRENT_TIMESTAMP',
        ]);

        $table->addColumn('email', 'string', ['notnull' => true]);

        $table->addColumn('origin', 'string', ['notnull' => true]);

        $table->addIndex(['date', 'email', 'origin']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE_NAME);
    }
}
