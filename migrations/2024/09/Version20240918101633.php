<?php

declare(strict_types=1);

namespace App\Migrations;

use <PERSON>B<PERSON>le\Constant\ShippingMethod;
use CatB<PERSON>le\MigrationHelper\TreeMigrationHelper;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240918101633 extends AbstractMigration
{
    private const array PICKUP_METHODS = [
        [
            'id' => ShippingMethod::UTR_TODAY,
            'label' => 'Vandaag ophalen winkel Utrecht',
            'after' => '229' // Rotterdam N
        ],
        [
            'id' => ShippingMethod::UTR_LATER,
            'label' => 'Later ophalen winkel Utrecht',
            'after' => ShippingMethod::UTR_TODAY,
        ],
    ];

    private const array PICKUP_SET_TODAY_IDS = [
        ['id' => ShippingMethod::UTR_TODAY, 'name' => 'utrecht'],
    ];

    private const array PICKUP_SET_LATER_IDS = [
        ['id' => ShippingMethod::UTR_LATER, 'name' => 'utrecht'],
    ];

    private const array SHOPS = [
        [
            'name' => 'utrecht',
            'label' => 'Utrecht'
        ],
    ];

    private const array SHOP_FIELDS = [
        [
            'name' => 'id',
            'label' => 'ID',
            'type' => 'int',
            'readonly' => true,
        ],
        [
            'name' => 'company',
            'label' => 'Bedrijfsnaam',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'name',
            'label' => 'Naam',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'street',
            'label' => 'Straat',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'houseNumber',
            'label' => 'Huisnummer',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'houseNumberExt',
            'label' => 'Toevoeging',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'postal',
            'label' => 'Postcode',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'city',
            'label' => 'Plaats',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'url',
            'label' => 'Link',
            'type' => 'string',
            'readonly' => true,
        ],
        [
            'name' => 'cocNumber',
            'label' => 'KVK nummer',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'vatNumber',
            'label' => 'BTW nummer',
            'type' => 'string',
            'readonly' => false,
        ],
        [
            'name' => 'hours',
            'label' => 'Openingstijden',
            'type' => 'category',
            'readonly' => false,
        ]
    ];

    private const array DAYS = [
        [
            'name' => 'monday',
            'label' => 'Maandag',
        ],
        [
            'name' => 'tuesday',
            'label' => 'Dinsdag',
        ],
        [
            'name' => 'wednesday',
            'label' => 'Woensdag',
        ],
        [
            'name' => 'thursday',
            'label' => 'Donderdag',
        ],
        [
            'name' => 'friday',
            'label' => 'Vrijdag',
        ],
        [
            'name' => 'saturday',
            'label' => 'Zaterdag',
        ],
        [
            'name' => 'sunday',
            'label' => 'Zondag',
        ],
    ];

    private const array DAYS_CHILDREN = [
        [
            'name' => 'opening',
            'label' => 'Openen',
            'type' => 'time',
            'description' => 'uur',
        ],
        [
            'name' => 'closing',
            'label' => 'Sluiten',
            'type' => 'time',
            'description' => 'uur',
        ],
        [
            'name' => 'closed',
            'label' => 'Gesloten',
            'type' => 'bool',
            'description' => null,
        ]
    ];

    private const array ADDRESSES = [
        'utrecht' => [
            'id' => '11',
            'company' => 'CameraNU.nl B.V.',
            'name' => 'Utrecht',
            'street' => 'Parijsboulevard',
            'houseNumber' => '201',
            'postal' => '3541 CS',
            'city' => 'Utrecht',
            'url' => 'utrecht',
            'cocNumber' => 'TODO',
            'vatNumber' => 'TODO',
        ],
    ];

    public function getDescription(): string
    {
        return 'CAM-5275  Websiteconfigurator pickup fields for sore Utrecht';
    }

    public function up(Schema $schema): void
    {
        $fieldsHelper = new TreeMigrationHelper(
            '`cameranu`.`websiteconfigurator_fields`',
            [
                'id' => '`id`',
                'lft' => '`lft`',
                'rgt' => '`rgt`',
                'level' => '`level`',
                'parent' => '`parent_field_id`',
                'root' => '`root_field_id`',
            ]
        );

        foreach (self::PICKUP_METHODS as $set) {
            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addAfterWhere(
                [
                    '`name`' => '\'' . $set['id'] . '\'',
                    '`label`' => '\'' . $set['label'] . '\'',
                    '`read_only`' => '0',
                    '`field_type`' => '\'category\'',
                ],
                '`name` = \'' . $set['after'] . '\' AND `field_type` = \'category\''
            ));

            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addChildWhere(
                [
                    '`name`' => '\'shop\'',
                    '`label`' => '\'Winkel\'',
                    '`read_only`' => '1',
                    '`field_type`' => '\'string\'',
                ],
                '`name` = \'' . $set['id'] . '\' AND `field_type` = \'category\''
            ));

            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addChildWhere(
                [
                    '`name`' => '\'fromTime\'',
                    '`label`' => '\'Ophalen vanaf\'',
                    '`description`' => '\'uur\'',
                    '`read_only`' => '0',
                    '`field_type`' => '\'time\'',
                ],
                '`name` = \'' . $set['id'] . '\' AND `field_type` = \'category\''
            ));

            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addChildWhere(
                [
                    '`name`' => '\'cutoffTitle\'',
                    '`label`' => '\'Afbreek tekst\'',
                    '`read_only`' => '0',
                    '`field_type`' => '\'string\'',
                ],
                '`name` = \'' . $set['id'] . '\' AND `field_type` = \'category\''
            ));

            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addChildWhere(
                [
                    '`name`' => '\'cutoff\'',
                    '`label`' => '\'Afbreek tijd\'',
                    '`description`' => '\'uur\'',
                    '`helper_text`' => '\'Tijd tot wanneer deze optie actief is. leeg laten = NIET actief/gesloten OF juist de standaard optie\'',
                    '`read_only`' => '0',
                    '`field_type`' => '\'time\'',
                ],
                '`name` = \'' . $set['id'] . '\' AND `field_type` = \'category\''
            ));
        }

        foreach (self::PICKUP_SET_TODAY_IDS as $set) {
            $parentQuery = '(SELECT `id` FROM `cameranu`.`websiteconfigurator_fields` WHERE `name` = \'' . $set['id'] . '\' AND `field_type` = \'category\')';
            $baseFieldQuery = 'SELECT id FROM `cameranu`.`websiteconfigurator_fields` WHERE parent_field_id = ' . $parentQuery;

            $values = [];
            $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'cutoff\'' . ')', '\'17:00\''];
            $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'cutoffTitle\'' . ')', '\'volgende werkdag\''];
            $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'fromTime\'' . ')', '\'15:00\''];
            $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'shop\'' . ')', '\'' . $set['name'] . '\''];

            foreach ($values as $value) {
                $valueList = '(' . implode(',', $value) . ')';
                $this->addSql('INSERT INTO `cameranu`.`websiteconfigurator_values` (`config_id`, `field_id`, `value`) VALUES ' . $valueList);
            }
        }

        foreach (self::PICKUP_SET_LATER_IDS as $set) {
            $parentQuery = '(SELECT `id` FROM `cameranu`.`websiteconfigurator_fields` WHERE `name` = \'' . $set['id'] . '\' AND `field_type` = \'category\')';
            $baseFieldQuery = 'SELECT id FROM `cameranu`.`websiteconfigurator_fields` WHERE parent_field_id = ' . $parentQuery;

            $values = [];
            $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'cutoffTitle\'' . ')', '\'dinsdag\''];
            $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'fromTime\'' . ')', '\'15:00\''];
            $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'shop\'' . ')', '\'' . $set['name'] . '\''];

            foreach ($values as $value) {
                $valueList = '(' . implode(',', $value) . ')';
                $this->addSql('INSERT INTO `cameranu`.`websiteconfigurator_values` (`config_id`, `field_id`, `value`) VALUES ' . $valueList);
            }
        }


        $lastShop = 'rotterdam';

        foreach (self::SHOPS as $shop) {
            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addAfterWhere(
                [
                    '`name`' => '\'' . $shop['name'] . '\'',
                    '`label`' => '\'' . $shop['label'] . '\'',
                    '`field_type`' => '\'category\'',
                ],
                '`name` = \'' . $lastShop . '\' AND `field_type` = \'category\'',
            ));

            foreach (array_reverse(self::SHOP_FIELDS) as $day) {
                array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addChildWhere(
                    [
                        '`name`' => '\'' . $day['name'] . '\'',
                        '`label`' => '\'' . $day['label'] . '\'',
                        '`read_only`' => $day['readonly'] ? '1' : '0',
                        '`field_type`' => '\'' . $day['type'] . '\'',
                    ],
                    '`name` = \'' . $shop['name'] . '\' AND `field_type` = \'category\''
                ));
            }

            foreach (array_reverse(self::DAYS) as $day) {
                array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addChildWhere(
                    [
                        '`name`' => '\'' . $day['name'] . '\'',
                        '`label`' => '\'' . $day['label'] . '\'',
                        '`description`' => '\'uur\'',
                        '`field_type`' => '\'category\'',
                    ],
                    '`name` = \'hours\' AND `field_type` = \'category\' AND `parent_field_id` = (SELECT `id` FROM `cameranu`.`websiteconfigurator_fields` WHERE `name` = \'' . $shop['name'] . '\' AND `field_type` = \'category\')'
                ));

                foreach (array_reverse(self::DAYS_CHILDREN) as $child) {
                    array_map(fn ($q) => $this->addSql($q), $fieldsHelper->addChildWhere(
                        [
                            '`name`' => '\'' . $child['name'] . '\'',
                            '`label`' => '\'' . $child['label'] . '\'',
                            '`description`' => $child['description'] ? '\'' . $child['description'] . '\'' : 'NULL',
                            '`field_type`' => '\'' . $child['type'] . '\'',
                        ],
                        // Beetje vreemde query omdat we zo diep zitten nu. We
                        // zoeken hier de dag die we hierboven hebben
                        // aangemaakt. Deze heeft "hours" als parent en "hours"
                        // heeft de "shop" als parent.
                        '`name` = \'' . $day['name'] . '\' AND `field_type` = \'category\' AND `parent_field_id` = (SELECT `id` FROM `cameranu`.`websiteconfigurator_fields` WHERE `name` = \'hours\' AND `field_type` = \'category\' AND `parent_field_id` = (SELECT `id` FROM `websiteconfigurator_fields` WHERE `name` = \'' . $shop['name'] . '\' AND `field_type` = \'category\'))',
                    ));
                }
            }

            $lastShop = $shop['name'];
        }

        foreach (self::ADDRESSES as $name => $fieldValues) {
            $parentQuery = '(SELECT `id` FROM `cameranu`.`websiteconfigurator_fields` WHERE `name` = \'' . $name . '\' AND `field_type` = \'category\')';
            $baseFieldQuery = 'SELECT id FROM `cameranu`.`websiteconfigurator_fields` WHERE parent_field_id = ' . $parentQuery;

            $values = [];
            foreach ($fieldValues as $field => $value) {
                $values[] = ['1', '(' . $baseFieldQuery . ' AND `name` = \'' . $field . '\')', '\'' . $value . '\''];
            }

            foreach ($values as $value) {
                $valueList = '(' . implode(',', $value) . ')';
                $this->addSql('INSERT INTO `cameranu`.`websiteconfigurator_values` (`config_id`, `field_id`, `value`) VALUES ' . $valueList);
            }

        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::SHOPS as $name => $values) {
            $fieldIdQuery = 'SELECT id FROM `cameranu`.`websiteconfigurator_fields` WHERE parent_field_id = (SELECT `id` FROM `websiteconfigurator_fields` WHERE `name` = \'' . $name . '\' AND `field_type` = \'category\')';

            $this->addSql('DELETE FROM `cameranu`.`websiteconfigurator_values` WHERE config_id = 1 AND field_id IN (' . $fieldIdQuery . ')');
        }

        foreach (array_merge(self::PICKUP_SET_TODAY_IDS, self::PICKUP_SET_LATER_IDS) as $set) {
            $fieldIdQuery = 'SELECT id FROM  `cameranu`.`websiteconfigurator_fields` WHERE parent_field_id = (SELECT `id` FROM `websiteconfigurator_fields` WHERE `name` = \'' . $set['id'] . '\' AND `field_type` = \'category\')';

            $this->addSql('DELETE FROM  `cameranu`.`websiteconfigurator_values` WHERE config_id = 1 AND field_id IN (' . $fieldIdQuery . ')');
        }

        $fieldsHelper = new TreeMigrationHelper(
            '`cameranu`.`websiteconfigurator_fields`',
            [
                'id' => '`id`',
                'lft' => '`lft`',
                'rgt' => '`rgt`',
                'level' => '`level`',
                'parent' => '`parent_field_id`',
                'root' => '`root_field_id`',
            ]
        );

        foreach (self::SHOPS as $shop) {
            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->removeWhere(
                '`name` = \'' . $shop['name'] . '\' AND `field_type` = \'category\''
            ));
        }

        foreach (self::PICKUP_METHODS as $set) {
            array_map(fn ($q) => $this->addSql($q), $fieldsHelper->removeWhere(
                '`name` = \'' . $set['id'] . '\' AND `field_type` = \'category\''
            ));
        }
    }
}
