<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240311113859 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4200 Add foreign key for verzendwijze.changeOrigin';
    }

    public function up(Schema $schema): void
    {
        // Remove unique constraint over (id, country_id) in bestelling_herkomst table because only id column is the
        // primary key and therefore already unique
        // This key doesn't prevent referencing bestelling_herkomst in foreign keys, but it somehow sets the composite
        // unique key as the referenced object instead of just the primary key
        $originTable = $schema->getTable('bestelling_herkomst');
        $originTable->dropIndex('country_id_UNIQUE');

        $shipmentMethodTable = $schema->getTable('verzendwijze');
        $shipmentMethodTable->addForeignKeyConstraint(
            'bestelling_herkomst',
            ['changeOrigin'],
            ['id'],
            [
                'onDelete' => 'SET NULL',
                'onUpdate' => 'CASCADE',
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $shipmentMethodTable = $schema->getTable('verzendwijze');
        foreach ($shipmentMethodTable->getForeignKeys() as $foreignKey) {
            if ($foreignKey->getLocalColumns() === ['changeOrigin']) {
                $shipmentMethodTable->removeForeignKey($foreignKey->getName());
            }
        }

        $originTable = $schema->getTable('bestelling_herkomst');
        $originTable->addUniqueIndex(['id', 'country_id'], 'country_id_UNIQUE');
    }
}
