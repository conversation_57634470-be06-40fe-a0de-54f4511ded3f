<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\Subgroup;

final class Version20240326090013 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3968 verzerkeringsproducten en administratiekosten toevoegen';
    }

    public function up(Schema $schema): void
    {
        // Hernoemen van bestaande groep
        $this->addSql(
            '
            update `cameranu`.`subgroepen`
            set naam = :name
            where id = :id
        ', [
                'id' => Subgroup::SUBGROUP_ID_INSURANCE_NL,
                'name' => 'Actua Verzekering NL',
            ]
        );

        // Nieuwe groep toevoegen voor Belgische verzekeringen
        $this->addSql(
            '
            insert into `cameranu`.`subgroepen`
                (
                 id, hoofdgroep_id, naam, flags, flags_verzend, pos,
                 grootboekcode, grootboekcodei, robots_description, robots_keywords, robots_title,
                 url, spiderinfo
                )
                (
                 select
                    :be_id,
                    hoofdgroep_id,
                    :name,
                    flags,
                    flags_verzend,
                    pos,
                    grootboekcode,
                    grootboekcodei,
                    robots_description,
                    robots_keywords,
                    robots_title,
                    url,
                    spiderinfo
                 from `cameranu`.`subgroepen`
                 where id = :nl_id
                )
        ', [
                'be_id' => Subgroup::SUBGROUP_ID_INSURANCE_BE,
                'nl_id' => Subgroup::SUBGROUP_ID_INSURANCE_NL,
                'name' => 'Actua Verzekering BE',
            ]
        );

        // Verzekeringen toevoegen
        $insuranceProducts = $this->connection->executeQuery(
            sprintf(
                '
            select * from `cameranu`.`artikelen`
            where subgroep_id = %d
            ',
                Subgroup::SUBGROUP_ID_INSURANCE_NL
            )
        )->fetchAllAssociative();

        foreach ($insuranceProducts as $insuranceProduct) {
            // Nederlandse prijs ophalen voor dit product
            $price = $this->connection->executeQuery(
                '
                select * from `cameranu`.`prijzen`
                where artikel_id = :id and land = :country and domain_id = :domain
                ', [
                    'id' => $insuranceProduct['id'],
                    'country' => 'NL',
                    'domain' => 1,
                ]
            )->fetchAssociative();

            // Id mag bij administratiekosten niet random zijn en de prijs hoeft voor dit product niet aangepast te worden
            if ($insuranceProduct['id'] !== Product::PRODUCT_ID_INSURANCE_ADMINISTRATION_COSTS_NL) {
                unset($insuranceProduct['id']);
                $price['prijs_ex'] /= 3;
                $price['prijs'] = round($price['prijs_ex'] * 1.21, 2);
            } else {
                $insuranceProduct['id'] = Product::PRODUCT_ID_INSURANCE_ADMINISTRATION_COSTS_BE;
            }

            $insuranceProduct['subgroep_id'] = Subgroup::SUBGROUP_ID_INSURANCE_BE;
            $insuranceProduct['naam'] = str_replace('3-Jaar', '1-Jaar', $insuranceProduct['naam']);

            $this->connection->insert('cameranu.artikelen', $insuranceProduct);
            $lastId = $this->connection->lastInsertId();

            unset($price['id'], $price['user_id'], $price['tstamp']);
            // Delen door drie om de Belgische prijs te berekenen
            $price['artikel_id'] = $lastId;
            $price['land'] = 'BE';

            $this->connection->insert('cameranu.prijzen', $price);

            // Voor de overzichtelijkheid in de admin vullen we de Nederlandse prijs ook in
            $price['land'] = 'NL';
            $this->connection->insert('cameranu.prijzen', $price);
        }
    }

    public function down(Schema $schema): void
    {
        // Verwijderen van prijzen
        $this->addSql(
            '
            delete from `cameranu`.`prijzen`
            where artikel_id in (
                select id from `cameranu`.`artikelen`
                where subgroep_id = :id
            )', [
                'id' => Subgroup::SUBGROUP_ID_INSURANCE_BE,
            ]
        );

        // Verwijderen van de verzekeringen
        $this->addSql(
            '
            delete from `cameranu`.`artikelen`
            where subgroep_id = :id', [
                'id' => Subgroup::SUBGROUP_ID_INSURANCE_BE,
            ]
        );

        // Verwijderen van de groep
        $this->addSql(
            '
            delete from `cameranu`.`subgroepen`
            where id = :id
        ', [
                'id' => Subgroup::SUBGROUP_ID_INSURANCE_BE,
            ]
        );

        // Hernoemen van de groep
        $this->addSql(
            '
            update `cameranu`.`subgroepen`
            set naam = :name
            where id = :id
        ', [
                'id' => Subgroup::SUBGROUP_ID_INSURANCE_NL,
                'name' => 'Actua Verzekering',
            ]
        );
    }
}
