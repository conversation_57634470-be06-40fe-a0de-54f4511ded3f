<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240321090143 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4299 Antwerpen terminals toevoegen betalingsmogelijkheden_herkomst';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `betalingsmogelijkheden_herkomst` (`betalingsmogelijkheden_id`, `herkomst_id`)
            VALUES
                (462, 369),
                (463, 369);
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden_herkomst` WHERE `betalingsmogelijkheden_id` in (462, 463);');
    }
}
