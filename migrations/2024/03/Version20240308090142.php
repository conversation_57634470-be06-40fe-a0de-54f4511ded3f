<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240308090142 extends AbstractMigration
{
    private const SPECS_ARTICLE_PROFILE_TABLE = 'specs_article_profile';
    private const SPECS_PROFILES_TABLE = 'specs_profiles';
    private const PRODUCTS_TABLE = 'artikelen';

    public function getDescription(): string
    {
        return 'CAM-3984 (extra) - FKs to specs_article_profile table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                "DELETE FROM %s WHERE articleId NOT IN (SELECT id FROM %s)",
                self::SPECS_ARTICLE_PROFILE_TABLE,
                self::PRODUCTS_TABLE
            )
        );
        $this->addSql(
            sprintf(
                "DELETE FROM %s WHERE profileId NOT IN (SELECT id FROM %s)",
                self::SPECS_ARTICLE_PROFILE_TABLE,
                self::SPECS_PROFILES_TABLE
            )
        );
    }

    public function down(Schema $schema): void
    {
        // Can't
    }
}
