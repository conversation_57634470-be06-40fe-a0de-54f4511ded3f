<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;

final class Version20240325094512 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3873 - Add Calumet and Cyfrowe as CBS suppliers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            <<<'EOF'
INSERT INTO leveranciers_verzamel (
                                   naam,
                                   email,
                                   email_attachment_file_type,
                                   afzender,
                                   minbedrag,
                                   info,
                                   betaaltermijn,
                                   uses_ubl,
                                   ubl_email,
                                   default_delivery_time,
                                   packing_slip_from_email,
                                   packing_slip_mapping,
                                   webuser_id,
                                   uses_cbs) VALUES (
                                                     'Calumet (CBS)',
                                                     '',
                                                    'xml',
                                                     '<EMAIL>',
                                                     0.00,
                                                     'CBS export naar bucket',
                                                     0,
                                                     0,
                                                     null,
                                                     0,
                                                        null,
                                                     null,
                                                     null,
                                                     1
                                                     ),
                                                 (
                                                     '<PERSON><PERSON><PERSON><PERSON> (CBS)',
                                                     '',
                                                    'xml',
                                                     '<EMAIL>',
                                                     0.00,
                                                     'CBS export naar bucket',
                                                     0,
                                                     0,
                                                     null,
                                                     0,
                                                        null,
                                                     null,
                                                     null,
                                                     1
                                                     )
EOF
        );
        $this->addSql(
            <<<'EOF'
INSERT INTO leveranciers (
                          leverancier,
                          crediteur,
                          verzamel_id,
                          adres,
                          huisnr,
                          ext,
                          postcode,
                          plaats,
                          land,
                          telefoon,
                          fax,
                          mobiel,
                          email,
                          naam_bestel,
                          email_bestel,
                          bcc_bestel,
                          contact,
                          valuta_id,
                          commissie,
                          vrachtkosten,
                          vrachtkosten_buit,
                          verwijderingsbijdrage,
                          formule_direct,
                          formule_achteraf,
                          provisiepstuk,
                          infoEuropakorting,
                          login,
                          wachtwoord,
                          url,
                          info,
                          domain_id,
                          flags,
                          buildInvoicePrice,
                          servicepartnerId,
                          ubl_ignore_price
                          ) VALUES (
                                    'Calumet (CBS)',
                                    '',
                                    (SELECT id FROM leveranciers_verzamel WHERE naam = 'Calumet (CBS)'),
                                    '',
                                    0,
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    ''
                                    '',
                                    '',
                                    '',
                                    '',
                                    1,
                                    0,
                                    0,
                                    0,
                                    0,
                                    '',
                                    '',
                                    0,
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    0,
                                    null,
                                    null,
                                    0
                          ),
                                (
                                    'Cyfrowe (CBS)',
                                    '',
                                    (SELECT id FROM leveranciers_verzamel WHERE naam = 'Cyfrowe (CBS)'),
                                    '',
                                    0,
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    ''
                                    '',
                                    '',
                                    '',
                                    '',
                                    1,
                                    0,
                                    0,
                                    0,
                                    0,
                                    '',
                                    '',
                                    0,
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    '',
                                    0,
                                    null,
                                    null,
                                    0
                          )

EOF
        );
        $this->addSql(
            sprintf(
                "UPDATE leverancier_feeds SET verzamel_id = (SELECT id FROM leveranciers_verzamel WHERE naam = 'Calumet (CBS)') WHERE id = %s",
                SupplierFeed::ID_CALUMET
            )
        );
        $this->addSql(
            sprintf(
                "UPDATE leverancier_feeds SET verzamel_id = (SELECT id FROM leveranciers_verzamel WHERE naam = 'Cyfrowe (CBS)') WHERE id = %s",
                SupplierFeed::ID_CYFROWE
            )
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                'UPDATE leverancier_feeds SET verzamel_id = null WHERE id IN (%s, %s)',
                SupplierFeed::ID_CALUMET,
                SupplierFeed::ID_CYFROWE
            )
        );
        // FK on leveranciers will set it to NULL when deleting supplier group, not delete it. So do it manually
        $this->addSql(
            "DELETE FROM leveranciers WHERE verzamel_id IN (SELECT id FROM leveranciers_verzamel WHERE naam IN ('Calumet (CBS)', 'Cyfrowe (CBS)'))"
        );
        $this->addSql("DELETE FROM leveranciers_verzamel WHERE naam IN ('Calumet (CBS)', 'Cyfrowe (CBS)')");
    }
}
