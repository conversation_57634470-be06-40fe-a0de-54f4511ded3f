<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240327142157 extends AbstractMigration
{
    public const MARGINS_TABLE = 'artikelen_marge';
    public const FK_MARGINS_TABLE_ARTICLE_ID = 'FK_artikelen_marge_articleId';

    public function getDescription(): string
    {
        return 'add FK to margins table';
    }

    public function up(Schema $schema): void
    {
        // Remove auto increment and make it signed to comply with artikelen.id
        $this->addSql(sprintf('ALTER TABLE %s MODIFY articleId INT NOT NULL', self::MARGINS_TABLE));
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE `%s`.`articleId` NOT IN (SELECT id FROM `artikelen`)',
                self::MARGINS_TABLE,
                self::MARGINS_TABLE
            )
        );

        $table = $schema->getTable(self::MARGINS_TABLE);
        $table->addForeignKeyConstraint('artikelen', ['articleId'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], self::FK_MARGINS_TABLE_ARTICLE_ID);
    }

    public function down(Schema $schema): void
    {
        // Need to only use addSql here because of order with Schema object...
        // addSql statements come before any sql you generate with Schema object
        $this->addSql(
            'ALTER TABLE `' . self::MARGINS_TABLE . '` DROP FOREIGN KEY `' . self::FK_MARGINS_TABLE_ARTICLE_ID . '`;'
        );
        $this->addSql(
            sprintf('ALTER TABLE %s MODIFY articleId INT UNSIGNED NOT NULL auto_increment', self::MARGINS_TABLE)
        );
    }
}
