<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240304081345 extends AbstractMigration
{
    private const TABLE = 'discountset_positions';
    private const COLUMN = 'discount_override_percent';

    public function getDescription(): string
    {
        return 'CAM-2385 - discount override on discountset positions';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->addColumn(self::COLUMN, Types::INTEGER, [
            'notnull' => false,
            'unsigned' => true,
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->dropColumn(self::COLUMN);
    }
}
