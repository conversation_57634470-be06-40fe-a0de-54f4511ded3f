<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\PaymentType;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240322092104 extends AbstractMigration
{
    private const ORIGIN_PROFILES = [
        Origin::AMSTERDAM => [
            'old' => '122,132,142,169,190',
            'new' => '122,132,142,169,190,191,444,445,446,447,448',
        ],
        Origin::APELDOORN => [
            'old' => '122,132,142,169,190',
            'new' => '122,132,142,169,190,191,458,459,460,461',
        ],
        Origin::GRONINGEN => [
            'old' => '192,194,196,190',
            'new' => '192,194,196,190,191,453,454',
        ],
        Origin::EINDHOVEN => [
            'old' => '220,222,224,226,228,190',
            'new' => '220,222,224,226,228,190,191,455,456,457',
        ],
        Origin::URK => [
            'old' => '1,2,3,4,5,8,14,54,115,190',
            'new' => '1,2,3,4,5,8,14,54,115,190,191,435,436,437,438,439,440,441,442,443',
        ],
        Origin::ROTTERDAM => [
            'old' => '241,243,245,247,249,251,190',
            'new' => '241,243,245,247,249,251,190,191,449,450,451,452',
        ],
        Origin::ANTWERPEN => [
            'old' => '420,421,422,428,429',
            'new' => '420,421,422,428,429,191,462,463',
        ],
    ];

    public function getDescription(): string
    {
        return 'CAM-4312 add Mollie payMethods to origin_profiles';
    }

    public function up(Schema $schema): void
    {
        foreach ($this::ORIGIN_PROFILES as $origin => $payMethods) {
            $this->addSql(
                'UPDATE `cameranu`.`origins_profiles` SET `pay_methods` = \'' . $payMethods['new'] . '\' WHERE `origin_id` = ' . $origin
            );

            $this->addSql(
                'INSERT INTO `cameranu`.`betalingsmogelijkheden_herkomst`
                        (`betalingsmogelijkheden_id`, `herkomst_id`)
                     VALUES
                        (' . PaymentType::TYPE_MOLLIE . ', ' . $origin . ')
            ');
        }

        $this->addSql(
            'INSERT INTO `cameranu`.`betalingsmogelijkheden_herkomst`
                        (`betalingsmogelijkheden_id`, `herkomst_id`)
                     VALUES
                        (' . PaymentType::TYPE_MOLLIE . ', ' . Origin::WEBSITE . '),
                        (' . PaymentType::TYPE_MOLLIE . ', ' . Origin::BEURS . ')
            ');
    }

    public function down(Schema $schema): void
    {
        foreach ($this::ORIGIN_PROFILES as $origin => $payMethods) {
            $this->addSql(
                'UPDATE `cameranu`.`origins_profiles` SET `pay_methods` = \'' . $payMethods['old'] . '\' WHERE `origin_id` = ' . $origin
            );
        }

        $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden_herkomst` WHERE `betalingsmogelijkheden_id` = ' . PaymentType::TYPE_MOLLIE);
    }
}
