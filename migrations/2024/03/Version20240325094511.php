<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240325094511 extends AbstractMigration
{
    public const SUPPLIER_FEEDS_TABLE = 'leverancier_feeds';
    public const SUPPLIER_GROUPS_TABLE = 'leveranciers_verzamel';
    public const SUPPLIER_STOCK_TABLE = 'leverancier_voorraad';
    public const PRODUCTS_TABLE = 'artikelen';
    public const MEMO_TABLE = 'voorraad_memo';
    public const SUPPLIERS_TABLE = 'leveranciers';
    public const PURCHASE_REFERENCE_TABLE = 'inkoop_referenties';
    public const FEEDS_TABLE_GROUP_ID_FK = 'FK_leverancier_feeds_verzamel_id';
    public const SUPPLIER_STOCK_TABLE_PRODUCT_ID_FK = 'FK_leverancier_voorraad_art_id';
    public const SUPPLIER_STOCK_TABLE_SUPPLIER_ID_FK = 'FK_leverancier_voorraad_leverancier';
    public const MEMO_TABLE_PRODUCT_ID_FK = 'FK_voorraad_memo_artikel_id';
    public const MEMO_TABLE_SUPPLIER_ID_FK = 'FK_voorraad_memo_leverancier_id';
    public const SUPPLIERS_TABLE_SUPPLIER_GROUP_ID_FK = 'FK_leveranciers_verzamel_id';
    public const PURCHASE_REFERENCE_TABLE_SUPPLIER_ID_FK = 'FK_inkoop_referenties_leverancier_id';

    public function getDescription(): string
    {
        return 'CAM-3873 - Many many supplier foreign keys';
    }

    public function up(Schema $schema): void
    {
        $this->fixForeignKeyViolations();
        $this->addForeignKeys($schema);

        $suppliersGroupsTable = $schema->getTable(self::SUPPLIER_GROUPS_TABLE);
        $suppliersGroupsTable->addColumn('uses_cbs', Types::BOOLEAN, ['default' => false]);
    }

    public function down(Schema $schema): void
    {
        $feedsTable = $schema->getTable(self::SUPPLIER_FEEDS_TABLE);
        $suppliersGroupsTable = $schema->getTable(self::SUPPLIER_GROUPS_TABLE);
        $supplierStockTable = $schema->getTable(self::SUPPLIER_STOCK_TABLE);
        $memoTable = $schema->getTable(self::MEMO_TABLE);

        $feedsTable->removeForeignKey(self::FEEDS_TABLE_GROUP_ID_FK);
        $supplierStockTable->removeForeignKey(self::SUPPLIER_STOCK_TABLE_PRODUCT_ID_FK);
        $supplierStockTable->removeForeignKey(self::SUPPLIER_STOCK_TABLE_SUPPLIER_ID_FK);
        $memoTable->removeForeignKey(self::MEMO_TABLE_PRODUCT_ID_FK);
        $memoTable->removeForeignKey(self::MEMO_TABLE_SUPPLIER_ID_FK);

        $suppliersGroupsTable->dropColumn('uses_cbs');

        // Need to manually drop these FKs because addSql statements run BEFORE schema statements, and we didn't
        // just drop rows but changed them

        // suppliers table
        $this->addSql(
            sprintf(
                'ALTER TABLE %s DROP FOREIGN KEY %s',
                self::SUPPLIERS_TABLE,
                self::SUPPLIERS_TABLE_SUPPLIER_GROUP_ID_FK
            )
        );
        $this->addSql(sprintf('UPDATE %s SET verzamel_id = 0 WHERE verzamel_id IS NULL', self::SUPPLIERS_TABLE));
        $this->addSql(
            sprintf(
                'ALTER TABLE %s CHANGE `verzamel_id` `verzamel_id` INT(11)  NOT NULL  DEFAULT 0;',
                self::SUPPLIERS_TABLE
            )
        );

        // Purchase references table
        $this->addSql(
            sprintf(
                'ALTER TABLE %s DROP FOREIGN KEY %s',
                self::PURCHASE_REFERENCE_TABLE,
                self::PURCHASE_REFERENCE_TABLE_SUPPLIER_ID_FK
            )
        );
        $this->addSql(
            sprintf('UPDATE %s SET leverancier_id = 0 WHERE leverancier_id IS NULL', self::PURCHASE_REFERENCE_TABLE)
        );
        $this->addSql(
            sprintf(
                'ALTER TABLE %s CHANGE leverancier_id leverancier_id INT(11)  NOT NULL',
                self::PURCHASE_REFERENCE_TABLE
            )
        );
    }

    private function fixForeignKeyViolations(): void
    {
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE verzamel_id NOT IN (SELECT id FROM %s)',
                self::SUPPLIER_FEEDS_TABLE,
                self::SUPPLIER_GROUPS_TABLE
            )
        );
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE art_id NOT IN (SELECT id FROM %s)',
                self::SUPPLIER_STOCK_TABLE,
                self::PRODUCTS_TABLE
            )
        );
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE leverancier NOT IN (SELECT id FROM %s)',
                self::SUPPLIER_STOCK_TABLE,
                self::SUPPLIER_FEEDS_TABLE
            )
        );
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE artikel_id NOT IN (SELECT id FROM %s)',
                self::MEMO_TABLE,
                self::PRODUCTS_TABLE
            )
        );
        $this->addSql(
            sprintf(
                'DELETE FROM %s WHERE leverancier_id NOT IN (SELECT id FROM %s)',
                self::MEMO_TABLE,
                self::SUPPLIERS_TABLE
            )
        );

        $this->addSql(sprintf('ALTER TABLE %s CHANGE verzamel_id verzamel_id INT(11) NULL', self::SUPPLIERS_TABLE));
        $this->addSql(sprintf('UPDATE %s SET verzamel_id = NULL WHERE verzamel_id = 0', self::SUPPLIERS_TABLE));

        $this->addSql(
            sprintf(
                'ALTER TABLE %s CHANGE `leverancier_id` `leverancier_id` INT(11)  NULL DEFAULT NULL;',
                self::PURCHASE_REFERENCE_TABLE
            )
        );
        $this->addSql(
            sprintf(
                'UPDATE %s SET leverancier_id = NULL WHERE leverancier_id NOT IN (SELECT id FROM %s)',
                self::PURCHASE_REFERENCE_TABLE,
                self::SUPPLIER_GROUPS_TABLE
            )
        );
    }

    private function addForeignKeys(Schema $schema): void
    {
        $feedsTable = $schema->getTable(self::SUPPLIER_FEEDS_TABLE);
        $supplierStockTable = $schema->getTable(self::SUPPLIER_STOCK_TABLE);
        $memoTable = $schema->getTable(self::MEMO_TABLE);
        $suppliersTable = $schema->getTable(self::SUPPLIERS_TABLE);
        $purchaseReferencesTable = $schema->getTable(self::PURCHASE_REFERENCE_TABLE);

        $feedsTable->addForeignKeyConstraint(self::SUPPLIER_GROUPS_TABLE, ['verzamel_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], self::FEEDS_TABLE_GROUP_ID_FK);

        $supplierStockTable->addForeignKeyConstraint(self::PRODUCTS_TABLE, ['art_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], self::SUPPLIER_STOCK_TABLE_PRODUCT_ID_FK);
        $supplierStockTable->addForeignKeyConstraint(self::SUPPLIER_FEEDS_TABLE, ['leverancier'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], self::SUPPLIER_STOCK_TABLE_SUPPLIER_ID_FK);

        $memoTable->addForeignKeyConstraint(self::PRODUCTS_TABLE, ['artikel_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], self::MEMO_TABLE_PRODUCT_ID_FK);
        $memoTable->addForeignKeyConstraint(self::SUPPLIERS_TABLE, ['leverancier_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'CASCADE',
        ], self::MEMO_TABLE_SUPPLIER_ID_FK);

        $suppliersTable->addForeignKeyConstraint(self::SUPPLIER_GROUPS_TABLE, ['verzamel_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'SET NULL',
        ], self::SUPPLIERS_TABLE_SUPPLIER_GROUP_ID_FK);

        $purchaseReferencesTable->addForeignKeyConstraint(self::SUPPLIER_GROUPS_TABLE, ['leverancier_id'], ['id'], [
            'onUpdate' => 'CASCADE',
            'onDelete' => 'SET NULL'
        ], self::PURCHASE_REFERENCE_TABLE_SUPPLIER_ID_FK);
    }
}
