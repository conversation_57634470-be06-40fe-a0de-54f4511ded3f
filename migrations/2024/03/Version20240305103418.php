<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240305103418 extends AbstractMigration
{
    private const IDS = [435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461];

    public function getDescription(): string
    {
        return 'CAM-3061 Add new terminals to database';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`betalingsmogelijkheden` (`id`, `pos`, `omschrijving`, `goedhart`, `flags`, `betaalwijze_id`)
            VALUES
                (435, 435, \'URK A920 1\', \'pin\', 1, 3),
                (436, 436, \'URK A920 2\', \'pin\', 1, 3),
                (437, 437, \'URK A920 3\', \'pin\', 1, 3),
                (438, 438, \'URK A920 4\', \'pin\', 1, 3),
                (439, 439, \'URK A920 5\', \'pin\', 1, 3),
                (440, 440, \'URK A35 1\', \'pin\', 1, 3),
                (441, 441, \'URK A35 2\', \'pin\', 1, 3),
                (442, 442, \'URK A35 3\', \'pin\', 1, 3),
                (443, 443, \'URK A35 4\', \'pin\', 1, 3),
                (444, 444, \'AMSTERDAM A35 1\', \'pin_ams\', 1, 3),
                (445, 445, \'AMSTERDAM A35 2\', \'pin_ams\', 1, 3),
                (446, 446, \'AMSTERDAM A35 3\', \'pin_ams\', 1, 3),
                (447, 447, \'AMSTERDAM A35 4\', \'pin_ams\', 1, 3),
                (448, 448, \'AMSTERDAM A920 1\', \'pin_ams\', 1, 3),
                (449, 449, \'ROTTERDAM A35 1\', \'pin_rot\', 1, 3),
                (450, 450, \'ROTTERDAM A35 2\', \'pin_rot\', 1, 3),
                (451, 451, \'ROTTERDAM A920 1\', \'pin_rot\', 1, 3),
                (452, 452, \'ROTTERDAM A920 2\', \'pin_rot\', 1, 3),
                (453, 453, \'GRONINGEN A35 1\', \'pin_gro\', 1, 3),
                (454, 454, \'GRONINGEN A920 1\', \'pin_gro\', 1, 3),
                (455, 455, \'EINDHOVEN A35 1\', \'pin_ein\', 1, 3),
                (456, 456, \'EINDHOVEN A35 2\', \'pin_ein\', 1, 3),
                (457, 457, \'EINDHOVEN A920 1\', \'pin_ein\', 1, 3),
                (458, 458, \'APELDOORN A35 1\', \'pin_apl\', 1, 3),
                (459, 459, \'APELDOORN A35 2\', \'pin_apl\', 1, 3),
                (460, 460, \'APELDOORN A35 3\', \'pin_apl\', 1, 3),
                (461, 461, \'APELDOORN A920 1\', \'pin_apl\', 1, 3),
                (462, 462, \'ANTWERPEN A35 1\', \'pin_ant\', 1, 3),
                (463, 463, \'ANTWERPEN A920 1\', \'pin_ant\', 1, 3);
        ');
        $this->addSql('
            INSERT INTO `cameranu`.`pin_terminals` (`payment_type_id`, `terminal_id`, `psp`, `enabled`)
            VALUES
                (435, \'term_k96qfhea8rHBesFhTvfkH\', \'mollie\', 1),
                (436, \'term_3q69YqvAfBUZv5AKgvfkH\', \'mollie\', 1),
                (437, \'term_39Qvb7rjCEFUiCDNovfkH\', \'mollie\', 1),
                (438, \'term_tKBQhSyG6ucVK88dtvfkH\', \'mollie\', 1),
                (439, \'term_uXhDhujVFN3yhavbawfkH\', \'mollie\', 1),
                (440, \'term_bhEvQyvdBZRX4aRnwwfkH\', \'mollie\', 1),
                (441, \'term_SPhCgE7DNsCZugevwwfkH\', \'mollie\', 1),
                (442, \'term_wk2qibFRbffkgg96xwfkH\', \'mollie\', 1),
                (443, \'term_epYRwVhhvrUVBdSExwfkH\', \'mollie\', 1),
                (444, \'term_ZGuxnDWLcLArqA7gywfkH\', \'mollie\', 1),
                (445, \'term_Tqaxsnk6kTALgbLpywfkH\', \'mollie\', 1),
                (446, \'term_H6y2xUgGsf785ebxywfkH\', \'mollie\', 1),
                (447, \'term_WcctfmUCwnikXnb7zwfkH\', \'mollie\', 1),
                (448, \'term_WCpwanam5X7EAUQruwfkH\', \'mollie\', 1),
                (449, \'term_UJShvHQCNSyH9Y7PywfkH\', \'mollie\', 1),
                (450, \'term_DGp8f95ScE2iKoiXywfkH\', \'mollie\', 1),
                (451, \'term_uxMjP6VpJxdNiwHYuwfkH\', \'mollie\', 1),
                (452, \'term_E9RrzpDCTcNreashuwfkH\', \'mollie\', 1),
                (453, \'term_HZuMnRhZeho2cTrEywfkH\', \'mollie\', 1),
                (454, \'term_BELwiewUdnyMLhFNuwfkH\', \'mollie\', 1),
                (455, \'term_GHDeyopL3Q2gqdjoxwfkH\', \'mollie\', 1),
                (456, \'term_3T8gSefaZVPgJs62ywfkH\', \'mollie\', 1),
                (457, \'term_J8N6ryN4s5RZhFVCuwfkH\', \'mollie\', 1),
                (458, \'term_EmJ64kseANPnmokNxwfkH\', \'mollie\', 1),
                (459, \'term_2ewiLaifbbMrpaDXxwfkH\', \'mollie\', 1),
                (460, \'term_kMtcashwtd67DNZfxwfkH\', \'mollie\', 1),
                (461, \'term_WST8Lw6exBDrENpxtwfkH\', \'mollie\', 1),
                (462, \'term_8yFEn6Y9WkdE24ZGzwfkH\', \'mollie\', 1),
                (463, \'term_f2BbRpMAs3EDqMM3vwfkH\', \'mollie\', 1);
        ');
        $this->addSql('
            INSERT INTO `betalingsmogelijkheden_herkomst` (`betalingsmogelijkheden_id`, `herkomst_id`)
            VALUES
                (435, 15),
                (436, 15),
                (437, 15),
                (438, 15),
                (439, 15),
                (440, 15),
                (441, 15),
                (442, 15),
                (443, 15),
                (444, 332),
                (445, 332),
                (446, 332),
                (447, 332),
                (448, 332),
                (449, 363),
                (450, 363),
                (451, 363),
                (452, 363),
                (453, 346),
                (454, 346),
                (455, 359),
                (456, 359),
                (457, 359),
                (458, 343),
                (459, 343),
                (460, 343),
                (461, 343);
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden` WHERE `id` in (' . implode(',', self::IDS) . ');');
        $this->addSql('DELETE FROM `cameranu`.`pin_terminals` WHERE `payment_type_id` in (' . implode(',', self::IDS) . ');');
        $this->addSql('DELETE FROM `cameranu`.`betalingsmogelijkheden_herkomst` WHERE `betalingsmogelijkheden_id` in (' . implode(',', self::IDS) . ');');
    }
}
