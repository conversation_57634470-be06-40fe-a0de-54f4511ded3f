<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240325155022 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4229 add field for kickBackPremium to the Adchieve productFeed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO `cameranu`.`productfeed_fields` (`key`, `feed_id`, `name`) VALUES (\'hasKickBackPremium\', '. ProductFeed::ADCHIEVE . ', \'kickback_premie\')');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `key` = \'hasKickBackPremium\'');
    }
}
