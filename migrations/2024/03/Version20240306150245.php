<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240306150245 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3057 Add status and pspReference properties to PSP\\Log';
    }

    public function up(Schema $schema): void
    {
        $pspLogTable = $schema->getTable('psp_log');

        $pspLogTable->addColumn('status', Types::STRING, [
            'notnull' => false,
            'length' => 255,
        ]);
        $pspLogTable->addColumn('psp_reference', Types::STRING, [
            'notnull' => true,
            'length' => 255,
        ]);

        $pspLogTable->addIndex(['psp_reference']);
        $pspLogTable->addIndex(['type']);
        $pspLogTable->addIndex(['status']);
    }

    public function down(Schema $schema): void
    {
        $pspLogTable = $schema->getTable('psp_log');

        foreach ($pspLogTable->getIndexes() as $index) {
            if (in_array($index->getColumns(), [['psp_reference'], ['type'], ['status']])) {
                $pspLogTable->dropIndex($index->getName());
            }
        }

        $pspLogTable->dropColumn('status');
        $pspLogTable->dropColumn('psp_reference');
    }
}
