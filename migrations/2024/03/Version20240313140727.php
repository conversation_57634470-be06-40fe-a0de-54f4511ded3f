<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductWarranty;
use Webdsign\GlobalBundle\Entity\Subgroup;

final class Version20240313140727 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4207 facturen van events corrigeren';
    }

    public function up(Schema $schema): void
    {
        // Garantie op artikelniveau corrigeren
        $this->addSql('
            update cameranu.artikelen as a
            set a.garantie_type = :warranty_none
            where a.subgroep_id = :events_subgroup
            and a.garantie_type = :warranty_two_years
        ', [
            'warranty_none' => ProductWarranty::NONE,
            'events_subgroup' => Subgroup::SUBGROUP_EVENTS_ID,
            'warranty_two_years' => ProductWarranty::TWO_YEARS,
        ]);

        // Garantie op winkelmandniveau corrigeren
        $this->addSql('
            update cameranu.shopcart as s
            inner join cameranu.artikelen as a on a.id = s.artikel_id and a.subgroep_id = :events_subgroup
            set s.garantie_id = :warranty_none
            where s.garantie_id = :warranty_two_years
        ', [
            'events_subgroup' => Subgroup::SUBGROUP_EVENTS_ID,
            'warranty_none' => ProductWarranty::NONE,
            'warranty_two_years' => ProductWarranty::TWO_YEARS,
        ]);

        // Factuur- en afronddatum corrigeren
        $this->addSql('
            update cameranu.bestelling_naw as bn
            inner join cameranu.shopcart as s on s.bestelling_id = bn.id
            inner join cameranu.artikelen as a on a.id = s.artikel_id and a.subgroep_id = :events_subgroup
            set bn.datum_factuur = bn.besteldatum, bn.datum_afgeh = bn.besteldatum
            where bn.factuurnummer > 0
        ', [
            'events_subgroup' => Subgroup::SUBGROUP_EVENTS_ID,
        ]);
    }

    public function down(Schema $schema): void
    {
        // Niet nodig
    }
}
