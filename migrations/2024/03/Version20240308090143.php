<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240308090143 extends AbstractMigration
{
    private const PRODUCTS_TABLE = 'artikelen';
    private const CBS_COLUMN = 'cbs_available';
    private const PRODUCTS_PROFILE_TABLE = 'specs_article_profile';
    private const SPECS_PROFILES_TABLE = 'specs_profiles';

    private const PRODUCTS_PROFILE_TABLE_PROD_FK = 'FK_specs_article_profile_articleId';
    private const PRODUCTS_PROFILE_TABLE_PROFILE_FK = 'FK_specs_article_profile_profileId';

    public function getDescription(): string
    {
        return 'CAM-3984 - Add cbs-available bool to products';
    }

    public function up(Schema $schema): void
    {
        $productsTable = $schema->getTable(self::PRODUCTS_TABLE);
        $productsTable->addColumn(self::CBS_COLUMN, Types::BOOLEAN, ['notnull' => true, 'default' => false]);

        $productsProfileTable = $schema->getTable(self::PRODUCTS_PROFILE_TABLE);
        $productsProfileTable->addForeignKeyConstraint(
            self::PRODUCTS_TABLE,
            ['articleId'],
            ['id'],
            ['onUpdate' => 'CASCADE', 'onDelete' => 'CASCADE'],
            self::PRODUCTS_PROFILE_TABLE_PROD_FK
        );
        $productsProfileTable->addForeignKeyConstraint(
            self::SPECS_PROFILES_TABLE,
            ['profileId'],
            ['id'],
            ['onUpdate' => 'CASCADE', 'onDelete' => 'CASCADE'],
            self::PRODUCTS_PROFILE_TABLE_PROFILE_FK
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::PRODUCTS_TABLE);
        $table->dropColumn(self::CBS_COLUMN);

        $productsProfileTable = $schema->getTable(self::PRODUCTS_PROFILE_TABLE);
        $productsProfileTable->removeForeignKey(self::PRODUCTS_PROFILE_TABLE_PROD_FK);
        $productsProfileTable->removeForeignKey(self::PRODUCTS_PROFILE_TABLE_PROFILE_FK);
    }
}
