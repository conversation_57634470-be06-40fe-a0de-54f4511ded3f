<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\PaymentMethod;

final class Version20240301105547 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3055 Add translations for Mollie payment method';
    }

    public function up(Schema $schema): void
    {
        $qb = $this->connection->createQueryBuilder();

        $qb
            ->insert('betaalwijze2')
            ->values([
                'parent_id' => ':parentId',
                'omschrijving' => ':description',
                'info' => ':info',
                'taal' => ':language',
            ])
            ->setParameters([
                'parentId' => PaymentMethod::ID_MOLLIE,
                'description' => 'Mollie',
                'info' => '',
                'language' => 'nl',
            ])
        ;

        $qb->executeQuery();

        $qb->setParameter('language', 'en');

        $qb->executeQuery();

        $paymentMethodTextTable = $schema->getTable('betaalwijze2');

        $paymentMethodTextTable->addForeignKeyConstraint(
            'betaalwijze1',
            ['parent_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $qb = $this->connection->createQueryBuilder();

        $qb
            ->delete('betaalwijze2')
            ->where('parent_id = :parentId')
            ->setParameter('parentId', PaymentMethod::ID_MOLLIE)
        ;

        $qb->executeQuery();

        $paymentMethodTextTable = $schema->getTable('betaalwijze2');

        foreach ($paymentMethodTextTable->getForeignKeys() as $foreignKey) {
            if ($foreignKey->getLocalColumns() === ['parent_id']) {
                $paymentMethodTextTable->removeForeignKey($foreignKey->getName());
            }
        }
    }
}
