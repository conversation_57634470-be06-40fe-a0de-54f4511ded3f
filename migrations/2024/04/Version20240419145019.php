<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240419145019 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3928 add margin indication to Tweakwise feed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`productfeed_fields` (`key`, `feed_id`, `name`)
            VALUES
                (\'margin_indication\', ' . ProductFeed::TWEAKWISE_ID . ', \'verhouding_x\'),
                (\'margin_indication\', ' . ProductFeed::TWEAKWISE_TEST_ID . ', \'verhouding_x\')
            ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `key` = \'margin_indication\'');
    }
}
