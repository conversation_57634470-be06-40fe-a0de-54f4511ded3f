<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240409143854 extends AbstractMigration
{
    private const TABLE = 'cameranu.events';
    private const COLUMN = 'canGoOffline';

    public function getDescription(): string
    {
        return 'CAM-4133 - Optie toevoegen om een event automatisch offline te laten zien op de website';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->addColumn(self::COLUMN, Types::BOOLEAN, [
            'notnull' => false,
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->dropColumn(self::COLUMN);
    }
}
