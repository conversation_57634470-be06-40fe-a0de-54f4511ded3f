<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240410090009 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3798 add be discount price fields to the Adchieve productFeed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`productfeed_fields` (`key`, `feed_id`, `name`)
            VALUES
                (\'priceFromBe\', '. ProductFeed::ADCHIEVE . ', \'prijs_was_be\'),
                (\'priceAfterBe\', '. ProductFeed::ADCHIEVE . ', \'prijs_na_promoties_be\'),
                (\'priceAfterDiscountBe\', '. ProductFeed::ADCHIEVE . ', \'prijs_na_korting_be\')
            ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `key` IN (\'priceFromBe\', \'priceAfterBe\', \'priceAfterDiscountBe\')');
    }
}
