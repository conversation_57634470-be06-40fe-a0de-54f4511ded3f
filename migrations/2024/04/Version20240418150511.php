<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Country;
use Webdsign\GlobalBundle\Entity\Domain;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\Parking;
use Webdsign\GlobalBundle\Entity\PaymentType;

final class Version20240418150511 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3874 - herkomst en parkeermap voor CBS toevoegen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO cameranu.bestelling_herkomst
            (id, country_id, source, parent, preferredLanguage, is_cameranu, description, code, flags_verzend)
            VALUES
            (:id, :country_id, :source, :parent, :preferred_language, :is_cameranu, :description, :code, :flags_verzend)
        ', [
            'id' => Origin::CBS,
            'country_id' => Country::COUNTRY_ID_NL,
            'source' => 'CBS',
            'parent' => 'cameranu',
            'preferred_language' => 'nl',
            'is_cameranu' => 1,
            'description' => 'Cameranu - CBS',
            'code' => 0,
            'flags_verzend' => 0,
        ]);

        $this->addSql('INSERT INTO cameranu.betalingsmogelijkheden_herkomst
            (betalingsmogelijkheden_id, herkomst_id)
            VALUES
            (:paymentMethod_id, :origin_id)
        ', [
            'paymentMethod_id' => PaymentType::BANKTRANSFER_URK,
            'origin_id' => Origin::CBS,
        ]);

        $this->addSql('INSERT INTO cameranu.betalingsmogelijkheden_herkomst
            (betalingsmogelijkheden_id, herkomst_id)
            VALUES
            (:paymentMethod_id, :origin_id)
        ', [
            'paymentMethod_id' => PaymentType::TYPE_TO_BE_RECEIVED,
            'origin_id' => Origin::CBS,
        ]);

        $this->addSql('INSERT INTO cameranu.parkeren
            (id, domain_id, pos, omschrijving, info, flags)
            VALUES
            (:id, :domain_id, :pos, :description, :info, :flags)
        ', [
            'id' => Parking::CBS_TO_BE_JUDGED,
            'domain_id' => Domain::CAMERANU,
            'pos' => 2866,
            'description' => 'Binnenkomende CBS-orders',
            'info' => '',
            'flags' => Parking::FLAG_EATING + Parking::FLAG_ALLOW_PARKING,
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM cameranu.bestelling_herkomst WHERE id = :id', ['id' => Origin::CBS]);
        $this->addSql('DELETE FROM cameranu.betalingsmogelijkheden_herkomst WHERE herkomst_id = :id', ['id' => Origin::CBS]);
        $this->addSql('DELETE FROM cameranu.parkeren WHERE id = :id', ['id' => Parking::CBS_JUDGED]);
    }
}
