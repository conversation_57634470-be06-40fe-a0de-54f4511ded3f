<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240430095707 extends AbstractMigration
{
    public const CATEGORY = 'Tweedehands';
    public const CONTENT_TYPES = [
        90 => 'imageFullWidth'
    ];
    public const SECONDHAND_TYPES = [
        'secondhandHero',
        'secondhandStep',
        'secondhandWhyPoint',
        'secondhandScrollableCards',
        'secondhandReviews',
    ];

    public function getDescription(): string
    {
        return 'CAM-4491 Nieuwe pageblokken toevoegen voor tweedehands';
    }

    public function up(Schema $schema): void
    {
        $timestamp = time();
        $sql = 'INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
                VALUES (:created, :modified, :type, :sort, :category)';

        foreach (self::CONTENT_TYPES as $sort => $type) {
            $this->addSql($sql, [
                'created' => $timestamp,
                'modified' => $timestamp,
                'type' => $type,
                'sort' => $sort,
                'category' => 'Content',
            ]);
        }
        foreach (self::SECONDHAND_TYPES as $sort => $type) {
            $this->addSql($sql, [
                'created' => $timestamp,
                'modified' => $timestamp,
                'type' => $type,
                'sort' => $sort,
                'category' => 'Tweedehands',
            ]);
        }
    }

    public function down(Schema $schema): void
    {
        $sql = 'DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = :type';

        foreach (self::CONTENT_TYPES as $type) {
            $this->addSql($sql, ['type' => $type]);
        }
        foreach (self::SECONDHAND_TYPES as $type) {
            $this->addSql($sql, ['type' => $type]);
        }
    }
}
