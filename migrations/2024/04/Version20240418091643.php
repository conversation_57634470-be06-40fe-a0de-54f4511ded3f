<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Customer;

final class Version20240418091643 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3874 - tokens toevoegen voor Calumet en Cyfrowe';
    }

    public function up(Schema $schema): void
    {
        // Tokens toevoegen
        $insertQuery = 'INSERT INTO cameranu.webusers_order_import (user_id, token) VALUES (:userId, :token)';
        $this->addSql(
            $insertQuery,
            ['userId' => Customer::ID_CBS_CALUMET, 'token' => '3c2b48d1-e2f1-4a1d-8f8e-c7e2b7d3f2c9']
        );
        $this->addSql(
            $insertQuery,
            ['userId' => Customer::ID_CBS_CYFROWE, 'token' => 'e87105c2-6d7e-400a-a531-5fd39b08eab0']
        );
    }

    public function down(Schema $schema): void
    {
        foreach ([Customer::ID_CBS_CALUMET, Customer::ID_CBS_CYFROWE] as $id) {
            $this->addSql('DELETE FROM cameranu.webusers_order_import WHERE user_id = :userId', ['userId' => $id]);
        }
    }
}
