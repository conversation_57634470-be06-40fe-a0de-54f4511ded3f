<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240415095533 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4420 add table for calculated invoice prices';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `calculated_purchase_prices` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `product_id` int(11) NOT NULL,
          `supplier_id` int(11) NOT NULL,
          `elp_price` decimal(10,2) NOT NULL DEFAULT 0.00,
          `original_price` decimal(10,2) NOT NULL DEFAULT 0.00,
          `direct_conditions` decimal(10,2) NOT NULL DEFAULT 0.00,
          `afterwards_conditions` decimal(10,2) NOT NULL DEFAULT 0.00,
          `sell_in_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
          `sell_out_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
          `marketing_conditions` decimal(10,2) NOT NULL DEFAULT 0.00,
          `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
          `invoice_price` decimal(10,2) NOT NULL DEFAULT 0.00,
          `fl1_price` decimal(10,2) NOT NULL DEFAULT 0.00,
          `updated` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `product_id` (`product_id`),
          KEY `supplier_id` (`supplier_id`),
          UNIQUE KEY `product_supplier` (`product_id`,`supplier_id`),
          CONSTRAINT `calculated_purchase_prices_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `artikelen` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
          CONSTRAINT `calculated_purchase_prices_ibfk_2` FOREIGN KEY (`supplier_id`) REFERENCES `leveranciers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ');
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('calculated_purchase_prices');
    }
}
