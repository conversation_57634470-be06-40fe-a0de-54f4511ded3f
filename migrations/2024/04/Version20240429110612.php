<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240429110612 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4546 - Index toevoegen aan second hand product state tabel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("
            ALTER TABLE `cameranu`.`second_hand_product_state`
                ADD INDEX `second_hand_product_state_product_id` (`product_id`)
        ");
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`second_hand_product_state` DROP INDEX `second_hand_product_state_product_id`');
    }
}
