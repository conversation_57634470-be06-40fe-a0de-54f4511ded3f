<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\InstantQuote\QuoteStep;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240419124615 extends AbstractMigration
{
    private const MAIN_GROUP_CAMERA_ID = 17119;
    private const MAIN_GROUP_VIDEO_ID = 17122;
    private const MAIN_GROUP_LENS_ID = 17125;
    private const STATE_10_ID = 3;
    private const STATE_9_ID = 6;
    private const STATE_8_ID = 9;
    private const STATE_7_ID = 12;
    private const STATE_6_ID = 15;

    public function getDescription(): string
    {
        return 'CAM-2945 - Instant Quotes';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            "
            create table if not exists `cameranu`.`quotes` (
                id                 int unsigned primary key not null auto_increment,
                created            datetime not null default current_timestamp(),
                deleted            datetime null,
                status             enum ('open', 'sent', 'cancelled', 'done') default 'open',
                customer_id        int null,
                intake_location_id int null,
                payout_choice      enum ('debit', 'coupon', 'cashback') null,
                debit_name         varchar(255) null,
                debit_iban         varchar(255) null,
                coupon_stock_id    int null,
                cashback_order_id  int null,
                order_id           int null,
                index `quotes_created` (created),
                index `quotes_deleted` (deleted),
                index `quotes_status` (status),
                index `quotes_customer_id` (customer_id),
                index `quotes_intake_location_id` (intake_location_id),
                index `quotes_coupon_stock_id` (coupon_stock_id),
                index `quotes_cashback_order_id` (cashback_order_id),
                index `quotes_order_id` (order_id),
                constraint quotes_customer_id_fk
                    foreign key (customer_id) references `cameranu`.`webusers` (id),
                constraint quotes_intake_location_id_fk
                    foreign key (intake_location_id) references `cameranu`.`stock_locations` (id),
                constraint quotes_coupon_stock_id_fk
                    foreign key (coupon_stock_id) references `cameranu`.`voorraad` (id),
                constraint quotes_cashback_order_id_fk
                    foreign key (cashback_order_id) references `cameranu`.`bestelling_naw` (id),
                constraint quotes_order_id_fk
                    foreign key (order_id) references `cameranu`.`bestelling_naw` (id)
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        "
        );

        $this->addSql(
            "
            create table if not exists `cameranu`.`quote_steps` (
                id                int unsigned primary key not null auto_increment,
                step              varchar(255) not null,
                show_on_status    json null
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        "
        );

        $this->addSql(
            "
            create table if not exists `cameranu`.`quote_steps_timeline` (
                id                int unsigned primary key not null auto_increment,
                added             datetime not null default current_timestamp(),
                added_by          int not null,
                quote_id          int unsigned not null,
                step_id           int unsigned not null,
                index `quote_steps_timeline_added` (added),
                index `quote_steps_timeline_added_by` (added_by),
                index `quote_steps_timeline_quote_id` (quote_id),
                index `quote_steps_timeline_step_id` (step_id),
                constraint quote_steps_timeline_added_by_fk
                    foreign key (added_by) references `cameranu`.`users` (id),
                constraint quote_steps_timeline_quote_id_fk
                    foreign key (quote_id) references `cameranu`.`quotes` (id),
                constraint quote_steps_timeline_step_id_fk
                    foreign key (step_id) references `cameranu`.`quote_steps` (id)
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        "
        );

        $this->addSql(
            "
            create table if not exists `cameranu`.`quote_products` (
                id                   int unsigned primary key not null auto_increment,
                created              datetime not null default current_timestamp(),
                deleted              datetime null,
                status               enum ('receive', 'judge', 'return', 'recycle', 'done') default 'receive',
                quote_id             int unsigned not null,
                main_group_id        int unsigned not null,
                product_id           int not null,
                product_name         varchar(255) null,
                grade_customer_id    int unsigned null,
                grade_definitive_id  int unsigned null,
                offer_customer       float null,
                offer_definitive     float null,
                questions            json null,
                grade_explanation    text null,
                serial_number        varchar(255) null,
                index `quote_products_created` (created),
                index `quote_products_deleted` (deleted),
                index `quote_products_status` (status),
                index `quote_products_quote_id` (quote_id),
                index `quote_products_main_group_id` (main_group_id),
                index `quote_products_product_id` (product_id),
                constraint quote_products_quote_id_fk
                    foreign key (quote_id) references `cameranu`.`quotes` (id),
                constraint quote_products_product_id_fk
                    foreign key (product_id) references `cameranu`.`artikelen` (id),
                constraint quote_products_grade_customer_id_fk
                    foreign key (grade_customer_id) references `cameranu`.`second_hand_state` (id),
                constraint quote_products_grade_definitive_id_fk
                    foreign key (grade_definitive_id) references `cameranu`.`second_hand_state` (id)
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        "
        );

        $this->addSql("
            create table if not exists `cameranu`.`quote_product_requests` (
                id                int unsigned primary key not null auto_increment,
                created           datetime not null default current_timestamp(),
                deleted           datetime null,
                quote_id          int unsigned not null,
                main_group_id     int unsigned not null,
                brand             text not null,
                model             text not null,
                questions         json null,
                index `quote_product_requests_created` (created),
                index `quote_product_requests_deleted` (deleted),
                index `quote_product_requests_quote_id` (quote_id),
                index `quote_product_requests_main_group_id` (main_group_id),
                index `quote_product_requests_brand` (brand(768)),
                index `quote_product_requests_model` (model(768)),
                constraint quote_product_requests_quote_id_fk
                    foreign key (quote_id) references `cameranu`.`quotes` (id)
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        ");

        $this->addSql(
            "
            create table if not exists `cameranu`.`quote_questions` (
                id                int unsigned primary key not null auto_increment,
                created           datetime not null default current_timestamp(),
                updated           datetime not null default current_timestamp() on update current_timestamp(),
                main_group_id     int unsigned not null,
                type              enum ('open', 'checkbox', 'radio') not null,
                show_to_customer  tinyint not null,
                question_title    varchar(255) null,
                question_text     text not null,
                sorting           int not null default 0,
                index `quote_questions_created` (created),
                index `quote_questions_updated` (updated),
                index `quote_questions_main_group_id` (main_group_id)
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        "
        );

        $this->addSql(
            "
            create table if not exists `cameranu`.`quote_question_options` (
                id                  int unsigned primary key not null auto_increment,
                created             datetime not null default current_timestamp(),
                deleted             datetime null,
                question_id         int unsigned not null,
                grade_id            int unsigned null,
                option_text         text not null,
                option_description  varchar(255) null,
                sorting             int not null default 0,
                index `quote_question_options_created` (created),
                index `quote_question_options_deleted` (deleted),
                index `quote_question_options_question_id` (question_id),
                constraint quote_question_options_question_id_fk
                    foreign key (question_id) references `cameranu`.`quote_questions` (id),
                constraint quote_question_options_grade_id_fk
                    foreign key (grade_id) references `cameranu`.`second_hand_state` (id)
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        "
        );

        $this->addSql('drop table if exists `cameranu`.`second_hand_temp_shopcart`');

        // Change specs profile accessories to main group accessories
        $this->addSql('TRUNCATE TABLE `cameranu`.`specs_profiles_accessories`');
        $this->addSql('ALTER TABLE `cameranu`.`specs_profiles_accessories` RENAME `cameranu`.`main_group_accessories`');
        $this->addSql('ALTER TABLE `cameranu`.`main_group_accessories` ADD COLUMN IF NOT EXISTS `main_group_id` int unsigned not null AFTER `id`');
        $this->addSql('ALTER TABLE `cameranu`.`main_group_accessories` DROP FOREIGN KEY IF EXISTS `main_group_accessories_ibfk_1`');
        $this->addSql('ALTER TABLE `cameranu`.`main_group_accessories` DROP INDEX IF EXISTS `specs_profile_id`');
        $this->addSql('ALTER TABLE `cameranu`.`main_group_accessories` DROP COLUMN IF EXISTS `specs_profile_id`');

        $this->addSql('ALTER TABLE `cameranu`.`specs_profiles` DROP COLUMN IF EXISTS `accessories_searchable`');

        /**
         * Insert or update default data
         */
        $this->addSql('UPDATE `cameranu`.`second_hand_state` SET `description` = "Als nieuw" WHERE `quality` = 10');
        $this->addSql('UPDATE `cameranu`.`second_hand_state` SET `description` = "Zo goed als nieuw" WHERE `quality` = 9');
        $this->addSql('UPDATE `cameranu`.`second_hand_state` SET `description` = "Nauwelijks gebruikssporen" WHERE `quality` = 8');
        $this->addSql('UPDATE `cameranu`.`second_hand_state` SET `description` = "Lichte gebruikssporen" WHERE `quality` = 7');
        $this->addSql('UPDATE `cameranu`.`second_hand_state` SET `description` = "Duidelijke gebruikssporen" WHERE `quality` = 6');

        $this->addSql('
            INSERT INTO `cameranu`.`quote_steps` (`id`, `step`, `show_on_status`) VALUES
            (' . QuoteStep::ID_QUOTE_ADDED . ', "Offerte aangemaakt", "[\"all\"]"),
            (' . QuoteStep::ID_PRODUCTS_RECEIVED . ', "Producten ontvangen", "[\"all\"]"),
            (' . QuoteStep::ID_PRODUCTS_JUDGED . ', "Producten beoordelen & definitieve offerte uitsturen", "[\"open\", \"sent\", \"done\"]"),
            (' . QuoteStep::ID_CUSTOMER_AGREES . ', "Klant akkoord", "[\"open\", \"sent\", \"done\"]"),
            (' . QuoteStep::ID_QUOTE_PAYMENT_DONE . ', "Uitbetaling gedaan", "[\"open\", \"sent\", \"done\"]"),
            (' . QuoteStep::ID_QUOTE_DONE . ', "Offerte afgerond", "[\"open\", \"sent\", \"done\"]"),
            (' . QuoteStep::ID_QUOTE_CANCELLED . ', "Offerte geannuleerd", "[\"cancelled\"]"),
            (' . QuoteStep::ID_PRODUCTS_RETURNED . ', "Producten geretourneerd", "[\"cancelled\", \"sent\", \"done\"]");
        ');

        /**
         * Add text block for second hand products in subgroups
         */
        $this->addSql('
            create table if not exists `cameranu`.`second_hand_state_content` (
                id                  int unsigned primary key not null auto_increment,
                main_group_id       int unsigned null,
                state_id            int unsigned not null,
                teaser              text null,
                short_description   text null,
                accessories         varchar(255) null,
                index `quote_question_options_main_group_id` (main_group_id),
                index `quote_question_options_state_id` (state_id),
                constraint quote_question_options_state_id_fk
                    foreign key (state_id) references `cameranu`.`second_hand_state` (id)
            ) engine=InnoDB charset=utf8mb4 collate=utf8mb4_unicode_ci;
        ');

        // Default text block
        $this->addSql('
            INSERT INTO `cameranu`.`second_hand_state_content`
                (`main_group_id`, `state_id`, `teaser`, `short_description`, `accessories`)
            VALUES
                (
                    null,
                    ' . self::STATE_10_ID . ',
                    "[::product::] in nieuwstaat. Geen gebruikssporen en technisch geheel in orde.",
                    "Tweedehands [::product::] in nieuwstaat - technisch geheel in orde",
                    null
                ),
                (
                    null,
                    ' . self::STATE_9_ID . ',
                    "[::product::] in uitstekende staat. Vrijwel geen gebruikssporen en technisch geheel in orde.",
                    "Tweedehands [::product::] in uitstekende staat - technisch geheel in orde",
                    null
                ),
                (
                    null,
                    ' . self::STATE_8_ID . ',
                    "[::product::] in zeer nette staat. Nauwelijks gebruikssporen en technisch geheel in orde.",
                    "Tweedehands [::product::] in zeer nette staat – nauwelijks gebruikssporen - technisch geheel in orde",
                    null
                ),
                (
                    null,
                    ' . self::STATE_7_ID . ',
                    "[::product::] met lichte gebruikssporen. Technisch geheel in orde.",
                    "Tweedehands [::product::] met lichte gebruikssporen - technisch geheel in orde",
                    null
                ),
                (
                    null,
                    ' . self::STATE_6_ID . ',
                    "[::product::] met duidelijke gebruikssporen. Technisch geheel in orde.",
                    "Tweedehands [::product::] met duidelijke gebruikssporen - technisch geheel in orde",
                    null
                )
        ');

        // Group - "Camera"
        $this->addSql('
            INSERT INTO `cameranu`.`second_hand_state_content`
                (`main_group_id`, `state_id`, `teaser`, `short_description`, `accessories`)
            VALUES
                (
                    ' . self::MAIN_GROUP_CAMERA_ID . ',
                    ' . self::STATE_10_ID . ',
                    "Camera in nieuwstaat. Geen gebruikssporen en technisch geheel in orde. Met de camera zijn minder dan 5000 foto\'s gemaakt. Inclusief minimaal een bodydop, accu, lader en draagriem.",
                    "Tweedehands camera in nieuwstaat - geen gebruikssporen - technisch geheel in orde",
                    "Bodydop, accu, lader en draagriem"
                ),
                (
                    ' . self::MAIN_GROUP_CAMERA_ID . ',
                    ' . self::STATE_9_ID . ',
                    "Camera in uitstekende staat. Vrijwel geen gebruikssporen en technisch geheel in orde. Met de camera zijn minder dan 25.000 foto\'s gemaakt. Inclusief minimaal een bodydop, accu, lader en draagriem.",
                    "Tweedehands camera in uitstekende staat - vrijwel geen gebruikssporen - technisch geheel in orde",
                    "Bodydop, accu, lader en draagriem"
                ),
                (
                    ' . self::MAIN_GROUP_CAMERA_ID . ',
                    ' . self::STATE_8_ID . ',
                    "Camera in zeer nette staat. Nauwelijks gebruikssporen en technisch geheel in orde. Met de camera zijn minder dan 75.000 foto\'s gemaakt. Inclusief minimaal een bodydop, accu, lader en draagriem.",
                    "Tweedehands camera in zeer nette staat - nauwelijks gebruikssporen - technisch geheel in orde",
                    "Bodydop, accu, lader en draagriem"
                ),
                (
                    ' . self::MAIN_GROUP_CAMERA_ID . ',
                    ' . self::STATE_7_ID . ',
                    "Camera met lichte gebruikssporen. Technisch geheel in orde. Met de camera zijn minder dan 150.000 foto\'s gemaakt. Inclusief minimaal een bodydop, accu, lader, draagriem.",
                    "Tweedehands camera met lichte gebruikssporen - technisch geheel in orde",
                    "Bodydop, accu, lader en draagriem"
                ),
                (
                    ' . self::MAIN_GROUP_CAMERA_ID . ',
                    ' . self::STATE_6_ID . ',
                    "Camera met duidelijke gebruikssporen. Technisch geheel in orde. Met de camera zijn minder dan 250.000 foto\'s gemaakt. Inclusief minimaal een bodydop, accu, lader en draagriem.",
                    "Tweedehands camera met duidelijke gebruikssporen - technisch geheel in orde",
                    "Bodydop, accu, lader en draagriem"
                )
        ');

        // Group - "Video"
        $this->addSql('
            INSERT INTO `cameranu`.`second_hand_state_content`
                (`main_group_id`, `state_id`, `teaser`, `short_description`, `accessories`)
            VALUES
                (
                    ' . self::MAIN_GROUP_VIDEO_ID . ',
                    ' . self::STATE_10_ID . ',
                    "Videocamera in nieuwstaat. Geen gebruikssporen en technisch geheel in orde. Inclusief minimaal de accu, lader en doppen (indien van toepassing).",
                    "Tweedehands videocamera in nieuwstaat - geen gebruikssporen - technisch geheel in orde",
                    "Accu, lader en doppen (indien van toepassing)"
                ),
                (
                    ' . self::MAIN_GROUP_VIDEO_ID . ',
                    ' . self::STATE_9_ID . ',
                    "Videocamera in uitstekende staat. Vrijwel geen gebruikssporen en technisch geheel in orde. Inclusief minimaal de accu, lader en doppen (indien van toepassing).",
                    "Tweedehands videocamera in uitstekende staat - vrijwel geen gebruikssporen - technisch geheel in orde",
                    "Accu, lader en doppen (indien van toepassing)"
                ),
                (
                    ' . self::MAIN_GROUP_VIDEO_ID . ',
                    ' . self::STATE_8_ID . ',
                    "Videocamera in zeer nette staat. Nauwelijks gebruikssporen en technisch geheel in orde. Inclusief minimaal de accu, lader en doppen (indien van toepassing).",
                    "Tweedehands videocamera in zeer nette staat - nauwelijks gebruikssporen - technisch geheel in orde",
                    "Accu, lader en doppen (indien van toepassing)"
                ),
                (
                    ' . self::MAIN_GROUP_VIDEO_ID . ',
                    ' . self::STATE_7_ID . ',
                    "Videocamera met lichte gebruikssporen. Technisch geheel in orde. Inclusief minimaal de accu, lader en doppen (indien van toepassing).",
                    "Tweedehands videocamera met lichte gebruikssporen - technisch geheel in orde",
                    "Accu, lader en doppen (indien van toepassing)"
                ),
                (
                    ' . self::MAIN_GROUP_VIDEO_ID . ',
                    ' . self::STATE_6_ID . ',
                    "Videocamera met duidelijke gebruikssporen. Technisch geheel in orde. Inclusief minimaal de accu, lader en doppen (indien van toepassing).",
                    "Tweedehands videocamera met duidelijke gebruikssporen - technisch geheel in orde",
                    "Accu, lader en doppen (indien van toepassing)"
                )
        ');

        // Group - "Lens"
        $this->addSql('
            INSERT INTO `cameranu`.`second_hand_state_content`
                (`main_group_id`, `state_id`, `teaser`, `short_description`, `accessories`)
            VALUES
                (
                    ' . self::MAIN_GROUP_LENS_ID . ',
                    ' . self::STATE_10_ID . ',
                    "Objectief in nieuwstaat. Geen gebruikssporen en optisch geheel in orde. Inclusief minimaal lensdoppen en de zonnekap (indien deze er nieuw ook bij zit).",
                    "Tweedehands objectief in nieuwstaat - geen gebruikssporen - optisch geheel in orde",
                    "Lensdoppen en de zonnekap (indien deze er nieuw ook bij zit)"
                ),
                (
                    ' . self::MAIN_GROUP_LENS_ID . ',
                    ' . self::STATE_9_ID . ',
                    "Objectief in uitstekende staat. Vrijwel geen gebruikssporen en optisch geheel in orde. Inclusief minimaal lensdoppen en de zonnekap (indien deze er nieuw ook bij zit).",
                    "Tweedehands objectief in uitstekende staat - vrijwel geen gebruikssporen - optisch geheel in orde",
                    "Lensdoppen en de zonnekap (indien deze er nieuw ook bij zit)"
                ),
                (
                    ' . self::MAIN_GROUP_LENS_ID . ',
                    ' . self::STATE_8_ID . ',
                    "Objectief in zeer nette staat. Nauwelijks gebruikssporen en optisch geheel in orde. Inclusief minimaal lensdoppen en de zonnekap (indien deze er nieuw ook bij zit).",
                    "Tweedehands objectief in zeer nette staat - nauwelijks gebruikssporen - optisch geheel in orde",
                    "Lensdoppen en de zonnekap (indien deze er nieuw ook bij zit)"
                ),
                (
                    ' . self::MAIN_GROUP_LENS_ID . ',
                    ' . self::STATE_7_ID . ',
                    "Objectief met lichte gebruikssporen. Optisch geheel in orde. Inclusief minimaal lensdoppen en de zonnekap (indien deze er nieuw ook bij zit).",
                    "Tweedehands objectief met lichte gebruikssporen - optisch geheel in orde",
                    "Lensdoppen en de zonnekap (indien deze er nieuw ook bij zit)"
                ),
                (
                    ' . self::MAIN_GROUP_LENS_ID . ',
                    ' . self::STATE_6_ID . ',
                    "Objectief met duidelijke gebruikssporen. Optisch geheel in orde. Inclusief minimaal lensdoppen en de zonnekap (indien deze er nieuw ook bij zit).",
                    "Tweedehands objectief met duidelijke gebruikssporen - optisch geheel in orde",
                    "Lensdoppen en de zonnekap (indien deze er nieuw ook bij zit)"
                )
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('drop table if exists `cameranu`.`quote_question_options`');
        $this->addSql('drop table if exists `cameranu`.`quote_questions`');
        $this->addSql('drop table if exists `cameranu`.`quote_product_requests`');
        $this->addSql('drop table if exists `cameranu`.`quote_products`');
        $this->addSql('drop table if exists `cameranu`.`quote_steps_timeline`');
        $this->addSql('drop table if exists `cameranu`.`quote_steps`');
        $this->addSql('drop table if exists `cameranu`.`quotes`');

        $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`second_hand_temp_shopcart` (
            `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
            `session_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `stock_id` int(11) DEFAULT NULL,
            `info` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `user_id` int(11) DEFAULT NULL,
            `type` enum(\'exchange\', \'buy\') COLLATE utf8mb4_unicode_ci NOT NULL,
            `price` decimal(12,3) DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');

        // Change back to specs profile accessories
        $this->addSql('ALTER TABLE `cameranu`.`main_group_accessories` RENAME `cameranu`.`specs_profiles_accessories`');

        $this->addSql('TRUNCATE TABLE `cameranu`.`main_group_accessories`');
        $this->addSql('ALTER TABLE `cameranu`.`main_group_accessories` RENAME `cameranu`.`specs_profiles_accessories`');
        $this->addSql('ALTER TABLE `cameranu`.`specs_profiles_accessories` ADD COLUMN IF NOT EXISTS `specs_profile_id` int unsigned not null AFTER `id`');
        $this->addSql('ALTER TABLE `cameranu`.`specs_profiles_accessories` DROP FOREIGN KEY IF EXISTS `specs_profile_accessories_ibfk_1`');
        $this->addSql('ALTER TABLE `cameranu`.`specs_profiles_accessories` DROP INDEX IF EXISTS `main_group_id`');
        $this->addSql('ALTER TABLE `cameranu`.`specs_profiles_accessories` DROP COLUMN IF EXISTS `main_group_id`');

        $this->addSql('ALTER TABLE `cameranu`.`specs_profiles` ADD COLUMN IF NOT EXISTS `accessories_searchable` int(1) not null');
    }
}
