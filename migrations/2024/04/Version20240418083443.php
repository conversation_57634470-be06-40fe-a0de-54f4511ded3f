<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240418083443 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3874 - tokens bij klanten om orders te importeren';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('cameranu.webusers_order_import');
        $table->addColumn('id', 'integer', [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true,
        ]);
        $table->addColumn('user_id', 'integer', [
            'notnull' => true,
        ]);
        $table->addColumn('token', 'string', [
            'length' => 255,
            'notnull' => true,
        ]);
        $table->addForeignKeyConstraint(
            'cameranu.webusers', ['user_id'], ['id'], [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ]
        );
        $table->setPrimaryKey(['id']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('cameranu.webusers_order_import');
    }
}
