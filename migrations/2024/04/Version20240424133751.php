<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Constant\ShippingMethod;
use CatBundle\Service\ProductGroupTypes;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ArticleGroupType;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240424133751 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4510 set default delivery_flags for 2nd hand products';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
             UPDATE
                `cameranu`.`artikelen`
             SET flags_verzend = ' . ShippingMethod::DEFAULT_FLAGS . '
             WHERE `id` IN (
                SELECT
                    artikelId
                FROM `cameranu`.`artikelgroepen_artikelen`
                INNER JOIN `cameranu`.artikelgroepen on artikelgroepen_artikelen.artikelgroepId = artikelgroepen.id
                INNER JOIN `artikelen` on artikelen.id = artikelId
                WHERE `article_group_type_id` = ' . ArticleGroupType::GROUP_TYPE_SECOND_HAND . '
            );
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
             UPDATE
                `cameranu`.`artikelen`
             SET flags_verzend = 0
             WHERE `id` IN (
                SELECT
                    artikelId
                FROM  `cameranu`.`artikelgroepen_artikelen`
                INNER JOIN `cameranu`.artikelgroepen on artikelgroepen_artikelen.artikelgroepId = artikelgroepen.id
                INNER JOIN `artikelen` on artikelen.id = artikelId
                WHERE `article_group_type_id` = ' . ArticleGroupType::GROUP_TYPE_SECOND_HAND . '
            );
        ');
    }
}
