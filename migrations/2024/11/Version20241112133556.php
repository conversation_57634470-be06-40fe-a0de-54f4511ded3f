<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241112133556 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5573 microtime toevoegen aan order_analytics_info tabel';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable('order_analytics_info')->addColumn('order_time_micro', Types::BIGINT, ['notnull' => false]);
    }

    public function down(Schema $schema): void
    {
        $schema->getTable('order_analytics_info')->dropColumn('order_time_micro');
    }
}
