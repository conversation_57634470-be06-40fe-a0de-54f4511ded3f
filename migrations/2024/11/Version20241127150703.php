<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241127150703 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5693 change events key in productfeed_fields for Adchieve';
    }

    public function up(Schema $schema): void
    {
         $this->addSql('UPDATE `cameranu`.`productfeed_fields` set `key` = \'events_plain\' WHERE `id` = 760');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE `cameranu`.`productfeed_fields` set `key` = \'events\' WHERE `id` = 760');
    }
}
