<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241112095748 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5466 Add title and subtitle columns to discountset_promotions';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.discountset_promotions');

        $table->addColumn('title', Types::STRING)
            ->setNotnull(false);
        $table->addColumn('subtitle', Types::STRING)
            ->setNotnull(false);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.discountset_promotions');

        $table->dropColumn('title');
        $table->dropColumn('subtitle');
    }
}
