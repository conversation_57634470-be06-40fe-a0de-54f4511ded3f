<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241104090738 extends AbstractMigration
{
    private const TABLE_BESTELLING_NAW = 'bestelling_naw';
    private const COLUMN_DOMAIN_ORIGIN = 'domain_origin';

    public function getDescription(): string
    {
        return 'CAM-5566 - Domein opslaan bij orders';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE_BESTELLING_NAW)
            ->addColumn(self::COLUMN_DOMAIN_ORIGIN,
                'string',
                [
                    'length' => 255,
                    'notnull' => false,
                ]);
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE_BESTELLING_NAW)
            ->dropColumn(self::COLUMN_DOMAIN_ORIGIN);
    }
}
