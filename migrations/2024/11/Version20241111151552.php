<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\StockLogAction;

final class Version20241111151552 extends AbstractMigration
{
    private const ACTION_TITLE = 'Secondhand artikel koppeling gewijzigd';

    public function getDescription(): string
    {
        return 'CAM-5321 Nieuw voorraad log type';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO cameranu.stock_log_action VALUES (:id, :title)', [
            'id' => StockLogAction::SECONDHAND_MOVE_STOCK_TO_OTHER_PRODUCT,
            'title' => self::ACTION_TITLE,
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM cameranu.stock_log_action WHERE id = :id', [
            'id' => StockLogAction::SECONDHAND_MOVE_STOCK_TO_OTHER_PRODUCT,
        ]);
    }
}
