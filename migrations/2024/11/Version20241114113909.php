<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241114113909 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5615 add identification_type to quotes and bestelling_occasion table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'ALTER TABLE `cameranu`.`quotes` ADD COLUMN `identification_type` enum(\'no_id\',\'nl_id\',\'foreign_id\',\'nl_passport\',\'foreign_passport\',\'nl_drivers_license\',\'foreign_drivers_license\',\'green_card\') NOT NULL DEFAULT \'no_id\''
        );

        $this->addSql(
            'ALTER TABLE `cameranu`.`bestelling_occasion` ADD COLUMN `identification_type` enum(\'no_id\',\'nl_id\',\'foreign_id\',\'nl_passport\',\'foreign_passport\',\'nl_drivers_license\',\'foreign_drivers_license\',\'green_card\') NOT NULL DEFAULT \'no_id\''
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('quotes');
        $table->dropColumn('identification_type');

        $table = $schema->getTable('bestelling_occasion');
        $table->dropColumn('identification_type');
    }
}
