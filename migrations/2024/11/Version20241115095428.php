<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241115095428 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5457 add events to Adchieve feed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES (\'events\', ' . ProductFeed::ADCHIEVE . ', \'events\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `productfeed_fields` WHERE `key` = \'events\' AND `feed_id` = ' . ProductFeed::ADCHIEVE);
    }
}
