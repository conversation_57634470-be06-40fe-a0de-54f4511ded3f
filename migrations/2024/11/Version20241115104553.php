<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241115104553 extends AbstractMigration
{
    private const string TABLE_NAME_MENUI18N = 'menuI18n';
    private const string COLUMN_NAME_DROPDOWN_NAME = 'dropdown_name';

    public function getDescription(): string
    {
        return 'CAM-5410 - Extra kolom voor menu-items';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_MENUI18N)
            ->addColumn(
                self::COLUMN_NAME_DROPDOWN_NAME,
                'string',
                [
                    'notnull' => false,
                    'default' => null,
                    'length' => 255,
                ],
            );
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_MENUI18N)
            ->dropColumn(self::COLUMN_NAME_DROPDOWN_NAME);
    }
}
