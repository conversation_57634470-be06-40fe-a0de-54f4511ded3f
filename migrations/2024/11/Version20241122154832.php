<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241122154832 extends AbstractMigration
{
    private const string TABLE_NAME_ARTIKELEN = 'artikelen';
    private const string COLUMN_NAME_CBS_AVAILABLE = 'cbs_available';

    public function getDescription(): string
    {
        return 'CAM-5665 - CBS optie standaard aan - migration';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_ARTIKELEN)
            ->getColumn(self::COLUMN_NAME_CBS_AVAILABLE)
            ->setOptions([
                'default' => true
            ]);
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_ARTIKELEN)
            ->getColumn(self::COLUMN_NAME_CBS_AVAILABLE)
            ->setOptions([
                'default' => false
            ]);
    }
}
