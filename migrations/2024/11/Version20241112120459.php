<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241112120459 extends AbstractMigration
{
    private const TABLE_NAME_FRONTPAGE_FOOTERAWARD = 'frontpage_footerAwards';

    public function getDescription(): string
    {
        return 'CAM-3883 - Awards footer in frontpage tool';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `'. self::TABLE_NAME_FRONTPAGE_FOOTERAWARD .'` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(255) NOT NULL,
                `sub_title` varchar(255),
                `sub_title_mobile` varchar(255),
                `url` varchar(255),
                `image` varchar(255),
                `alt` varchar(255),
                `created` datetime DEFAULT current_timestamp(),
                `updated` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                `createdBy` int(11),
                `updatedBy` int(11),
                `domain` enum(\'cameranu.nl\',\'cameranu.be\') DEFAULT NULL,
                `position` int(11),
                 PRIMARY KEY (`id`)
            )    ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE_NAME_FRONTPAGE_FOOTERAWARD);
    }
}
