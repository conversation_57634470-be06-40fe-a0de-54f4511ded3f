<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241111132434 extends AbstractMigration
{
    private const TABLE_NAME_PRODUCT_IMAGES = 'artikelen_images';
    private const COLUMN_NAME_ALT = 'alt';

    public function getDescription(): string
    {
        return 'CAM-5600 - Alt tag toevoegen aan product afbeeldingen';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_PRODUCT_IMAGES)
            ->addColumn(self::COLUMN_NAME_ALT, 'text', ['length' => 255, 'notnull' => false]);

    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_PRODUCT_IMAGES)->dropColumn(self::COLUMN_NAME_ALT);
    }
}
