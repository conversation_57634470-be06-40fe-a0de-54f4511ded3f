<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241129084300 extends AbstractMigration
{
    private const string TABLE_NAME_EVENTS_PRODUCTS = 'event_products';

    public function getDescription(): string
    {
        return 'CAM-4006 - Events - Prullenbak';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_EVENTS_PRODUCTS)
            ->addColumn('softdelete',
                Types::BOOLEAN,
                [
                    'default' => false,
                ]
            );
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_EVENTS_PRODUCTS)
            ->dropColumn('softdelete');
    }
}
