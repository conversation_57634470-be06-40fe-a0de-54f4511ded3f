<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241111130858 extends AbstractMigration
{
    private const string TABLE_QUOTE_PRODUCTS = 'quote_products';
    private const string COLUMN_STOPHELING_HAS_HIT = 'stopheling_has_hit';

    public function getDescription(): string
    {
        return 'CAM-5584 Add HasHit column to quote_products table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_QUOTE_PRODUCTS);
        $table->addColumn(
            self::COLUMN_STOPHELING_HAS_HIT,
            Types::BOOLEAN,
            ['notnull' => false]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_QUOTE_PRODUCTS);
        $table->dropColumn(self::COLUMN_STOPHELING_HAS_HIT);
    }
}
