<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\StockLocation;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241125134601 extends AbstractMigration
{
    private const array INTERNAL_INVOICE_INFO = [
        StockLocation::UTR_STORE => [
            'footerInformation' => 'Levering geschiedt onder eigendomsvoorbehoud tot het volledige factuurbedrag op onze rekening is ontvangen',
            'sepaName' => 'CameraNU.nl Utrecht',
            'sepaIban' => '******************',
            'sepaBic' => 'ABNANL2A',
            'sepaId' => '*******************',
            'email' => '<EMAIL>',
            'telephone' => '0527-690404',
            'address' => 'Parijsboulevard 201',
            'postcode' => '3541CS',
            'city' => 'Utrecht',
            'coc' => '39083060',
            'vatNumber' => 'NL859932874B01',
        ],
    ];

    public function getDescription(): string
    {
        return 'CAM-5648 Add Utrecht to internal invoice information';
    }

    public function up(Schema $schema): void
    {
        foreach (self::INTERNAL_INVOICE_INFO as $stockLocationId => $info) {
            $this->addSql('
                INSERT INTO `cameranu`.`internal_invoice_stock_location_information` (
                    `stockLocationId`,
                    `footerInformation`,
                    `sepaName`,
                    `sepaIban`,
                    `sepaBic`,
                    `sepaId`,
                    `email`,
                    `telephone`,
                    `address`,
                    `postcode`,
                    `city`,
                    `coc`,
                    `vatNumber`
                ) VALUES (
                    ' . $stockLocationId . ',
                    "' . $info['footerInformation'] . '",
                    "' . $info['sepaName'] . '",
                    "' . $info['sepaIban'] . '",
                    "' . $info['sepaBic'] . '",
                    "' . $info['sepaId'] . '",
                    "' . $info['email'] . '",
                    "' . $info['telephone'] . '",
                    "' . $info['address'] . '",
                    "' . $info['postcode'] . '",
                    "' . $info['city'] . '",
                    "' . $info['coc'] . '",
                    "' . $info['vatNumber'] . '"
                )
            ');
        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::INTERNAL_INVOICE_INFO as $stockLocationId => $info) {
            $this->addSql('DELETE FROM `cameranu`.`internal_invoice_stock_location_information` WHERE `stockLocationId` = ' . $stockLocationId);
        }
    }
}
