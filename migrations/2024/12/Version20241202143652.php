<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241202143652 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5728 - Klantenservice Link blok toevoegen';
    }

    public function up(Schema $schema): void
    {
        $timestamp = time();
        $this->addSql(
            'INSERT INTO `cameranu`.`pages_blocktypes`
            (`created`, `modified`, `type`, `sort`, `category`)
            VALUES (:created, :modified, :type, :sort, :category)',
            [
                'created' => $timestamp,
                'modified' => $timestamp,
                'type' => 'customerServiceLink',
                'sort' => 120,
                'category' => 'Content (Rebranding)'
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            'DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = :type',
            [
                'type' => 'customerServiceLink',
            ]
        );
    }
}
