<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20241218103312 extends AbstractMigration
{
    private const string TABLE_STOCK_SCAN_LIST = 'cameranu.stock_scan_list';
    private const string TABLE_USERS = 'cameranu_urk.users';
    private const string FK_CHECKED_BY = 'fk_checked_by';

    public function getDescription(): string
    {
        return 'CAM-2702 - add extra column to store person who checked a list';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_STOCK_SCAN_LIST);

        $table->addColumn('checked_by', Types::INTEGER, [
            'notnull' => false,
        ]);

        $table->addColumn('checked_at', Types::DATETIME_MUTABLE, [
            'notnull' => false,
        ]);

        $table->addForeignKeyConstraint(
            self::TABLE_USERS,
            ['checked_by'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ],
            self::FK_CHECKED_BY,
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_STOCK_SCAN_LIST);
        $table->dropColumn('checked_by');
        $table->dropColumn('checked_at');
        $table->removeForeignKey(self::FK_CHECKED_BY);
    }
}
