<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241219072438 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5320 add customer type to quotes table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`quotes` ADD COLUMN `customer_type` enum(\'consumer\',\'business\') NOT NULL DEFAULT \'consumer\'');
        $this->addSql('CREATE INDEX idx_customer_type ON `cameranu`.`quotes` (customer_type)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`quotes` DROP COLUMN `customer_type`');
    }
}
