<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241209155915 extends AbstractMigration
{
    const string STOCK_AVAILABILITY_PERCENTAGE_STOCK_TABLE = 'stock_availability_percentage_stock';
    const string STOCK_AVAILABILITY_PERCENTAGE_TABLE = 'stock_availability_percentage';

    public function getDescription(): string
    {
        return 'CAM-5626 - Stock Availability Percentages';
    }

    public function up(Schema $schema): void
    {
        $stockAvailabilityPercentageStock = $schema->createTable(self::STOCK_AVAILABILITY_PERCENTAGE_STOCK_TABLE);

        $stockAvailabilityPercentageStock->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
        ]);

        $stockAvailabilityPercentageStock->addColumn('product_id', Types::INTEGER, [
            'notnull' => true,
        ]);

        $stockAvailabilityPercentageStock->addColumn('stock', Types::INTEGER, [
            'default' => 0,
        ]);

        $stockAvailabilityPercentageStock->addColumn('stock_location_id', Types::INTEGER, [
           'notnull' =>  true,
        ]);

        $stockAvailabilityPercentageStock->addColumn('date_created', Types::DATETIME_MUTABLE, [
            'notnull' => true,
            'default' => 'CURRENT_TIMESTAMP',
        ]);

        $stockAvailabilityPercentageStock->setPrimaryKey(['id']);
        $stockAvailabilityPercentageStock->addIndex([
            'product_id',
            'stock_location_id',
        ]);

        $stockAvailabilityPercentage = $schema->createTable(self::STOCK_AVAILABILITY_PERCENTAGE_TABLE);

        $stockAvailabilityPercentage->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
        ]);

        $stockAvailabilityPercentage->addColumn('product_id', Types::INTEGER, [
            'notnull' => true,
        ]);

        $stockAvailabilityPercentage->addColumn('stock_location_id', Types::INTEGER, [
            'notnull' =>  true,
        ]);

        $stockAvailabilityPercentage->addColumn('availability_percentage', Types::INTEGER, [
            'default' => 0,
        ]);

        $stockAvailabilityPercentage->addColumn('date_calculated', Types::DATETIME_MUTABLE, [
            'notnull' => true,
        ]);

        $stockAvailabilityPercentage->setPrimaryKey(['id']);
        $stockAvailabilityPercentage->addIndex([
            'product_id',
            'stock_location_id',
        ]);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::STOCK_AVAILABILITY_PERCENTAGE_TABLE);
        $schema->dropTable(self::STOCK_AVAILABILITY_PERCENTAGE_STOCK_TABLE);
    }
}
