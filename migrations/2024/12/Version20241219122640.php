<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241219122640 extends AbstractMigration
{
    private const string TABLE_NAME_PICK_LIST_LINK = 'pick_list_link';
    private const string COLOMN_NAME_ORDER_ID = 'order_id';

    public function getDescription(): string
    {
        return 'CAM-5653 - Losse order handmatig als picklijst toevoegen';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_PICK_LIST_LINK)
            ->addColumn(self::COLOMN_NAME_ORDER_ID, 'integer', [
                'notnull' => false,
            ]);
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_PICK_LIST_LINK)->dropColumn(self::COLOMN_NAME_ORDER_ID);
    }
}
