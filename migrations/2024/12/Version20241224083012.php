<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20241224083012 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5836 new table to save afas order info for year exports';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `afas_invoice_info` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `order_id` int(11) DEFAULT NULL,
          `invoice_number` bigint(20) NOT NULL,
          `year` int(4) NOT NULL,
          `origin_id` int(11) DEFAULT NULL,
          `totalInc` decimal(11,3) NOT NULL,
          `timestamp` datetime NOT NULL DEFAULT current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `invoice_number` (`invoice_number`),
          KEY `order_id` (`order_id`),
          KEY `year` (`year`),
          KEY `timestamp` (`timestamp`),
          KEY `origin_id` (`origin_id`),
          CONSTRAINT `afas_invoice_info_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `bestelling_naw` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
          CONSTRAINT `afas_invoice_info_ibfk_2` FOREIGN KEY (`origin_id`) REFERENCES `bestelling_herkomst` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('afas_invoice_info');
    }
}
