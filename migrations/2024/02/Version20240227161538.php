<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\PaymentType;

final class Version20240227161538 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3055 Add Mollie payment type';
    }

    public function up(Schema $schema): void
    {
        $qb = $this->connection->createQueryBuilder();

        $qb
            ->insert('betalingsmogelijkheden')
            ->values([
                'id' => ':id',
                'pos' => ':pos',
                'omschrijving' => ':omschrijving',
                'goedhart' => ':goedhart',
                'flags' => ':flags',
                'betaalwijze_id' => ':betaalwijzeId',
            ])
            ->setParameters([
                'id' => PaymentType::TYPE_MOLLIE,
                'pos' => 100,
                'omschrijving' => 'Mollie',
                'goedhart' => 'mollie',
                'flags' => PaymentType::FLAG_ACTIVE,
                'betaalwijzeId' => PaymentMethod::ID_MOLLIE,
            ])
        ;

        $qb->executeQuery();
    }

    public function down(Schema $schema): void
    {
        $qb = $this->connection->createQueryBuilder();

        $qb
            ->delete('betalingsmogelijkheden')
            ->where('id = :id')
            ->setParameter('id', PaymentType::TYPE_MOLLIE)
        ;

        $qb->executeQuery();
    }
}
