<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240215134454 extends AbstractMigration
{
    private const TRUNKRS_TABLE = 'cameranu.trunkrs';

    private const ORDERS_TABLE = 'cameranu.bestelling_naw';

    private const ORDER_ID_FK = 'FK_bestelling_id_trunkrs';

    public function getDescription(): string
    {
        return 'CAM-3936 (extra-curricular) - Trunkrs FKs';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TRUNKRS_TABLE);
        $table->addForeignKeyConstraint(
            self::ORDERS_TABLE,
            ['bestelling_id'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE'],
            self::ORDER_ID_FK
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TRUNKRS_TABLE);
        $table->removeForeignKey(self::ORDER_ID_FK);
    }
}
