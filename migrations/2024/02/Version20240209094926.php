<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240209094926 extends AbstractMigration
{
    private const PRODUCTS_TABLE = 'artikelen';

    private const RECOMMENDED_ACCESSORIES_TABLE = 'product_recommended_accessories';

    private const RECOMMENDED_PRODUCT_ID_FK = 'FK_recommended_accessories_product_id';
    private const RECOMMENDED_ACCESSORY_PRODUCT_ID_FK = 'FK_recommended_accessories_accessory_product_id';

    public function getDescription(): string
    {
        return 'Add foreign keys to recommended accessories table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::RECOMMENDED_ACCESSORIES_TABLE);
        $table->addForeignKeyConstraint(
            self::PRODUCTS_TABLE,
            ['product_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            self::RECOMMENDED_PRODUCT_ID_FK
        );

        $table->addForeignKeyConstraint(
            self::PRODUCTS_TABLE,
            ['accessory_product_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            self::RECOMMENDED_ACCESSORY_PRODUCT_ID_FK
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::RECOMMENDED_ACCESSORIES_TABLE);
        $table->removeForeignKey(self::RECOMMENDED_PRODUCT_ID_FK);
        $table->removeForeignKey(self::RECOMMENDED_ACCESSORY_PRODUCT_ID_FK);
    }
}
