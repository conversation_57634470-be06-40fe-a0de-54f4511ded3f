<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240227151558 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3061 New table for PSP terminals';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`pin_terminals` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `payment_type_id` int(11) DEFAULT NULL,
              `terminal_id` varchar(255) DEFAULT NULL,
              `psp` enum(\'mollie\',\'adyen\') NOT NULL,
              `enabled` tinyint(1) NOT NULL,
              PRIMARY KEY (`id`),
              KEY `payment_type_id` (`payment_type_id`),
              CONSTRAINT `fk_payment_type` FOREIGN KEY (`payment_type_id`) REFERENCES `betalingsmogelijkheden` (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ');
        $this->addSql('
            INSERT INTO `cameranu`.`pin_terminals` (`payment_type_id`, `terminal_id`, `psp`)
            VALUES
                (194, \'P400Plus-275384861\', \'adyen\'),
                (6, \'e285-401742285\', \'adyen\'),
                (145, \'P400Plus-275383518\', \'adyen\'),
                (132, \'P400Plus-275384778\', \'adyen\'),
                (142, \'P400Plus-275384785\', \'adyen\'),
                (177, \'P400Plus-275384800\', \'adyen\'),
                (179, \'P400Plus-275384875\', \'adyen\'),
                (3, \'P400Plus-275384884\', \'adyen\'),
                (4, \'P400Plus-275384871\', \'adyen\'),
                (431, \'P400Plus-275383262\', \'adyen\'),
                (14, \'P400Plus-275384726\', \'adyen\'),
                (252, \'P400Plus-275384804\', \'adyen\'),
                (7, \'e285-401742178\', \'adyen\'),
                (233, \'P400Plus-803681396\', \'adyen\'),
                (224, \'P400Plus-803681457\', \'adyen\'),
                (225, \'e285p-860028221\', \'adyen\'),
                (245, \'P400Plus-803846081\', \'adyen\'),
                (247, \'P400Plus-803844764\', \'adyen\'),
                (350, \'P400Plus-806184943\', \'adyen\'),
                (351, \'P400Plus-806047481\', \'adyen\'),
                (352, \'P400Plus-275086271\', \'adyen\'),
                (353, \'P400Plus-805318364\', \'adyen\'),
                (354, \'P400Plus-805318121\', \'adyen\'),
                (355, \'P400Plus-805683651\', \'adyen\'),
                (356, \'P400Plus-805683506\', \'adyen\'),
                (357, \'P400Plus-805318063\', \'adyen\'),
                (402, \'P400Plus-805449509\', \'adyen\'),
                (403, \'P400Plus-805449660\', \'adyen\'),
                (412, \'P400Plus-805449674\', \'adyen\'),
                (413, \'P400Plus-805449686\', \'adyen\'),
                (422, \'P400Plus-805450155\', \'adyen\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`pin_terminals`');
    }
}
