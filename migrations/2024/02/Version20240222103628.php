<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240222103628 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3055 Add PSPLog table';
    }

    public function up(Schema $schema): void
    {
        $pspLogTable = $schema->createTable('psp_log');

        $pspLogTable->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
        ]);
        $pspLogTable->setPrimaryKey(['id']);

        $pspLogTable->addColumn('order_id', Types::INTEGER, [
            'unsigned' => false,
            'notnull' => false,
        ]);
        $pspLogTable->addForeignKeyConstraint(
            'bestelling_naw',
            ['order_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );

        $pspLogTable->addColumn('payment_method_id', Types::INTEGER, [
            'unsigned' => false,
            'notnull' => true,
        ]);
        $pspLogTable->addForeignKeyConstraint(
            'betaalwijze1',
            ['payment_method_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );

        $pspLogTable->addColumn('type', Types::STRING, [
            'length' => 255,
            'notnull' => true,
        ]);

        $pspLogTable->addColumn('processed', Types::BOOLEAN, [
            'notnull' => true,
            'default' => 0,
        ]);

        $pspLogTable->addColumn('data', Types::JSON, [
            'notnull' => true,
        ]);

        $pspLogTable->addColumn('hash', Types::STRING, [
            'notnull' => true,
            'length' => 32,
        ]);

        $pspLogTable->addColumn('date', Types::DATETIME_IMMUTABLE, [
            'notnull' => true,
        ]);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('psp_log');
    }
}
