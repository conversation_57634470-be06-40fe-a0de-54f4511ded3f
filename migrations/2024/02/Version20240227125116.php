<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240227125116 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3055 Update order payments: remove unlinked payments';
    }

    public function up(Schema $schema): void
    {
        $qb = $this->connection->createQueryBuilder();
        $qb
            ->select('bb.bestelling_id')
            ->from('bestelling_betaling', 'bb')
            ->leftJoin('bb', 'bestelling_naw', 'bn', 'bb.bestelling_id = bn.id')
            ->where('bn.id IS NULL')
        ;
        $orderIds = array_unique(array_column($qb->executeQuery()->fetchAllAssociative(), 'bestelling_id'));

        $qb = $this->connection->createQueryBuilder();
        $qb
            ->delete('bestelling_betaling')
            ->where('bestelling_id IN (:orderIds)')
            ->setParameter('orderIds', $orderIds, ArrayParameterType::INTEGER)
        ;

        $qb->executeQuery();

        $qb = $this->connection->createQueryBuilder();
        $qb
            ->select('ep.bestelling_id')
            ->from('extpayment', 'ep')
            ->leftJoin('ep', 'bestelling_naw', 'bn', 'ep.bestelling_id = bn.id')
            ->where('bn.id IS NULL')
        ;
        $orderIds = array_unique(array_column($qb->executeQuery()->fetchAllAssociative(), 'bestelling_id'));

        $qb = $this->connection->createQueryBuilder();
        $qb
            ->delete('extpayment')
            ->where('bestelling_id IN (:orderIds)')
            ->setParameter('orderIds', $orderIds, ArrayParameterType::INTEGER)
        ;

        $qb->executeQuery();
    }

    public function down(Schema $schema): void
    {
        // No way back
    }
}
