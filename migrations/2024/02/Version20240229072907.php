<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;
use Exception;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240229072907 extends AbstractMigration
{
    private const DISCOUNTSETS_TABLE = 'discountsets';
    private const NAME_COLUMN = 'name';

    public function getDescription(): string
    {
        return 'CAM-2908 add name column to discountsets tabel';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::DISCOUNTSETS_TABLE);
        $table
            ->addColumn(self::NAME_COLUMN, Types::STRING)
            ->setLength(255)
            ->setNotnull(false);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::DISCOUNTSETS_TABLE);
        $table->dropColumn(self::NAME_COLUMN);
    }
}
