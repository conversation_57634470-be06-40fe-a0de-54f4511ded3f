<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240229082053 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3055 Add index for order payments PSP reference';
    }

    public function up(Schema $schema): void
    {
        $orderPaymentsTable = $schema->getTable('bestelling_betaling');

        $orderPaymentsTable->addIndex(['psp_reference']);
    }

    public function down(Schema $schema): void
    {
        $orderPaymentsTable = $schema->getTable('bestelling_betaling');

        foreach ($orderPaymentsTable->getIndexes() as $index) {
            if ($index->getColumns() === ['psp_reference']) {
                $orderPaymentsTable->dropIndex($index->getName());
            }
        }
    }
}
