<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;

final class Version20240222130713 extends AbstractMigration
{
    private const TABLE = 'leverancier_feeds';

    private const FK_NAME = 'FK_leverancier_feeds_verzamel_id';

    public function getDescription(): string
    {
        return 'CAM-3875 - Catalogus van <PERSON> bedrijven importeren ';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                "INSERT INTO `cameranu`.`%s` (id, naam, verzamel_id, `show`, use_on_website, token, importable) VALUES (%s, \"Calumet\", NULL, 0, 0, 'a2fd0639-e8c6-4e56-9fe7-a04156e1b907', 1)",
                self::TABLE, SupplierFeed::ID_CALUMET
            )
        );
        $this->addSql(
            sprintf(
                "INSERT INTO `cameranu`.`%s` (id, naam, verzamel_id, `show`, use_on_website, token, importable) VALUES (%s, \"Cyfrowe\", NULL, 0, 0, '67af1647-234a-40a7-9d3e-5ce4fc8a1001', 1)",
                self::TABLE, SupplierFeed::ID_CYFROWE
            )
        );

        // Also a nice-to-have extra FK
        $table = $schema->getTable(self::TABLE);
        $table->addForeignKeyConstraint(
            'leveranciers_verzamel',
            ['verzamel_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE'
            ],
            self::FK_NAME
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                "DELETE FROM `cameranu`.`%s` WHERE id IN (%s,%s)",
                self::TABLE, SupplierFeed::ID_CALUMET, SupplierFeed::ID_CYFROWE
            )
        );

        $table = $schema->getTable(self::TABLE);
        $table->removeForeignKey(self::FK_NAME);
    }
}
