<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Domain;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240228101109 extends AbstractMigration
{

    private const DATA = [
        'address_old' => 'Groenendaal 27a',
        'address_new' => 'Vierhavensstraat 29',
        'zip_place_old' => '3011SK Rotterdam',
        'zip_place_new' => '3029BB Rotterdam',
        'name_old' => 'Cameranu Rotterdam Centrum',
        'name_new' => 'Cameranu Rotterdam'
    ];

    public function getDescription(): string
    {
        return 'CAM-4136 Change address for Rotterdam';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            UPDATE
                `cameranu`.`domeinen`
            SET
                `domeinnaam` = :domainName,
                `adres` = :address,
                `pcplaats` = :zipPlace
            WHERE
                `id` = :id', [
            'domainName' => self::DATA['name_new'],
            'address' => self::DATA['address_new'],
            'zipPlace' => self::DATA['zip_place_new'],
            'id' => Domain::CAMERANU_ROTTERDAM,
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            UPDATE
                `cameranu`.`domeinen`
            SET
                `domeinnaam` = :domainName,
                `adres` = :address,
                `pcplaats` = :zipPlace
            WHERE
                `id` = :id', [
            'domainName' => self::DATA['name_old'],
            'address' => self::DATA['address_old'],
            'zipPlace' => self::DATA['zip_place_old'],
            'id' => Domain::CAMERANU_ROTTERDAM,
        ]);
    }
}
