<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240219153729 extends AbstractMigration
{
    private const GENERIC_DISCOUNTSET_ID = 3347226;

    public function getDescription(): string
    {
        return 'CAM-3058 Mollie paymentmethods and update dynamic combodeal category id';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`betaalwijze1` (pos, naam, descr, verzend_flags, active) VALUES (3, \'mollie\', \'<PERSON>llie\', 137438952766, 0);
        ');
        $this->addSql('
            ALTER TABLE `cameranu`.`payment_method_sorting` ADD CONSTRAINT `fk_payment_method_id` FOREIGN KEY (`payment_method_id`) REFERENCES `cameranu`.`betaalwijze1`(`id`);
        ');
        $this->addSql('
            UPDATE `cameranu`.`artikelen` SET `cat_id` = 5 WHERE `id` = ' . self::GENERIC_DISCOUNTSET_ID
        );
        $this->addSql('
            ALTER TABLE `cameranu`.`bestelling_naw` ADD COLUMN `psp_reference` VARCHAR(50);
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `cameranu`.`betaalwijze1` WHERE naam = \'mollie\';
        ');
        $this->addSql('
            ALTER TABLE `cameranu`.`payment_method_sorting` DROP FOREIGN KEY `fk_payment_method_id`;
        ');
        $this->addSql('
            UPDATE `cameranu`.`artikelen` SET `cat_id` = 1 WHERE `id` = ' . self::GENERIC_DISCOUNTSET_ID
        );
        $this->addSql('
            ALTER TABLE `cameranu`.`bestelling_naw` DROP COLUMN `psp_reference`;
        ');
    }
}
