<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240213094252 extends AbstractMigration
{
    public const EVENT_TABLES = [
        'events_themes',
        'event_themes',
        'event_reminders',
        'event_registrations',
        'event_products',
        'events',
        'event_locations',
        'event_instructors',
        'event_categories',
        'event_checklist_items',
        'events_checklist_items',
    ];

    public function getDescription(): string
    {
        return 'CAM-4041 collation van events goedzetten';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('create index psp_reference_idx on `cameranu`.`extpayment` (pptoken)');

        foreach (self::EVENT_TABLES as $table) {
            $this->addSql(sprintf('ALTER TABLE `cameranu`.`%s` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci', $table));
        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::EVENT_TABLES as $table) {
            $this->addSql(sprintf('ALTER TABLE `cameranu`.`%s` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci', $table));
        }

        $this->addSql('ALTER TABLE `cameranu`.`extpayment` DROP INDEX psp_reference_idx');
    }
}
