<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240228090549 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4129 Set supplierGroup for Sigma feed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE leverancier_feeds SET verzamel_id = ' . SupplierGroup::ID_SIGMA . ' WHERE id = ' . SupplierFeed::ID_SIGMA);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE leverancier_feeds SET verzamel_id = null WHERE id = ' . SupplierFeed::ID_SIGMA);
    }
}
