<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240208150104 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3700 add option for intake-ability to products tabel';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.artikelen');

        try {
            $table->getColumn('intakeable_as_second_hand');//throws scheme exception isd column doesn't exists
        } catch (SchemaException) {
            $newColumn = $table->addColumn('intakeable_as_second_hand', Types::INTEGER);
            $newColumn->setDefault(1);
            $newColumn->setNotnull(false);
            $newColumn->setLength(1);
            $table->addIndex([$newColumn->getName()], 'idx_' . $newColumn->getName());
        }
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.artikelen');

        try {
            $table->getColumn('intakeable_as_second_hand');//throws scheme exception isd column doesn't exists
            $table->dropColumn('intakeable_as_second_hand');
        } catch (SchemaException) {
            //Do nothing column doesn't exists
        }
    }
}
