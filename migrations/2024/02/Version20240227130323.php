<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20240227130323 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3055 Update order payments: table updates';
    }

    public function up(Schema $schema): void
    {
        $orderPaymentsTable = $schema->getTable('bestelling_betaling');

        $orderPaymentsTable->addForeignKeyConstraint(
            'bestelling_naw',
            ['bestelling_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );

        $orderPaymentsTable->addForeignKeyConstraint(
            'betalingsmogelijkheden',
            ['soort'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ],
        );

        $orderPaymentsTable->addColumn('psp_type', Types::STRING, [
            'notnull' => false,
        ]);

        $orderPaymentsTable->addColumn('psp_log_id', Types::INTEGER, [
            'unsigned' => true,
            'notnull' => false,
        ]);
        $orderPaymentsTable->addForeignKeyConstraint(
            'psp_log',
            ['psp_log_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );

        $orderPaymentsTable->addColumn('refunded', Types::BOOLEAN, [
            'default' => false,
            'notnull' => true,
        ]);

        $orderPaymentsTable->addColumn('psp_reference', Types::STRING, [
            'notnull' => false,
        ]);

        if ($orderPaymentsTable->hasIndex('soort')) {
            $orderPaymentsTable->dropIndex('soort');
        }

        $externalPaymentsTable = $schema->getTable('extpayment');

        $externalPaymentsTable->addForeignKeyConstraint(
            'bestelling_naw',
            ['bestelling_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );

        $externalPaymentsTable->addIndex(['ptype1']);

        $paymentTypeTable = $schema->getTable('betalingsmogelijkheden');

        $paymentTypeTable->addForeignKeyConstraint(
            'betaalwijze1',
            ['betaalwijze_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $orderPaymentsTable = $schema->getTable('bestelling_betaling');

        foreach ($orderPaymentsTable->getForeignKeys() as $foreignKey) {
            $orderPaymentsTable->removeForeignKey($foreignKey->getName());
        }

        $orderPaymentsTable->dropColumn('psp_type');
        $orderPaymentsTable->dropColumn('psp_log_id');
        $orderPaymentsTable->dropColumn('refunded');
        $orderPaymentsTable->dropColumn('psp_reference');

        $externalPaymentsTable = $schema->getTable('extpayment');

        foreach ($externalPaymentsTable->getForeignKeys() as $foreignKey) {
            $externalPaymentsTable->removeForeignKey($foreignKey->getName());
        }

        foreach ($externalPaymentsTable->getIndexes() as $index) {
            if ($index->getColumns() === ['ptype1']) {
                $externalPaymentsTable->dropIndex($index->getName());
            }
        }

        $paymentTypeTable = $schema->getTable('betalingsmogelijkheden');

        foreach ($paymentTypeTable->getForeignKeys() as $foreignKey) {
            $paymentTypeTable->removeForeignKey($foreignKey->getName());
        }
    }
}
