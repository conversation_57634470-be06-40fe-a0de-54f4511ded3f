<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240212075404 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-3895 New table `cameranu`.`product_purchased_additionally`';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`product_purchased_additionally` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `product_id` int(11) DEFAULT NULL,
          `additional_product_id` int(11) DEFAULT NULL,
          `amount_sold` int(11) DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `product_id` (`product_id`),
          KEY `additional_product_id` (`additional_product_id`),
          UNIQUE KEY uq_product_id_additional_product_id (product_id, additional_product_id),
          CONSTRAINT `product`
            FOREIGN KEY (`product_id`) REFERENCES `artikelen` (`id`)
            ON DELETE CASCADE
            ON UPDATE NO ACTION,
          CONSTRAINT `additional_product`
            FOREIGN KEY (`additional_product_id`) REFERENCES `artikelen` (`id`)
            ON DELETE CASCADE
            ON UPDATE NO ACTION
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`product_purchased_additionally`');
    }
}
