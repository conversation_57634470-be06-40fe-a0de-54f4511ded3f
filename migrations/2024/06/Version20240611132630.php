<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240611132630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4559 add new table and columns to finish quote from website';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `cameranu`.`quote_user_return_delivery` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `method` enum(\'delivery\',\'store_pickup\',\'postnl_pickup\') NOT NULL,
          `country` enum(\'NL\',\'BE\') NOT NULL,
          `firstname` varchar(255) DEFAULT NULL,
          `infix` varchar(11) DEFAULT NULL,
          `lastname` varchar(255) DEFAULT NULL,
          `zipcode` varchar(11) DEFAULT NULL,
          `house_number` int(11) DEFAULT NULL,
          `house_number_extension` varchar(11) DEFAULT NULL,
          `street` varchar(255) DEFAULT NULL,
          `city` varchar(255) DEFAULT NULL,
          `shop_id` int(11) DEFAULT NULL,
          `postnl_location_code` int(11) DEFAULT NULL,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');

        $table = $schema->getTable('quote_products');
        $table->addColumn('customer_reason', Types::TEXT, ['default' => null, 'notnull' => false]);

        $table = $schema->getTable('quotes');
        $table->addColumn('debit_bic', Types::STRING, ['notnull' => false]);
        $table->addColumn('user_return_delivery_id', Types::INTEGER, ['notnull' => false, 'default' => null, 'unsigned' => true]);

        $table->addForeignKeyConstraint(
            'cameranu.quote_user_return_delivery',
            ['user_return_delivery_id'],
            ['id'],
            [],
            'user_return_delivery_ibfk'
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('quote_products');
        $table->dropColumn('customer_reason');

        $table = $schema->getTable('quotes');
        $table->dropColumn('debit_bic');
        $table->removeForeignKey('user_return_delivery_ibfk');
        $table->dropIndex(self::getIndexNameByColumnName('user_return_delivery_id', 'quotes'));
        $table->dropColumn('user_return_delivery_id');

        $schema->dropTable('quote_user_return_delivery');

    }

    private function getIndexNameByColumnName(string $columnName, string $table): string
    {
        $columnNames = array_merge([$table], [$columnName]);
        $hash = implode('', array_map(static function ($column): string {
            return dechex(crc32($column));
        }, $columnNames));

        return strtoupper(substr('idx_' . $hash, 0, 63));
    }
}
