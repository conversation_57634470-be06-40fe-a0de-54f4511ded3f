<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Domain;
use Webdsign\GlobalBundle\Entity\Parking;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240624132018 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4844 - <PERSON><PERSON> orders automatisch parkeren';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO cameranu.parkeren
            (id, domain_id, pos, omschrijving, info, flags)
            VALUES
            (:id, :domain_id, :pos, :description, :info, :flags)
        ', [
            'id' => Parking::LARGE_ORDER_AMOUNT,
            'domain_id' => Domain::CAMERANU,
            'pos' => 3215,
            'description' => 'Grote orders (€5000+)',
            'info' => '',
            'flags' => Parking::FLAG_ALLOW_PARKING,
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM cameranu.parkeren WHERE id = :id', ['id' => Parking::LARGE_ORDER_AMOUNT]);
    }
}
