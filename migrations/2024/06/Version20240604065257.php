<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240604065257 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4662 New product feed wih content for Adchieve';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO `cameranu`.`productfeeds` (`id`, `partner`, `hash`, `file_type`, `feed_type`)
            VALUES (' . ProductFeed::ADCHIEVE_CONTENT_ID . ', \'AdchieveContent\', \'c51833255126cd5c0beb04ce7fe140dd\', \'xml\', \'product\')');

        $this->addSql('INSERT INTO `productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES
            (\'sku_id\', ' . ProductFeed::ADCHIEVE_CONTENT_ID . ', \'id\'),
            (\'beschrijving_product\',  ' . ProductFeed::ADCHIEVE_CONTENT_ID . ', \'content\');
');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `feed_id` = ' . ProductFeed::ADCHIEVE_CONTENT_ID);
        $this->addSql('DELETE FROM `cameranu`.`productfeeds` WHERE `id` = ' . ProductFeed::ADCHIEVE_CONTENT_ID);
    }
}
