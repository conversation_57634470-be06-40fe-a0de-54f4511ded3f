<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240619094458 extends AbstractMigration
{
    private const TABLE_NAME = 'quote_analytics_info';

    public function getDescription(): string
    {
        return 'CAM-4323 add table for saving ga4 id\'s for InstantQuotes';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable(self::TABLE_NAME);

        $table->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true
        ]);

        $table->setPrimaryKey(['id']);

        $table->addColumn('quote_id', Types::INTEGER, [
            'notnull' => true,
            'unsigned' => true,
        ]);

        $table->addColumn('user_id', Types::STRING, [
            'length' => 255,
            'notnull' => false,
            'default' => null,
        ]);

        $table->addColumn('session_id', Types::INTEGER, [
            'length' => 11,
            'notnull' => false,
            'default' => null,
        ]);

        $table->addForeignKeyConstraint(
            'cameranu.quotes',
            ['quote_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_quote_analytics_info_quotes'
        );

        $table->addIndex(['user_id']);
        $table->addIndex(['session_id']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE_NAME);
    }
}
