<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240618101819 extends AbstractMigration
{
    private const TABLE_NAME = 'cameranu.hoofdgroepen';

    public function getDescription(): string
    {
        return 'CAM-4791 - Er kunnen geen afbeeldingen geupload worden bij tweedhands product types';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);

        $table->addColumn('image', Types::STRING, [
            'length' => 255,
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE_NAME);

        try {
            $table->getColumn('image'); // throws scheme exception if column doesn't exist
            $table->dropColumn('image');
        } catch (SchemaException) {
            // Do nothing if column doesn't exist
        }
    }
}
