<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240620130014 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4559 add extra payoutChoice';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE quotes MODIFY COLUMN payout_choice ENUM(\'debit\',\'coupon\',\'cashback\',\'exchange\')');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE quotes MODIFY COLUMN payout_choice ENUM(\'debit\',\'coupon\',\'cashback\');');
    }
}
