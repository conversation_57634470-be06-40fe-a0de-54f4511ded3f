<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231117143921 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2721 - Levertijden aanpassen en nalopen';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('
            UPDATE `cameranu`.`leverancier_feeds`
               SET `use_on_website` = 1
             WHERE `importable` = 1
               AND `show` = 1
               AND `use_on_website` = 0
        ');

        // Lege leverancier_feeds leverancier weggooien
        $this->addSql('DELETE FROM `cameranu`.`leverancier_feeds` WHERE `id` = 55');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}
