<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231110073452 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-601 - Facturen Sony importeren';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            '
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `ubl_email` = :email,
                `uses_ubl` = :uses
            WHERE `id` = :id
        ',
            [
                'email' => '<EMAIL>',
                'uses' => true,
                'id' => SupplierGroup::ID_SONY_BENELUX,
            ]
        );

        $this->addSql('ALTER TABLE `cameranu`.`ubl_leveranciers_verzamel_fields` ADD COLUMN IF NOT EXISTS `allowance_charge_indicator` varchar(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE `cameranu`.`ubl_leveranciers_verzamel_fields` ADD COLUMN IF NOT EXISTS `allowance_charge_amount` varchar(255) DEFAULT NULL');

        $this->addSql(
            '
            INSERT INTO `cameranu`.`ubl_leveranciers_verzamel_fields` (
                `name`,
                `leveranciers_verzamel_email`,
                `own_reference`,
                `customer_reference`,
                `line_reference`,
                `invoice_date`,
                `invoice_line`,
                `invoice_code`,
                `item_code`,
                `item_amount`,
                `invoice_amount`,
                `calculate_amount`,
                `allowance_charge_indicator`,
                `allowance_charge_amount`
            ) VALUES (
                :name,
                :leveranciers_verzamel_email,
                :own_reference,
                :customer_reference,
                :line_reference,
                :invoice_date,
                :invoice_line,
                :invoice_code,
                :item_code,
                :item_amount,
                :invoice_amount,
                :calculate_amount,
                :allowance_charge_indicator,
                :allowance_charge_amount
            )
        ',
            [
                'name' => 'SONY BENELUX',
                'leveranciers_verzamel_email' => '<EMAIL>',
                'own_reference' => 'OrderReference-ID',
                'customer_reference' => '',
                'line_reference' => 'InvoiceLine-OrderLineReference-OrderReference-ID',
                'invoice_date' => 'IssueDate',
                'invoice_line' => 'InvoiceLine',
                'invoice_code' => 'ID',
                'item_code' => 'InvoiceLine-Item-SellersItemIdentification-ID',
                'item_amount' => 'InvoiceLine-InvoicedQuantity',
                'invoice_amount' => 'InvoiceLine-Price-PriceAmount',
                'calculate_amount' => null,
                'allowance_charge_indicator' => 'InvoiceLine-Price-AllowanceCharge-ChargeIndicator',
                'allowance_charge_amount' => 'InvoiceLine-Price-AllowanceCharge-Amount',
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            '
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `ubl_email` = :email,
                `uses_ubl` = :uses
            WHERE `id` = :id
        ',
            [
                'id' => SupplierGroup::ID_SONY_BENELUX,
                'email' => null,
                'uses' => false
            ]
        );

        $this->addSql(
            '
            DELETE FROM `ubl_leveranciers_verzamel_fields`
            WHERE `name` = :name
            AND `leveranciers_verzamel_email` = :email
        ',
            [
                'name' => 'SONY BENELUX',
                'email' => '<EMAIL>',
            ]
        );

        $this->addSql('ALTER TABLE `cameranu`.`ubl_leveranciers_verzamel_fields` DROP COLUMN IF EXISTS `allowance_charge_indicator`');
        $this->addSql('ALTER TABLE `cameranu`.`ubl_leveranciers_verzamel_fields` DROP COLUMN IF EXISTS `allowance_charge_amount`');
    }
}
