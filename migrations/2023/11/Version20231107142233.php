<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Driver\Exception;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231107142233 extends AbstractMigration
{
    private const DISCOUNTSETS_TABLE = 'discountsets';
    private const POSITIONS_TABLE = 'discountset_positions';
    private const BRANDS_TABLE = 'discountset_brands';

    private const FOREIGN_KEYS = [
        self::DISCOUNTSETS_TABLE => [
            [
                'table' => 'specs_profiles',
                'local_column' => 'spec_profile_id',
                'on_update' => 'CASCADE',
                'on_delete' => 'CASCADE',
            ],
            [
                'table' => 'users',
                'local_column' => 'created_by',
                'on_update' => 'CASCADE',
                'on_delete' => 'CASCADE',
            ],
            [
                'table' => 'users',
                'local_column' => 'updated_by',
                'on_update' => 'CASCADE',
                'on_delete' => 'CASCADE',
            ]
        ],
        self::POSITIONS_TABLE => [
            [
                'table' => self::DISCOUNTSETS_TABLE,
                'local_column' => 'discountset_id',
                'on_update' => 'CASCADE',
                'on_delete' => 'CASCADE',
            ],
            [
                'table' => 'specs_profiles',
                'local_column' => 'spec_profile_id',
                'on_update' => 'CASCADE',
                'on_delete' => 'CASCADE',
            ],
            [
                'table' => 'artikelen',
                'local_column' => 'product_id',
                'on_update' => 'CASCADE',
                'on_delete' => 'CASCADE',
            ],
        ],
        'discountset_discounts' => [
            [
                'table' => self::DISCOUNTSETS_TABLE,
                'local_column' => 'discountset_id',
                'on_update' => 'CASCADE',
                'on_delete' => 'CASCADE',
            ],
        ]
    ];

    public function getDescription(): string
    {
        return 'CAM-2384 Combodeals - Meerdere merken kiezen';
    }

    public function up(Schema $schema): void
    {
        // See comment on afterFuncConvertToAddSql
        $schema = clone $schema;

        $this->removeForeignKeyViolations();
        $this->addMissingForeignKeys($schema);
        $this->createBrandsTable($schema);
        $this->moveOverOldDataInPositionsTableToOurNewTable($schema);
    }

    public function down(Schema $schema): void
    {
        // See up() comment
        $schema = clone $schema;

        $this->restoreColumnsInPositionsTable($schema);
        $this->afterFuncConvertToAddSql(static function (Schema $schema) {
            $schema->dropTable('discountset_brands');
        }, $schema);
        $this->dropAddedForeignKeys($schema);
    }

    public function removeForeignKeyViolations(): void
    {
        // In adding our foreign keys, we might encounter invalid data, make sure we get rid of those early on
        // It is trash data
        foreach (self::FOREIGN_KEYS as $originTable => $foreignKeys) {
            foreach ($foreignKeys as $info) {
                // Using normal prepared statement doesn't work somehow, we know input is safe
                // as we wrote it
                $fkViolations = $this->connection->executeQuery(
                    sprintf(
                        'SELECT id FROM %s WHERE %s NOT IN (SELECT id FROM %s)',
                        $originTable,
                        $info['local_column'],
                        $info['table']
                    )
                )->fetchAllAssociative();

                if (count($fkViolations)) {
                    $ids = array_map(static fn ($x) => (int)$x['id'], $fkViolations);
                    $this->addSql(sprintf('DELETE FROM `%s` WHERE id in (%s)', $originTable, implode(',', $ids)));
                }
            }
        }
    }

    /**
     * @param Schema $schema
     * @return void
     * @throws \Doctrine\DBAL\Schema\SchemaException
     */
    public function addMissingForeignKeys(Schema $schema): void
    {
        // FK needs both columns to be exactly the same
        // Most of these local FK columns are not unsigned yet...
        // Can also be issue with the foreign column
        $this->afterFuncConvertToAddSql(static function (Schema $schema) {
            foreach (self::FOREIGN_KEYS as $originTable => $keys) {
                $table = $schema->getTable($originTable);
                foreach ($keys as $info) {
                    $localColumn = $table->getColumn($info['local_column']);
                    $foreignColumn = $schema->getTable($info['table'])->getColumn('id');
                    switch ([$localColumn->getUnsigned(), $foreignColumn->getUnsigned()]) {
                        case [true, true]:
                        case [false, false]:
                            // We're happy, nothing to do
                            break;
                        case [true, false]:
                            // Should make the foreign column unsigned, but "out of our territory" now
                            // A sad day that we leave id fields signed...
                            $localColumn->setUnsigned(false);
                            break;
                        case [false, true]:
                            $localColumn->setUnsigned(true);
                            break;
                        default:
                            throw new \LogicException();
                    }
                }
            }
        }, $schema);

        $this->afterFuncConvertToAddSql(static function (Schema $schema) {
            foreach (self::FOREIGN_KEYS as $originTable => $keys) {
                $table = $schema->getTable($originTable);
                foreach ($keys as $info) {
                    $table->addForeignKeyConstraint(
                        $info['table'],
                        [$info['local_column']],
                        ['id'],
                        ['onUpdate' => $info['on_update'], 'onDelete' => $info['on_delete']]
                    );
                }
            }
        }, $schema);
    }

    /**
     * @param Schema $schema
     * @return void
     */
    public function createBrandsTable(Schema $schema): void
    {
        $this->afterFuncConvertToAddSql(static function (Schema $schema) {
            $brandsTable = $schema->createTable(self::BRANDS_TABLE);

            // Columns
            $brandsTable->addColumn('id', Types::INTEGER, [
                'unsigned' => true,
                'autoincrement' => true,
                'notnull' => true
            ]);
            $brandsTable->addColumn('discountset_position_id', Types::INTEGER, [
                'unsigned' => true,
            ]);
            // This should be unsigned, as it's an ID column, but because the referencing column
            // is signed, and FK's demand it needs to be same type, it will be signed as well...
            // A sad day...
            $brandsTable->addColumn('specs_specification_id', Types::INTEGER, [
                'unsigned' => false,
                'notnull' => true,
            ]);
            $brandsTable->addColumn('specs_specification_value', Types::STRING);
            $brandsTable->addColumn('position', Types::INTEGER, [
                'unsigned' => true,
                'notnull' => true,
            ]);


            // Other things
            $brandsTable->setPrimaryKey(['id']);
            $brandsTable->addForeignKeyConstraint(
                self::POSITIONS_TABLE,
                ['discountset_position_id'],
                ['id'],
                [
                    'onDelete' => 'CASCADE',
                    'onUpdate' => 'CASCADE',
                ],
            );
            $brandsTable->addForeignKeyConstraint(
                'specs_specifications',
                ['specs_specification_id'],
                ['id'],
                [
                    'onDelete' => 'CASCADE',
                    'onUpdate' => 'CASCADE',
                ]
            );
        }, $schema);
    }

    /**
     * @param Schema $schema
     * @return void
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Schema\SchemaException
     */
    public function moveOverOldDataInPositionsTableToOurNewTable(Schema $schema): void
    {
        $positions = $this->connection->executeQuery(
            'SELECT * FROM discountset_positions WHERE product_id IS NULL'
        )->fetchAllAssociative();
        if (count($positions)) {
            $toSqlValue = static fn (array $positionRow) => sprintf(
                '(%s,%s,\'%s\')',
                $positionRow['id'],
                $positionRow['specs_specification_id'],
                $positionRow['specs_specification_value']
            );
            $sqlValues = array_map($toSqlValue, $positions);

            $this->addSql(
                sprintf(
                    'INSERT INTO %s (discountset_position_id, specs_specification_id, specs_specification_value) VALUES %s',
                    self::BRANDS_TABLE,
                    implode(
                        ',',
                        $sqlValues
                    )
                )
            );
        }

        $this->afterFuncConvertToAddSql(static function (Schema $schema) {
            $discountSetPositionsTable = $schema->getTable('discountset_positions');
            $discountSetPositionsTable->dropColumn('specs_specification_id');
            $discountSetPositionsTable->dropColumn('specs_specification_value');
        }, $schema);
    }

    /**
     * @param Schema $schema
     * @return void
     * @throws Exception
     * @throws SchemaException
     * @throws \Doctrine\DBAL\Exception
     */
    public function restoreColumnsInPositionsTable(Schema $schema): void
    {
        $this->afterFuncConvertToAddSql(static function (Schema $schema) {
            $discountSetPositionsTable = $schema->getTable(self::POSITIONS_TABLE);
            $discountSetPositionsTable->addColumn('specs_specification_id', Types::INTEGER);
            $discountSetPositionsTable->addColumn('specs_specification_value', Types::STRING);
        }, $schema);

        // Move data back to positions table
        // But we're going from multiple brands rows to one, we want the 'oldest' one, as that's the primary one
        // So just iterate over brands rows in reverse order, and update the row in positions table
        $rowsBrandsTable = $this->connection
            ->executeQuery('SELECT * FROM ' . self::BRANDS_TABLE . ' ORDER BY id DESC ')->fetchAllAssociative();
        foreach ($rowsBrandsTable as $row) {
            $this->addSql(
                sprintf(
                    'UPDATE %s SET specs_specification_id = %s, , specs_specification_value = %s WHERE id = %s',
                    self::POSITIONS_TABLE,
                    $row['specs_specification_id'],
                    $row['specs_specification_value'],
                    $row['discountset_position_id']
                )
            );
        }
    }

    /**
     * @param Schema $schema
     * @return void
     * @throws SchemaException
     */
    public function dropAddedForeignKeys(Schema $schema): void
    {
        $this->afterFuncConvertToAddSql(static function (Schema $schema) {
            foreach (self::FOREIGN_KEYS as $originTable => $keys) {
                $table = $schema->getTable($originTable);
                foreach ($table->getForeignKeys() as $key) {
                    $table->removeForeignKey($key->getName());
                }
            }
        }, $schema);
    }

    /**
     * The DbalExecutor that does this migration does not like us mixing addSql() calls with calls to $schema object
     * When it does our up() or down(), it first gathers all sql that we added with addSql(), and then gets all sql
     * statements it gets from doing a diff on Schema objects. But that messes up the order! This fixes that.
     *
     * We just make a local clone of Schema object, do stuff to it, then immediately we just a diff object to get the
     * sql statements and add that. So to the DbalExecutor it looks like we never make calls to $schema!
     *
     * @param callable $f
     * @param Schema $schema
     * @return void
     */
    private function afterFuncConvertToAddSql(callable $f, Schema $schema)
    {
        $old = clone $schema;
        $f($schema);
        foreach ($old->getMigrateToSql($schema, $this->connection->getDatabasePlatform()) as $sql) {
            $this->addSql($sql);
        }
    }
}
