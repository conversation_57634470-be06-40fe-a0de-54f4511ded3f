<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231130075235 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2632 Parameters toevoegen aan purchase event GA4';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'CREATE TABLE IF NOT EXISTS `cameranu`.`order_analytics_info` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `order_id` int(11) NOT NULL,
                `session_id` varchar(255) NOT NULL DEFAULT \'\',
                `push_type` enum(\'backend\', \'website\') DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `order_id` (`order_id`),
                KEY `session_id` (`session_id`),
                <PERSON><PERSON><PERSON> `push_type` (`push_type`),
                CONSTRAINT `order_analytics_info_bestelling_naw`
                    FOREIGN KEY (`order_id`)
                        REFERENCES `bestelling_naw` (`id`)
                        ON DELETE CASCADE
                        ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`order_analytics_info`');
    }
}
