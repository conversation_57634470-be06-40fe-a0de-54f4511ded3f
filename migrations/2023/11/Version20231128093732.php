<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231128093732 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2845 Achtergrondkleur pagina\'s aanpassen (migration)';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE `pages_themes` (
                `page_id` int(11) NOT NULL,
                `background` varchar(32) NOT NULL,
                PRIMARY KEY (`page_id`),
                UNIQUE KEY `page_unique_theme` (`page_id`,`background`),
                CONSTRAINT `FK_pages_themes_pages` FOREIGN KEY (`page_id`) REFERENCES `pages` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE `cameranu`.`pages_themes`');
    }
}
