<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231107113058 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2724 index_name toevoegen aan pages tabel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`pages`
            ADD COLUMN `index_name` VARCHAR(255) DEFAULT NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`pages`
            DROP COLUMN `index_name`;
        ');
    }
}
