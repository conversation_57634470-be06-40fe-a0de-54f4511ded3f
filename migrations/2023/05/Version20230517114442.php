<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230517114442 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1589 Add a name column';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("ALTER TABLE `cameranu`.`frontpage_hero_banners` ADD COLUMN `name` VARCHAR(255) NOT NULL DEFAULT '' AFTER `id`");
        $this->addSql("ALTER TABLE `cameranu`.`frontpage_hero_banners` ADD COLUMN `publish_start_date` DATETIME NULL AFTER `active`");
        $this->addSql("ALTER TABLE `cameranu`.`frontpage_hero_banners` ADD COLUMN `publish_end_date` DATETIME NULL AFTER `publish_start_date`");
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('<PERSON>TE<PERSON> TABLE `cameranu`.`frontpage_hero_banners` DROP COLUMN IF EXISTS `name`');
        $this->addSql('ALTER TABLE `cameranu`.`frontpage_hero_banners` DROP COLUMN IF EXISTS `publish_start_date`');
        $this->addSql('ALTER TABLE `cameranu`.`frontpage_hero_banners` DROP COLUMN IF EXISTS `publish_end_date`');
    }
}
