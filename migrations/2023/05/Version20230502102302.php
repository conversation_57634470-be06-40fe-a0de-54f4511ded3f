<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230502102302 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1620 - Klantsegmenten beheren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`customer_segment` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                `name_nl` VARCHAR(255) NOT NULL,
                `name_en` VARCHAR(255) NOT NULL,
                `lft` INT(11) NOT NULL,
                `rgt` INT(11) NOT NULL,
                `lvl` INT(11) NOT NULL,
                `parent_id` INT(11) UNSIGNED DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `customer_segment_parent_fk_idx` (`parent_id`),
                CONSTRAINT `customer_segment_parent_fk` FOREIGN KEY (`parent_id`)
                    REFERENCES `cameranu`.`customer_segment` (`id`)
                    ON DELETE CASCADE
                    ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

            ALTER TABLE `cameranu`.`webusers`
            ADD COLUMN IF NOT EXISTS `segment_id` INT(11) UNSIGNED DEFAULT NULL,
            ADD KEY IF NOT EXISTS `customer_segment_fk_idx` (`segment_id`),
            ADD CONSTRAINT `customer_segment_fk` FOREIGN KEY IF NOT EXISTS (`segment_id`)
                REFERENCES `cameranu`.`customer_segment` (`id`)
                ON DELETE SET NULL
                ON UPDATE CASCADE;
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`webusers`
                DROP FOREIGN KEY IF EXISTS `customer_segment_fk`,
                DROP COLUMN IF EXISTS `segment_id`;
            DROP TABLE IF EXISTS `cameranu`.`customer_segment`;
        ');
    }
}
