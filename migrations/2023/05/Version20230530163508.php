<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ClaimsAndFeesKind;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230530163508 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1557 - Stock compensation toevoegen als soort voor claims en vergoedingen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees` MODIFY `date_from` datetime DEFAULT NULL');
        $this->addSql('
            INSERT INTO `cameranu`.`claims_and_fees_kind` (`id`, `name`) VALUES
            (' . ClaimsAndFeesKind::STOCK_COMPENSATION . ', "Stock compensation")
        ');

        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports_rows` MODIFY `order_id` INT(11) DEFAULT NULL');
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports_rows` ADD COLUMN IF NOT EXISTS `stock_id` INT(11) DEFAULT NULL AFTER `order_id`');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`claims_and_fees_kind` WHERE `id` = ' . ClaimsAndFeesKind::STOCK_COMPENSATION);
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports_rows` DROP COLUMN IF EXISTS `stock_id`');
    }
}
