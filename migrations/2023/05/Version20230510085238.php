<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230510085238 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1663 add bool option to prevent stacking discount codes on products';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`discountcodes` ADD COLUMN IF NOT EXISTS `only_one_per_product` tinyint(1) NOT NULL DEFAULT 1');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`discountcodes` DROP COLUMN IF EXISTS `only_one_per_product`');
    }
}
