<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230530085942 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1630 Verhuizing Amsterdam';
    }

    private const UPDATES = [
        '`cameranu`.`stock_locations`' => [
            '`description`' => [
                'Winkel Amsterdam Centrum' => 'Winkel Amsterdam',
                'Transit Amsterdam Centrum' => 'Transit Amsterdam',
                'B-voorraad Amsterdam Centrum' => 'B-voorraad Amsterdam',
                'Koerier Amsterdam Centrum' => 'Koerier Amsterdam',
                'Zoeklocatie Amsterdam Centrum' => 'Zoeklocatie Amsterdam',
                'Retouren Amsterdam Centrum' => 'Retouren Amsterdam',
                'Verhuur Amsterdam Centrum' => 'Verhuur Amsterdam',
                'Demo Amsterdam Centrum' => 'Demo Amsterdam',
                'X-voorraad Amsterdam Centrum' => 'X-voorraad Amsterdam',
            ],
        ],
        '`cameranu`.`websiteconfigurator_fields`' => [
            '`label`' => [
                'Amsterdam Centrum' => 'Amsterdam',
                'Vandaag ophalen winkel Amsterdam Centrum' => 'Vandaag ophalen winkel Amsterdam',
                'Later ophalen winkel Amsterdam Centrum' => 'Later ophalen winkel Amsterdam',
            ],
        ],
        '`cameranu`.`verzendwijze`' => [
            '`verzendwijze`' => [
                'Vandaag afhalen in Amsterdam Centrum' => 'Vandaag afhalen in Amsterdam',
                'Volgende werkdag afhalen in Amsterdam Centrum' => 'Volgende werkdag afhalen in Amsterdam',
            ],
        ],
        '`cameranu`.`verzendwijze2`' => [
          '`verzendwijze`' => [
              'Amsterdam Centrum' => 'Amsterdam',
              'Amsterdam | Center' => 'Amsterdam',
          ],
          '`verzendwijze_kort`' => [
              'Vandaag afhalen in onze winkel in Amsterdam Centrum' => 'Vandaag afhalen in onze winkel in Amsterdam',
              'Volgende werkdag afhalen in onze winkel in Amsterdam Centrum' => 'Volgende werkdag afhalen in onze winkel in Amsterdam',
              'Pick up today at our store in Amsterdam Center' => 'Pick up today at our store in Amsterdam',
              'Pick up next business day at our store in Amsterdam Center' => 'Pick up next business day at our store in Amsterdam',
          ],
        ],
        '`cameranu`.`origins_profiles`' => [
            '`name`' => [
                'Winkel Amsterdam Centrum' => 'Winkel Amsterdam',
            ],
        ],
        '`cameranu`.`domeinen`' => [
            '`domeinnaam`' => [
                'Cameranu Amsterdam Centrum' => 'Cameranu Amsterdam',
            ],
            '`adres`' => [
                'Van Woustraat 242' => 'Amstelveenseweg 288-292',
            ],
            '`pcplaats`' => [
                '1073 NC Amsterdam' => '1075 XX Amsterdam',
            ],
        ],
        '`cameranu`.`bestelling_herkomst`' => [
            '`source`' => [
                'Winkel Amsterdam Centrum' => 'Winkel Amsterdam',
            ],
            '`description`' => [
                'Cameranu - Amsterdam | Centrum' => 'Cameranu - Amsterdam',
            ],
            '`winkelnaam`' => [
                'Amsterdam Centrum' => 'Amsterdam',
            ],
        ],
        '`cameranu`.`internal_invoice_stock_location_information`' => [
            '`sepaName`' => [
                'CameraNU.nl Amsterdam Centrum' => 'Cameranu Amsterdam',
            ],
            '`address`' => [
                'Van Woustraat 242' => 'Amstelveenseweg 288-292',
            ],
            '`postcode`' => [
                '1073NC' => '1075XX'
            ],
        ],
        'websiteconfigurator_values' => [
            '`value`' => [
                'amsterdam-centrum' => 'amsterdam',
            ],
        ],
    ];

    public function up(Schema $schema): void
    {
        foreach (self::UPDATES as $table => $columns) {
            foreach ($columns as $column => $values) {
                foreach ($values as $oldValue => $newValue) {
                    $this->addSql(
                        'UPDATE ' . $table . ' SET ' . $column . ' = :newValue WHERE ' . $column . ' = :oldValue',
                        [
                            'oldValue' => $oldValue,
                            'newValue' => $newValue,
                        ],
                    );
                }
            }
        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::UPDATES as $table => $columns) {
            foreach ($columns as $column => $values) {
                foreach ($values as $oldValue => $newValue) {
                    $this->addSql(
                        'UPDATE ' . $table . ' SET ' . $column . ' = :oldValue WHERE ' . $column . ' = :newValue',
                        [
                            'oldValue' => $oldValue,
                            'newValue' => $newValue,
                        ],
                    );
                }
            }
        }
    }
}
