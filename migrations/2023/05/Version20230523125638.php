<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230523125638 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1170 FL marge toevoegen aan leveranciersformules tabel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`leveranciersformules`
            ADD COLUMN `fl_margin_id` int(11) DEFAULT NULL AFTER `description_id`;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`leveranciersformules`
            DROP COLUMN `fl_margin_id`;
        ');
    }
}
