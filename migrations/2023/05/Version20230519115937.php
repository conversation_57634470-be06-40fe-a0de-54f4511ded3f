<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230519115937 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1388 FL marge tabellen toevoegen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`fl_margin` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `code` varchar(5) DEFAULT NULL,
              `name` varchar(20) DEFAULT NULL,
              `description` varchar(50) DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
        $this->addSql('
            CREATE TABLE `cameranu`.`fl_margin_sub` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `fl_margin_id` int(11) DEFAULT NULL,
              `description` varchar(50) DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `fl_margin_id` (`fl_margin_id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
        $this->addSql('
            ALTER TABLE `cameranu`.`additional_contributions`
            ADD `fl_margin_id` int(11) DEFAULT NULL AFTER `customer_number`,
            ADD `fl_margin_sub_id` int(11) DEFAULT NULL AFTER `fl_margin_id`;
        ');
        $this->addSql('
            INSERT INTO `cameranu`.`fl_margin` (`id`, `code`, `name`, `description`)
            VALUES
                (1, \'FL1\', \'Full margin 1\', \'Directly product attributale\'),
                (4, \'FL2\', \'Full margin 2\', \'Directly brand sales attributable\'),
                (7, \'FL3\', \'Full margin 3\', \'Full Brand Support\');
        ');
        $this->addSql('
            INSERT INTO `cameranu`.`fl_margin_sub` (`id`, `fl_margin_id`, `description`)
            VALUES
                (1, 1, \'Product premie sell-out\'),
                (4, 1, \'Product premie sell-in\'),
                (7, 1, \'Stockcompensatie\'),
                (10, 1, \'Bijdrage inkoop overig\'),
                (13, 1, \'Conditionele marketingbijdrage\'),
                (16, 1, \'Conditioneel productpremie\'),
                (19, 4, \'Bijdrage inkoop overig\'),
                (22, 4, \'Additionele marketingbijdrage\'),
                (25, 4, \'Conditionele marketingbijdrage\'),
                (28, 4, \'Conditioneel productpremie\'),
                (31, 7, \'Bijdrage inkoop overig\'),
                (34, 7, \'Additionele marketingbijdrage\'),
                (37, 7, \'Conditionele marketingbijdrage\'),
                (40, 7, \'Conditioneel productpremie\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`fl_margin`;');
        $this->addSql('DROP TABLE `cameranu`.`fl_margin_sub`;');
        $this->addSql('
            ALTER TABLE `cameranu`.`additional_contributions`
            DROP COLUMN `fl_margin_id`,
            DROP COLUMN `fl_margin_sub_id`;
        ');
    }
}
