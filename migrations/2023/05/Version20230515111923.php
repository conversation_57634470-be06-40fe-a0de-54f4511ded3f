<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\PaymentMethod;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230515111923 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1693 - Betaaltermijn instellen voor klanten';
    }

    public function up(Schema $schema) : void
    {
        // Create payment_period table
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`payment_period` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                `days` VARCHAR(10) NOT NULL,
                `default` TINYINT(1) DEFAULT 0 NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');

        // Insert payment_period rows
        $this->addSql('
            INSERT INTO `cameranu`.`payment_period` (`id`, `days`, `default`) VALUES
            (1, "0", 0),
            (2, "7", 0),
            (3, "14", 1),
            (4, "21", 0),
            (5, "30", 0)
        ');

        // Add payment_period_id column for customers
        $this->addSql('
            ALTER TABLE `cameranu`.`webusers`
            ADD COLUMN IF NOT EXISTS `payment_period_id` INT(11) UNSIGNED DEFAULT NULL,
            ADD KEY IF NOT EXISTS `customer_payment_period_fk_idx` (`payment_period_id`),
            ADD CONSTRAINT `customer_payment_period_fk` FOREIGN KEY IF NOT EXISTS (`payment_period_id`)
                REFERENCES `cameranu`.`payment_period` (`id`);
        ');

        // Update business users for default payment_period_id
        $this->addSql('
            UPDATE `cameranu`.`webusers` SET `payment_period_id` = 3
            WHERE `customer_type` IN ("klein_zakelijk", "zakelijk")
        ');

        // Add payment_period_id column for orders
        $this->addSql('
            ALTER TABLE `cameranu`.`bestelling_naw`
            ADD COLUMN IF NOT EXISTS `payment_period_id` INT(11) UNSIGNED DEFAULT NULL,
            ADD KEY IF NOT EXISTS `order_payment_period_fk_idx` (`payment_period_id`),
            ADD CONSTRAINT `order_payment_period_fk` FOREIGN KEY IF NOT EXISTS (`payment_period_id`)
                REFERENCES `cameranu`.`payment_period` (`id`);
        ');

        // Update payment method name for id 33
        $this->addSql('
            UPDATE `cameranu`.`betaalwijze1` SET `descr` = "Betaling op rekening"
            WHERE `id` = ' . PaymentMethod::BANKTRANSFER . '
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`webusers` DROP FOREIGN KEY IF EXISTS `customer_payment_period_fk`');
        $this->addSql('ALTER TABLE `cameranu`.`webusers` DROP COLUMN IF EXISTS `payment_period_id`');
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`payment_period`');

        // Reset payment method name for id 33
        $this->addSql('
            UPDATE `cameranu`.`betaalwijze1` SET `descr` = "Vooruit per bank"
            WHERE `id` = ' . PaymentMethod::BANKTRANSFER . '
        ');
    }
}
