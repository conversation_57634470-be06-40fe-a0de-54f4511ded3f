<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230517083753 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1603 create product successor relation table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`product_successors` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `product_id` int(11) NOT NULL,
                `successor_id` int(11) NOT NULL,
                PRIMARY KEY (`id`),
                KEY `product_id` (`product_id`),
                KEY `successor_id` (`successor_id`),
                CONSTRAINT
                    `product_ successors_ibfk_1`
                    FOREIGN KEY (`product_id`)
                        REFERENCES `artikelen` (`id`)
                        ON DELETE CASCADE
                        ON UPDATE CASCADE,
                CONSTRAINT
                    `product_ successors_ibfk_2`
                    FOREIGN KEY (`successor_id`)
                        REFERENCES `artikelen` (`id`)
                        ON DELETE CASCADE
                        ON UPDATE CASCADE,
                CONSTRAINT
                    `uc_successor`
                     UNIQUE (product_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
         ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`product_successors`');
    }
}
