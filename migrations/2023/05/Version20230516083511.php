<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230516083511 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1250 Alter RFM score tables to base recency on lastOrderDate';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` ADD COLUMN IF NOT EXISTS `last_order_date` datetime NOT NULL DEFAULT \'0000-00-00 00:00:00\'');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` DROP COLUMN IF EXISTS `score`');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` DROP COLUMN IF EXISTS `recency`');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` DROP COLUMN IF EXISTS `last_order_date`');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` ADD COLUMN IF NOT EXISTS `score` int(11) DEFAULT NULL');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` ADD COLUMN IF NOT EXISTS `recency` int(11) DEFAULT 0');
    }
}
