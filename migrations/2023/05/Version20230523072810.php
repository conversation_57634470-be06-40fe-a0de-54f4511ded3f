<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230523072810 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1171 Toevoegen FL marge kolom aan claims & vergoedingen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees`
            ADD COLUMN `fl_margin_id` int(11) DEFAULT NULL AFTER `value`;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees`
            DROP COLUMN `fl_margin_id`;
        ');
    }
}
