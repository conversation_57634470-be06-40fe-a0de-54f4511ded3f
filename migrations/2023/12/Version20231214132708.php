<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231214132708 extends AbstractMigration
{
    private const TABLE = 'cameranu_urk.service_rma';
    private const NEW_COLUMN_NAME = 'is_vat_free';

    public function getDescription() : string
    {
        return 'CAM-2717 - Retour nemen van product kan niet exclusief btw';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`service_rma` ADD COLUMN `is_vat_free` TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`service_rma` DROP COLUMN `is_vat_free`');
    }
}
