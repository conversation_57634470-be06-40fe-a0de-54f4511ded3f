<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231204134242 extends AbstractMigration
{
    private const TABLE = 'internal_invoice_lines';
    private const COLUMN = 'stockId';
    private const INDEX_NAME = 'ix_stock_id';

    public function getDescription(): string
    {
        return 'CAM-2580';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE)->addIndex([self::COLUMN], self::INDEX_NAME);
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE)->dropIndex(self::INDEX_NAME);
    }
}
