<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231213153600 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1444 Reminder tabel wijzigen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`event_reminders` CHANGE category_id theme_id int(11);
            ALTER TABLE `cameranu`.`event_products` ADD COLUMN `created` DATETIME NOT NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`event_reminders` CHANGE theme_id category_id int(11);
            ALTER TABLE `cameranu`.`event_products` DROP COLUMN `created`;
        ');
    }
}
