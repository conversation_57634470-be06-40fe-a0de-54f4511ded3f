<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231211103808 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2316 Add purchase_advice_log table and voorraad_memo.purchase_advice_diff column';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`purchase_advice_log` (
                `product_id` int(11) NOT NULL,
                `date` datetime NOT NULL DEFAULT current_timestamp() COMMENT \'(DC2Type:datetime_immutable)\',
                PRIMARY KEY (`product_id`),
                CONSTRAINT `FK_A01527104584665A` FOREIGN KEY (`product_id`) REFERENCES `cameranu`.`artikelen` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`voorraad_memo`
            ADD COLUMN `purchase_advice_diff` INT(11) DEFAULT NULL
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`purchase_advice_log`');
    }
}
