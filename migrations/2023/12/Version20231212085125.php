<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231212085125 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2620 - created toevoegen aan bestelling_betaling';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`bestelling_betaling` ADD COLUMN IF NOT EXISTS `created` DATETIME NULL');
        $this->addSql('ALTER TABLE `cameranu`.`bestelling_betaling` MODIFY COLUMN `created` DATETIME NULL DEFAULT CURRENT_TIMESTAMP');
        $this->addSql('CREATE INDEX bestelling_betaling_created_IDX USING BTREE ON `cameranu`.`bestelling_betaling` (`created`)');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`bestelling_betaling` DROP COLUMN IF EXISTS `created`');
    }
}
