<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231229082337 extends AbstractMigration
{
    private const TYPE = 'largeCarousel';
    private const SORT = 100;
    private const CATEGORY = 'Content (Rebranding)';

    public function getDescription() : string
    {
        return 'CAM-2769 Large Carousel Pageblock (migration)';
    }

    public function up(Schema $schema): void
    {
        $timestamp = time();
        $sql = 'INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
                VALUES (:created, :modified, :type, :sort, :category)';
        $this->addSql($sql, [
            'created' => $timestamp,
            'modified' => $timestamp,
            'type' => self::TYPE,
            'sort' => self::SORT,
            'category' => self::CATEGORY,
        ]);
    }

    public function down(Schema $schema): void
    {
        $sql = 'DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = :type';
        $this->addSql($sql, [
            'type' => self::TYPE,
        ]);
    }
}
