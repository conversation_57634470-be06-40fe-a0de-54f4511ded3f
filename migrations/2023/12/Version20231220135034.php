<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use RuntimeException;
use stdClass;
use Webdsign\GlobalBundle\Entity\InternalInvoice;

final class Version20231220135034 extends AbstractMigration
{
    public const BACKUP_TABLE = 'migration_backups';
    public const STOCK_LOG_TABLE = 'stock_log';
    public const INTERNAL_INVOICES_TABLE = 'internal_invoices';
    public const INTERNAL_INVOICE_LINES_TABLE = 'internal_invoice_lines';
    public const CUTOFF_DATE = '2023-12-05';

    private const DOWN_CHUNK_SIZE = 100;

    public function getDescription(): string
    {
        return 'CAM-2977 (part 2) Interne facturen genereren zorgt voor dubbele facturen';
    }

    public function up(Schema $schema): void
    {
        $backupRowCounts = [];
        $deletedCounts = [];

        $backupRowCounts[self::STOCK_LOG_TABLE] = $this->writeToBackup(
            'SELECT * FROM `stock_log` WHERE internal_invoice_line_id IN
                                (SELECT id FROM `internal_invoice_lines` WHERE created > ? AND internalInvoiceId IN
                                                                                               (SELECT id FROM internal_invoices WHERE invoice_type IN (?, ?, ?)))',
            [
                self::CUTOFF_DATE,
                InternalInvoice::TYPE_RMA,
                InternalInvoice::TYPE_MIXED_STOCK,
                InternalInvoice::TYPE_COURIER_LIST
            ],
            self::STOCK_LOG_TABLE
        );
        $backupRowCounts[self::INTERNAL_INVOICE_LINES_TABLE] = $this->writeToBackup(
            'SELECT * FROM `internal_invoice_lines` WHERE created > ? AND internalInvoiceId IN
                                                             (SELECT id FROM internal_invoices WHERE invoice_type IN (?, ?, ?))',
            [
                self::CUTOFF_DATE,
                InternalInvoice::TYPE_RMA,
                InternalInvoice::TYPE_MIXED_STOCK,
                InternalInvoice::TYPE_COURIER_LIST
            ],
            self::INTERNAL_INVOICE_LINES_TABLE
        );
        $backupRowCounts[self::INTERNAL_INVOICES_TABLE] = $this->writeToBackup(
            'SELECT * FROM `internal_invoices` WHERE created > ? AND invoice_type IN (?, ?, ?)',
            [
                self::CUTOFF_DATE,
                InternalInvoice::TYPE_RMA,
                InternalInvoice::TYPE_MIXED_STOCK,
                InternalInvoice::TYPE_COURIER_LIST
            ],
            self::INTERNAL_INVOICES_TABLE
        );

        $deletedCounts[self::STOCK_LOG_TABLE] = $this
            ->connection
            ->executeStatement(
                'DELETE FROM `stock_log` WHERE internal_invoice_line_id IN
                              (SELECT id FROM `internal_invoice_lines` WHERE created > ? AND internalInvoiceId IN
                                                                                             (SELECT id FROM internal_invoices WHERE invoice_type IN (?, ?, ?)))',
                [
                    self::CUTOFF_DATE,
                    InternalInvoice::TYPE_RMA,
                    InternalInvoice::TYPE_MIXED_STOCK,
                    InternalInvoice::TYPE_COURIER_LIST
                ],
            );
        $deletedCounts[self::INTERNAL_INVOICE_LINES_TABLE] = $this
            ->connection
            ->executeStatement(
                'DELETE FROM internal_invoice_lines WHERE created > ? AND internalInvoiceId IN
                                                         (SELECT id FROM internal_invoices WHERE invoice_type IN (?, ?, ?))',
                [
                    self::CUTOFF_DATE,
                    InternalInvoice::TYPE_RMA,
                    InternalInvoice::TYPE_MIXED_STOCK,
                    InternalInvoice::TYPE_COURIER_LIST
                ],
            );
        $deletedCounts[self::INTERNAL_INVOICES_TABLE] = $this
            ->connection
            ->executeStatement(
                'DELETE FROM internal_invoices WHERE created > ? AND invoice_type IN (?, ?, ?)',
                [
                    self::CUTOFF_DATE,
                    InternalInvoice::TYPE_RMA,
                    InternalInvoice::TYPE_MIXED_STOCK,
                    InternalInvoice::TYPE_COURIER_LIST
                ],
            );

        foreach ($deletedCounts as $table => $deletedCount) {
            if ($deletedCount !== $backupRowCounts[$table]) {
                throw new RuntimeException(
                    sprintf(
                        '%s rows backed up but %s deleted for table  %s',
                        $backupRowCounts[$table],
                        $deletedCount,
                        $table
                    )
                );
            }
        }
    }

    public function down(Schema $schema): void
    {
        $backupsQuery = $this
            ->connection
            ->executeQuery('SELECT * FROM `migration_backups` WHERE version = ?', [self::class]);

        $results = $backupsQuery->fetchAllAssociative();
        $results = [
            self::STOCK_LOG_TABLE => json_decode(gzdecode($results[0]['payload'])),
            self::INTERNAL_INVOICE_LINES_TABLE => json_decode(gzdecode($results[1]['payload'])),
            self::INTERNAL_INVOICES_TABLE => json_decode(gzdecode($results[2]['payload'])),
        ];
        foreach ($results as $table => $result) {
            $this->restoreFromBackup($table, $result);
            $this->write(sprintf('Restore for table %s finished.', $table));
        }
        $this->connection->executeStatement('DELETE FROM `migration_backups` WHERE version = ?', [self::class]);
    }

    protected function writeToBackup(string $query, array $bindings, string $table): int
    {
        $result = $this
            ->connection
            ->executeQuery($query, $bindings)
            ->fetchAllAssociative();
        $inserted = $this->connection->insert(self::BACKUP_TABLE, [
            'version' => self::class,
            // gzencode makes it 15x smaller
            'payload' => gzencode(json_encode($result, JSON_THROW_ON_ERROR)),
        ]);
        if ($inserted !== 1) {
            throw new RuntimeException('Insert of backup failed, rows affected: ' . $inserted);
        }
        $rowsBackedUp = count($result);
        $this->write(sprintf('Finished backing up %s rows from %s (%s)', $rowsBackedUp, $table, self::class));
        return $rowsBackedUp;
    }

    protected function restoreFromBackup(string $table, array $results): void
    {
        $results = array_map(static fn (stdClass $row) => array_values((array)$row), $results);
        $columnCount = count($results[0]);

        foreach (array_chunk($results, self::DOWN_CHUNK_SIZE) as $chunk) {
            $rowPlaceholders = array_fill(0, $columnCount, '?');
            $row = '(' . implode(',', $rowPlaceholders) . ')';
            $rows = array_fill(0, count($chunk), $row);
            $rows = implode(',' . PHP_EOL, $rows);
            $values = array_merge(...$chunk);

            $affected = $this->connection->executeStatement(
                'INSERT INTO `' . $table . '` VALUES ' . $rows,
                $values
            );
            if ($affected === 0) {
                throw new RuntimeException('Could not restore backup for table ' . $table);
            }
            $this->write('Finished restoration of ' . count($chunk) . ' rows for table ' . $table);
        }
    }
}
