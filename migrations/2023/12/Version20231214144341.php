<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231214144341 extends AbstractMigration
{
    private const SUPPLIER_GROUP_ID = 30;
    private const PACKING_SLIP_MAPPING = [
        'orderNumber' => 'orderNumber',
        'reference' => 'orderReference',
        'orderDate' => 'orderDate',
        'deliveryDate' => 'deliveryDate',
        'productCode' => 'productCode',
        'productName' => 'orderReference',
        'ean' => 'EAN',
        'amountOrdered' => 'amountOrdered',
        'amountDelivered' => 'amountDelivered',
        'serialnumbers' => 'serialNumbers',
    ];

    public function getDescription(): string
    {
        return 'CAM-2859 Aanpassing TSE pakbonnen mapping';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            update `cameranu`.`leveranciers_verzamel`
            set `packing_slip_mapping` = :packing_slip_mapping
            where `id` = :supplier_group_id',
            [
                'packing_slip_mapping' => json_encode(self::PACKING_SLIP_MAPPING, JSON_THROW_ON_ERROR),
                'supplier_group_id' => self::SUPPLIER_GROUP_ID,
            ]
        );
    }

    public function down(Schema $schema): void
    {
        //Point of no return
    }
}
