<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231219112106 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2762 Foreign keys for discount log tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            DELETE odl
            FROM cameranu.order_discount_log odl
            LEFT JOIN cameranu.bestelling_velden bv ON odl.ledger_field_id = bv.id
            WHERE bv.id IS NULL;
        ');

        $this->addSql('
            ALTER TABLE cameranu.order_discount_log
            CHANGE COLUMN discount_code_id discount_code_id INT(11) DEFAULT NULL;
        ');

        $this->addSql('
            ALTER TABLE order_discount_log
            ADD CONSTRAINT FK_6D6FE5A88D9F6D38
                FOREIGN KEY (order_id)
                REFERENCES cameranu.bestelling_naw (id)
                ON UPDATE CASCADE
                ON DELETE CASCADE
        ');
        $this->addSql('
            ALTER TABLE order_discount_log
            ADD CONSTRAINT FK_6D6FE5A84584665A
                FOREIGN KEY (product_id)
                REFERENCES cameranu.artikelen (id)
                ON UPDATE CASCADE
                ON DELETE SET NULL
        ');
        $this->addSql('
            ALTER TABLE order_discount_log
            ADD CONSTRAINT FK_6D6FE5A891D29306
                FOREIGN KEY (discount_code_id)
                REFERENCES cameranu.discountcodes (discountId)
                ON UPDATE CASCADE
                ON DELETE SET NULL
        ');
        $this->addSql('
            ALTER TABLE order_discount_log
            ADD CONSTRAINT FK_6D6FE5A81527024A
                FOREIGN KEY (ledger_field_id)
                REFERENCES cameranu.bestelling_velden (id)
                ON UPDATE CASCADE
                ON DELETE CASCADE
        ');
        $this->addSql('
            ALTER TABLE order_discount_log_stock
            ADD CONSTRAINT FK_F73FDEF68D9F6D38
                FOREIGN KEY (order_id)
                REFERENCES cameranu.bestelling_naw (id)
                ON UPDATE CASCADE
                ON DELETE CASCADE
        ');
        $this->addSql('
            ALTER TABLE order_discount_log_stock
            ADD CONSTRAINT FK_F73FDEF6DCD6110
                FOREIGN KEY (stock_id)
                REFERENCES cameranu.voorraad (id)
                ON UPDATE CASCADE
                ON DELETE CASCADE
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu.order_discount_log_stock DROP FOREIGN KEY FK_F73FDEF68D9F6D38;
            ALTER TABLE cameranu.order_discount_log_stock DROP FOREIGN KEY FK_F73FDEF6DCD6110;
        ');

        $this->addSql('
            ALTER TABLE cameranu.order_discount_log DROP FOREIGN KEY FK_6D6FE5A84584665A;
            ALTER TABLE cameranu.order_discount_log DROP FOREIGN KEY FK_6D6FE5A88D9F6D38;
            ALTER TABLE cameranu.order_discount_log DROP FOREIGN KEY FK_6D6FE5A81527024A;
            ALTER TABLE cameranu.order_discount_log DROP FOREIGN KEY FK_6D6FE5A891D29306;
        ');
    }
}
