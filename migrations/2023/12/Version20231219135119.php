<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231219135119 extends AbstractMigration
{
    public const TABLE = 'migration_backups';

    public function getDescription(): string
    {
        return 'CAM-2977 (part 1) Interne facturen genereren zorgt voor dubbele facturen';
    }

    public function up(Schema $schema): void
    {
        // In migrations table the version is the key, but as one migration may require multiple
        // rows in our backup (for whatever reason), we need an id.
        // Blob (binary) allows for most flexibility for the future (storing images if required etc).
        $this->addSql(
            'CREATE TABLE `cameranu`.`' . self::TABLE . '` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `version` varchar(255) NOT NULL,
  `payload` longblob NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`' . self::TABLE . '`;');
    }
}
