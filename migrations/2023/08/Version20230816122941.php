<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230816122941 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2218 veld huisnr vergroten';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`bestelling_naw` MODIFY `huisnr` varchar(32)');
        $this->addSql('ALTER TABLE `cameranu`.`webusers_addresses` MODIFY `huisnr` varchar(32)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`bestelling_naw` MODIFY `huisnr` varchar(10)');
        $this->addSql('ALTER TABLE `cameranu`.`webusers_addresses` MODIFY `huisnr` varchar(10)');
    }
}
