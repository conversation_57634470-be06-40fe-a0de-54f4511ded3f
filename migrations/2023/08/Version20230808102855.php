<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230808102855 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2009 Additionele bijdragen linken aan leveranciers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE
                `cameranu`.`additional_contributions`
            ADD COLUMN IF NOT EXISTS
                `supplier_group_id` INT(11) DEFAULT NULL
            AFTER
                `supplier_id`,
            ADD KEY `supplier_group_id` (`supplier_group_id`),
            ADD CONSTRAINT `supplier_group_id` FOREIGN KEY (`supplier_group_id`)
               REFERENCES `cameranu`.`leveranciers_verzamel` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`additional_contributions`
            DROP FOREIGN KEY IF EXISTS `supplier_group_id`
        ');
        $this->addSql('
            ALTER TABLE
                `cameranu`.`additional_contributions`
            DROP COLUMN IF EXISTS
                `supplier_group_id`;
        ');
    }
}
