<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230830091116 extends AbstractMigration
{
    public const SAMEDAY_TRANSPORTER_ID = 2;
    public const HANDLE_SAMEDAY_RESOURCE_ID = 4995;
    public function getDescription(): string
    {
        return 'CAM-248 rename sandd to trunkrs';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`sandd` RENAME `trunkrs`');
        $this->addSql('UPDATE `cameranu`.`vervoerders` SET `short` = \'trunkrs\', `long` = \'Trunkrs\' WHERE `id` = ' . self::SAMEDAY_TRANSPORTER_ID);
        $this->addSql('UPDATE `cameranu_urk`.`xs_resources` SET `name` = \'Trunkrs verwerken\', `shortDescription` = \'trunkrs-verwerken\' WHERE `resourceId` = ' . self::HANDLE_SAMEDAY_RESOURCE_ID);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`trunkrs` RENAME `sandd`');
        $this->addSql('UPDATE `cameranu`.`vervoerders` SET `short` = \'sandd\', `long` = \'Sandd\' WHERE `id` = ' . self::SAMEDAY_TRANSPORTER_ID);
        $this->addSql('UPDATE `cameranu_urk`.`xs_resources` SET `name` = \'Sandd verwerken\', `shortDescription` = \'sandd-verwerken\' WHERE `resourceId` = ' . self::HANDLE_SAMEDAY_RESOURCE_ID);

    }
}
