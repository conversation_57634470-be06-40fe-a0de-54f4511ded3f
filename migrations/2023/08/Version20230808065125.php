<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230808065125 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2143 betalingsvoorwaarde vullen bij zakelijke klanten waar dat niet ingevuld is';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("
            update webusers
            set payment_period_id = (select p.id from payment_period as p where p.`default` = 1 limit 1)
            where payment_period_id is null
            and customer_type not in ('consument', 'CNU-groep')
        ");
    }

    public function down(Schema $schema) : void
    {
        // jammer maar helaas
    }
}
