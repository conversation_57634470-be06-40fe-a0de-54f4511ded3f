<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230811133808 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2110 - <PERSON><PERSON><PERSON><PERSON> van andere leveranciers en retouren meenemen in claims';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('CREATE TABLE `claims_and_fees_supplier_groups` (
            `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
            `claims_and_fees_id` int(11) unsigned NOT NULL,
            `supplier_group_id` int(11) NOT NULL,
            PRIMARY KEY (`id`),
            KEY `claims_and_fees_id` (`claims_and_fees_id`),
            KEY `supplier_group_id` (`supplier_group_id`),
            UNIQUE KEY `claims_and_fees_supplier_group` (`claims_and_fees_id`,`supplier_group_id`),
            CONSTRAINT `claims_and_fees_supplier_groups_ibfk_1` FOREIGN KEY (`claims_and_fees_id`) REFERENCES `claims_and_fees` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
            CONSTRAINT `claims_and_fees_supplier_groups_ibfk_2` FOREIGN KEY (`supplier_group_id`) REFERENCES `leveranciers_verzamel` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

        $this->addSql('CREATE TABLE `claims_and_fees_suppliers` (
            `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
            `claims_and_fees_id` int(11) unsigned NOT NULL,
            `supplier_id` int(11) NOT NULL,
            PRIMARY KEY (`id`),
            KEY `claims_and_fees_id` (`claims_and_fees_id`),
            KEY `supplier_id` (`supplier_id`),
            UNIQUE KEY `claims_and_fees_supplier` (`claims_and_fees_id`,`supplier_id`),
            CONSTRAINT `claims_and_fees_suppliers_ibfk_1` FOREIGN KEY (`claims_and_fees_id`) REFERENCES `claims_and_fees` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
            CONSTRAINT `claims_and_fees_suppliers_ibfk_2` FOREIGN KEY (`supplier_id`) REFERENCES `leveranciers` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

        // Convert data from claims_and_fees.supplier_group_id
        $this->addSql('INSERT INTO `cameranu`.`claims_and_fees_supplier_groups` (`claims_and_fees_id`, `supplier_group_id`)
            SELECT `claims_and_fees`.`id` AS claims_and_fees_id,
                   `claims_and_fees`.`supplier_group_id` AS supplier_group_id
            FROM `cameranu`.`claims_and_fees`
            WHERE `claims_and_fees`.`supplier_group_id` IS NOT NULL');

        // Convert data from claims_and_fees.supplier_id
        $this->addSql('INSERT INTO `cameranu`.`claims_and_fees_suppliers` (`claims_and_fees_id`, `supplier_id`)
            SELECT `claims_and_fees`.`id` AS claims_and_fees_id,
                   `claims_and_fees`.`supplier_id` AS supplier_id
            FROM `cameranu`.`claims_and_fees`
            WHERE `claims_and_fees`.`supplier_id` IS NOT NULL');

        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees` DROP COLUMN IF EXISTS `supplier_group_id`');
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees` DROP COLUMN IF EXISTS `supplier_id`');

        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports` ADD COLUMN IF NOT EXISTS `supplier_group_id` INT(11) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        // Add column supplier_group and supplier to claims_and_fees
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees` ADD COLUMN IF NOT EXISTS `supplier_group_id` INT(11) DEFAULT NULL');
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees` ADD COLUMN IF NOT EXISTS `supplier_id` INT(11) DEFAULT NULL');

        // Reset data to claims_and_fees.supplier_group_id
        $this->addSql('UPDATE `cameranu`.`claims_and_fees` AS cf
                INNER JOIN (
                    SELECT `claims_and_fees_id`, `supplier_group_id`
                      FROM `cameranu`.`claims_and_fees_supplier_groups`
                  GROUP BY `claims_and_fees_id`
                ) as sg ON cf.`id` = sg.`claims_and_fees_id`
            SET cf.`supplier_group_id` = sg.`supplier_group_id`');

        // Reset data to claims_and_fees.supplier_id
        $this->addSql('UPDATE `cameranu`.`claims_and_fees` AS cf
                INNER JOIN (
                    SELECT `claims_and_fees_id`, `supplier_id`
                      FROM `cameranu`.`claims_and_fees_suppliers`
                  GROUP BY `claims_and_fees_id`
                ) as sg ON cf.`id` = sg.`claims_and_fees_id`
            SET cf.`supplier_id` = sg.`supplier_id`');

        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`claims_and_fees_supplier_groups`');
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`claims_and_fees_suppliers`');

        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports` DROP COLUMN IF EXISTS `supplier_group_id`');
    }
}
