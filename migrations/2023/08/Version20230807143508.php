<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230807143508 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2122 - Verwijderde voorraad tonen bij claims en vergoedingen';
    }

    public function up(Schema $schema) : void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports_rows` ADD COLUMN IF NOT EXISTS `removed` int(1) DEFAULT 0');
    }

    public function down(Schema $schema) : void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports_rows` DROP COLUMN IF EXISTS `removed`');
    }
}
