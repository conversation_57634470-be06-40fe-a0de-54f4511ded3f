<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\StockLocation;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230822091452 extends AbstractMigration
{
    private const GENERIC_DYNAMIC_DISCOUNTSET_ARTICLE = 3347226;

    public function getDescription(): string
    {
        return 'CAM-2233 Voorraad toevoegen voor dynamische voordeelset artikel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO
                `cameranu`.`articles_stock_per_location` (`product_id`, `stock_location_id`, `stock`)
            VALUES
                (' . $this::GENERIC_DYNAMIC_DISCOUNTSET_ARTICLE . ', ' . StockLocation::MAGAZIJN_URK . ', 999999999)
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM
                `cameranu`.`articles_stock_per_location`
            WHERE
                `product_id` = ' . $this::GENERIC_DYNAMIC_DISCOUNTSET_ARTICLE .  '
            AND
                `stock_location_id` = ' . StockLocation::MAGAZIJN_URK .  '
            AND
                `stock` > 0
        ');
    }
}
