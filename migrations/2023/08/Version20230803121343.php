<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230803121343 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1806 update session table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`sessions` MODIFY COLUMN `sess_lifetime` INTEGER UNSIGNED NOT NULL;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`sessions` MODIFY COLUMN `sess_lifetime` MEDIUMINT(9) NOT NULL');
    }
}
