<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230828081633 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2070 Inkooporders kunnen voorzien van Excel spreadsheet';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("
            ALTER TABLE `cameranu`.`leveranciers_verzamel`
            ADD COLUMN `email_attachment_file_type` VARCHAR(256) NOT NULL DEFAULT 'csv' AFTER `email`
        ");
        $this->write('Nieuwe kolom toegevoegd: "email_attachment_file_type" in tabel `leveranciers_verzamel`');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql("
            ALTER TABLE `cameranu`.`leveranciers_verzamel`
            DROP COLUMN `email_attachment_file_type`
        ");
        $this->write('De kolom: "email_attachment_file_type" van tabel `leveranciers_verzamel` is verwijderd');
    }
}
