<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230828081634 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2041 Hama inkooporders aanpassen naar .xlsx ipv .csv';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `email_attachment_file_type` = 'xlsx'
            WHERE `id` = :id
        ", ['id' => SupplierGroup::ID_HAMA_GROUP]);
        $this->write('Verzamel leverancier "Hama Group" zal bij het ontvangen van de inkooporder e-mail de bijlage als een Excel bestand krijgen');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql("
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `email_attachment_file_type` = 'csv'
            WHERE `id` = :id
        ", ['id' => SupplierGroup::ID_HAMA_GROUP]);
        $this->write('Verzamel leverancier "Hama Group" zal bij het ontvangen van de inkooporder e-mail de bijlage als een .csv bestand krijgen');
    }
}
