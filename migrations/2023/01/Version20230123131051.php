<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230123131051 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-3911 - Facturen TSE importeren';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `ubl_email` = :email,
                `uses_ubl` = :uses
            WHERE `id` = :id
        ', [
            'email' => '<EMAIL>',
            'uses' => true,
            'id' => SupplierGroup::ID_TSE_IMAGING,
        ]);

        $this->addSql('
            INSERT INTO `cameranu`.`ubl_leveranciers_verzamel_fields` (
                `name`,
                `leveranciers_verzamel_email`,
                `own_reference`,
                `customer_reference`,
                `line_reference`,
                `invoice_date`,
                `invoice_line`,
                `invoice_code`,
                `item_code`,
                `item_amount`,
                `invoice_amount`,
                `calculate_amount`
            ) VALUES (
                :name,
                :leveranciers_verzamel_email,
                :own_reference,
                :customer_reference,
                :line_reference,
                :invoice_date,
                :invoice_line,
                :invoice_code,
                :item_code,
                :item_amount,
                :invoice_amount,
                :calculate_amount
            )
        ', [
            'name' => 'TSE IMAGING',
            'leveranciers_verzamel_email' => '<EMAIL>',
            'own_reference' => 'ID',
            'customer_reference' => '',
            'line_reference' => 'InvoiceLine-OrderLineReference-OrderReference-ID',
            'invoice_date' => 'IssueDate',
            'invoice_line' => 'InvoiceLine',
            'invoice_code' => 'ID',
            'item_code' => 'InvoiceLine-Item-SellersItemIdentification-ID',
            'item_amount' => 'InvoiceLine-InvoicedQuantity',
            'invoice_amount' => 'InvoiceLine-Price-PriceAmount',
            'calculate_amount' => null,
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `ubl_email` = :email,
                `uses_ubl` = :uses
            WHERE `id` = :id
        ', [
            'id' => SupplierGroup::ID_TSE_IMAGING,
            'email' => null,
            'uses' => false
        ]);

        $this->addSql('
            DELETE FROM `ubl_leveranciers_verzamel_fields`
            WHERE `name` = :name
            AND `leveranciers_verzamel_email` = :email
        ', [
            'name' => 'TSE IMAGING',
            'email' => '<EMAIL>',
        ]);
    }
}
