<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230102092450 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-3880 - Voorraadgegevens Sony importeren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`leverancier_feeds` (`id`, `naam`, `verzamel_id`, `show`, `use_on_website`, `token`)
            VALUES (' . SupplierFeed::ID_SONY . ', "Sony", NULL, 0, 0, NULL);
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu`.`leverancier_feeds` WHERE `id` = ' . SupplierFeed::ID_SONY);
    }
}
