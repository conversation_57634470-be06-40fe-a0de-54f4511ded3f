<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230124082031 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-3902 mapping voor pakbonnen in de database';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('alter table `cameranu`.`leveranciers_verzamel` add column if not exists `packing_slip_mapping` text default null');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('alter table `cameranu`.`leveranciers_verzamel` drop column if exists `packing_slip_mapping`');
    }
}
