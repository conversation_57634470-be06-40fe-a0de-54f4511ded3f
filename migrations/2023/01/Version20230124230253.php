<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230124230253 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-3721 oAuth settings';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            ALTER TABLE `cameranu_urk`.`service_mailboxes`
            ADD COLUMN IF NOT EXISTS `oauth` TINYINT DEFAULT 0,
            ADD COLUMN IF NOT EXISTS `oauth_endpoint_version` VARCHAR(8) DEFAULT \'2.0\',
            ADD COLUMN IF NOT EXISTS `oauth_client_id` VARCHAR(255),
            ADD COLUMN IF NOT EXISTS `oauth_client_secret` VARCHAR(255)
        ');

    }

    public function down(Schema $schema) : void
    {
    }
}
