<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230124075711 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-3902 pakbonnen opslaan op basis van verzamelleverancier';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('alter table `cameranu`.`leveranciers_verzamel` add column if not exists `packing_slip_from_email` varchar(255) default null');
        $this->addSql('alter table `cameranu`.`packing_slip` add column if not exists `supplier_group_id` int(11) default null');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('alter table `cameranu`.`leveranciers_verzamel` drop column if exists `packing_slip_from_email`');
        $this->addSql('alter table `cameranu`.`packing_slip` drop column if exists `supplier_group_id`');
    }
}
