<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230103092438 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-4011 add column priceEx to bGenius and Adchieve productfeeds';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES 
                (\'prijs_ex_btw\', ' . ProductFeed::BGENIUS_ID . ' , \'priceEx\'),
                (\'prijs_ex_btw\', ' . ProductFeed::ADCHIEVE . ' , \'priceEx\')
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `cameranu`.`productfeed_fields`
            WHERE `key` = \'priceEx\'
            AND `feed_id` IN (' . ProductFeed::BGENIUS_ID . ', ' . ProductFeed::ADCHIEVE . ')');
    }
}
