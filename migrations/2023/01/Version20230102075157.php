<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230102075157 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-4041 - Boolean voor importeren leverancier voorraad feed';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`leverancier_feeds` ADD COLUMN IF NOT EXISTS `importable` tinyint(1) NOT NULL DEFAULT 0');

        $this->addSql('UPDATE `cameranu`.`leverancier_feeds` SET `importable` = 1 WHERE `id` IN (
            ' . SupplierFeed::ID_MAFICO . ',
            ' . SupplierFeed::ID_RINGFOTO . ',
            ' . SupplierFeed::ID_TSE . ',
            ' . SupplierFeed::ID_INGRAM_MICRO . ',
            ' . SupplierFeed::ID_JUPIO . ',
            ' . SupplierFeed::ID_AVIOS . ',
            ' . SupplierFeed::ID_SIGMA . '
        )');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`leverancier_feeds` DROP COLUMN IF EXISTS `importable`');

    }
}
