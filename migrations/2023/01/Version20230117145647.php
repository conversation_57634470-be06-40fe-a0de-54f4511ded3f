<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Tag;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230117145647 extends AbstractMigration
{
    private const QUERY_PARAMETERS_TAG_TYPES = [
        'tagTypeId' => Tag::TYPE_ID_ACCESSORYSETS,
        'type' => 'accessorysets'
    ];

    public function getDescription() : string
    {
        return 'CAT-4136 Neuwe tagType migration';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('INSERT INTO `cameranu`.`tagTypes` (`tagTypeId`, `type`) VALUES (:tagTypeId, :type)', self::QUERY_PARAMETERS_TAG_TYPES);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu`.`tagTypes` WHERE `tagTypeId` = :tagTypeId AND `type` = :type',self::QUERY_PARAMETERS_TAG_TYPES);
    }
}
