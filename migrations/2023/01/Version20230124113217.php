<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230124113217 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-3838 serienummers voor pakbonnen in koppeltabel';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            create table `cameranu`.`packing_slip_line_serial_numbers` (
              `id` int(11) unsigned not null auto_increment,
              `packing_slip_line_id` int(11) default null,
              `serial_number` varchar(255) default null,
              `scanned` tinyint(1) not null default 0,
              primary key (`id`),
              key `packing_slip_line_id` (`packing_slip_line_id`)
            ) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('drop table if exists `cameranu`.`packing_slip_line_serial_numbers`');
    }
}
