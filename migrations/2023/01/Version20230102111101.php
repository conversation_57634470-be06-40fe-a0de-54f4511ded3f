<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230102111101 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-4106 USP\' block type toevoegen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
            VALUES (NOW(), NOW(), \'usps\', 70, \'Content (Rebranding)\')');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = \'usps\'');
    }
}
