<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230126072019 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-4142 ingescande voorraadregels tonen bij pakbon';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`packing_slip_stock` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `stock_id` int(11) NOT NULL,
              `packing_slip_line_id` int(11) NOT NULL,
              PRIMARY KEY (`id`),
              KEY `stock_idx` (`stock_id`),
              KEY `packing_slip_line_idx` (`packing_slip_line_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`packing_slip_stock`');
    }
}
