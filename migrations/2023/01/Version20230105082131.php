<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230105082131 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-3896 - Voorraadgegevens ALSO importeren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`leverancier_feeds`
                (`id`, `naam`, `verzamel_id`, `show`, `use_on_website`, `token`, `importable`)
            VALUES (' . SupplierFeed::ID_ALSO . ', "ALSO", null, 0, 0, NULL, true);
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu`.`leverancier_feeds` WHERE id = ' . SupplierFeed::ID_ALSO);
    }
}
