<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use JsonException;
use Webdsign\GlobalBundle\Entity\PackingSlip;

final class Version20230124082301 extends AbstractMigration
{
    private const SUPPLIER_GROUP_ID = 404;
    private const PACKING_SLIP_MAPPING = [
        'supplierPackingSlipId' => 'Pakbonnummer',
        'orderNumber' => 'Ordernummer',
        'reference' => 'UwReferentie',
        'orderDate' => 'DatumBesteld',
        'deliveryDate' => 'Leverdatum',
        'productCode' => 'Artikelnummer',
        'productName' => 'Artikelomschrijving',
        'ean' => 'EAN',
        'amountOrdered' => 'AantalBesteld',
        'noteAmount' => 'AantalInNota',
        'amountDelivered' => 'AantalGeleverd',
    ];

    public function getDescription() : string
    {
        return 'CAT-3902 Disnet toevoegen aan pakbonmapping';
    }

    /**
     * @throws JsonException
     */
    public function up(Schema $schema) : void
    {
        $this->addSql('
            update `cameranu`.`leveranciers_verzamel`
            set `packing_slip_mapping` = :packing_slip_mapping,
                `packing_slip_from_email` = :packing_slip_email
            where `id` = :supplier_group_id',
            [
                'packing_slip_email' => PackingSlip::DISNET_MAIL,
                'packing_slip_mapping' => json_encode(self::PACKING_SLIP_MAPPING, JSON_THROW_ON_ERROR),
                'supplier_group_id' => self::SUPPLIER_GROUP_ID,
            ]
        );
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            update `cameranu`.`leveranciers_verzamel`
            set `packing_slip_mapping` = :packing_slip_mapping,
                `packing_slip_from_email` = :packing_slip_email
            where `id` = :supplier_group_id',
            [
                'packing_slip_email' => null,
                'packing_slip_mapping' => null,
                'supplier_group_id' => self::SUPPLIER_GROUP_ID,
            ]
        );
    }
}
