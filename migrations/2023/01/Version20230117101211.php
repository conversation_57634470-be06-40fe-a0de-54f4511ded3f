<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230117101211 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-3814 add isScanned field op serialNumbers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`packing_slip_line_serial_numbers` ADD COLUMN IF NOT EXISTS `scanned` tinyint(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`packing_slip_line_serial_numbers` DROP COLUMN IF  EXISTS `scanned`');
    }
}
