<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230109154129 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-3838 Add table to save packing slip serial-numbers';
    }

    public function up(Schema $schema): void
    {
       $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`packing_slip_line_serial_numbers` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `packing_slip_line_id` int(11) DEFAULT NULL,
          `serial_number` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `packing_slip_line_id` (`packing_slip_line_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE iF EXISTS `cameranu`.`packing_slip_line_serial_numbers`');
    }
}
