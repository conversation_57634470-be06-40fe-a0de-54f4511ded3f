<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Driver\Connection;
use Doctrine\DBAL\FetchMode;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use RuntimeException;
use Webdsign\GlobalBundle\Entity\SpecsArticleProfile;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230614122816 extends AbstractMigration
{
    private const DIRECTORY_TEMP = 'temp';
    private const FILE_ARTICLE_IDS = 'Version20230614122816_article_ids.txt';
    private const FROM = SpecsArticleProfile::ID_COMPACT_CAMERA;
    private const TO = SpecsArticleProfile::ID_GENERAL_PHOTOGRAPHIC_CAMERA;

    public function getDescription() : string
    {
        return 'CAM-1642 Specificatieprofiel kopiëren (Compact camera - niet meer toepassen)';
    }

    public function preUp(Schema $schema): void
    {
        $this->createDirectory($this->getAbsolutePathOfDirectoryTemp());

        $specsArticleProfiles = $this->getSpecsArticleProfilesBy([self::FROM]);
        $bytesWritten = $this->saveSpecsArticleProfilesToTempFile($specsArticleProfiles);

        $invalidArticleIdsFile = $this->isValidArticleIdsFile() === false;
        $this->warnIf($bytesWritten === 0, 'Nothing written to temp file');
        $this->abortIf($invalidArticleIdsFile, 'Unable to store article ids');
    }

    public function up(Schema $schema) : void
    {
        $articleIds = $this->getArticleIds();
        foreach ($articleIds as $articleId) {
            $this->addSql('DELETE FROM `cameranu`.`specs_article_profile` WHERE articleId = :articleId AND profileId = :profileId', [
                'articleId' => $articleId,
                'profileId' => self::FROM,
            ]);

            $this->addSql('INSERT IGNORE INTO `cameranu`.`specs_article_profile` (`articleId`, `profileId`) VALUES (:articleId, :profileId)', [
                'articleId' => $articleId,
                'profileId' => self::TO,
            ]);
        }
    }

    public function down(Schema $schema) : void
    {
        $articleIds = $this->getArticleIds();
        foreach ($articleIds as $articleId) {
            $this->addSql('DELETE FROM `cameranu`.`specs_article_profile` WHERE articleId = :articleId AND profileId = :profileId', [
                'articleId' => $articleId,
                'profileId' => self::TO,
            ]);
            $this->addSql('INSERT IGNORE INTO `cameranu`.`specs_article_profile` (`articleId`, `profileId`) VALUES (:articleId, :profileId)', [
                'articleId' => $articleId,
                'profileId' => self::FROM,
            ]);
        }
    }
    private function getSpecsArticleProfilesBy(array $array): array
    {
        $statement = $this->getConnection()->prepare('
            SELECT * FROM cameranu.specs_article_profile
            WHERE profileId IN( ? )
        ');
        $statement->execute($array);
        return $statement->fetchAll(FetchMode::ASSOCIATIVE);
    }

    private function getConnection(): Connection
    {
        $connection = $this->connection->getWrappedConnection();
        if ($connection === null) {
            $this->abortIf(true, 'Unable to get wrapped connection');
        }
        return $connection;
    }

    private function createDirectory(string $directory): void
    {
        if (is_dir($directory)) {
            return;
        }

        $success = mkdir($directory, 0777, true);
        if ($success === false) {
            throw new RuntimeException(
                sprintf('Directory "%s" was not created', $directory)
            );
        }
    }

    private function saveSpecsArticleProfilesToTempFile(array $specsArticleProfiles): int
    {
        $bytesWritten = 0;
        $filePointer = fopen($this->getAbsolutePathOfFileArticleIdTextFile(), 'wb');
        foreach ($specsArticleProfiles as $specsArticleProfile) {
            $articleId = (int)($specsArticleProfile['articleId'] ?? 0);
            if ($articleId === 0) {
                continue;
            }
            $numberOfBytesWritten  = fwrite($filePointer, $articleId . PHP_EOL);
            if ($numberOfBytesWritten !== false) {
                $bytesWritten += $numberOfBytesWritten;
            }
        }
        fclose($filePointer);
        return $bytesWritten;
    }

    private function isValidArticleIdsFile(): bool
    {
        return file_exists($this->getAbsolutePathOfFileArticleIdTextFile());
    }

    private function getAbsolutePathOfDirectoryTemp(): string
    {
        return sprintf('%s/%s', __DIR__, self::DIRECTORY_TEMP);
    }
    private function getAbsolutePathOfFileArticleIdTextFile(): string
    {
        return sprintf('%s/%s', $this->getAbsolutePathOfDirectoryTemp(), self::FILE_ARTICLE_IDS);
    }

    private function getArticleIds(): array
    {
        $filePointer = fopen($this->getAbsolutePathOfFileArticleIdTextFile(), 'rb');

        $articleIds = [];
        while (($line = fgets($filePointer)) !== false) {
            $articleId = (int)$line;
            if ($articleId === 0) {
                continue;
            }
            $articleIds[] = $articleId;
        }
        fclose($filePointer);

        return $articleIds;
    }
}
