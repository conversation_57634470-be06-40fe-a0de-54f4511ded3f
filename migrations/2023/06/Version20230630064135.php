<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230630064135 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1870 Aanvullen wex categorieën';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (1, 105, \'Digital Backs\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (1, 106, \'Film Cameras\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (4, 402, \'Desktops\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (4, 415, \'Mobile Phones\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (4, 416, \'Printers\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (6, 611, \'Studio Accessories\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1101,	\'Used Cameras\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1102,	\'Used Lenses\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1103,	\'Used Accessories\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1104,	\'Used Computing\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1105,	\'Used Lighting & Studio\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1106,	\'Used Video\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1108,	\'Used Optics\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (11, 1109,	\'Used Astronomy\');
            -- INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (12, 1201,	\'Rental Cameras\');
            -- INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (12, 1202,	\'Rental Lenses\');
            -- INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (12, 1203,	\'Rental Video\');
            -- INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (12, 1204,	\'Rental Lighting & Studio\');
            -- INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (12, 1205,	\'Other Rental\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (13, 1302,	\'Warranties\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (13, 1304,	\'Repairs Outside\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (13, 1305,	\'Workshops & Events\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (13, 1306,	\'Computer support & Maintenance\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (13, 1307,	\'Gift cards\');
            INSERT INTO `cameranu`.`wex_subgroepen` (`wex_maingroup_id`, `id`, `description`) VALUES (13, 1309,	\'Photography\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 105;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 106;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 402;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 415;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 416;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 611;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1101;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1102;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1103;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1104;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1105;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1106;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1108;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1109;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1201;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1202;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1203;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1204;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1205;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1302;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1304;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1305;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1306;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1307;
            DELETE FROM `cameranu`.`wex_subgroepen` WHERE `id` = 1309;
        ');
    }
}
