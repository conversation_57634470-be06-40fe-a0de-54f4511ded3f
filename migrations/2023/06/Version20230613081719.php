<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230613081719 extends AbstractMigration
{
    private const GENERIC_DYNAMIC_DISCOUNTSET_PRODUCT = 3347226;

    public function getDescription(): string
    {
        return 'CAM-1327 Voordeelsets';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`discountsets` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `spec_profile_id` int(11) NOT NULL,
              `created_on` datetime DEFAULT NULL,
              `modified_on` datetime DEFAULT NULL,
              `created_by` int(11) DEFAULT NULL,
              `updated_by` int(11) DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `spec_profile_id` (`spec_profile_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
        $this->addSql('
            CREATE TABLE `cameranu`.`discountset_positions` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `position` int(11) NOT NULL,
              `discountset_id` int(11) NOT NULL,
              `spec_profile_id` int(11) NOT NULL,
              `product_id` int(11) DEFAULT NULL,
              `specs_specification_id` int(11) DEFAULT NULL,
              `specs_specification_value` varchar(255) DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
        $this->addSql('
            CREATE TABLE `cameranu`.`discountset_discounts` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `discountset_id` int(11) NOT NULL,
              `amount_of_products` int(11) NOT NULL,
              `discount` decimal(10,2) NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
        $this->addSql('
            ALTER TABLE `cameranu`.`shopcart`
            ADD COLUMN `dynamic_discountset_key` varchar(50) DEFAULT NULL;
        ');
        $this->addSql('
            UPDATE `cameranu`.`artikelen` SET flags = 553656595 WHERE id = ' . $this::GENERIC_DYNAMIC_DISCOUNTSET_PRODUCT);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP TABLE `cameranu`.`discountset_discounts`;
        ');
        $this->addSql('
            DROP TABLE `cameranu`.`discountset_products`;
        ');
        $this->addSql('
            DROP TABLE `cameranu`.`discountsets`;
        ');
        $this->addSql('
            ALTER TABLE `cameranu`.`shopcart`
            DROP COLUMN `dynamic_discountset_key`;
        ');
        $this->addSql('
            UPDATE `cameranu`.`artikelen` SET flags = 16785425 WHERE id = ' . $this::GENERIC_DYNAMIC_DISCOUNTSET_PRODUCT);
    }
}
