<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230606094555 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1805 - Extra betaaltermijnen op klantkaart';
    }

    public function up(Schema $schema): void
    {
        // Insert payment_period rows
        $this->addSql(
            'INSERT INTO `cameranu`.`payment_period` (`id`, `days`, `default`) VALUES
            (6, "45", 0),
            (7, "60", 0)'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`payment_period` WHERE `id` IN (6, 7)');
    }
}
