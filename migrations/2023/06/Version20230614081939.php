<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230614081939 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1559 Add menu item column to FrontpageMenu';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`frontpage_menu`
                ADD COLUMN IF NOT EXISTS `menuItemId` INT(11) DEFAULT NULL,
                ADD CONSTRAINT `frontpage_menu_menu_item_fk` FOREIGN KEY IF NOT EXISTS (`menuItemId`)
                    REFERENCES `cameranu`.`menuItems` (`menuItemId`);
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`frontpage_popularMenu`
                ADD COLUMN IF NOT EXISTS `menuItemId` INT(11) DEFAULT NULL,
                ADD CONSTRAINT `frontpage_popularMenu_menu_item_fk` FOREIGN KEY IF NOT EXISTS (`menuItemId`)
                    REFERENCES `cameranu`.`menuItems` (`menuItemId`);
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`frontpage_menu`
                DROP FOREIGN KEY IF EXISTS `frontpage_menu_menu_item_fk`,
                DROP COLUMN IF EXISTS `menuItemId`;
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`frontpage_popularMenu`
                DROP FOREIGN KEY IF EXISTS `frontpage_popularMenu_menu_item_fk`,
                DROP COLUMN IF EXISTS `menuItemId`;
        ');
    }
}
