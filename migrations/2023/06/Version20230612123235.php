<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230612123235 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1839 add block type popular filters';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
            VALUES
            (current_timestamp, current_timestamp(), \'popularFilters\', 80, \'Content (Rebranding)\')');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = \'popularFilters\'');
    }
}
