<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230605141222 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1600 add column for image url table menuItems';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`menuItems` ADD COLUMN IF NOT EXISTS `image_url` varchar(255) NOT NULL DEFAULT \'\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`menuItems` DROP COLUMN IF EXISTS `image_url`');
    }
}
