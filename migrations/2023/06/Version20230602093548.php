<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230602093548 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1512 add dashboard task categorie for empty product lists';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`dashboard_taskCategories` (`categoryId`, `name`, `description`, `defaultTaskType`)
            VALUES
            (233, \'Lege productlijsten\', \'Productlijst blokken die online staan maar geen producten bevatten\', \'pageTask\');
        ');

        $this->addSql('
            INSERT INTO `cameranu`.`dashboard_taskCategories_departments` (`categoryId`, `groupId`)
            SELECT 233, `groupId` FROM `cameranu`.`dashboard_taskCategories_departments` WHERE `categoryId`= 230
        ');

        $this->addSql('
            INSERT INTO `cameranu`.`dashboard_taskCategories_controlGroups` (`categoryId`, `controlGroupId`)
            SELECT 233, `controlGroupId` FROM `cameranu`.`dashboard_taskCategories_controlGroups` WHERE `categoryId`= 230
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`dashboard_taskCategories` WHERE `categoryId` = 233');
        $this->addSql('DELETE FROM `cameranu`.`dashboard_taskCategories_controlGroups` WHERE `categoryId` = 233');
        $this->addSql('DELETE FROM `cameranu`.`dashboard_taskCategories_departments` WHERE `categoryId` = 233');
    }
}
