<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\StockLocation;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230605074500 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1790 verhuur voor AMS aanzetten';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE `cameranu`.`stock_locations` SET `is_available_for_rentals` = 1 WHERE `id` = ' . StockLocation::AMS_STORE);

    }

    public function down(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`stock_locations` SET `is_available_for_rentals` = 0 WHERE `id` = ' . StockLocation::AMS_STORE);
    }
}
