<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230620123323 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1559';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `frontpage_menuSubmenu` (
                `frontpageMenuId` INT(11),
                `menuItemId` INT(11),
                PRIMARY KEY (`frontpageMenuId`, `menuItemId`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            ALTER TABLE `cameranu`.`frontpage_menuSubmenu`
                ADD CONSTRAINT `frontpage_menuSubmenu_frontpage_menu_fk` FOREIGN KEY (`frontpageMenuId`)
                    REFERENCES `cameranu`.`frontpage_menu` (`id`) ON DELETE CASCADE,
                ADD CONSTRAINT `frontpage_menuSubmenu_menu_item_fk` FOREIGN KEY (`menuItemId`)
                    REFERENCES `cameranu`.`menuItems` (`menuItemId`) ON DELETE CASCADE;
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            DROP TABLE IF EXISTS `frontpage_menuSubmenu`;
        ');
    }
}
