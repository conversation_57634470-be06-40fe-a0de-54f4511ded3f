<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230607141035 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1442 Action banners beheren';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`frontpage_action_banner` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `position` int(11) NOT NULL DEFAULT 0,
              `created_on` datetime NOT NULL DEFAULT current_timestamp(),
              `updated` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              `created_by` int(11) NOT NULL,
              `updated_by` int(11) DEFAULT NULL,
              `domain` enum(\'cameranu.nl\',\'cameranu.be\') DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
        $this->addSql('
            CREATE TABLE `cameranu`.`frontpage_action_banner_translations` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `frontpage_action_banner_id` int(11) NOT NULL,
              `language` varchar(255) NOT NULL,
              `title` varchar(255) NOT NULL,
              `subtitle` varchar(255) DEFAULT NULL,
              `button` varchar(255) DEFAULT NULL,
              `image` varchar(255) DEFAULT NULL,
              `url` varchar(255) NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `frontpage_action_banner_translations_UN` (`language`,`frontpage_action_banner_id`),
              KEY `frontpage_action_banner_translations_FK` (`frontpage_action_banner_id`),
              CONSTRAINT `frontpage_action_banner_translations_FK` FOREIGN KEY (`frontpage_action_banner_id`) REFERENCES `frontpage_action_banner` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`frontpage_action_banner_translations`;');
        $this->addSql('DROP TABLE `cameranu`.`frontpage_action_banner`;');
    }
}
