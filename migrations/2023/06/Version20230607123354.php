<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230607123354 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1796 Entities voor AFAS logs';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("
            CREATE TABLE IF NOT EXISTS `afas_logs` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `type` enum('error','success') COLLATE utf8mb4_unicode_ci NOT NULL,
              `date` datetime NOT NULL,
              `order_id` int(11) DEFAULT NULL,
              `customer_id` int(11) DEFAULT NULL,
              `endpoint` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `payload` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `response` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `processed` int(1) DEFAULT 0,
              PRIMARY KEY (`id`),
              KEY `afas_logs_order_id_IDX` (`order_id`) USING BTREE,
              KEY `afas_logs_customer_id_IDX` (`customer_id`) USING BTREE,
              KEY `afas_logs_date_IDX` (`date`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`afas_logs`');
    }
}
