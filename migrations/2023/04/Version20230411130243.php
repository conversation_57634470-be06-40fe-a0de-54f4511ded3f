<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230411130243 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1352 add temp shopcart table for secondhand';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`second_hand_temp_shopcart` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `session_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
          `stock_id` int(11) DEFAULT NULL,
          `info` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
          `user_id` int(11) DEFAULT NULL,
          `type` enum(\'exchange\', \'buy\') COLLATE utf8mb4_unicode_ci NOT NULL,
          `price` decimal(12,3) DEFAULT NULL,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS  `cameranu`.`second_hand_temp_shopcart`');
    }
}
