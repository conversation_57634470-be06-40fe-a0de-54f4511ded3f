<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Domain;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230412083657 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1463 - CameraNU.nl referenties (adres_footer)';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`domeinen` 
                           SET `adres_footer` = REPLACE(`adres_footer`, :adres_footer_from, :adres_footer_to) 
                           WHERE `id` = :id', [
            'adres_footer_from' => 'CameraNU.nl B.V.',
            'adres_footer_to' => 'Cameranu B.V.',
            'id' => Domain::CAMERANU,
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`domeinen` 
                           SET `adres_footer` = REPLACE(`adres_footer`, :adres_footer_from, :adres_footer_to) 
                           WHERE `id` = :id', [
            'adres_footer_from' => 'Cameranu B.V.',
            'adres_footer_to' => 'CameraNU.nl B.V.',
            'id' => Domain::CAMERANU,
        ]);
    }
}
