<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Domain;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230412083348 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1463 - CameraNU.nl referenties (maildomeinnaam)';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `maildomeinnaam` = :maildomeinnaam WHERE `id` = :id', [
            'maildomeinnaam' => 'Cameranu.nl',
            'id' => Domain::CAMERANU,
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `maildomeinnaam` = :maildomeinnaam WHERE `id` = :id', [
            'maildomeinnaam' => 'CameraNU.nl',
            'id' => Domain::CAMERANU,
        ]);
    }
}
