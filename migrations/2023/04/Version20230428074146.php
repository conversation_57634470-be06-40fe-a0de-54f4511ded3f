<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230428074146 extends AbstractMigration
{
    private const INSERTS = [
        [
            'name' => 'Events',
            'name_en' => 'Events',
            'category' => 2,
        ],
        [
            'name' => 'Smartphone',
            'name_en' => 'Smartphone',
            'category' => 14,
        ],
        [
            'name' => 'OM System',
            'name_en' => 'OM System',
            'category' => 8,
        ],
        [
            'name' => 'Architectuur',
            'name_en' => 'Architecture',
            'category' => 6,
        ],
        [
            'name' => 'Auto',
            'name_en' => 'Car',
            'category' => 6,
        ],
        [
            'name' => 'Experimenteel',
            'name_en' => 'Experimental',
            'category' => 6,
        ],
        [
            'name' => 'Luchtvaart',
            'name_en' => 'Aviation',
            'category' => 6,
        ],
        [
            'name' => 'Fashion',
            'name_en' => 'Fashion',
            'category' => 6,
        ],
        [
            'name' => 'Portret',
            'name_en' => 'Portrait',
            'category' => 6,
        ],
        [
            'name' => 'Event',
            'name_en' => 'Event',
            'category' => 6,
        ],
        [
            'name' => 'Studio',
            'name_en' => 'Studio',
            'category' => 6,
        ],
        [
            'name' => 'Trouwen',
            'name_en' => 'Wedding',
            'category' => 6,
        ],
    ];

    private const UPDATES = [
        55 => [
            'new_name' => 'Onderzoek',
            'old_name' => 'Klantonderzoek'
        ],
        110 => [
            'new_name' => 'Drone',
            'old_name' => 'Drones'
        ],
        315 => [
            'new_name' => 'Verrekijkers',
            'old_name' => 'Verrekijker'
        ],
        225 => [
            'new_name' => 'DJI',
            'old_name' => 'Dji'
        ],
        105 => [
            'new_name' => 'Documentaire / Reportage',
            'old_name' => 'Documentaires'
        ],
        150 => [
            'new_name' => 'Natuur (Overig)',
            'old_name' => 'Natuur'
        ],
        155 => [
            'new_name' => 'Producten',
            'old_name' => 'Product'
        ],
        95 => [
            'new_name' => 'Zakelijk / Commercieel',
            'old_name' => 'Commercieel'
        ],
        175 => [
            'new_name' => 'Straat / Urban',
            'old_name' => 'Straat'
        ],
        160 => [
            'new_name' => 'Reizen',
            'old_name' => 'Reis'
        ],

    ];

    public function getDescription(): string
    {
        return 'CAM-1598 add squeezely key, change some names and add some new mailing preferences';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`mailing_types` ADD COLUMN IF NOT EXISTS  `squeezely_key` varchar(255) DEFAULT NULL');

        foreach (self::INSERTS as $insert) {
            $this->addSql('INSERT INTO `cameranu`.`mailing_types` (`name`, `name_en`, `active`, `mailing_category_id`)
                VALUES ("' . $insert['name'] . '", "' . $insert['name_en'] . '", 1, ' . $insert['category'] . ')
            ');
        }

        $this->addSql('
            UPDATE `cameranu`.`mailing_types`
            SET `squeezely_key` = CONCAT(\'custom_mailpref_\',  LOWER(REPLACE(`name`, \' \', \'_\')))
        ');

        foreach (self::UPDATES as $id => $update) {
            $this->addSql('UPDATE `cameranu`.`mailing_types` SET name = "' . $update['new_name'] . '" WHERE id = ' . $id);
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`mailing_types` DROP COLUMN IF EXISTS `squeezely_key`');

        foreach (self::INSERTS as $insert) {
            $this->addSql('DELETE FROM `cameranu`.`mailing_types` WHERE name = "' . $insert['name'] . '" AND mailing_category_id = ' . $insert['category']);
        }

        foreach (self::UPDATES as $id => $update) {
            $this->addSql('UPDATE `cameranu`.`mailing_types` SET name = "' . $update['old_name'] . '" WHERE id = ' . $id);
        }
    }
}
