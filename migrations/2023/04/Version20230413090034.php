<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230413090034 extends AbstractMigration
{
    private const MAIL_CONTENT_TEXT_SCHADEBRIEF_NL = 1914;

    public function getDescription() : string
    {
        return 'CAM-1463 - CameraNU.nl referenties (mail_content2.bericht)';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`mail_content2` 
                           SET `bericht` = REPLACE(`bericht`, :from, :to) 
                           WHERE `id` = :id', [
            'from' => 'CameraNU.nl B.V.',
            'to' => 'Cameranu B.V.',
            'id' => self::MAIL_CONTENT_TEXT_SCHADEBRIEF_NL,
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`mail_content2` 
                           SET `bericht` = REPLACE(`bericht`, :from, :to) 
                           WHERE `id` = :id', [
            'from' => 'Cameranu B.V.',
            'to' => 'CameraNU.nl B.V.',
            'id' => self::MAIL_CONTENT_TEXT_SCHADEBRIEF_NL,
        ]);
    }
}
