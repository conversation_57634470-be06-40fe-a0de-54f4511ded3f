<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230405093953 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1135 add table with quality states for secondhand products';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`second_hand_state` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `quality` int(11) NOT NULL DEFAULT 0,
          `description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT \'unknown\',
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');

        //TODO juiste state descriptions
        $this->addSql('
            INSERT INTO `cameranu`.`second_hand_state` (`quality`, `description`)
            VALUES
                (10, \'Zo goed als nieuw\'),
                (9, \'Netjes\'),
                (8, \'Wat gebruikerssporen\'),
                (7, \'Hier en daar wat krasjes\'),
                (6, \'Kan nog net\')
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`second_hand_state`');
    }
}
