<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230428140957 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1496 add column business_account_manager to webusers table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`webusers` ADD COLUMN IF NOT EXISTS `business_account_manager_id` int(11)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`webusers` DROP COLUMN `business_account_manager_id`');
    }
}
