<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230404134820 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1429 position veld toevoegen aan frontpage_hero_banners';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            ALTER TABLE frontpage_hero_banners ADD COLUMN `position` int(11) NOT NULL DEFAULT 0 AFTER `id`;
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            ALTER TABLE frontpage_hero_banners DROP COLUMN `position`;
        ');
    }
}
