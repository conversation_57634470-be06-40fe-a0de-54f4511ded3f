<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230403103030 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1143 Add new group_type for second-hand';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`artikelgroepen_type` (`id`, `name`, `description`, `added`, `changed`)
            VALUES(71, \'Tweedehands\', \'"Kies staat"\', NOW(), NOW());
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`artikelgroepen_type` WHERE `name` = \'Tweedehands\'');
    }
}
