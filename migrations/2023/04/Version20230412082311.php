<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Domain;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230412082311 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1463 - CameraNU.nl referenties (domeinnaam)';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu',
            'id' => Domain::CAMERANU,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Amsterdam Centrum',
            'id' => Domain::CAMERANU_AMSTERDAM,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Apeldoorn',
            'id' => Domain::CAMERANU_APELDOORN,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Groningen',
            'id' => Domain::CAMERANU_GRONINGEN,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Eindhoven',
            'id' => Domain::CAMERANU_EINDHOVEN,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Rotterdam Centrum',
            'id' => Domain::CAMERANU_ROTTERDAM,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Amsterdam Zuidoost',
            'id' => Domain::CAMERANU_AMSTERDAM_PRO,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Rotterdam Noord',
            'id' => Domain::CAMERANU_ROTTERDAM_PRO,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'Cameranu Antwerpen',
            'id' => Domain::CAMERANU_ANTWERPEN,
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU',
            'id' => Domain::CAMERANU,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Amsterdam Centrum',
            'id' => Domain::CAMERANU_AMSTERDAM,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Apeldoorn',
            'id' => Domain::CAMERANU_APELDOORN,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Groningen',
            'id' => Domain::CAMERANU_GRONINGEN,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Eindhoven',
            'id' => Domain::CAMERANU_EINDHOVEN,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Rotterdam Centrum',
            'id' => Domain::CAMERANU_ROTTERDAM,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Amsterdam Zuidoost',
            'id' => Domain::CAMERANU_AMSTERDAM_PRO,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Rotterdam Noord',
            'id' => Domain::CAMERANU_ROTTERDAM_PRO,
        ]);
        $this->addSql('UPDATE `cameranu`.`domeinen` SET `domeinnaam` = :domeinnaam WHERE `id` = :id', [
            'domeinnaam' => 'CameraNU Antwerpen',
            'id' => Domain::CAMERANU_ANTWERPEN,
        ]);
    }
}
