<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230419085400 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1369 create tables for second-hand prices';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS
                `cameranu`.`second_hand_prices` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `product_id` int(11) NOT NULL,
                    `intake_price` decimal(12,2) NOT NULL DEFAULT 0.00,
                    `sales_price` decimal(12,2) NOT NULL DEFAULT 0.00,
                    `date_created` datetime NOT NULL DEFAULT current_timestamp(),
                    `date_changed` datetime NOT NULL DEFAULT current_timestamp(),
                    `is_base_price` tinyint(1) NOT NULL DEFAULT 0,
                PRIMARY KEY (`id`),
                <PERSON><PERSON>Y `product_id` (`product_id`),
                CONSTRAINT `second_hand_prices_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `artikelen` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');

        $this->addSql('
            CREATE TABLE IF NOT EXISTS
                `cameranu`.`second_hand_formula` (
                    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                    `price_id` int(11) unsigned NOT NULL,
                    `state_id` int(11) unsigned NOT NULL,
                    `intake_formula` int(2) NOT NULL,
                    `sales_formula` int(2) NOT NULL,
                PRIMARY KEY (`id`),
                KEY `price_id` (`price_id`),
                KEY `state_id` (`state_id`),
                CONSTRAINT `second_hand_formula_ibfk_1` FOREIGN KEY (`price_id`) REFERENCES `second_hand_prices` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT `second_hand_formula_ibfk_2` FOREIGN KEY (`state_id`) REFERENCES `second_hand_state` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`second_hand_formula`');
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`second_hand_prices`');
    }
}
