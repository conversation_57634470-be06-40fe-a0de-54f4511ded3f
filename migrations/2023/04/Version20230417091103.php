<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230417091103 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1304 - Tweedehands product toevoegen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`second_hand_product_accessory` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `product_id` int(11) NOT NULL,
                `name` varchar(255) NOT NULL,
                `price` decimal(12,2) DEFAULT 0.00,
                PRIMARY KEY (`id`),
                KEY `product_id` (`product_id`),
                CONSTRAINT `second_hand_product_accessory_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `artikelen` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`second_hand_product_accessory`');
    }
}
