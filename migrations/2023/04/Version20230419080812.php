<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230419080812 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1507 Web groep velden toevoegen aan groep tabellen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`wex_hoofdgroepen` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `description` varchar(255) NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');

        $this->addSql('
            CREATE TABLE `cameranu`.`wex_subgroepen` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `wex_maingroup_id` int(11) NOT NULL,
              `description` varchar(255) NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`subgroepen` ADD `wex_subgroup_id` int(11) DEFAULT NULL AFTER `hoofdgroep_id`;
        ');

        $this->addSql('CREATE INDEX wex_subgroup_id ON `cameranu`.`subgroepen` (`wex_subgroup_id`);');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`wex_hoofdgroepen`');
        $this->addSql('DROP TABLE `cameranu`.`wex_subgroepen`');
        $this->addSql('ALTER TABLE `cameranu`.`subgroepen` DROP INDEX wex_subgroup_id;');
        $this->addSql('ALTER TABLE `cameranu`.`subgroepen` DROP COLUMN `wex_subgroup_id`;');
    }
}
