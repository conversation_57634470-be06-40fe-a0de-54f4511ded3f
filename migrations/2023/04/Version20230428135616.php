<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230428135616 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1628 menuItems type enum aanpassen';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`menuItems` ADD `brand_image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL NULL;');
        $this->addSql('ALTER TABLE `cameranu`.`menuItems` MODIFY COLUMN `type` enum(\'regular\',\'separator\',\'title\',\'expressive\',\'brand\') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT \'regular\' NOT NULL;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`menuItems` DROP COLUMN `brand_image_url`;');
        $this->addSql('UPDATE `cameranu`.`menuItems` SET `type` = :to WHERE `type` = :from', [
            'from' => 'brand',
            'to' => 'regular'
        ]);
        $this->addSql('ALTER TABLE `cameranu`.`menuItems` MODIFY COLUMN `type` enum(\'regular\',\'separator\',\'title\',\'expressive\') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT \'regular\' NOT NULL;');
    }
}
