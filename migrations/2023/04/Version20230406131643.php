<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230406131643 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1135 add table to connect secondhand stock with accessories';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`second_hand_stock_accessory` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `stock_id` int(11) DEFAULT NULL,
          `name` varchar(255) NOT NULL DEFAULT \'\',
          `price` decimal(12,2) DEFAULT 0.00,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS  `cameranu`.`second_hand_stock_accessory`');
    }
}
