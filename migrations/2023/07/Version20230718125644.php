<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\InternalInvoiceStockLocationInformation;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230718125644 extends AbstractMigration
{
    private const VAT_NUMBERS_OLD = [
        ['id' => InternalInvoiceStockLocationInformation::INTERNAL_INVOICE_STOCK_LOCATION_INFORMATION_AMSTERDAM_WINKEL_ID, 'vatNumber' => 'NL859932424B01'],
        ['id' => InternalInvoiceStockLocationInformation::INTERNAL_INVOICE_STOCK_LOCATION_INFORMATION_AMSTERDAM_PRINTSHOP_ID, 'vatNumber' => 'NL859932424B01'],
    ];

    private const VAT_NUMBERS_NEW = [
        ['id' => InternalInvoiceStockLocationInformation::INTERNAL_INVOICE_STOCK_LOCATION_INFORMATION_AMSTERDAM_WINKEL_ID, 'vatNumber' => 'NL859932874B01'],
        ['id' => InternalInvoiceStockLocationInformation::INTERNAL_INVOICE_STOCK_LOCATION_INFORMATION_AMSTERDAM_PRINTSHOP_ID, 'vatNumber' => 'NL859932874B01'],
    ];

    public function getDescription() : string
    {
        return 'CAM-2055 Btw nummer Amsterdam aanpassen';
    }

    public function up(Schema $schema) : void
    {
        foreach (self::VAT_NUMBERS_NEW as $data) {
            $this->addSql('UPDATE internal_invoice_stock_location_information SET vatNumber = :vatNumber WHERE id = :id', $data);
        }
    }

    public function down(Schema $schema) : void
    {
        foreach (self::VAT_NUMBERS_OLD as $data) {
            $this->addSql('UPDATE internal_invoice_stock_location_information SET vatNumber = :vatNumber WHERE id = :id', $data);
        }
    }
}
