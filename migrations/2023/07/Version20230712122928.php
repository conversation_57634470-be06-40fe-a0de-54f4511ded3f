<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230712122928 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1689 Frontpagetool Video Thumbnails';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`frontpage_videos` ADD COLUMN `thumbnail_url` TEXT NULL DEFAULT NULL AFTER `position`');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`frontpage_videos` DROP COLUMN IF EXISTS `thumbnail_url`');
    }
}
