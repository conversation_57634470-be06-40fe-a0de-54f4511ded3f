<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230713104018 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1846 Wat aanpassingen aan mailing preferences';
    }

    public function up(Schema $schema): void
    {
        // SMS uitzetten
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `active` = 0 WHERE `id` = 70');

        // Blog nieuwsbrief 65 verwijderen
        // Olympus 260 verwijderen
        $this->addSql('DELETE FROM `cameranu`.`mailing_types` WHERE `id` IN (65, 260)');

        //Update some names
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Drone\' WHERE `id` = 305');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Fotocamera\' WHERE `id` = 310');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Verrekijker\' WHERE `id` = 315');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Videocamera\' WHERE `id` = 320');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = "Auto\'s / Voertuigen" WHERE `id` = 333');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Evenementen\' WHERE `id` = 348');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Portretten\' WHERE `id` = 345');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Bruiloften\' WHERE `id` = 354');
    }

    public function down(Schema $schema): void
    {
        // SMS aanzetten
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `active` = 1 WHERE `id` = 70');

        //Blog nieuwsbrief en olympus weer toevoegen
        $this->addSql('
            INSERT INTO `cameranu`.`mailing_types` (`id`, `name`, `name_en`, `active`, `default_for_new_user`, `mailing_category_id`, `squeezely_key`)
            VALUES
            (65, \'Blog nieuwsbrief\', \'Blog newsletter\', 1, 0, 2, \'custom_mailpref_blog_nieuwsbrief\'),
            (260, \'Olympus\', \'Olympus\', 1, 0, 8, \'custom_mailpref_olympus\')
        ');

        //Update some names
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Drones\' WHERE `id` = 305');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Foto\' WHERE `id` = 310');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Verrekijkers\' WHERE `id` = 315');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Video\' WHERE `id` = 320');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Auto\' WHERE `id` = 333');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Event\' WHERE `id` = 348');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Portret\' WHERE `id` = 345');
        $this->addSql('UPDATE `cameranu`.`mailing_types` SET `name` = \'Trouwen\' WHERE `id` = 354');
    }
}
