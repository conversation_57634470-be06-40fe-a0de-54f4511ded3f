<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230703083236 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1536 koppeltabel voor C&V rapportages';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS`cameranu`.`claims_and_fees_reports_group` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `claim_and_fee_id` int(11) unsigned NOT NULL,
          `report_id` int(11) unsigned NOT NULL,
          PRIMARY KEY (`id`),
          KEY `claim_and_fee_id` (`claim_and_fee_id`),
          KEY `report_id` (`report_id`),
          CONSTRAINT `claims_and_fees_reports_group_ibfk_1` FOREIGN KEY (`claim_and_fee_id`)
              REFERENCES `claims_and_fees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
          CONSTRAINT `claims_and_fees_reports_group_ibfk_2` FOREIGN KEY (`report_id`)
              REFERENCES `claims_and_fees_reports` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');

        // move current claims_and_fees_id to new table before deleting column
        $this->addSql('
            INSERT INTO `cameranu`.`claims_and_fees_reports_group` (`claim_and_fee_id`, `report_id`)
            SELECT `claims_and_fees_id`, `id` FROM `cameranu`.`claims_and_fees_reports`
        ');

        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports` DROP COLUMN IF EXISTS `claims_and_fees_id`');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`claims_and_fees_reports` ADD COLUMN IF NOT EXISTS `claims_and_fees_id` int(11)');

        $this->addSql('
            UPDATE claims_and_fees_reports, claims_and_fees_reports_group
            SET claims_and_fees_reports.claims_and_fees_id = claims_and_fees_reports_group.claim_and_fee_id
            WHERE claims_and_fees_reports_group.report_id = claims_and_fees_reports.id
        ');

        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`claims_and_fees_reports_group`');
    }
}
