<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230713102534 extends AbstractMigration
{
    private const AMSTERDAM_CENTRUM = 4;
    private const AMSTERDAM_ZUIDOOST = 7;

    public function getDescription(): string
    {
        return 'CAM-1994 - Agenda toont nog Ams Zuidoost';
    }

    public function up(Schema $schema): void
    {
        // Update content_agenda_locations Amsterdam-Centrum to Amsterdam
        $this->addSql('UPDATE `cameranu`.`content_agenda_locations` SET `name` = :name WHERE `id` = :id', [
            'name' => 'Amsterdam',
            'id' => self::AMSTERDAM_CENTRUM,
        ]);

        // Update content_agenda location_id Amsterdam-Zuidoost to Amsterdam
        $this->addSql(
            'UPDATE `cameranu`.`content_agenda` SET `location_id` = :centrum WHERE `location_id` = :zuidoost',
            [
                'centrum' => self::AMSTERDAM_CENTRUM,
                'zuidoost' => self::AMSTERDAM_ZUIDOOST,
            ]
        );

        // Remove Amsterdam-Zuidoost location
        $this->addSql('DELETE FROM `cameranu`.`content_agenda_locations` WHERE `id` = :id', [
            'id' => self::AMSTERDAM_ZUIDOOST,
        ]);
    }

    public function down(Schema $schema): void
    {
        // Update Amsterdam back to Amsterdam-Centrum
        $this->addSql('UPDATE `cameranu`.`content_agenda_locations` SET `name` = :name WHERE `id` = :id', [
            'name' => 'Amsterdam Centrum',
            'id' => self::AMSTERDAM_CENTRUM
        ]);

        // Add Amsterdam-Zuidoost
        $this->addSql(
            'INSERT INTO `cameranu`.`content_agenda_locations` (`id`, `name`, `img`) VALUES (:id, :name, :image)',
            [
                'id' => self::AMSTERDAM_ZUIDOOST,
                'name' => 'Amsterdam Zuidoost',
                'image' => 'Amsterdam_Pro_CameraNU.png',
            ]
        );
    }
}
