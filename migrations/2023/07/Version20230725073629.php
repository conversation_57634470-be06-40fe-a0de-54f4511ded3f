<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230725073629 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2088 - Migration - Klantnummer toevoegen aan verzamelleveranciers';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`leveranciers_verzamel` ADD COLUMN IF NOT EXISTS  `webuser_id` int(11) DEFAULT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`leveranciers_verzamel` DROP COLUMN IF EXISTS  `webuser_id`');
    }
}
