<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230707084322 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1703 add relation to stock on reportrows and relation to user on reports';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports_rows`
                ADD COLUMN IF NOT EXISTS `stock_id` int(11) DEFAULT NULL,
                ADD KEY `stock_id` (`stock_id`),
                ADD CONSTRAINT `claims_and_fees_reports_rows_ibfk_1` FOREIGN KEY (`stock_id`)
                   REFERENCES `cameranu`.`voorraad` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION');

        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports`
                ADD COLUMN IF NOT EXISTS `user_id` int(11) DEFAULT NULL,
                ADD KEY `user_id` (`user_id`),
                ADD CONSTRAINT `claims_and_fees_reports_user_ibfk_2` FOREIGN KEY (`user_id`)
                   REFERENCES `cameranu_urk`.`users` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports_rows`
            DROP FOREIGN KEY IF EXISTS `claims_and_fees_reports_rows_ibfk_1`
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports_rows`
            DROP COLUMN IF EXISTS `stock_id`
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports`
            DROP FOREIGN KEY IF EXISTS `claims_and_fees_reports_user_ibfk_2`
        ');

        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports`
            DROP COLUMN IF EXISTS `user_id`
        ');
    }
}
