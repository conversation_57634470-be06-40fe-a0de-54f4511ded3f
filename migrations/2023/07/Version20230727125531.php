<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230727125531 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1964 - Migration - Button toevoegen aan actieblokken tabel';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`content_acties` MODIFY COLUMN `description_site` TEXT');
        $this->addSql('ALTER TABLE `cameranu`.`content_acties` ADD COLUMN IF NOT EXISTS `btn_text` VARCHAR(255)');
        $this->addSql('ALTER TABLE `cameranu`.`content_acties` ADD COLUMN IF NOT EXISTS `btn_url` VARCHAR(255)');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`content_acties` MODIFY COLUMN `description_site` VARCHAR(255)');
        $this->addSql('ALTER TABLE `cameranu`.`content_acties` DROP COLUMN IF EXISTS `btn_text`');
        $this->addSql('ALTER TABLE `cameranu`.`content_acties` DROP COLUMN IF EXISTS `btn_url`');
    }
}
