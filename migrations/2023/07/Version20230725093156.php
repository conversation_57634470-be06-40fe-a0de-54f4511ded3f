<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230725093156 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2003 - FL optie voor historische claims en vergoedingen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `claims_and_fees`
            SET `claims_and_fees`.`fl_margin_id` = 1
            WHERE `claims_and_fees`.`fl_margin_id` IS NULL
            AND date("2023-01-01") BETWEEN claims_and_fees.date_from AND claims_and_fees.date_till
        ');
    }

    public function down(Schema $schema) : void
    {
        // Can't go back
    }
}
