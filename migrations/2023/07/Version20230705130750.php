<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

final class Version20230705130750 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1856 Fix nikon feed supplier group';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'UPDATE `cameranu`.`leverancier_feeds` SET `verzamel_id` = :nikonVerzamelId WHERE `id` = :nikonFeedId',
            [
                'nikonVerzamelId' => SupplierGroup::ID_NIKON,
                'nikonFeedId' => SupplierFeed::ID_NIKON,
            ]
        );
    }

    public function down(Schema $schema): void
    {
    }
}
