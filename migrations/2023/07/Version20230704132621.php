<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230704132621 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1705 Claim omzetten naar add. bijdrage';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`additional_contributions` MODIFY `supplier_id` int(11);
            ALTER TABLE `cameranu`.`claims_and_fees_reports`
                ADD COLUMN `additional_contribution_id` int(11) unsigned AFTER `claims_and_fees_reports_status_id`,
                ADD CONSTRAINT `additional_contribution_id` FOREIGN KEY (`additional_contribution_id`) REFERENCES `cameranu`.`additional_contributions`(`id`) ON DELETE SET NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`additional_contributions` MODIFY `supplier_id` int(11) NOT NULL;
            ALTER TABLE `cameranu`.`claims_and_fees_reports` DROP COLUMN `additional_contribution_id`;
        ');
    }
}
