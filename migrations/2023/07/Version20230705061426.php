<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230705061426 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-1699 ADD report type extern';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO `cameranu`.`claims_and_fees_reports_type` (`name`) VALUES (\'Extern\')');
        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports`
            ADD COLUMN IF NOT EXISTS `date_from` datetime NOT NULL,
            ADD COLUMN IF NOT EXISTS `date_till` datetime NOT NULL
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`claims_and_fees_reports_type` WHERE `name` = \'Extern\'');
        $this->addSql('
            ALTER TABLE `cameranu`.`claims_and_fees_reports`
            DROP COLUMN IF EXISTS `date_from`,
            DROP COLUMN IF EXISTS `date_till`
        ');
    }
}
