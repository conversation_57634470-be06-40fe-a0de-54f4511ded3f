<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\FlMargin;
use Webdsign\GlobalBundle\Entity\FlMarginSubcategory;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230727083624 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1968 - Type bijdrage verwijderen bij aanmaken additionele bijdrage';
    }

    public function up(Schema $schema) : void
    {
        // 'Productpremie sell-out' => // FULL_MARGIN_1 - PRODUCT_PREMIUM_SELL_OUT
        $this->addSql('
            UPDATE `cameranu`.`additional_contributions`
            SET `fl_margin_id` = :flMargin,
                `fl_margin_sub_id` = :flMarginSub
            WHERE `contribution_type` = :contributionType
        ', [
            'flMargin' => FlMargin::FL_MARGIN_1,
            'flMarginSub' => FlMarginSubcategory::PRODUCT_PREMIUM_SELL_OUT,
            'contributionType' => 'Productpremie sell-out',
        ]);

        // 'Productpremie sell-in' => FULL_MARGIN_1 - PRODUCT_PREMIUM_SELL_IN
        $this->addSql('
            UPDATE `cameranu`.`additional_contributions`
            SET `fl_margin_id` = :flMargin,
                `fl_margin_sub_id` = :flMarginSub
            WHERE `contribution_type` = :contributionType
        ', [
            'flMargin' => FlMargin::FL_MARGIN_1,
            'flMarginSub' => FlMarginSubcategory::PRODUCT_PREMIUM_SELL_IN,
            'contributionType' => 'Productpremie sell-in',
        ]);

        // 'Conditioneel' => FULL_MARGIN_1 - ADDITIONAL_CONTRIBUTION_CONDITIONAL
        $this->addSql('
            UPDATE `cameranu`.`additional_contributions`
            SET `fl_margin_id` = :flMargin,
                `fl_margin_sub_id` = :flMarginSub
            WHERE `contribution_type` = :contributionType
        ', [
            'flMargin' => FlMargin::FL_MARGIN_1,
            'flMarginSub' => FlMarginSubcategory::ADDITIONAL_CONTRIBUTION_CONDITIONAL,
            'contributionType' => 'Conditioneel',
        ]);

        // 'Bijdragen inkoop overig' => FULL_MARGIN_2 - ADDITIONAL_SELL_IN_OTHER
        $this->addSql('
            UPDATE `cameranu`.`additional_contributions`
            SET `fl_margin_id` = :flMargin,
                `fl_margin_sub_id` = :flMarginSub
            WHERE `contribution_type` = :contributionType
        ', [
            'flMargin' => FlMargin::FL_MARGIN_2,
            'flMarginSub' => FlMarginSubcategory::ADDITIONAL_SELL_IN_OTHER,
            'contributionType' => 'Bijdragen inkoop overig',
        ]);

        // 'Marketingbijdrage', => FULL_MARGIN_2 - ADDITIONAL_MARKETING
        $this->addSql('
            UPDATE `cameranu`.`additional_contributions`
            SET `fl_margin_id` = :flMargin,
                `fl_margin_sub_id` = :flMarginSub
            WHERE `contribution_type` = :contributionType
        ', [
            'flMargin' => FlMargin::FL_MARGIN_2,
            'flMarginSub' => FlMarginSubcategory::ADDITIONAL_MARKETING,
            'contributionType' => 'Marketingbijdrage',
        ]);

        // Remove column `contribution_type`
        $this->addSql('ALTER TABLE `cameranu`.`additional_contributions` DROP COLUMN IF EXISTS `contribution_type`');
    }

    public function down(Schema $schema) : void
    {
        // Point of no return
    }
}
