<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230712141702 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`articles_stock_per_location` ADD COLUMN IF NOT EXISTS  `reserved_stock_for_route` int(11) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`articles_stock_per_location` DROP COLUMN IF EXISTS  `reserved_stock_for_route`');
    }
}
