<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Constant\EventConstants;

final class Version20231002142018 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-560 event entities';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            "
        CREATE TABLE cameranu.event_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.event_instructors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(255) NOT NULL,
            last_name VARCHAR(255) NOT NULL,
            prefix VARCHAR(255),
            phone_number VA<PERSON>HAR(255),
            email_address VARCHAR(255),
            description TEXT,
            image VARCHAR(255)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.event_locations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            street VARCHAR(255),
            house_number VARCHAR(255),
            house_number_addition VARCHAR(255),
            postal_code VARCHAR(255),
            city VARCHAR(255),
            country VARCHAR(255),
            province VARCHAR(255)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            sprintf(
                "
        CREATE TABLE cameranu.events (
            id INT AUTO_INCREMENT PRIMARY KEY,
            visible TINYINT,
            title VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            program_description TEXT,
            program_list TEXT,
            practical_information TEXT,
            included_list TEXT,
            hero_image VARCHAR(255),
            impression_images JSON,
            type ENUM('%s') NOT NULL,
            level ENUM('%s') NOT NULL,
            created DATETIME NOT NULL,
            updated DATETIME NOT NULL,
            created_by INT NOT NULL,
            updated_by INT NOT NULL,
            stock_location_id INT NOT NULL,
            category_id INT NOT NULL,
            instructor_id INT NOT NULL,
            INDEX IX_events_type (type),
            INDEX IX_events_level (level),
            CONSTRAINT FK_events_created_by FOREIGN KEY (created_by) REFERENCES cameranu.users (id),
            CONSTRAINT FK_events_updated_by FOREIGN KEY (updated_by) REFERENCES cameranu.users (id),
            CONSTRAINT FK_events_stock_location_id FOREIGN KEY (stock_location_id) REFERENCES cameranu.stock_locations (id),
            CONSTRAINT FK_events_category_id FOREIGN KEY (category_id) REFERENCES cameranu.event_categories (id),
            CONSTRAINT FK_events_instructor_id FOREIGN KEY (instructor_id) REFERENCES cameranu.event_instructors (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
                implode("','", EventConstants::VALID_TYPES),
                implode("','", EventConstants::VALID_LEVELS)
            )
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.event_products (
            id INT AUTO_INCREMENT PRIMARY KEY,
            start_date_and_time DATETIME NOT NULL,
            end_date_and_time DATETIME NOT NULL,
            maximum_capacity INT,
            needs_registration TINYINT,
            needs_payment TINYINT,
            event_id INT NOT NULL,
            product_id INT NOT NULL,
            location_id INT NOT NULL,
            INDEX IX_event_products_start_date_and_time (start_date_and_time),
            INDEX IX_event_products_end_date_and_time (end_date_and_time),
            CONSTRAINT FK_event_products_event_id FOREIGN KEY (event_id) REFERENCES cameranu.events (id),
            CONSTRAINT FK_event_products_product_id FOREIGN KEY (product_id) REFERENCES cameranu.artikelen (id),
            CONSTRAINT FK_event_products_location_id FOREIGN KEY (location_id) REFERENCES cameranu.event_locations (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.event_registrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            created DATETIME NOT NULL,
            is_cancelled TINYINT,
            is_refunded TINYINT,
            refund_requested TINYINT,
            first_name VARCHAR(255) NOT NULL,
            last_name VARCHAR(255) NOT NULL,
            prefix VARCHAR(255),
            phone_number VARCHAR(255),
            email_address VARCHAR(255),
            access_code VARCHAR(255) NOT NULL,
            psp_reference VARCHAR(255),
            product_id INT NOT NULL,
            order_id INT,
            cart_item_id INT,
            customer_id INT,
            CONSTRAINT FK_event_registrations_product_id FOREIGN KEY (product_id) REFERENCES cameranu.event_products (id),
            CONSTRAINT FK_event_registrations_order_id FOREIGN KEY (order_id) REFERENCES cameranu.bestelling_naw (id),
            CONSTRAINT FK_event_registrations_cart_item_id FOREIGN KEY (cart_item_id) REFERENCES cameranu.shopcart (id),
            CONSTRAINT FK_event_registrations_customer_id FOREIGN KEY (customer_id) REFERENCES cameranu.webusers (id),
            CONSTRAINT UC_event_registrations_access_code UNIQUE (access_code)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.event_reminders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            created DATETIME NOT NULL,
            first_name VARCHAR(255) NOT NULL,
            last_name VARCHAR(255) NOT NULL,
            prefix VARCHAR(255),
            phone_number VARCHAR(255),
            email_address VARCHAR(255),
            event_id INT,
            category_id INT,
            customer_id INT,
            CONSTRAINT FK_event_reminders_event_id FOREIGN KEY (event_id) REFERENCES cameranu.events (id),
            CONSTRAINT FK_event_reminders_category_id FOREIGN KEY (category_id) REFERENCES cameranu.event_categories (id), -- Replace 'cameranu_event_categories' with the actual name of your category table
            CONSTRAINT FK_event_reminders_customer_id FOREIGN KEY (customer_id) REFERENCES cameranu.webusers (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.event_themes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.events_themes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_id INT NOT NULL,
            theme_id INT NOT NULL,
            CONSTRAINT FK_events_themes_event_id FOREIGN KEY (event_id) REFERENCES cameranu.events (id),
            CONSTRAINT FK_events_themes_theme_id FOREIGN KEY (theme_id) REFERENCES cameranu.event_themes (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS cameranu.events_themes');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_themes');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_reminders');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_registrations');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_products');
        $this->addSql('DROP TABLE IF EXISTS cameranu.events');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_locations');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_instructors');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_categories');
    }
}
