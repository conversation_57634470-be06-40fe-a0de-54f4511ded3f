<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231030133759 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2565 add table for supplier formula price type';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`supplier_formula_price_type` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `name` varchar(255) NOT NULL DEFAULT \'\',
          `title` varchar(255) NOT NULL DEFAULT \'\',
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci');

        $this->addSql('
            ALTER TABLE `cameranu`.`leveranciersformules`
            ADD COLUMN IF NOT EXISTS `price_type_id` int(11) unsigned DEFAULT NULL,
            ADD CONSTRAINT `leveranciersformules_ibfk_1`
                FOREIGN KEY IF NOT EXISTS (`price_type_id`)
                REFERENCES `supplier_formula_price_type` (`id`)
                ON DELETE CASCADE
                ON UPDATE CASCADE
        ');

        $this->addSql('INSERT INTO `cameranu`.`supplier_formula_price_type` (`id`, `name`, `title`)
            VALUES
            (1, \'originalPrice\', \'Originele prijs\'),
            (2, \'elp\', \'ELP\'),
            (3, \'betweenInvoiceDiscount\', \'Tussen fact korting\'),
            (4, \'invoicePrice\', \'Fact prijs\'),
            (99, \'archive\', \'Archief\')
            ON DUPLICATE KEY UPDATE name=name'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`leveranciersformules` DROP FOREIGN KEY IF EXISTS `leveranciersformules_ibfk_1`');
        $this->addSql('ALTER TABLE `cameranu`.`leveranciersformules` DROP COLUMN IF EXISTS `price_type_id`');
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`supplier_formula_price_type`');
    }
}
