<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20231013084733 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2204 event checklists';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            "
        CREATE TABLE cameranu.event_checklist_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name TEXT NOT NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );

        $this->addSql(
            "
        CREATE TABLE cameranu.events_checklist_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_id INT NOT NULL,
            checklist_item_id INT NOT NULL,
            CONSTRAINT FK_events_checklist_items_event_id FOREIGN KEY (event_id) REFERENCES cameranu.events (id),
            CONSTRAINT FK_events_checklist_items_checklist_item_id FOREIGN KEY (checklist_item_id) REFERENCES cameranu.event_checklist_items (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS cameranu.events_checklist_items');
        $this->addSql('DROP TABLE IF EXISTS cameranu.event_checklist_items');
    }
}
