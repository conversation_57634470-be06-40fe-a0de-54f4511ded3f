<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231024123743 extends AbstractMigration
{
    private string $partner = 'Vocas';

    private array $fields = [
        'id' => 'artikel_id',
        'name' => 'titel',
        'description' => 'omschrijving',
        'EAN' => 'ean',
        'SKU' => 'Leverancierscode',
        'purchase_price' => 'inkoopprijs_excl_btw',
        'priceEx' => 'prijs_excl_btw',
        'fulfillment_fee' => 'fulfillment_fee',
        'brand' => 'merk',
        'categorie' => 'categorie',
        'stockPerLocation' => 'Voorraad',
    ];

    public function getDescription() : string
    {
        return 'CAM-2622 - Productfeed Vocas';
    }

    public function up(Schema $schema) : void
    {
        $hash = md5('prodfeed42-' . strtolower($this->partner));

        // feed toevoegen
        $this->addSql(
            'INSERT INTO `cameranu`.`productfeeds` (`id`, `partner`, `hash`, `file_type`, `feed_type`)
            VALUES (' . ProductFeed::VOCAS . ', \'' . $this->partner . '\', \'' . $hash . '\', \'xml\', \'product\')'
        );

        // velden toevoegen aan feed
        $statement = 'INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`) VALUES ';

        foreach ($this->fields as $key => $name) {
            $statement .= ' ("' . $name . '", "' . ProductFeed::VOCAS . '", "' . $key . '"),';
        }

        $statement = substr($statement, 0, -1);
        $this->addSql($statement);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeeds` WHERE `id` =' . ProductFeed::VOCAS);
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `feed_id` = ' . ProductFeed::VOCAS);
    }
}
