<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231023133225 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2528 - Voorraadgegevens V2Future importeren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`leverancier_feeds`
                (`id`, `naam`, `verzamel_id`, `show`, `use_on_website`, `token`, `importable`)
            VALUES (' . SupplierFeed::ID_V2FUTURE . ', "V2Future", ' . SupplierGroup::ID_V2FUTURE . ', 1, 0, NULL, 1);
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `leverancier_feeds` WHERE `id` = ' . SupplierFeed::ID_V2FUTURE);
    }
}
