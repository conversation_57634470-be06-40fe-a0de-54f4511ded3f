<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231020142637 extends AbstractMigration
{
    private const TYPE = 'eventsTheme';
    private const SORT = 61; // 61 is used because pages_blocktypes with id 381 (events) = 60
    private const CATEGORY = 'Content (Rebranding)';

    public function getDescription(): string
    {
        return 'CAM-2631 - Events thema blok in Pagebuilder';
    }

    public function up(Schema $schema): void
    {
        $timestamp = time();
        $sql = 'INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
                VALUES (:created, :modified, :type, :sort, :category)';
        $this->addSql($sql, [
            'created' => $timestamp,
            'modified' => $timestamp,
            'type' => self::TYPE,
            'sort' => self::SORT,
            'category' => self::CATEGORY,
        ]);
    }

    public function down(Schema $schema): void
    {
        $sql = 'DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = :type';
        $this->addSql($sql, [
            'type' => self::TYPE,
        ]);
    }
}
