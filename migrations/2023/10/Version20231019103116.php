<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231019103116 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2619 Black friday leadgen - verlanglijst acties';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`wishlist_specials` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `action` varchar(50) DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

            CREATE TABLE `cameranu`.`wishlist_specials_sent` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `special_id` int(11) NOT NULL,
              `user_id` int(11) NOT NULL,
              `sent` tinyint(1) NOT NULL DEFAULT 0,
              PRIMARY KEY (`id`),
              <PERSON><PERSON>Y `special_id` (`special_id`),
              KEY `user_id` (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

            INSERT INTO `cameranu`.`wishlist_specials` (`action`)
            VALUES
                (\'Black Friday\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DROP TABLE `cameranu`.`wishlist_specials`;
            DROP TABLE `cameranu`.`wishlist_specials_sent`;
        ');
    }
}
