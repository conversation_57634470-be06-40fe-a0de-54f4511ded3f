<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231031121826 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2701 - Events productlijst blok';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
            VALUES
            (current_timestamp, current_timestamp(), \'eventsProducts\', 90, \'Content (Rebranding)\')');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`pages_blocktypes` WHERE `type` = \'eventsProducts\'');
    }
}
