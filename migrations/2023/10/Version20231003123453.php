<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231003123453 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2475 add country column to omnia_products';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`omnia_products` ADD COLUMN IF NOT EXISTS `country` varchar(3) NOT NULL DEFAULT \'nl\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`omnia_products` DROP COLUMN IF EXISTS `country`');
    }
}
