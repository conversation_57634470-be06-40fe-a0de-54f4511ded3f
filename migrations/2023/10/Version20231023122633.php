<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231023122633 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2561 index_name toevoegen aan menu-item tabel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`menuItems`
            ADD COLUMN `index_name` VARCHAR(255) DEFAULT NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`menuItems`
            DROP COLUMN `index_name`;
        ');
    }
}
