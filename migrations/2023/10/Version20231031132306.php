<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ContentDashboard\DashboardTask;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231031132306 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2562 add dashboard task categorie for products not in product lists';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`dashboard_taskCategories` (`categoryId`, `name`, `description`, `defaultTaskType`)
            VALUES
            (
                ' . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_LIST_ID . ',
                \'Producten zonder productlijst\',
                \'Producten die niet in productlijsten staan\',
                \'productTask\'
            )
        ');

        $this->addSql('
            INSERT INTO `cameranu`.`dashboard_taskCategories_departments` (`categoryId`, `groupId`)
            SELECT ' . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_LIST_ID  . ', `groupId`
            FROM `cameranu`.`dashboard_taskCategories_departments`
            WHERE `categoryId`=' . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_MENU_ITEM_ID
        );

        $this->addSql('
            INSERT INTO `cameranu`.`dashboard_taskCategories_controlGroups` (`categoryId`, `controlGroupId`)
            SELECT ' . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_LIST_ID  . ', `controlGroupId`
            FROM `cameranu`.`dashboard_taskCategories_controlGroups`
            WHERE `categoryId`=' . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_MENU_ITEM_ID
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`dashboard_taskCategories` WHERE `categoryId` = ' . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_LIST_ID);
        $this->addSql('DELETE FROM `cameranu`.`dashboard_taskCategories_controlGroups` WHERE `categoryId` = ' . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_LIST_ID);
        $this->addSql('DELETE FROM `cameranu`.`dashboard_taskCategories_departments` WHERE `categoryId` = '  . DashboardTask::TASK_CATEGORY_PRODUCTS_WITHOUT_LIST_ID);
    }
}
