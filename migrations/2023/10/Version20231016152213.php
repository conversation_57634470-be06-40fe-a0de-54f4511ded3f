<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231016152213 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2465 - Voorraadgegevens DIFOX importeren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `leverancier_feeds`
            SET `verzamel_id` = ' . SupplierGroup::ID_DIFOX . ',
                `show` = 1,
                `importable` = 1
            WHERE `id` = ' . SupplierFeed::ID_DIFOX
        );
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `leverancier_feeds`
            SET `verzamel_id` = NULL,
                `show` = 0,
                `importable` = 0
            WHERE `id` = ' . SupplierFeed::ID_DIFOX
        );
    }
}
