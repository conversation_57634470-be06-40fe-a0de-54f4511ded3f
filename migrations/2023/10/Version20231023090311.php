<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231023090311 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2570 Add column finished_by to voorraad_memo';
    }

    public function up(Schema $schema): void
    {
       $this->addSql('ALTER TABLE `cameranu`.`voorraad_memo` ADD COLUMN IF NOT EXISTS `finished_by` int(11) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`voorraad_memo` DROP COLUMN IF EXISTS `finished_by`');
    }
}
