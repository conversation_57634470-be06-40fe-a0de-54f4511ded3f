<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\PaymentType;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231010075254 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2529 Hinder bij verwerking bon: 169650748044.';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            INSERT INTO cameranu.betalingsmogelijkheden_herkomst (betalingsmogelijkheden_id, herkomst_id)
            VALUES(:betalingsmogelijkheden_id, :herkomst_id)
        ', [
            'betalingsmogelijkheden_id' => PaymentType::VENDIRO_CAMERATOOLS,
            'herkomst_id' => Origin::VENDIRO_AMAZON_BE
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            DELETE FROM cameranu.betalingsmogelijkheden_herkomst
            WHERE betalingsmogelijkheden_id = :betalingsmogelijkheden_id AND herkomst_id = :herkomst_id
        ', [
            'betalingsmogelijkheden_id' => PaymentType::VENDIRO_CAMERATOOLS,
            'herkomst_id' => Origin::VENDIRO_AMAZON_BE
        ]);
    }
}
