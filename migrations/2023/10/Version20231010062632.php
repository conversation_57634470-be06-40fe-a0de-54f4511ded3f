<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Maingroup;
use Webdsign\GlobalBundle\Entity\Rootgroup;
use Webdsign\GlobalBundle\Entity\Subgroup;

final class Version20231010062632 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-570 hoofd- en subgroepen aanmaken voor gekoppelde (admin)producten aan events';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                "
        insert into cameranu.hoofdgroepen
            (
                id,
                rootgroep_id,
                naam,
                info,
                categorie,
                pos,
                flags,
                robots_title,
                robots_description,
                robots_keywords
             ) values (
                %d,
                %d,
                '%s',
                '',
                '',
                %d,
                %d,
                '',
                '',
                ''
            )
        ",
                Maingroup::MAINGROUP_ID_EVENTS,
                Rootgroup::WORKSHOP_ID,
                'Eventsysteem',
                30,
                Maingroup::FLAG_SHOW | Maingroup::FLAG_VISIBLE | Maingroup::FLAG_CAMERANU
            )
        );

        $this->addSql(
            sprintf(
                "
        insert into cameranu.subgroepen
            (
                id,
                hoofdgroep_id,
                naam,
                flags,
                pos,
                url,
                spidercategorie,
                spiderinfo,
                robots_title,
                robots_description,
                robots_keywords,
                grootboekcode,
                grootboekcodei
             ) values (
                %d,
                %d,
                '%s',
                %d,
                %d,
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                ''
            )
        ",
                Subgroup::SUBGROUP_EVENTS_ID,
                Maingroup::MAINGROUP_ID_EVENTS,
                'Alle events',
                Subgroup::FLAG_SHOW | Subgroup::FLAG_VISIBLE | Subgroup::FLAG_ALL_SHIPPING_METHODS_AVAILABLE | Subgroup::FLAG_IS_SERVICE,
                10,
            )
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                "
        delete from cameranu.hoofdgroepen where id = %d
        ",
                Maingroup::MAINGROUP_ID_EVENTS
            )
        );

        $this->addSql(
            sprintf(
                "
        delete from cameranu.subgroepen where id = %d
        ",
                Subgroup::SUBGROUP_EVENTS_ID
            )
        );
    }
}
