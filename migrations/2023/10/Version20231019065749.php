<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20231019065749 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-580 Add events to tweakwise feeds';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`) VALUES (\'events\', ' . ProductFeed::TWEAKWISE_ID.  ', \'events\')');
        $this->addSql('INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`) VALUES (\'events\', ' . ProductFeed::TWEAKWISE_TEST_ID . ', \'events\')');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `name` = \'events\' AND `feed_id` = ' . ProductFeed::TWEAKWISE_ID);
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `name` = \'events\' AND `feed_id` = ' . ProductFeed::TWEAKWISE_TEST_ID);
    }
}
