<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230228082802 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-733 - Content zoeken en vervangen';
    }

    public function up(Schema $schema) : void
    {
        // cameranu.artikelen - titel, description
        $this->addSql('
            UPDATE `cameranu`.`artikelen`
            SET `titel` = REPLACE(`titel`, "CameraNU.nl", "Cameranu"),
                `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`artikelen` a
                WHERE a.`titel` REGEXP "^CameraNU.nl"
				   OR a.`titel` REGEXP " CameraNU.nl"
                   OR a.`description` REGEXP "^CameraNU.nl"
                   OR a.`description` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.artikelen_content - title, content, content_org
        $this->addSql('
            UPDATE `cameranu`.`artikelen_content`
            SET `title` = REPLACE(`title`, "CameraNU.nl", "Cameranu"),
                `content` = REPLACE(`content`, "CameraNU.nl", "Cameranu"),
                `content_org` = REPLACE(`content_org`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`artikelen_content` a
                WHERE a.`title` REGEXP "^CameraNU.nl"
				   OR a.`title` REGEXP " CameraNU.nl"
                   OR a.`content` REGEXP "^CameraNU.nl"
                   OR a.`content` REGEXP " CameraNU.nl"
                   OR a.`content_org` REGEXP "^CameraNU.nl"
                   OR a.`content_org` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.banners - name, title, description
        $this->addSql('
            UPDATE `cameranu`.`banners`
            SET `name` = REPLACE(`name`, "CameraNU.nl", "Cameranu"),
                `title` = REPLACE(`title`, "CameraNU.nl", "Cameranu"),
                `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`banners` a
                WHERE a.`name` REGEXP "^CameraNU.nl"
				   OR a.`name` REGEXP " CameraNU.nl"
                   OR a.`title` REGEXP "^CameraNU.nl"
                   OR a.`title` REGEXP " CameraNU.nl"
                   OR a.`description` REGEXP "^CameraNU.nl"
                   OR a.`description` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.bestelling_herkomst - description
        $this->addSql('
            UPDATE `cameranu`.`bestelling_herkomst`
            SET `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`bestelling_herkomst` a
                WHERE a.`description` REGEXP "^CameraNU.nl"
                   OR a.`description` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.codes - naam
        $this->addSql('
            UPDATE `cameranu`.`codes`
            SET `naam` = REPLACE(`naam`, "CameraNU.nl", "Cameranu")
            WHERE `code` IN (
                SELECT a.`code`
                FROM `cameranu`.`codes` a
                WHERE a.`naam` REGEXP "^CameraNU.nl"
                   OR a.`naam` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.content - info, naam
        $this->addSql('
            UPDATE `cameranu`.`content`
            SET `info` = REPLACE(`info`, "CameraNU.nl", "Cameranu"),
                `naam` = REPLACE(`naam`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`content` a
                WHERE a.`info` REGEXP "^CameraNU.nl"
				   OR a.`info` REGEXP " CameraNU.nl"
                   OR a.`naam` REGEXP "^CameraNU.nl"
                   OR a.`naam` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.content_acties - title, title_new, description, description_site
        $this->addSql('
            UPDATE `cameranu`.`content_acties`
            SET `title` = REPLACE(`title`, "CameraNU.nl", "Cameranu"),
                `title_new` = REPLACE(`title_new`, "CameraNU.nl", "Cameranu"),
                `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu"),
                `description_site` = REPLACE(`description_site`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`content_acties` a
                WHERE a.`title` REGEXP "^CameraNU.nl"
				   OR a.`title` REGEXP " CameraNU.nl"
                   OR a.`title_new` REGEXP "^CameraNU.nl"
                   OR a.`title_new` REGEXP " CameraNU.nl"
                   OR a.`description` REGEXP "^CameraNU.nl"
                   OR a.`description` REGEXP " CameraNU.nl"
                   OR a.`description_site` REGEXP "^CameraNU.nl"
                   OR a.`description_site` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.content_agenda - omschrijving, meerinfo
        $this->addSql('
            UPDATE `cameranu`.`content_agenda`
            SET `omschrijving` = REPLACE(`omschrijving`, "CameraNU.nl", "Cameranu"),
                `meerinfo` = REPLACE(`meerinfo`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`content_agenda` a
                WHERE a.`omschrijving` REGEXP "^CameraNU.nl"
				   OR a.`omschrijving` REGEXP " CameraNU.nl"
                   OR a.`meerinfo` REGEXP "^CameraNU.nl"
                   OR a.`meerinfo` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.content2 - title, body
        $this->addSql('
            UPDATE `cameranu`.`content2`
            SET `title` = REPLACE(`title`, "CameraNU.nl", "Cameranu"),
                `body` = REPLACE(`body`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`content2` a
                WHERE a.`title` REGEXP "^CameraNU.nl"
				   OR a.`title` REGEXP " CameraNU.nl"
                   OR a.`body` REGEXP "^CameraNU.nl"
                   OR a.`body` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.forms - name, description, mailText
        $this->addSql('
            UPDATE `cameranu`.`forms`
            SET `name` = REPLACE(`name`, "CameraNU.nl", "Cameranu"),
                `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu"),
                `mailText` = REPLACE(`mailText`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`forms` a
                WHERE a.`name` REGEXP "^CameraNU.nl"
				   OR a.`name` REGEXP " CameraNU.nl"
                   OR a.`description` REGEXP "^CameraNU.nl"
                   OR a.`description` REGEXP " CameraNU.nl"
                   OR a.`mailText` REGEXP "^CameraNU.nl"
                   OR a.`mailText` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.frontpage_footerLinks - name, name_en
        $this->addSql('
            UPDATE `cameranu`.`frontpage_footerLinks`
            SET `name` = REPLACE(`name`, "CameraNU.nl", "Cameranu"),
                `name_en` = REPLACE(`name_en`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`frontpage_footerLinks` a
                WHERE a.`name` REGEXP "^CameraNU.nl"
				   OR a.`name` REGEXP " CameraNU.nl"
                   OR a.`name_en` REGEXP "^CameraNU.nl"
                   OR a.`name_en` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.mail_banner - naam, titel
        $this->addSql('
            UPDATE `cameranu`.`mail_banner`
            SET `naam` = REPLACE(`naam`, "CameraNU.nl", "Cameranu"),
                `titel` = REPLACE(`titel`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`mail_banner` a
                WHERE a.`naam` REGEXP "^CameraNU.nl"
				   OR a.`naam` REGEXP " CameraNU.nl"
                   OR a.`titel` REGEXP "^CameraNU.nl"
                   OR a.`titel` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.mail_content2 - onderwerp, bericht, info
        $this->addSql('
            UPDATE `cameranu`.`mail_content2`
            SET `onderwerp` = REPLACE(`onderwerp`, "CameraNU.nl", "Cameranu"),
                `bericht` = REPLACE(`bericht`, "CameraNU.nl", "Cameranu"),
                `info` = REPLACE(`info`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`mail_content2` a
                WHERE a.`onderwerp` REGEXP "^CameraNU.nl"
				   OR a.`onderwerp` REGEXP " CameraNU.nl"
                   OR a.`bericht` REGEXP "^CameraNU.nl"
                   OR a.`bericht` REGEXP " CameraNU.nl"
                   OR a.`info` REGEXP "^CameraNU.nl"
                   OR a.`info` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.meta - titel, h1, description, tag
        $this->addSql('
            UPDATE `cameranu`.`meta`
            SET `titel` = REPLACE(`titel`, "CameraNU.nl", "Cameranu"),
                `h1` = REPLACE(`h1`, "CameraNU.nl", "Cameranu"),
                `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu"),
                `tag` = REPLACE(`tag`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`meta` a
                WHERE a.`titel` REGEXP "^CameraNU.nl"
				   OR a.`titel` REGEXP " CameraNU.nl"
                   OR a.`h1` REGEXP "^CameraNU.nl"
                   OR a.`h1` REGEXP " CameraNU.nl"
                   OR a.`description` REGEXP "^CameraNU.nl"
                   OR a.`description` REGEXP " CameraNU.nl"
                   OR a.`tag` REGEXP "^CameraNU.nl"
                   OR a.`tag` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.notificaties            - tekst, tekst_en, description, tag
        $this->addSql('
            UPDATE `cameranu`.`notificaties`
            SET `tekst` = REPLACE(`tekst`, "CameraNU.nl", "Cameranu"),
                `tekst_en` = REPLACE(`tekst_en`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`notificaties` a
                WHERE a.`tekst` REGEXP "^CameraNU.nl"
				   OR a.`tekst` REGEXP " CameraNU.nl"
                   OR a.`tekst_en` REGEXP "^CameraNU.nl"
                   OR a.`tekst_en` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.pages - description
        $this->addSql('
            UPDATE `cameranu`.`pages`
            SET `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`pages` a
                WHERE a.`description` REGEXP "^CameraNU.nl"
				   OR a.`description` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.pages_content - content
        $this->addSql('
            UPDATE `cameranu`.`pages_content`
            SET `content` = REPLACE(`content`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`pages_content` a
                WHERE a.`content` REGEXP "^CameraNU.nl"
                   OR a.`content` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.pages_extra_content - content
        $this->addSql('
            UPDATE `cameranu`.`pages_extra_content`
            SET `content` = REPLACE(`content`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`pages_extra_content` a
                WHERE a.`content` REGEXP "^CameraNU.nl"
                   OR a.`content` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.pages_titles - title, description
        $this->addSql('
            UPDATE `cameranu`.`pages_titles`
            SET `title` = REPLACE(`title`, "CameraNU.nl", "Cameranu"),
                `description` = REPLACE(`description`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`pages_titles` a
                WHERE a.`title` REGEXP "^CameraNU.nl"
				   OR a.`title` REGEXP " CameraNU.nl"
                   OR a.`description` REGEXP "^CameraNU.nl"
                   OR a.`description` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.product_faq - answer
        $this->addSql('
            UPDATE `cameranu`.`product_faq`
            SET `answer` = REPLACE(`answer`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`product_faq` a
                WHERE a.`answer` REGEXP "^CameraNU.nl"
                   OR a.`answer` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.stores - name
        $this->addSql('
            UPDATE `cameranu`.`stores`
            SET `name` = REPLACE(`name`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`stores` a
                WHERE a.`name` REGEXP "^CameraNU.nl"
                   OR a.`name` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.terms - omschrijving_klein
        $this->addSql('
            UPDATE `cameranu`.`terms`
            SET `omschrijving_klein` = REPLACE(`omschrijving_klein`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`terms` a
                WHERE a.`omschrijving_klein` REGEXP "^CameraNU.nl"
                   OR a.`omschrijving_klein` REGEXP " CameraNU.nl"
            );
        ');

        // cameranu.tooltips - content
        $this->addSql('
            UPDATE `cameranu`.`tooltips`
            SET `content` = REPLACE(`content`, "CameraNU.nl", "Cameranu")
            WHERE `id` IN (
                SELECT a.`id`
                FROM `cameranu`.`tooltips` a
                WHERE a.`content` REGEXP "^CameraNU.nl"
                   OR a.`content` REGEXP " CameraNU.nl"
            );
        ');
    }

    public function down(Schema $schema) : void
    {
        // Point Of No return :)
    }
}
