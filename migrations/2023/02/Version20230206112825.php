<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230206112825 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-4151 - Veld KVK-nummer toevoegen op klantkaart in servicetool';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`webusers`
            ADD COLUMN IF NOT EXISTS `coc_number` VARCHAR(50) DEFAULT NULL AFTER `bedrijfsnaam`');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`webusers` DROP COLUMN IF EXISTS `coc_number`');
    }
}
