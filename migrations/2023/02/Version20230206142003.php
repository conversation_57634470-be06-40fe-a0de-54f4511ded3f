<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230206142003 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-4177 - A/B test kunnen releasen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`experiment`
            ADD COLUMN IF NOT EXISTS `winner_variation` VARCHAR(10) DEFAULT NULL AFTER `variations`');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`experiment` DROP COLUMN IF EXISTS `winner_variation`');
    }
}
