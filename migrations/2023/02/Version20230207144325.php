<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230207144325 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-4182 containeroptie voor Belgische website';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("
            alter table `cameranu`.`pages_containers` add column if not exists `domain` enum('cameranu.nl','cameranu.be') null
        ");
    }

    public function down(Schema $schema) : void
    {
        $this->addSql("
            alter table `cameranu`.`pages_containers` drop column if exists `domain`
        ");
    }
}
