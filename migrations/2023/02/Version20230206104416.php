<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230206104416 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-4091 add column to supplier formulas to base calculations oin original price';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`leveranciersformules` ADD COLUMN IF NOT EXISTS `based_on_original_price` tinyint(4) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`leveranciersformules` DROP COLUMN IF EXISTS `based_on_original_price`');
    }
}
