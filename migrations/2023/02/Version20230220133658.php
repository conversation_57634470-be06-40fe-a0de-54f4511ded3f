<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230220133658 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1103 - Autoriteitpersoonsgegevens en db cameranu_urk';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`users` DROP COLUMN IF EXISTS `password`');
        $this->addSql('ALTER TABLE `cameranu_urk`.`users` DROP COLUMN IF EXISTS `password_enc`');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu_urk`.`users` ADD COLUMN IF NOT EXISTS `password` VARCHAR(30) DEFAULT NULL AFTER `naam`');
        $this->addSql('ALTER TABLE `cameranu_urk`.`users` ADD COLUMN IF NOT EXISTS `password_enc` VARCHAR(32) DEFAULT NULL AFTER `password`');
    }
}
