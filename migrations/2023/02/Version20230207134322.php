<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230207134322 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-4180 add finished column to table packing_slip';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`packing_slip` ADD COLUMN IF NOT EXISTS `finished` tinyint(4) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`packing_slip` DROP COLUMN IF EXISTS `finished`');
    }
}
