<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230227112744 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-543 Kortingen per product';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`order_discount_log` (
                `id` INT PRIMARY KEY AUTO_INCREMENT,
                `order_id` INT NOT NULL,
                `product_id` INT DEFAULT NULL,
                `value` DECIMAL(10,2) NOT NULL,
                `code` VARCHAR(255) NOT NULL,
                `discount_code_id` INT NOT NULL,
                `ledger_field_id` INT NOT NULL,
                `created_at` DATETIME NOT NULL
            ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE `cameranu`.`order_discount_log`;');
    }
}
