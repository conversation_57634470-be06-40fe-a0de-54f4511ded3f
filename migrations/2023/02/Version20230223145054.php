<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230223145054 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-461 new tabel for recommended_accessories';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE IF NOT EXISTS `cameranu`.`product_recommended_accessories` (
          `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
          `product_id` int(11) DEFAULT NULL,
          `accessory_product_id` int(11) DEFAULT NULL,
          `amount_sold` int(11) DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `product_id` (`product_id`),
          KEY `accessory_product_id` (`accessory_product_id`),
          UNIQUE KEY uq_product_Id_accessory_id (product_id, accessory_product_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`product_recommended_accessories`');
    }
}
