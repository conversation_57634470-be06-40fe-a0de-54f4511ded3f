<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230210084215 extends AbstractMigration
{
    private const DATABASE = 'cameranu';
    private const COLUMN = 'domain';
    private const TABLES = [
        'frontpage_usp',
        'frontpage_menu',
        'frontpage_videos',
        'frontpage_social',
        'frontpage_brands',
        'frontpage_banners',
        'frontpage_reviews',
        'frontpage_popularMenu',
        'frontpage_searchTerms',
        'frontpage_footerLinks',
        'frontpage_action_block',
        'frontpage_hero_banners',
        'frontpage_inspiration_banners',
    ];

    public function getDescription(): string
    {
        return 'CAT-4123 add option to frontpage_tool tables for Belgian website';
    }

    public function up(Schema $schema): void
    {
        foreach (self::TABLES as $table) {
            $addColumnQuery = sprintf(
                'ALTER TABLE `%s`.`%s` ADD COLUMN IF NOT EXISTS `%s` enum(\'cameranu.nl\', \'cameranu.be\') null',
                self::DATABASE,
                $table,
                self::COLUMN
            );

            $this->addSql($addColumnQuery);
        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::TABLES as $table) {
            $dropColumnQuery = sprintf(
                'ALTER TABLE `%s`.`%s` DROP COLUMN IF EXISTS `%s`',
                self::DATABASE,
                $table,
                self::COLUMN
            );

            $this->addSql($dropColumnQuery);
        }
    }
}
