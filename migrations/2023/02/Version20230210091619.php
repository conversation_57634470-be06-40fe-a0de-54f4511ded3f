<?php

declare(strict_types=1);

namespace App\Migrations;

use DateTime;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230210091619 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAT-4188 - Breadcrumbs bepaald aan laatste toegevoegde groep';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`artikelen_menuItems`
            ADD COLUMN IF NOT EXISTS `date_added` DATETIME DEFAULT NULL AFTER `path`');

        $currentDateTime = new DateTime();
        $this->addSql('UPDATE `cameranu`.`artikelen_menuItems` SET `date_added` = :now', [
            'now' => $currentDateTime->format('Y-m-d H:i:s')
        ]);

        $this->addSql('ALTER TABLE `cameranu`.`artikelen_menuItems` MODIFY `date_added` DATETIME NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`artikelen_menuItems` DROP COLUMN IF EXISTS `date_added`');
    }
}
