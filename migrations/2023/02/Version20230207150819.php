<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230207150819 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAT-4072 Extra RFM velden';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` ADD COLUMN IF NOT EXISTS `recency` int(11) default 0');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` ADD COLUMN IF NOT EXISTS `frequency` int(11) default 0');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` ADD COLUMN IF NOT EXISTS `monetary` varchar(255) default \'0.00\'');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` DROP COLUMN IF EXISTS `recency`');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` DROP COLUMN IF EXISTS `frequency`');
        $this->addSql('ALTER TABLE `cameranu`.`customer_rfm_score` DROP COLUMN IF EXISTS `monetary`');
    }
}
