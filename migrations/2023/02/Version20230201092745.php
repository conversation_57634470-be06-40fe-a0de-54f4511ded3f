<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use JsonException;

final class Version20230201092745 extends AbstractMigration
{
    private const SUPPLIER_GROUP_ID = 213;
    private const PACKING_SLIP_EMAIL = '<EMAIL>';
    private const PACKING_SLIP_MAPPING = [
        'orderNumber' => 'orderNumber',
        'reference' => 'reference',
        'orderDate' => 'orderDate',
        'deliveryDate' => 'deliveryDate',
        'productCode' => 'productCode',
        'productName' => 'productName',
        'ean' => 'ean',
        'amountOrdered' => 'amountOrdered',
        'amountDelivered' => 'amountDelivered',
        'serialnumbers' => 'serialnumbers',
    ];

    public function getDescription() : string
    {
        return 'CAT-3952 Sennheiser toevoegen aan pakbonmapping';
    }

    /**
     * @throws JsonException
     */
    public function up(Schema $schema) : void
    {
        $this->addSql('
            update `cameranu`.`leveranciers_verzamel`
            set `packing_slip_mapping` = :packing_slip_mapping,
                `packing_slip_from_email` = :packing_slip_email 
            where `id` = :supplier_group_id',
            [
                'packing_slip_email' => self::PACKING_SLIP_EMAIL,
                'packing_slip_mapping' => json_encode(self::PACKING_SLIP_MAPPING, JSON_THROW_ON_ERROR),
                'supplier_group_id' => self::SUPPLIER_GROUP_ID,
            ]
        );
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            update `cameranu`.`leveranciers_verzamel`
            set `packing_slip_mapping` = :packing_slip_mapping, 
                `packing_slip_from_email` = :packing_slip_email
            where `id` = :supplier_group_id',
            [
                'packing_slip_email' => null,
                'packing_slip_mapping' => null,
                'supplier_group_id' => self::SUPPLIER_GROUP_ID,
            ]
        );
    }
}
