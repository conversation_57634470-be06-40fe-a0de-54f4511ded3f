<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\User;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230223133911 extends AbstractMigration
{
    public function getDescription() : string
    {
        return '1082 - Extra orderinformatie historisch vullen';
    }

    public function up(Schema $schema) : void
    {
        /**
         * Add user team table with contents
         */
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu_urk`.`users_teams` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci'
        );
        $this->addSql('INSERT INTO `cameranu_urk`.`users_teams` (`id`, `name`) VALUES
            (1, "Warehouse"),
            (2, "Finance"),
            (3, "Purchase"),
            (4, "E-commerce"),
            (5, "Retail store"),
            (6, "Customer service"),
            (7, "B2B"),
            (8, "Marketplaces"),
            (9, "Others")'
        );

        /**
         * Add orders_history table
         */
        $this->addSql('
            CREATE TABLE `cameranu`.`order_history` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `order_id` int(11) NOT NULL,
                `user_id` int(11) NOT NULL,
                `team_id` int(11) DEFAULT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ');

        /**
         * Add team_id to users table
         */
        $this->addSql('ALTER TABLE `cameranu_urk`.`users` ADD COLUMN `team_id` int(11) DEFAULT NULL AFTER `pincode`');

        /**
         * Change and Add New System Users
         */
        $this->addSql('
            UPDATE `cameranu_urk`.`users`
            SET `naam` = "systeem_admin",
                `naam_lang` = "Systeem - Admin",
                `team_id` = 9
            WHERE `id` = ' . User::SYSTEM_USER_ADMIN
        );
        $this->addSql('
            INSERT INTO `cameranu_urk`.`users` (
                `id`, `naam_lang`, `naam`, `passwd_enc2`, `team_id`,
                `groep`, `werkgever`, `afdeling`, `location`
            ) VALUES (
	            ' . User::SYSTEM_USER_MARKETPLACES . ',
	            "Systeem - Marketplaces",
	            "systeem_marketplaces",
	            "",
	            9,
	            "-",
	            "0",
	            "Algemeen",
	            "cameranu"
	        ), (
	            ' . User::SYSTEM_USER_WEBSITE . ',
	            "Systeem - Website",
	            "systeem_website",
	            "",
	            9,
	            "-",
	            "0",
	            "Algemeen",
	            "cameranu"
	        );
	    ');
    }

    public function down(Schema $schema) : void
    {
        /**
         * Delete team_id from users table
         */
        $this->addSql('ALTER TABLE `cameranu_urk`.`users` DROP COLUMN IF EXISTS `team_id`');

        /**
         * Delete orders_history table
         */
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`order_history`');

        /**
         * Delete users_teams table
         */
        $this->addSql('DROP TABLE IF EXISTS `cameranu_urk`.`users_teams`');

        /**
         * Change and Delete New System Users
         */
        $this->addSql('
            UPDATE `cameranu_urk`.`users`
            SET `naam` = "systeem",
                `naam_lang` = "Systeem"
            WHERE `id` = ' . User::SYSTEM_USER_ADMIN
        );
        $this->addSql('
            DELETE FROM `cameranu_urk`.`users`
            WHERE `id` IN (' . User::SYSTEM_USER_MARKETPLACES . ', ' . User::SYSTEM_USER_WEBSITE . ')'
        );
    }
}
