<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230323161916 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1137 - Per groep tweedehands accessoires aangeven';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`specs_profiles_accessories` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `specs_profile_id` int(11) NOT NULL,
                `name` varchar(255) NOT NULL,
                `price` decimal(12,2) DEFAULT 0.00,
                PRIMARY KEY (`id`),
                KEY `specs_profile_id` (`specs_profile_id`),
                CONSTRAINT `specs_profiles_accessories_ibfk_1` FOREIGN KEY (`specs_profile_id`) REFERENCES `specs_profiles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`specs_profiles_accessories`');
    }
}
