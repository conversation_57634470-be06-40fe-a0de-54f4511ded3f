<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230306153850 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1062 Zakelijke email veld toevoegen ';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE  `cameranu`.`webusers` ADD `business_email` varchar(60) DEFAULT NULL AFTER  `email`');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE  `cameranu`.`webusers` DROP COLUMN `business_email`');
    }
}
