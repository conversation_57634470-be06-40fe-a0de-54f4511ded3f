<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230322092403 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-543 Kortingen per product';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`order_discount_log_stock` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                `order_id` INT(11) NOT NULL,
                `stock_id` INT(11) NOT NULL,
                `sales_price_discount` DECIMAL(10,2) NOT NULL,
                `sales_price_net` DECIMAL(10,2) NOT NULL,
                PRIMARY KEY (`id`),
                KEY `order_id` (`order_id`),
                KEY `stock_id` (`stock_id`)
            ) ENGINE=InnoDB CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`order_discount_log_stock`');
    }
}
