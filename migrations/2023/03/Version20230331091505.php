<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230331091505 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-771 - Voorraadgegevens Servix importeren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu`.`leverancier_feeds`
            SET `importable` = :importable
            WHERE `id` = :id
        ', [
            'importable' => true,
            'id' => SupplierFeed::ID_SERVIX,
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu`.`leverancier_feeds`
            SET `importable` = :importable
            WHERE `id` = :id
        ', [
            'importable' => false,
            'id' => SupplierFeed::ID_SERVIX,
        ]);
    }
}
