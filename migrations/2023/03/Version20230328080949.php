<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230328080949 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1409 Geen gratis in PostNL verzendwijze';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['Bezorgd door PostNL', 1]);
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['Delivery by PostNL', 29]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['<span class="color-positive">Gratis</span> bezorgd door PostNL', 1]);
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['<span class="color-positive">Free</span> delivery by PostNL', 29]);
    }
}
