<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230327083844 extends AbstractMigration
{
    private const SUPPLIER_GROUP_ID = SupplierGroup::ID_A_AND_C_SYSTEMS;
    private const PACKING_SLIP_EMAIL = '<EMAIL>';
    private const PACKING_SLIP_MAPPING = [
        'orderNumber' => 'orderNumber',
        'reference' => 'reference',
        'orderDate' => 'orderDate',
        'deliveryDate' => 'deliveryDate',
        'productCode' => 'productCode',
        'ean' => 'ean',
        'productName' => 'productName',
        'amountOrdered' => 'amountOrdered',
        'amountDelivered' => 'amountDelivered',
        'serialnumbers' => 'serialnumbers',
    ];

    public function getDescription() : string
    {
        return 'CAM-609 - Pakbonnen A&C importeren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu`.`leveranciers_verzamel` 
            SET `packing_slip_mapping` = :packing_slip_mapping, 
                `packing_slip_from_email` = :packing_slip_email 
            WHERE `id` = :supplier_group_id',
            [
                'packing_slip_email' => self::PACKING_SLIP_EMAIL,
                'packing_slip_mapping' => json_encode(self::PACKING_SLIP_MAPPING, JSON_THROW_ON_ERROR),
                'supplier_group_id' => self::SUPPLIER_GROUP_ID,
            ]
        );
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu`.`leveranciers_verzamel` 
            SET `packing_slip_mapping` = :packing_slip_mapping, 
                `packing_slip_from_email` = :packing_slip_email
            WHERE `id` = :supplier_group_id',
            [
                'packing_slip_email' => null,
                'packing_slip_mapping' => null,
                'supplier_group_id' => self::SUPPLIER_GROUP_ID,
            ]
        );
    }
}
