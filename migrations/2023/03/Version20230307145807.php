<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230307145807 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1204 - Hero banner actief veld toevoegen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE  `cameranu`.`frontpage_hero_banners` ADD `active` tinyint(1) NOT NULL');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE  `cameranu`.`frontpage_hero_banners` DROP COLUMN `active`');
    }
}
