<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230327082206 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-106 <PERSON>rij<PERSON> ex btw tonen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE cameranu.webusers_configurations (
                webuser_id INT NOT NULL,
                display_vat_excluded TINYINT(1) NULL DEFAULT 0,
                PRIMARY KEY (`webuser_id`)
            )
        ');

        $this->addSql('
            ALTER TABLE cameranu.webusers_configurations 
            ADD FOREIGN KEY (webuser_id) 
            REFERENCES cameranu.webusers(id)
            ON DELETE CASCADE
            ON UPDATE NO ACTION
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE `cameranu`.`webusers_configurations`');
    }
}
