<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\SupplierGroup;

final class Version20230329094558 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-618 - facturen ALSO Nederland importeren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `ubl_email` = :ubl_email,
                `uses_ubl` = :uses_ubl
            WHERE `id` = :id
        ', [
            'ubl_email' => '<EMAIL>',
            'uses_ubl' => true,
            'id' => SupplierGroup::ID_ALSO_NEDERLAND,
        ]);

        $this->addSql('
            INSERT INTO `cameranu`.`ubl_leveranciers_verzamel_fields` (
                `name`,
                `leveranciers_verzamel_email`,
                `own_reference`,
                `customer_reference`,
                `line_reference`,
                `invoice_date`,
                `invoice_line`,
                `invoice_code`,
                `item_code`,
                `item_amount`,
                `invoice_amount`,
                `calculate_amount`
            ) VALUES (
                :name,
                :leveranciers_verzamel_email,
                :own_reference,
                :customer_reference,
                :line_reference,
                :invoice_date,
                :invoice_line,
                :invoice_code,
                :item_code,
                :item_amount,
                :invoice_amount,
                :calculate_amount
            )
        ', [
            'name' => 'ALSO Nederland',
            'leveranciers_verzamel_email' => '<EMAIL>',
            'own_reference' => 'OrderReference-ID',
            'customer_reference' => 'OrderReference-SalesOrderID',
            'line_reference' => 'InvoiceLine-OrderLineReference-LineID',
            'invoice_date' => 'IssueDate',
            'invoice_line' => 'InvoiceLine',
            'invoice_code' => 'ID',
            'item_code' => 'InvoiceLine-Item-SellersItemIdentification-ID',
            'item_amount' => 'InvoiceLine-InvoicedQuantity',
            'invoice_amount' => 'InvoiceLine-Price-PriceAmount',
            'calculate_amount' => null,
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('
            UPDATE `cameranu`.`leveranciers_verzamel`
            SET `ubl_email` = :ubl_email,
                `uses_ubl` = :uses_ubl
            WHERE `id` = :id
        ', [
            'id' => SupplierGroup::ID_ALSO_NEDERLAND,
            'ubl_email' => null,
            'uses_ubl' => false,
        ]);

        $this->addSql('
            DELETE FROM `ubl_leveranciers_verzamel_fields`
            WHERE `name` = :name
            AND `leveranciers_verzamel_email` = :email
        ', [
            'name' => 'ALSO Nederland',
            'email' => '<EMAIL>',
        ]);
    }
}
