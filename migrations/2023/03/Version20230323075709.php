<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230323075709 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-1302 Verzendwijze vertalingen aanpassen';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['<span class="color-positive">Gratis</span> bezorgd door PostNL', 1]);
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['<span class="color-positive">Free</span> delivery by PostNL', 29]);

        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['Bezorgd door Cameranu koerier', 2]);
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['Delivery by Cameranu courier (only in the Netherlands)', 83]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['Bezorgd door <strong>PostNL</strong>', 1]);
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['PostNL registered (only in the Netherlands)', 29]);

        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['Bezorgen door CameraNU.nl koerier / 1-3 werkdagen', 2]);
        $this->addSql('UPDATE `cameranu`.`verzendwijze2` SET verzendwijze = ? WHERE id = ?', ['CameraNU.nl courier (only in the Netherlands)', 83]);
    }
}
