<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230918124639 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2305 - Order API Adchieve';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `adchieve_logs` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `type` enum("error","success") COLLATE utf8mb4_unicode_ci NOT NULL,
              `method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `date` datetime NOT NULL,
              `order_id` int(11) DEFAULT NULL,
              `payload` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `response` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `adchieve_logs_order_id_IDX` (`order_id`) USING BTREE,
              KEY `adchieve_logs_date_IDX` (`date`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`adchieve_logs`');
    }
}
