<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;
use Webdsign\GlobalBundle\Entity\ProductFeedFields;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230915150455 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2306 OP_is_OP kenmerk in Adchieve feed';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`) VALUES (:name, :partnerId, :key)', [
            'name' => ProductFeedFields::NAME_OP_IS_OP,
            'partnerId' => ProductFeed::ADCHIEVE,
            'key' => ProductFeedFields::KEY_OP_IS_OP,
        ]);
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `name` = :name AND `feed_id` = :partnerId AND `key` = :key', [
            'name' => ProductFeedFields::NAME_OP_IS_OP,
            'partnerId' => ProductFeed::ADCHIEVE,
            'key' => ProductFeedFields::KEY_OP_IS_OP,
        ]);
    }
}
