<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Customer\SegmentConstant;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230906090051 extends AbstractMigration
{
    public const LEVEL_1_SEGMENTS_TO_BE_DELETED = [
        SegmentConstant::PRIMARY_SCHOOLS => [
            'name_nl' => 'Basisscholen', 'name_en' => 'Primary schools', 'lft' => 127, 'rgt' => 128, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::SECONDARY_SCHOOLS => [
            'name_nl' => 'Middelbare scholen', 'name_en' => 'Secondary schools', 'lft' => 131, 'rgt' => 132, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::UNIVERSITY_COLLEGE => [
            'name_nl' => 'Universiteit/Hoger onderwijs', 'name_en' => 'University/College', 'lft' => 137, 'rgt' => 138, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::ADULT_EDUCATION => [
            'name_nl' => 'Volwassenenonderwijs', 'name_en' => 'Adult education', 'lft' => 139, 'rgt' => 140, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::LECTURERS_TEACHERS => [
            'name_nl' => 'Docenten/Leraren', 'name_en' => 'Lecturers/Teachers', 'lft' => 129, 'rgt' => 130, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::STUDENTS => [
            'name_nl' => 'Studenten', 'name_en' => 'Students', 'lft' => 135, 'rgt' => 136, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::CARE_HOMES => [
            'name_nl' => 'Verzorgingshuizen', 'name_en' => 'Care homes', 'lft' => 119, 'rgt' => 120, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::HOSPITALS => [
            'name_nl' => 'Ziekenhuizen', 'name_en' => 'Hospitals', 'lft' => 123, 'rgt' => 124, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::TEACHING_HOSPITALS => [
            'name_nl' => 'Academische ziekenhuizen', 'name_en' => 'Teaching hospitals', 'lft' => 107, 'rgt' => 108, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::LABORATORY => [
            'name_nl' => 'Laboratoria', 'name_en' => 'Laboratory', 'lft' => 111, 'rgt' => 112, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::PRIVATE_CLINICS => [
            'name_nl' => 'Privéklinieken', 'name_en' => 'Private clinics', 'lft' => 115, 'rgt' => 116, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::DENTISTS => [
            'name_nl' => 'Tandartsen', 'name_en' => 'Dentists', 'lft' => 117, 'rgt' => 118, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::PHARMACY => [
            'name_nl' => 'Apotheken', 'name_en' => 'Pharmacy', 'lft' => 109, 'rgt' => 110, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::VETERINARY => [
            'name_nl' => 'Veterinair', 'name_en' => 'Veterinary', 'lft' => 121, 'rgt' => 122, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::INDEPENDENT_SPECIALISTS => [
            'name_nl' => 'Onafhankelijke specialisten', 'name_en' => 'Independent specialists', 'lft' => 113, 'rgt' => 114, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::MAGAZINES => [
            'name_nl' => 'Tijdschriften', 'name_en' => 'Magazines', 'lft' => 61, 'rgt' => 62, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::NIEUWSBLADEN => [
            'name_nl' => 'Newspapers', 'name_en' => 'Nieuwsbladen', 'lft' => 55, 'rgt' => 56, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::PRESS_AGENCIES => [
            'name_nl' => 'Persbureaus   ', 'name_en' => 'Press agencies', 'lft' => 57, 'rgt' => 58, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::STOCK_LIBRARIES => [
            'name_nl' => 'Beeldbanken', 'name_en' => 'Stock libraries', 'lft' => 53, 'rgt' => 54, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::ADVERTISING => [
            'name_nl' => 'Reclame', 'name_en' => 'Advertising', 'lft' => 59, 'rgt' => 60, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::ANIMATION_COMPANIES => [
            'name_nl' => 'Animatiebedrijven', 'name_en' => 'Animation companies', 'lft' => 73, 'rgt' => 74, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::BROADCAST => [
            'name_nl' => 'Uitzending', 'name_en' => 'Broadcast', 'lft' => 83, 'rgt' => 84, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::FILM_TV_PRODUCTION => [
            'name_nl' => 'Film & tv productie', 'name_en' => 'Film & tv production', 'lft' => 75, 'rgt' => 76, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::POST_PRODUCTEN => [
            'name_nl' => 'Postproductie', 'name_en' => 'Post producten', 'lft' => 77, 'rgt' => 78, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::PRODUCTION_COMPANIES => [
            'name_nl' => 'Productiebedrijven', 'name_en' => 'Production companies', 'lft' => 79, 'rgt' => 80, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::TV_STATIONS => [
            'name_nl' => 'Tv stations', 'name_en' => 'TV stations', 'lft' => 81, 'rgt' => 82, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::AERIAL => [
            'name_nl' => 'Luchtfoto\'s', 'name_en' => 'Aerial', 'lft' => 44, 'rgt' => 45, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::ARCHITECTURE => [
            'name_nl' => 'Architectuur', 'name_en' => 'Architecture', 'lft' => 28, 'rgt' => 29, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::DOCUMENTARY => [
            'name_nl' => 'Documentaires', 'name_en' => 'Documentary', 'lft' => 32, 'rgt' => 33, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::FASHION => [
            'name_nl' => 'Mode', 'name_en' => 'Fashion', 'lft' => 46, 'rgt' => 47, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::INDUSTRY => [
            'name_nl' => 'Industrie', 'name_en' => 'Industry', 'lft' => 40, 'rgt' => 41, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::LANDSCAPE => [
            'name_nl' => 'Landschap', 'name_en' => 'Landscape', 'lft' => 42, 'rgt' => 43, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::NATURE_WILDLIFE => [
            'name_nl' => 'Natuur en wild', 'name_en' => 'Nature & wildlife', 'lft' => 48, 'rgt' => 49, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::PAPPARAZI => [
            'name_nl' => 'Papparazi', 'name_en' => 'Papparazi', 'lft' => 50, 'rgt' => 51, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::PHOTO_JOURNALISM => [
            'name_nl' => 'Fotojournalistiek', 'name_en' => 'Photo journalism', 'lft' => 36, 'rgt' => 37, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::PHOTO_STUDIO_CHAINS => [
            'name_nl' => 'Fotostudio ketens', 'name_en' => 'Photo studio chains', 'lft' => 38, 'rgt' => 39, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::PORTRAIT => [
            'name_nl' => 'Portret', 'name_en' => 'Portrait', 'lft' => 64, 'rgt' => 65, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::PRODUCT => [
            'name_nl' => 'Product', 'name_en' => 'Product', 'lft' => 66, 'rgt' => 67, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::SPORT => [
            'name_nl' => 'Sport', 'name_en' => 'Sport', 'lft' => 68, 'rgt' => 69, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::WEDDING => [
            'name_nl' => 'Trouwen', 'name_en' => 'Wedding', 'lft' => 70, 'rgt' => 71, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::ARCHIVES => [
            'name_nl' => 'Archieven', 'name_en' => 'Archives', 'lft' => 147, 'rgt' => 148, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::PLACE_OF_WORSHIP => [
            'name_nl' => 'Godshuizen', 'name_en' => 'Place of worship', 'lft' => 157, 'rgt' => 158, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::COUNCIL => [
            'name_nl' => 'Bestuur', 'name_en' => 'Council', 'lft' => 149, 'rgt' => 150, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::COURTHOUSE => [
            'name_nl' => 'Gerechtsgebouw', 'name_en' => 'Courthouse', 'lft' => 155, 'rgt' => 156, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::DETENTION_CENTRES => [
            'name_nl' => 'Detentiecentra', 'name_en' => 'Detention centres', 'lft' => 153, 'rgt' => 154, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::EMBASSY => [
            'name_nl' => 'Ambassade', 'name_en' => 'Embassy', 'lft' => 143, 'rgt' => 144, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::EMPLOYMENT_AGENCY => [
            'name_nl' => 'Arbeidsbemiddelingsbureau', 'name_en' => 'Employment agency', 'lft' => 145, 'rgt' => 146, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::LIBRARY => [
            'name_nl' => 'Bibliotheek', 'name_en' => 'Library', 'lft' => 151, 'rgt' => 152, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::MUSEUM => [
            'name_nl' => 'Musea', 'name_en' => 'Museum', 'lft' => 133, 'rgt' => 134, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::AIRFORCE => [
            'name_nl' => 'Luchtmacht', 'name_en' => 'Airforce', 'lft' => 97, 'rgt' => 98, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::ARMY => [
            'name_nl' => 'Landmacht', 'name_en' => 'Army', 'lft' => 95, 'rgt' => 96, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::BORDER_PATROL_CUSTOMS => [
            'name_nl' => 'Douane', 'name_en' => 'Border patrol & customs', 'lft' => 93, 'rgt' => 94, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::NAVY => [
            'name_nl' => 'Marine', 'name_en' => 'Navy', 'lft' => 99, 'rgt' => 100, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::POLICE => [
            'name_nl' => 'Politie', 'name_en' => 'Police', 'lft' => 101, 'rgt' => 102, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::SPECIAL_FORCES => [
            'name_nl' => 'Speciale eenheden', 'name_en' => 'Special forces', 'lft' => 103, 'rgt' => 104, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::OTHER_SECURITY_FORCES => [
            'name_nl' => 'Andere veiligheidsdiensten', 'name_en' => 'Other security forces', 'lft' => 91, 'rgt' => 92, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::TRANSPORTATION_OLD => [
            'name_nl' => 'Transport (oud!)', 'name_en' => 'Transportation', 'lft' => 160, 'rgt' => 161, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::AIRPORT => [
            'name_nl' => 'Vliegveld', 'name_en' => 'Airport', 'lft' => 169, 'rgt' => 170, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::PORTS_HARBOUR => [
            'name_nl' => 'Havens', 'name_en' => 'Ports/harbour', 'lft' => 161, 'rgt' => 162, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::RAILWAY => [
            'name_nl' => 'Spoorwegen', 'name_en' => 'Railway', 'lft' => 163, 'rgt' => 164, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::TRAFFIC_CONTROL => [
            'name_nl' => 'Verkeersregeling', 'name_en' => 'Traffic control', 'lft' => 167, 'rgt' => 168, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::TRANSPORTATION => [
            'name_nl' => 'Transport', 'name_en' => 'Transportation', 'lft' => 165, 'rgt' => 166, 'lvl' => 1, 'parent_id' => 18
        ],
        SegmentConstant::THREE_DIMENSIONAL_SCAN_PRINT => [
            'name_nl' => '3D scan & print', 'name_en' => '3D scan & print', 'lft' => 175, 'rgt' => 176, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::AUTOMATIVE_AVIATION => [
            'name_nl' => 'Auto en luchtvaart', 'name_en' => 'Automative & aviation', 'lft' => 177, 'rgt' => 178, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::CONSTRUCTION => [
            'name_nl' => 'Bouw', 'name_en' => 'Construction', 'lft' => 179, 'rgt' => 180, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::ENERGY_COMPANIES => [
            'name_nl' => 'Energiemaatschappijen', 'name_en' => 'Energy companies', 'lft' => 181, 'rgt' => 182, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::ENGINEERING => [
            'name_nl' => 'Techniek', 'name_en' => 'Engineering', 'lft' => 189, 'rgt' => 190, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::MANUFACTURE => [
            'name_nl' => 'Productie', 'name_en' => 'Manufacture', 'lft' => 187, 'rgt' => 188, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::PHARMACEUTICAL_CHEMICAL => [
            'name_nl' => 'Farmaceutisch & chemisch', 'name_en' => 'Pharmaceutical & chemical', 'lft' => 183, 'rgt' => 184, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::QUALITY_CONTROL => [
            'name_nl' => 'Kwaliteitscontrole', 'name_en' => 'Quality control', 'lft' => 185, 'rgt' => 186, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::VIDEO_ANALYTICS => [
            'name_nl' => 'Video-analyse', 'name_en' => 'Video Analytics', 'lft' => 85, 'rgt' => 86, 'lvl' => 1, 'parent_id' => 15
        ],
        SegmentConstant::E_COMMERCE => [
            'name_nl' => 'E-commerce', 'name_en' => 'E-commerce', 'lft' => 15, 'rgt' => 16, 'lvl' => 1, 'parent_id' => 306
        ],
        SegmentConstant::REAL_ESTATE_LEVEL_2 => [
            'name_nl' => 'Vastgoed', 'name_en' => 'Real estate', 'lft' => 193, 'rgt' => 194, 'lvl' => 1, 'parent_id' => 21
        ],
        SegmentConstant::RESTAURANT_BAR_HOTEL => [
            'name_nl' => 'Restaurant/Bar/Hotel', 'name_en' => 'Restaurant/Bar/Hotel', 'lft' => 209, 'rgt' => 210, 'lvl' => 1, 'parent_id' => 330
        ],
        SegmentConstant::RETAIL_INDUSTRY => [
            'name_nl' => 'Retail', 'name_en' => 'Retail industry', 'lft' => 21, 'rgt' => 22, 'lvl' => 1, 'parent_id' => 306
        ],
        SegmentConstant::SPORTS => [
            'name_nl' => 'Sport', 'name_en' => 'Sports', 'lft' => 203, 'rgt' => 204, 'lvl' => 1, 'parent_id' => 330
        ],
        SegmentConstant::TRAVEL => [
            'name_nl' => 'Reizen', 'name_en' => 'Travel', 'lft' => 207, 'rgt' => 208, 'lvl' => 1, 'parent_id' => 330
        ],
        SegmentConstant::GAMING => [
            'name_nl' => 'Gaming', 'name_en' => 'Gaming', 'lft' => 199, 'rgt' => 200, 'lvl' => 1, 'parent_id' => 330
        ],
        SegmentConstant::SIMULATION => [
            'name_nl' => 'Simulatie', 'name_en' => 'Simulation', 'lft' => 201, 'rgt' => 202, 'lvl' => 1, 'parent_id' => 330
        ],
        SegmentConstant::TOURISM => [
            'name_nl' => 'Toerisme', 'name_en' => 'Tourism', 'lft' => 211, 'rgt' => 212, 'lvl' => 1, 'parent_id' => 330
        ],
        SegmentConstant::BANK_FINANCIAL_SERVICES => [
            'name_nl' => 'Banken en financiële dienstverlening', 'name_en' => 'Bank & financial services', 'lft' => 7, 'rgt' => 8, 'lvl' => 1, 'parent_id' => 306
        ],
        SegmentConstant::INSURANCE_COMPANIES => [
            'name_nl' => 'Verzekeringsmaatschappijen', 'name_en' => 'Insurance companies', 'lft' => 9, 'rgt' => 10, 'lvl' => 1, 'parent_id' => 306
        ],
        SegmentConstant::RENTAL_CAMERA_EQUIPMENT => [
            'name_nl' => 'Camera verhuur', 'name_en' => 'Rental camera equipment', 'lft' => 13, 'rgt' => 14, 'lvl' => 1, 'parent_id' => 306
        ],
    ];

    public function getDescription() : string
    {
        return 'CAM-2210 - Een aantal level 1 klant segmenten verwijderen';
    }

    public function up(Schema $schema) : void
    {
        foreach (array_keys(self::LEVEL_1_SEGMENTS_TO_BE_DELETED) as $segmentId) {
            $this->addSql('DELETE FROM cameranu.customer_segment WHERE id = :id', ['id' => $segmentId]);
        }
    }

    public function down(Schema $schema) : void
    {
        foreach (self::LEVEL_1_SEGMENTS_TO_BE_DELETED as $segmentId => $customerSegment) {
            $this->addSql('INSERT INTO cameranu.customer_segment (
                id, name_nl, name_en, lft, rgt, lvl, parent_id
            ) VALUES (:id, :name_nl, :name_en, :lft, :rgt, :lvl, :parent_id)', array_merge(['id' => $segmentId], $customerSegment));
        }
    }
}
