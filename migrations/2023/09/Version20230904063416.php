<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230904063416 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2148 New table for generic secondhand formula';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`second_hand_generic_formula` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `state_id` int(11) unsigned NOT NULL,
                `intake_formula` int(11) NOT NULL DEFAULT 0,
                `sales_formula` int(11) NOT NULL DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `state_id` (`state_id`),
            CONSTRAINT `second_hand_generic_formula_ibfk_1` FOREIGN KEY (`state_id`) REFERENCES `second_hand_state` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;');

        $this->addSql('ALTER TABLE `cameranu`.`second_hand_formula` ADD COLUMN IF NOT EXISTS `generic` tinyint(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`second_hand_generic_formula`');
        $this->addSql('ALTER TABLE `cameranu`.`second_hand_formula` DROP COLUMN IF EXISTS `generic`');
    }
}
