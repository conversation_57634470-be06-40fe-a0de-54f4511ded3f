<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Customer\SegmentConstant;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230905151110 extends AbstractMigration
{
    private const SEGMENT_LEVEL_2_STATE_ON_5_SEPTEMBER = [
        SegmentConstant::PRIMARY_SCHOOLS,
        SegmentConstant::SECONDARY_SCHOOLS,
        SegmentConstant::UNIVERSITY_COLLEGE,
        SegmentConstant::ADULT_EDUCATION,
        SegmentConstant::LECTURERS_TEACHERS,
        SegmentConstant::STUDENTS,
        SegmentConstant::CARE_HOMES,
        SegmentConstant::HOSPITALS,
        SegmentConstant::TEACHING_HOSPITALS,
        SegmentConstant::LABORATORY,
        SegmentConstant::PRIVATE_CLINICS,
        SegmentConstant::DENTISTS,
        SegmentConstant::PHARMACY,
        SegmentConstant::VETERINARY,
        SegmentConstant::INDEPENDENT_SPECIALISTS,
        SegmentConstant::MAGAZINES,
        SegmentConstant::NIEUWSBLADEN,
        SegmentConstant::PRESS_AGENCIES,
        SegmentConstant::STOCK_LIBRARIES,
        SegmentConstant::ADVERTISING,
        SegmentConstant::ANIMATION_COMPANIES,
        SegmentConstant::BROADCAST,
        SegmentConstant::FILM_TV_PRODUCTION,
        SegmentConstant::POST_PRODUCTEN,
        SegmentConstant::PRODUCTION_COMPANIES,
        SegmentConstant::TV_STATIONS,
        SegmentConstant::ARCHIVES,
        SegmentConstant::PLACE_OF_WORSHIP,
        SegmentConstant::COUNCIL,
        SegmentConstant::COURTHOUSE,
        SegmentConstant::DETENTION_CENTRES,
        SegmentConstant::EMBASSY,
        SegmentConstant::EMPLOYMENT_AGENCY,
        SegmentConstant::LIBRARY,
        SegmentConstant::MUSEUM,
        SegmentConstant::AIRFORCE,
        SegmentConstant::ARMY,
        SegmentConstant::BORDER_PATROL_CUSTOMS,
        SegmentConstant::NAVY,
        SegmentConstant::POLICE,
        SegmentConstant::SPECIAL_FORCES,
        SegmentConstant::OTHER_SECURITY_FORCES,
        SegmentConstant::AIRPORT,
        SegmentConstant::PORTS_HARBOUR,
        SegmentConstant::RAILWAY,
        SegmentConstant::TRAFFIC_CONTROL,
        SegmentConstant::TRANSPORTATION,
        SegmentConstant::THREE_DIMENSIONAL_SCAN_PRINT,
        SegmentConstant::AUTOMATIVE_AVIATION,
        SegmentConstant::CONSTRUCTION,
        SegmentConstant::ENERGY_COMPANIES,
        SegmentConstant::ENGINEERING,
        SegmentConstant::MANUFACTURE,
        SegmentConstant::PHARMACEUTICAL_CHEMICAL,
        SegmentConstant::QUALITY_CONTROL,
        SegmentConstant::VIDEO_ANALYTICS,
        SegmentConstant::E_COMMERCE,
        SegmentConstant::REAL_ESTATE_LEVEL_2,
        SegmentConstant::RESTAURANT_BAR_HOTEL,
        SegmentConstant::RETAIL_INDUSTRY,
        SegmentConstant::SPORTS,
        SegmentConstant::TRAVEL,
        SegmentConstant::GAMING,
        SegmentConstant::SIMULATION,
        SegmentConstant::TOURISM,
        SegmentConstant::BANK_FINANCIAL_SERVICES,
        SegmentConstant::INSURANCE_COMPANIES,
        SegmentConstant::RENTAL_CAMERA_EQUIPMENT,
    ];

    private const SEGMENT_LEVEL_2_PARENT_ID_STATE_5_SEPTEMBER = [
        SegmentConstant::PRIMARY_SCHOOLS => SegmentConstant::EDUCATION_CULTURE,
        SegmentConstant::SECONDARY_SCHOOLS => SegmentConstant::EDUCATION_CULTURE,
        SegmentConstant::UNIVERSITY_COLLEGE => SegmentConstant::EDUCATION_CULTURE,
        SegmentConstant::ADULT_EDUCATION => SegmentConstant::EDUCATION_CULTURE,
        SegmentConstant::LECTURERS_TEACHERS => SegmentConstant::EDUCATION_CULTURE,
        SegmentConstant::STUDENTS => SegmentConstant::EDUCATION_CULTURE,
        SegmentConstant::MUSEUM => SegmentConstant::EDUCATION_CULTURE,
        SegmentConstant::CARE_HOMES => SegmentConstant::HEALTHCARE,
        SegmentConstant::HOSPITALS => SegmentConstant::HEALTHCARE,
        SegmentConstant::TEACHING_HOSPITALS => SegmentConstant::HEALTHCARE,
        SegmentConstant::LABORATORY => SegmentConstant::HEALTHCARE,
        SegmentConstant::PRIVATE_CLINICS => SegmentConstant::HEALTHCARE,
        SegmentConstant::DENTISTS => SegmentConstant::HEALTHCARE,
        SegmentConstant::PHARMACY => SegmentConstant::HEALTHCARE,
        SegmentConstant::VETERINARY => SegmentConstant::HEALTHCARE,
        SegmentConstant::INDEPENDENT_SPECIALISTS => SegmentConstant::HEALTHCARE,
        SegmentConstant::MAGAZINES => SegmentConstant::PRESS_MEDIA_ENTERTAINMENT,
        SegmentConstant::NIEUWSBLADEN => SegmentConstant::PRESS_MEDIA_ENTERTAINMENT,
        SegmentConstant::PRESS_AGENCIES => SegmentConstant::PRESS_MEDIA_ENTERTAINMENT,
        SegmentConstant::STOCK_LIBRARIES => SegmentConstant::PRESS_MEDIA_ENTERTAINMENT,
        SegmentConstant::ADVERTISING => SegmentConstant::PRESS_MEDIA_ENTERTAINMENT,
        SegmentConstant::ANIMATION_COMPANIES => SegmentConstant::VIDEO_PRODUCTION,
        SegmentConstant::BROADCAST => SegmentConstant::VIDEO_PRODUCTION,
        SegmentConstant::FILM_TV_PRODUCTION => SegmentConstant::VIDEO_PRODUCTION,
        SegmentConstant::POST_PRODUCTEN => SegmentConstant::VIDEO_PRODUCTION,
        SegmentConstant::PRODUCTION_COMPANIES => SegmentConstant::VIDEO_PRODUCTION,
        SegmentConstant::TV_STATIONS => SegmentConstant::VIDEO_PRODUCTION,
        SegmentConstant::VIDEO_ANALYTICS => SegmentConstant::VIDEO_PRODUCTION,
        SegmentConstant::ARCHIVES => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::PLACE_OF_WORSHIP => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::COUNCIL => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::COURTHOUSE => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::DETENTION_CENTRES => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::EMBASSY => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::EMPLOYMENT_AGENCY => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::LIBRARY => SegmentConstant::GOVERNMENT_MUNICIPALITIES,
        SegmentConstant::AIRFORCE => SegmentConstant::DEFENSE_PUBLIC_SAFETY,
        SegmentConstant::ARMY => SegmentConstant::DEFENSE_PUBLIC_SAFETY,
        SegmentConstant::BORDER_PATROL_CUSTOMS => SegmentConstant::DEFENSE_PUBLIC_SAFETY,
        SegmentConstant::NAVY => SegmentConstant::DEFENSE_PUBLIC_SAFETY,
        SegmentConstant::POLICE => SegmentConstant::DEFENSE_PUBLIC_SAFETY,
        SegmentConstant::SPECIAL_FORCES => SegmentConstant::DEFENSE_PUBLIC_SAFETY,
        SegmentConstant::OTHER_SECURITY_FORCES => SegmentConstant::DEFENSE_PUBLIC_SAFETY,
        SegmentConstant::AIRPORT => SegmentConstant::TRANSPORTATION_OLD,
        SegmentConstant::PORTS_HARBOUR => SegmentConstant::TRANSPORTATION_OLD,
        SegmentConstant::RAILWAY => SegmentConstant::TRANSPORTATION_OLD,
        SegmentConstant::TRAFFIC_CONTROL => SegmentConstant::TRANSPORTATION_OLD,
        SegmentConstant::TRANSPORTATION => SegmentConstant::TRANSPORTATION_OLD,
        SegmentConstant::THREE_DIMENSIONAL_SCAN_PRINT => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::AUTOMATIVE_AVIATION => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::CONSTRUCTION => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::ENERGY_COMPANIES => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::ENGINEERING => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::MANUFACTURE => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::PHARMACEUTICAL_CHEMICAL => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::QUALITY_CONTROL => SegmentConstant::INDUSTRY_CONSTRUCTION,
        SegmentConstant::E_COMMERCE => SegmentConstant::TRADING_COMMERCE,
        SegmentConstant::RENTAL_CAMERA_EQUIPMENT => SegmentConstant::TRADING_COMMERCE,
        SegmentConstant::REAL_ESTATE_LEVEL_2 => SegmentConstant::REAL_ESTATE_LEVEL_1,
        SegmentConstant::RESTAURANT_BAR_HOTEL => SegmentConstant::TOURISM_HOSPITALITY,
        SegmentConstant::TRAVEL => SegmentConstant::TOURISM_HOSPITALITY,
        SegmentConstant::TOURISM => SegmentConstant::TOURISM_HOSPITALITY,
        SegmentConstant::RETAIL_INDUSTRY => SegmentConstant::RETAIL_FASHION,
        SegmentConstant::SPORTS => SegmentConstant::SPORTS_RECREATION,
        SegmentConstant::GAMING => SegmentConstant::SPORTS_RECREATION,
        SegmentConstant::SIMULATION => SegmentConstant::SPORTS_RECREATION,
        SegmentConstant::BANK_FINANCIAL_SERVICES => SegmentConstant::FINANCIAL_SERVICES,
        SegmentConstant::INSURANCE_COMPANIES => SegmentConstant::FINANCIAL_SERVICES,
    ];

    public const LEVEL_1_LEVEL_0_RELATIONSHIP_STATE_5_SEPTEMBER = [
        SegmentConstant::PRESS_MEDIA_ENTERTAINMENT => SegmentConstant::PHOTO_AND_OR_VIDEO_PRODUCTION_LEVEL_0,
        SegmentConstant::VIDEO_PRODUCTION => SegmentConstant::PHOTO_AND_OR_VIDEO_PRODUCTION_LEVEL_0,
        SegmentConstant::GOVERNMENT_MUNICIPALITIES => SegmentConstant::PUBLIC_SECTOR,
        SegmentConstant::DEFENSE_PUBLIC_SAFETY => SegmentConstant::PUBLIC_SECTOR,
        SegmentConstant::TRANSPORTATION_OLD => SegmentConstant::PUBLIC_SECTOR,
        SegmentConstant::HEALTHCARE => SegmentConstant::PUBLIC_SECTOR,
        SegmentConstant::EDUCATION_CULTURE => SegmentConstant::PUBLIC_SECTOR,
        SegmentConstant::INDUSTRY_CONSTRUCTION => SegmentConstant::TECHNOLOGY_PRODUCTION_CONSTRUCTION,
        SegmentConstant::REAL_ESTATE_LEVEL_1 => SegmentConstant::TECHNOLOGY_PRODUCTION_CONSTRUCTION,
        SegmentConstant::FINANCIAL_SERVICES => SegmentConstant::TRADE_SERVICES,
        SegmentConstant::RETAIL_FASHION => SegmentConstant::TRADE_SERVICES,
        SegmentConstant::TRADING_COMMERCE => SegmentConstant::TRADE_SERVICES,
        SegmentConstant::SPORTS_RECREATION => SegmentConstant::TOURISM_RECREATION_HOSPITALITY,
        SegmentConstant::TOURISM_HOSPITALITY => SegmentConstant::TOURISM_RECREATION_HOSPITALITY,
    ];

    public function getDescription() : string
    {
        return 'CAM-2210 - Level 2 klant segmenten verplaatsen naar level 1';
    }

    public function up(Schema $schema) : void
    {
        foreach (self::SEGMENT_LEVEL_2_STATE_ON_5_SEPTEMBER as $segmentId) {
            $parentId = $this->connection->fetchOne("SELECT parent_id FROM cameranu.customer_segment WHERE id = :id", ['id' => $segmentId]);
            $grandParentId = (int)(self::LEVEL_1_LEVEL_0_RELATIONSHIP_STATE_5_SEPTEMBER[$parentId] ?? 0) ?: null;
            if ($grandParentId === null) {
                continue;
            }

            $this->addSql("UPDATE `cameranu`.`customer_segment`
                               SET `lvl` = :level,
                                   `parent_id` = :parent_id
                               WHERE `id` = :id", [
                'level' => 1,
                'parent_id' => $grandParentId,
                'id' => $segmentId,
            ]);
        }
    }

    public function down(Schema $schema) : void
    {
        foreach (self::SEGMENT_LEVEL_2_PARENT_ID_STATE_5_SEPTEMBER as $segmentId => $parentId) {

            $this->addSql("UPDATE `cameranu`.`customer_segment`
                               SET `lvl` = :level,
                                   `parent_id` = :parent_id
                               WHERE `id` = :id", [
                'level' => 2,
                'parent_id' => $parentId,
                'id' => $segmentId,
            ]);
        }
    }
}
