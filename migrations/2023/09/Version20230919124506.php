<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230919124506 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-477 Extra veld toevoegen aan Zoho export';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql("ALTER TABLE `cameranu`.`zoho_export` ADD COLUMN `payment_methods` VARCHAR(255) NULL DEFAULT NULL AFTER `betaalwijze_id`");
    }

    public function down(Schema $schema) : void
    {
        $this->addSql("ALTER TABLE `cameranu`.`zoho_export` DROP COLUMN `payment_methods`");
    }
}
