<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230926141845 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2272 - Post<PERSON> labels maken/printen zonder order';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql(
            'CREATE TABLE `cameranu`.`package_labels` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `transporter_id` int(11) NOT NULL,
                `company` varchar(255) DEFAULT NULL,
                `name` varchar(255) NOT NULL,
                `street` varchar(255) NOT NULL,
                `house_number` varchar(50) NOT NULL,
                `house_number_addition` varchar(50) DEFAULT NULL,
                `postal_code` varchar(100) NOT NULL,
                `residence` varchar(255) NOT NULL,
                `country_id` int(11) NOT NULL,
                `filepath` varchar(255) NOT NULL,
                `notes` text DEFAULT NULL,
                `user_id` int(11) DEFAULT NULL,
                `date_created` timestamp NOT NULL DEFAULT current_timestamp(),
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci'
        );

        $this->addSql('ALTER TABLE `cameranu`.`external_communication` ADD COLUMN `label_id` INT(11) DEFAULT NULL AFTER `rma_id`');
        $this->addSql('ALTER TABLE `cameranu`.`postnl` ADD COLUMN `label_id` INT(11) DEFAULT NULL AFTER `rma_id`');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`external_communication` DROP COLUMN IF EXISTS `label_id`');
        $this->addSql('ALTER TABLE `cameranu`.`postnl` DROP COLUMN IF EXISTS `label_id`');
        $this->addSql('DROP TABLE IF EXISTS `cameranu`.`package_labels`');
    }
}
