<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230921064444 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-2421 - Actietags automatiseren';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`content_acties`
                ADD COLUMN `must_auto_unlink` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
                ADD COLUMN `unlink_after_days` INT(11) UNSIGNED NOT NULL DEFAULT 0,
                ADD COLUMN `unlink_after_weeks` INT(11) UNSIGNED NOT NULL DEFAULT 0,
                ADD COLUMN `unlink_after_months` INT(11) UNSIGNED NOT NULL DEFAULT 0
        ');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('ALTER TABLE `cameranu`.`content_acties`
                        DROP COLUMN `unlink_after_months`,
                        DROP COLUMN `unlink_after_weeks`,
                        DROP COLUMN `unlink_after_days`,
                        DROP COLUMN `must_auto_unlink`
        ');
    }
}
