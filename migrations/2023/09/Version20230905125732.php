<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230905125732 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2336 Aanmaakdatum veld toevoegen aan voorraad_memo tabel';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`voorraad_memo` ADD COLUMN `created` datetime DEFAULT CURRENT_TIMESTAMP AFTER `user_id`;
        ');

        $this->addSql('
            UPDATE `cameranu`.`voorraad_memo` SET `created` = NULL;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`voorraad_memo` DROP COLUMN `created`;
        ');
    }
}
