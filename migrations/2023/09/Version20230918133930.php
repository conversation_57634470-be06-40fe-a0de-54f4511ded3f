<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230918133930 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-2285 Add price_ex fields for discount log values';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`order_discount_log_stock` ADD COLUMN IF NOT EXISTS `sales_price_discount_ex` decimal(10,2) NOT NULL AFTER `sales_price_discount`');
        $this->addSql('ALTER TABLE `cameranu`.`order_discount_log_stock` ADD COLUMN IF NOT EXISTS `sales_price_net_ex` decimal(10,2) NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE `cameranu`.`order_discount_log_stock` DROP COLUMN IF EXISTS `sales_price_discount_ex`');
        $this->addSql('ALTER TABLE `cameranu`.`order_discount_log_stock` DROP COLUMN IF EXISTS `sales_price_net_ex`');
    }
}
