<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Mailbox;
use Webdsign\GlobalBundle\Entity\Origin;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230925102840 extends AbstractMigration
{
    public function getDescription() : string
    {
        return 'CAM-446 - Vragen mailbox toevoegen en sortering goed zetten';
    }

    public function up(Schema $schema) : void
    {
        $this->addSql('INSERT INTO `cameranu_urk`.`service_mailboxes` (
            `id`, `name`, `server`, `port`, `username`, `password`, `origin`, `default_from_for_origin`, `addImapSetting`,
            `oauth`, `oauth_endpoint_version`, `oauth_client_id`, `oauth_client_secret`
        ) VALUES (
            ' . Mailbox::ID_QUESTIONS_AT_CAMERANU_DOT_NL . ', "<EMAIL>", "outlook.office365.com", 933,
            "", "", ' . Origin::WEBSITE . ', 0, 1, 1, "2.0", "", ""
        )');
    }

    public function down(Schema $schema) : void
    {
        $this->addSql('DELETE FROM `cameranu_urk`.`service_mailboxes` WHERE `id` = ' . Mailbox::ID_QUESTIONS_AT_CAMERANU_DOT_NL);
    }
}
