<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250127112347 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6029 - Migration new buttonPill pagebuilder block';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `pages_blocktypes` (`created`, `modified`, `type`, `sort`, `category`)
            VALUES
                (\'**********\', \'**********\', \'buttonPill\', \'45\', \'Content (Rebranding)\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `pages_blocktypes` WHERE `type` = \'buttonPill\';
        ');
    }
}
