<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250129101352 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6016 Update stock availability percentage stock table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage_stock
            DROP CONSTRAINT `PRIMARY`,
            DROP COLUMN id,
            DROP INDEX `IDX_C62479934584665AD98387BA`,
            ADD COLUMN `date` DATE NOT NULL COMMENT \'(DC2Type:date_immutable)\',
            ADD COLUMN `hour` TINYINT UNSIGNED NOT NULL
        ');

        $this->addSql('
            UPDATE cameranu.stock_availability_percentage_stock
            SET `date` = DATE(date_created), `hour` = HOUR(date_created)
        ');

        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage_stock
            DROP COLUMN date_created,
            ADD CONSTRAINT PRIMARY KEY (product_id, stock_location_id, `date`, `hour`),
            ADD CONSTRAINT stock_availability_percentage_stock_product_id_fk
                FOREIGN KEY (product_id) REFERENCES cameranu.artikelen(id)
                ON UPDATE CASCADE ON DELETE RESTRICT,
            ADD CONSTRAINT stock_availability_percentage_stock_stock_location_id_fk
                FOREIGN KEY (stock_location_id) REFERENCES cameranu.stock_locations(id)
                ON UPDATE CASCADE ON DELETE RESTRICT
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage_stock
            ADD COLUMN date_created DATETIME NOT NULL,
            DROP CONSTRAINT stock_availability_percentage_stock_product_id_fk,
            DROP CONSTRAINT stock_availability_percentage_stock_stock_location_id_fk
        ');

        $this->addSql('
            UPDATE cameranu.stock_availability_percentage_stock
            SET date_created = TIMESTAMP(`date`, SEC_TO_TIME(`hour` * 3600))
        ');

        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage_stock
            DROP CONSTRAINT `PRIMARY`,
            ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY,
            ADD INDEX IDX_C62479934584665AD98387BA (product_id, stock_location_id)
        ');
    }
}
