<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250103102205 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5867 Migration for pagebuilder page structured data';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable('pages')
            ->addColumn('structured_data_store', 'integer', [
                'notnull' => false,
                'default' => null,
                'length' => 11
            ]);
    }

    public function down(Schema $schema): void
    {
        $schema->getTable('pages')
            ->dropColumn('structured_data_store');
    }
}
