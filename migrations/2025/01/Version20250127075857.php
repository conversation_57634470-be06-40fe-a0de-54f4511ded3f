<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250127075857 extends AbstractMigration
{

    public function getDescription(): string
    {
        return 'CAM-6044 - Weergave winkelinformatie block aanmaken';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO pages_blocktypes
            (`created`, `modified`, `type`, `sort`, `category`) VALUES
            (NOW(), NOW(), \'shopInfo\', 69, \'Content\')
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM pages_blocktypes
            where type = \'shopInfo\' and category = \'content\' and sort = \'69\'
            LIMIT 1
        ');
    }
}
