<?php

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

class Version20250219153900 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5820 - Create manufacturer_gpsr table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            '
            CREATE TABLE cameranu.manufacturer_gpsr (
                id INT AUTO_INCREMENT NOT NULL,
                internal_name VARCHAR(255) DEFAULT NULL,
                manufacturer_name VARCHAR(255) DEFAULT NULL,
                manufacturer_address VARCHAR(255) DEFAULT NULL,
                representative_name VARCHAR(255) DEFAULT NULL,
                representative_address VARCHAR(255) DEFAULT NULL,
                product_safety_information TEXT NOT NULL,
                PRIMARY KEY(id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;
        '
        );

        $this->addSql(
            '
            ALTER TABLE cameranu.artikelen
            ADD manufacturer_gpsr_id INT DEFAULT NULL;
        '
        );

        $this->addSql(
            '
            ALTER TABLE cameranu.artikelen
            ADD CONSTRAINT FK_PRODUCT_MANUFACTURER_GPSR
            FOREIGN KEY (manufacturer_gpsr_id)
            REFERENCES cameranu.manufacturer_gpsr (id)
            ON DELETE SET NULL;
        '
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            '
            ALTER TABLE cameranu.manufacturer_gpsr
            DROP FOREIGN KEY FK_PRODUCT_MANUFACTURER_GPSR;
        '
        );

        $this->addSql('DROP TABLE cameranu.manufacturer_gpsr');

        $this->addSql(
            '
            ALTER TABLE cameranu.artikelen
            DROP FOREIGN KEY FK_PRODUCT_MANUFACTURER_GPSR;
        '
        );

        $this->addSql(
            '
            ALTER TABLE cameranu.artikelen
            DROP COLUMN manufacturer_gpsr_id;
        '
        );
    }
}
