<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250106150003 extends AbstractMigration
{
    const TABLE_NAME = 'cameranu.promotions_paddington';

    public function getDescription(): string
    {
        return 'CAM-5894 - Paddington promotion';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable(self::TABLE_NAME);

        $table->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('first_name', Types::STRING);
        $table->addColumn('prefix', Types::STRING, [
            'notnull' => false
        ]);
        $table->addColumn('last_name', Types::STRING);
        $table->addColumn('email', Types::STRING);
        $table->addColumn('image_cloudflare_id', Types::STRING, [
            'unsigned' => true,
        ]);
        $table->addColumn('image_url', Types::STRING);
        $table->addColumn('created_at', Types::DATETIME_IMMUTABLE, [
            'default' => 'CURRENT_TIMESTAMP',
        ]);

        $table->setPrimaryKey(['id']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE_NAME);
    }
}
