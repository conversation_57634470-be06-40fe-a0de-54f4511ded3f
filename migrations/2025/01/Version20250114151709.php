<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20250114151709 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5869 - add a log table for stock scan lists';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('cameranu.stock_scan_list_log');

        $table->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
        ]);
        $table->setPrimaryKey(['id']);
        $table->addColumn('created_by', Types::INTEGER, [
            'notnull' => false,
        ]);
        $table->addColumn('created_at', Types::DATETIME_IMMUTABLE);
        $table->addColumn('stock_scan_list_id', Types::INTEGER, [
            'unsigned' => true,
        ]);
        $table->addColumn('action', 'stock_scan_list_log_action_type_enum', [
            'notnull' => true,
        ]);
        $table->addColumn('barcode', Types::STRING, [
            'notnull' => false,
        ]);
        $table->addColumn('message', Types::STRING, [
            'notnull' => false,
        ]);
        $table->addForeignKeyConstraint(
            'cameranu_urk.users',
            ['created_by'],
            ['id'],
            [
                'onDelete' => 'SET NULL',
                'onUpdate' => 'CASCADE',
            ],
            'FK_STOCK_SCAN_LIST_LOG_CREATED_BY'
        );
        $table->addForeignKeyConstraint(
            'cameranu.stock_scan_list',
            ['stock_scan_list_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ],
            'FK_STOCK_SCAN_LIST_LOG_STOCK_SCAN_LIST'
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('cameranu.stock_scan_list_log');
    }
}
