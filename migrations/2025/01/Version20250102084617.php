<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;
use CatB<PERSON>le\MigrationHelper\ManagesForeignKeys;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250102084617 extends AbstractMigration
{
    use ManagesForeignKeys;

    public function getDescription(): string
    {
        return 'CAM-5770 add user column to shopcart table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('shopcart');
        $table->addColumn('user_id', Types::INTEGER, [
            'unsigned' => false,
            'notnull' => false,
        ]);

        $table->addForeignKeyConstraint(
            'cameranu_urk.users',
            ['user_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE'
            ],
            'fk_cameranu_urk_users'
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('shopcart');
        $table->removeForeignKey('fk_cameranu_urk_users');
        $fkIndex = self::getIndexNameByColumnName('shopcart', 'user_id');
        $table->dropIndex($fkIndex);

        $table->dropColumn('user_id');
    }
}
