<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250129115220 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6016 Add daily stock measurements to availability_percentage table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage
            MODIFY COLUMN availability_percentage TINYINT UNSIGNED NOT NULL,
            ADD COLUMN total_stock SMALLINT UNSIGNED NOT NULL,
            ADD COLUMN amount_in_customer_orders SMALLINT NOT NULL
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage
            MODIFY COLUMN availability_percentage INT NOT NULL,
            DROP COLUMN total_stock,
            DROP COLUMN dispatchable_stock
        ');
    }
}
