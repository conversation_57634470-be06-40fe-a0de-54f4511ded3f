<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\DateImmutableType;
use Doctrine\DBAL\Types\DateTimeImmutableType;
use Doctrine\Migrations\AbstractMigration;

final class Version20250127143231 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6016 Update stock availability percentage table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('stock_availability_percentage');

        $table->dropPrimaryKey();
        $table->dropIndex('IDX_4259C1E04584665AD98387BA');
        $table->dropColumn('id');
        $table->modifyColumn('date_calculated', [
            'type' => new DateImmutableType(),
            'notnull' => true,
        ]);
        $table->setPrimaryKey(['product_id', 'stock_location_id', 'date_calculated']);

        $table->addForeignKeyConstraint(
            'artikelen',
            ['product_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );

        $table->addForeignKeyConstraint(
            'stock_locations',
            ['stock_location_id'],
            ['id'],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'CASCADE',
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('stock_availability_percentage');
        foreach ($table->getForeignKeys() as $foreignKey) {
            if ($foreignKey->getForeignTableName() === 'artikelen' || $foreignKey->getForeignTableName() === 'stock_locations') {
                $table->removeForeignKey($foreignKey->getName());
            }
        }

        $table->addIndex(['product_id', 'stock_location_id'], 'IDX_4259C1E04584665AD98387BA');
        $table->addColumn('id', 'integer', ['autoincrement' => true, 'notnull' => true]);
        $table->setPrimaryKey(['id']);
        $table->modifyColumn('date_calculated', [
            'type' => new DateTimeImmutableType(),
            'notnull' => true,
        ]);
    }
}
