<?php

declare(strict_types=1);

namespace App\Migrations;

use CatB<PERSON>le\MigrationHelper\ManagesForeignKeys;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250121073623 extends AbstractMigration
{
    use ManagesForeignKeys;

    public function getDescription(): string
    {
        return 'CAM-5483 add spec_profile and producttype to recommendedAccessories';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('product_recommended_accessories');
        $table->addColumn('accessory_spec_profile_id', Types::INTEGER, [
            'notnull' => false,
            'default' => null,
        ]);

        $table->addColumn('accessory_product_type', Types::STRING, [
            'notnull' => false,
            'default' => null,
        ]);

        $table->addForeignKeyConstraint('specs_profiles',
            ['accessory_spec_profile_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE'
            ],
            'fk_spec_profile_id',
        );

        $table->addIndex(['accessory_product_type'], 'idx_accessory_product_type');
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('product_recommended_accessories');
        $table->removeForeignKey('fk_spec_profile_id');
        $fkIndex = self::getIndexNameByColumnName('product_recommended_accessories', 'accessory_spec_profile_id');
        $table->dropIndex($fkIndex);
        $table->dropIndex('idx_accessory_product_type');
        $table->dropColumn('accessory_spec_profile_id');
        $table->dropColumn('accessory_product_type');
    }
}
