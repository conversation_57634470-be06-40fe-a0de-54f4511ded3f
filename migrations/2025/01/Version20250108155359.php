<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250108155359 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5291 - Squeezely/Spotler API koppelen aan: CAM-4344';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`productsets_feeds_squeezely` (
                `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                `feed_url` varchar(255) NOT NULL,
                `specs_profile` int(11) NOT NULL,
                `min_btw` float,
                `max_btw` float,
                PRIMARY KEY (`id`),
                FOREIGN KEY (`specs_profile`) REFERENCES `specs_profiles` (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');

        $this->addSql('
            INSERT INTO `cameranu`.`productsets_feeds_squeezely` (`feed_url`, `specs_profile`, `min_btw`, `max_btw`) VALUES
            (\'/products?account=V09ERHlwTDZaalJqcGNMTmpHcE1CZz09&productset=5CFDLMV2R670\', 10, 0, 750),
            (\'/products?account=V09ERHlwTDZaalJqcGNMTmpHcE1CZz09&productset=315UTU2OILFG\', 10, 750, 1500),
            (\'/products?account=V09ERHlwTDZaalJqcGNMTmpHcE1CZz09&productset=5DMV97TT7120\', 10, 1500, 2250),
            (\'/products?account=V09ERHlwTDZaalJqcGNMTmpHcE1CZz09&productset=3T4B8137BK50\', 10, 2250, 3000),
            (\'/products?account=V09ERHlwTDZaalJqcGNMTmpHcE1CZz09&productset=21TVJUPJAP50\', 10, 3000, 3750),
            (\'/products?account=V09ERHlwTDZaalJqcGNMTmpHcE1CZz09&productset=ABRAPO2CQHI0\', 10, 3750, 4500),
            (\'/products?account=V09ERHlwTDZaalJqcGNMTmpHcE1CZz09&productset=52LI23V7PMT0\', 10, 4500, null)
        ');
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('productsets_feeds_squeezely');
    }
}
