<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250415123352 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6473 Virtual stock column for stock measurements';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cameranu.stock_availability_percentage_stock ADD COLUMN virtual_stock INT NOT NULL DEFAULT 0;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cameranu.stock_availability_percentage_stock DROP COLUMN virtual_stock;');
    }
}
