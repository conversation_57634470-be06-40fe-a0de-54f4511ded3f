<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250422124126 extends AbstractMigration
{
    private const string FEED_NAME = 'TweakwiseContent';

    public function getDescription(): string
    {
        return 'CAM-6358 New product feed wih content for Tweakwise';
    }

    public function up(Schema $schema): void
    {
        $hash = md5('prodfeed42-' . self::FEED_NAME);
        $xsl = <<<XSL
            <?xml version="1.0" encoding="UTF-8"?>
            <xsl:stylesheet version="1.0"
              xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xmlns:xsd="http://www.w3.org/2001/XMLSchema"
              exclude-result-prefixes="xsi xsd">

              <xsl:output method="xml" indent="yes" cdata-section-elements="value"/>

              <!-- Match the root tweakwise element -->
              <xsl:template match="/tweakwisecontent">
                <items xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                       xmlns:xsd="http://www.w3.org/2001/XMLSchema">
                       <!-- no empty values -->
                  <xsl:apply-templates select="items/item[normalize-space(content) != '']"/>
                </items>
              </xsl:template>

              <!-- Transform each item -->
              <xsl:template match="item">
                <item>
                  <id><xsl:value-of select="id"/></id>
                  <values>
                    <!-- Cutoff on 399 because off Tweakwise limit -->
                    <value><xsl:value-of select="substring(content, 1, 399)"/></value>
                  </values>
                </item>
              </xsl:template>
            </xsl:stylesheet>
            XSL;
        $escapedXsl = addcslashes($xsl, "'\\");
        $this->addSql('INSERT INTO `cameranu`.`productfeeds` (`id`, `partner`, `hash`, `file_type`, `feed_type`, `xsl`)
            VALUES (' . ProductFeed::TWEAKWISE_CONTENT_ID . ', \'' . self::FEED_NAME . '\', \'' . $hash . '\', \'xml\', \'product\', \'' . $escapedXsl . '\')');

        $this->addSql('INSERT INTO `productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES
            (\'id\', ' . ProductFeed::TWEAKWISE_CONTENT_ID . ', \'id\'),
            (\'content\', ' . ProductFeed::TWEAKWISE_CONTENT_ID . ', \'content\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`productfeed_fields` WHERE `feed_id` = ' . ProductFeed::TWEAKWISE_CONTENT_ID);
        $this->addSql('DELETE FROM `cameranu`.`productfeeds` WHERE `id` = ' . ProductFeed::TWEAKWISE_CONTENT_ID);
    }
}
