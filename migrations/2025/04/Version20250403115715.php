<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250403115715 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6304 fill performance target table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `second_hand_performance_targets` (`key`, `name`, `extra`)
            VALUES
                (\'stock_amount\', \'Voorraadniveau\', \'stuks\'),
                (\'average_selling_rate\', \'ASR\', \'dagen\'),
                (\'days_sales_in_inventory_quarter\', \'DSI 12 weken\', \'dagen\'),
                (\'days_sales_in_inventory_year\', \'DSI Jaar\', \'dagen\'),
                (\'conversion_rate\', \'Quote Conversion Rate\', \'%\'),
                (\'minimal_margin\', \'Minimale marge\', \'%\'),
                (\'default_snooze_time\', \'Snooze tijd\', \'dagen\'),
                (\'last_date_in\', \'Laatst binnen\', \'dagen\'),
                (\'last_date_out\', \'Laatst verkocht\', \'dagen\')
        ');
    }

    public function down(Schema $schema): void
    {
        // handled in Version20250403113311
    }
}
