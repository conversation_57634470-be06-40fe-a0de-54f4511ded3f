<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250404102759 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5609 add parking folder for check missing shipments';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`parkeren` (`id`, `domain_id`, `pos`, `omschrijving`, `info`, `flags`)
            VALUES (2450, 1, 187, \'Controle - Vermissingen\', \'\', 40)
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `cameranu`.`parkeren` WHERE `id` = 2450');
    }
}
