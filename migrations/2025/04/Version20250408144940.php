<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250408144940 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6308 add margin and prices to stock factors table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('second_hand_product_stock_factors');
        $table->addColumn('sales_price', Types::DECIMAL, [
            'notnull' => true,
            'precision' => 7,
            'scale' => 2
        ]);

        $table->addColumn('intake_price', Types::DECIMAL, [
            'notnull' => true,
            'precision' => 7,
            'scale' => 2
        ]);

        $table->addColumn('margin_percentage', Types::DECIMAL, [
            'notnull' => true,
            'precision' => 7,
            'scale' => 2
        ]);

        $table->addColumn('intake_price_kamera_express', Types::DECIMAL, [
            'notnull' => true,
            'precision' => 7,
            'scale' => 2
        ]);

        $table->addColumn('intake_price_mpb', Types::DECIMAL, [
            'notnull' => true,
            'precision' => 7,
            'scale' => 2
        ]);

        $table->addColumn('sales_price_kamera_express', Types::DECIMAL, [
            'notnull' => true,
            'precision' => 7,
            'scale' => 2
        ]);

        $table->addColumn('sales_price_mpb', Types::DECIMAL, [
            'notnull' => true,
            'precision' => 7,
            'scale' => 2
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('second_hand_product_stock_factors');
        $table->dropColumn('sales_price');
        $table->dropColumn('intake_price');
        $table->dropColumn('margin_percentage');
        $table->dropColumn('intake_price_kamera_express');
        $table->dropColumn('intake_price_mpb');
        $table->dropColumn('sales_price_kamera_express');
        $table->dropColumn('sales_price_mpb');
    }
}
