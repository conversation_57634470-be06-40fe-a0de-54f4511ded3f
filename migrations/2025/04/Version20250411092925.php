<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Constant\ShippingMethod;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ArticleGroupType;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250411092925 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6336 set PakjeGemak flags for all secondhand products';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`artikelen` SET `flags_verzend` = (`flags_verzend` | ' . ShippingMethod::PAKJE_GEMAK . ')
            WHERE `id` IN (
                SELECT aga.artikelId FROM `cameranu`.`artikelgroepen_artikelen` aga
                INNER JOIN `cameranu`.`artikelgroepen` ag ON ag.id = aga.artikelGroepId AND article_group_type_id = ' . ArticleGroupType::GROUP_TYPE_SECOND_HAND . '
            )
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`artikelen` SET `flags_verzend` = (`flags_verzend` &~ ' . ShippingMethod::PAKJE_GEMAK . ')
            WHERE `id` IN (
                SELECT aga.artikelId FROM `cameranu`.`artikelgroepen_artikelen` aga
                INNER JOIN `cameranu`.`artikelgroepen` ag ON ag.id = aga.artikelGroepId AND article_group_type_id = ' . ArticleGroupType::GROUP_TYPE_SECOND_HAND . '
            )
        ');
    }
}
