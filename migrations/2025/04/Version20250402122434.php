<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250402122434 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6386 ProductFeed PowerReviews';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`productfeeds`
            ADD `xsl` text DEFAULT NULL;
        ');

        $this->addSql('
            INSERT INTO `cameranu`.`productfeeds` (`id`, `partner`, `hash`, `file_type`, `feed_type`, `xsl`)
            VALUES
                (\'37\', \'PowerReviews\', \'beb0b63987161999f2aaa7219c6194d9\', \'csv\', \'product\', \'<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<xsl:stylesheet version=\"1.0\" xmlns:xsl=\"http://www.w3.org/1999/XSL/Transform\">\n    <xsl:output method=\"text\" encoding=\"UTF-8\"/>\n    \n    <xsl:template match=\"/powerreviews\">\n        <xsl:text>\"page_id\",\"product_url\",\"name\",\"image_url\",\"category\",\"brand\",\"upc\",\"page_id_variant\"&#10;</xsl:text>\n        <xsl:apply-templates select=\"items/item\"/>\n    </xsl:template>\n\n    <xsl:template match=\"item\">\n        <xsl:text>\"</xsl:text><xsl:value-of select=\"normalize-space(page_id)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(product_url)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(name)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(image_url)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(category)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(brand)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(upc)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(page_id_variant)\"/><xsl:text>\"&#10;</xsl:text>\n    </xsl:template>\n</xsl:stylesheet>\n\');
        ');

        $this->addSql('
            INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES
                (\'brand\', \'37\', \'brand\'),
                (\'upc\', \'37\', \'EAN\'),
                (\'page_id_variant\', \'37\', \'page_id_variant\'),
                (\'page_id\', \'37\', \'page_id\'),
                (\'product_url\', \'37\', \'productUrl\'),
                (\'name\', \'37\', \'name\'),
                (\'image_url\', \'37\', \'image\'),
                (\'category\', \'37\', \'breadcrumb\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE `cameranu`.`productfeeds`
            DROP `xsl`;
        ');

        $this->addSql('DELETE FROM `productfeeds` WHERE `id` = \'37\';');
        $this->addSql('DELETE FROM `productfeed_fields` WHERE `feed_id` = \'37\';');
    }
}
