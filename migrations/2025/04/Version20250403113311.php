<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250403113311 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6304 create performance target tables';
    }

    public function up(Schema $schema): void
    {
        $targetsTable = $schema->createTable('second_hand_performance_targets');
        $targetsTable->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'notnull' => true,
            'unsigned' => true,
        ]);

        $targetsTable->addColumn('key', Types::STRING, [
            'notnull' => true
        ]);

        $targetsTable->addColumn('name', Types::STRING, [
            'notnull' => true
        ]);

        $targetsTable->addColumn('extra', Types::STRING, [
            'notnull' => true
        ]);

        $targetsTable->setPrimaryKey(['id']);

        $mainGroupTargetsTable = $schema->createTable('second_hand_main_group_performance_targets');
        $mainGroupTargetsTable->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'notnull' => true,
            'unsigned' => true,
        ]);

        $mainGroupTargetsTable->addColumn('performance_target_id', Types::INTEGER, [
            'notnull' => true,
            'unsigned' => true,
        ]);

        $mainGroupTargetsTable->addColumn('value', Types::DECIMAL, [
            'precision' => 7,
            'scale' => 2,
            'notnull' => true,
        ]);

        $mainGroupTargetsTable->addColumn('main_group_id', Types::INTEGER, [
            'notnull' => true,
        ]);

        $mainGroupTargetsTable->setPrimaryKey(['id']);
        $mainGroupTargetsTable->addForeignKeyConstraint(
            'hoofdgroepen',
            ['main_group_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ],
        'performance_target_main_group'
        );

        $mainGroupTargetsTable->addForeignKeyConstraint(
            'second_hand_main_group_performance_targets',
            ['performance_target_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ],
            'main_group_performance_target'
        );

        $mainGroupTargetsTable->addUniqueConstraint(['main_group_id', 'performance_target_id']);

        $productTargetsTable = $schema->createTable('second_hand_product_performance_targets');
        $productTargetsTable->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'notnull' => true,
            'unsigned' => true,
        ]);

        $productTargetsTable->addColumn('performance_target_id', Types::INTEGER, [
            'notnull' => true,
            'unsigned' => true,
        ]);

        $productTargetsTable->addColumn('value', Types::DECIMAL, [
            'precision' => 7,
            'scale' => 2,
            'notnull' => true,
        ]);

        $productTargetsTable->addColumn('product_id', Types::INTEGER, [
            'notnull' => true,
        ]);

        $productTargetsTable->setPrimaryKey(['id']);
        $productTargetsTable->addForeignKeyConstraint(
            'artikelen',
            ['product_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ],
            'performance_target_product'
        );

        $productTargetsTable->addForeignKeyConstraint(
            'second_hand_performance_targets',
            ['performance_target_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
                'onUpdate' => 'CASCADE',
            ],
            'product_performance_target'
        );

        $productTargetsTable->addUniqueConstraint(['product_id', 'performance_target_id']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('second_hand_main_group_performance_targets');
        $schema->dropTable('second_hand_product_performance_targets');
        $schema->dropTable('second_hand_performance_targets');
    }
}
