<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250423101859 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6392 Create PowerReviews table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('powerreviews');
        $table->addColumn('id', Types::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
        ]);
        $table->addColumn('product_id', Types::INTEGER, [
            'notnull' => true,
        ]);
        $table->addColumn('amount', Types::INTEGER, [
            'notnull' => true,
        ]);
        $table->addColumn('rating', Types::INTEGER, [
            'notnull' => true,
        ]);

        $table->setPrimaryKey(['id']);

        $table->addForeignKeyConstraint(
            'artikelen',
            ['product_id'],
            ['id'],
            ['onDelete' => 'NO ACTION', 'onUpdate' => 'NO ACTION'],
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('powerreviews');
    }
}
