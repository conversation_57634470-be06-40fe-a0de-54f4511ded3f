<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250425133215 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6227 Add coc_number column to quotes table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('quotes');
        $table->addColumn('coc_number', Types::STRING, [
            'length' => 255,
            'notnull' => false,
            'default' => null,
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('quotes');
        $table->dropColumn('coc_number');
    }
}
