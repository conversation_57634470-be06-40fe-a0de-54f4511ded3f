<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250418091012 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6345 Create tables for RFM history';
    }

    public function up(Schema $schema): void
    {
        $historyTabel = $schema->createTable('customer_rfm_history');
        $historyTabel->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'notnull' => true,
            'unsigned' => true,
        ]);
        $historyTabel->setPrimaryKey(['id']);
        $historyTabel->addColumn('customer_id', Types::INTEGER, [
            'notnull' => true,
        ]);
        $historyTabel->addColumn('segment', Types::STRING, ['notnull' => true]);
        $historyTabel->addColumn('timestamp', Types::DATETIME_MUTABLE, ['notnull' => true]);

        $historyTabel->addUniqueConstraint(['customer_id', 'segment', 'timestamp']);

        $historyTabel->addIndex(['segment'], 'idx_segment');
        $historyTabel->addIndex(['timestamp'], 'idx_timestamp');

        $snapShotTabel = $schema->createTable('customer_rfm_snapshot');
        $snapShotTabel->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'notnull' => true,
            'unsigned' => true,
        ]);
        $snapShotTabel->setPrimaryKey(['id']);
        $snapShotTabel->addColumn('segment', Types::STRING, ['notnull' => true]);
        $snapShotTabel->addColumn('amount', Types::INTEGER, ['notnull' => true]);
        $snapShotTabel->addColumn('snapshot_date', Types::DATETIME_MUTABLE, ['notnull' => true]);
        $snapShotTabel->addIndex(['segment'], 'idx_segment');
        $snapShotTabel->addIndex(['snapshot_date'], 'idx_snapshot_date');
        $snapShotTabel->addUniqueConstraint(['segment', 'snapshot_date']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('customer_rfm_history');
        $schema->dropTable('customer_rfm_snapshot');
    }
}
