<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250401132736 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6425 Add measurement_count column to cameranu.stock_availability_percentage table';
    }

    public function up(Schema $schema): void
    {
        // First add column with default 5 since this is what current calculations are based on
        $this->addSql('ALTER TABLE cameranu.stock_availability_percentage ADD measurement_count TINYINT NOT NULL DEFAULT 5');

        // Then remove default, this has to be set from the stock availability calculation command
        $this->addSql('ALTER TABLE cameranu.stock_availability_percentage MODIFY measurement_count TINYINT NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cameranu.stock_availability_percentage DROP COLUMN measurement_count');
    }
}
