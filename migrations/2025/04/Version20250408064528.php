<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250408064528 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6308 Add column for discrepancy snoozing to products table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('snoozed_discrepant_second_hand_products');
        $table->addColumn('id', Types::INTEGER, ['autoincrement' => true]);
        $table->setPrimaryKey(['id']);
        $table->addColumn('product_id', Types::INTEGER, ['notnull' => true]);
        $table->addColumn('snoozed_until', Types::DATETIME_MUTABLE, ['notnull' => true]);
        $table->addIndex(['snoozed_until'], 'idx_snoozed_until');

        $table->addForeignKeyConstraint(
            'artikelen',
            ['product_id'],
            ['id'],
            [
                'onDelete' => 'CASCADE',
            ],
            'snoozed_product'
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('snoozed_discrepant_second_hand_products');
    }
}
