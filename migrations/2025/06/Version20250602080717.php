<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20250602080717 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6087 add meta fields to events';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.events');
        $table->addColumn('meta_title', Types::STRING, [
            'notnull' => false,
        ]);
        $table->addColumn('meta_description', Types::TEXT, [
            'notnull' => false,
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.events');
        $table->dropColumn('meta_title');
        $table->dropColumn('meta_description');
    }
}
