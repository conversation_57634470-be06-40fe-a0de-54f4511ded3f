<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250611125641 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6774 Seperate PowerReview feed for Belgium';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `productfeeds` (`id`, `partner`, `hash`, `file_type`, `feed_type`, `xsl`)
            VALUES
                (\'42\', \'PowerReviewsBe\', \'6e26ed517be2753a6a68e6b1b1a49770\', \'csv\', \'product\', \'<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<xsl:stylesheet version=\"1.0\" xmlns:xsl=\"http://www.w3.org/1999/XSL/Transform\">\n    <xsl:output method=\"text\" encoding=\"UTF-8\"/>\n    \n    <xsl:template match=\"/powerreviewsbe\">\n        <xsl:text>\"page_id\",\"product_url\",\"name\",\"image_url\",\"category\",\"brand\",\"upc\",\"page_id_variant\"&#10;</xsl:text>\n        <xsl:apply-templates select=\"items/item\"/>\n    </xsl:template>\n\n    <xsl:template match=\"item\">\n        <xsl:text>\"</xsl:text><xsl:value-of select=\"normalize-space(page_id)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(product_url)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(name)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(image_url)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(category)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(brand)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(upc)\"/><xsl:text>\",\"</xsl:text>\n        <xsl:value-of select=\"normalize-space(page_id_variant)\"/><xsl:text>\"&#10;</xsl:text>\n    </xsl:template>\n</xsl:stylesheet>\n\');
        ');

        $this->addSql('
            INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES
                (\'brand\', \'42\', \'brand\'),
                (\'upc\', \'42\', \'EAN\'),
                (\'page_id_variant\', \'42\', \'page_id_variant\'),
                (\'page_id\', \'42\', \'page_id\'),
                (\'product_url\', \'42\', \'productUrlBe\'),
                (\'name\', \'42\', \'name\'),
                (\'image_url\', \'42\', \'image\'),
                (\'category\', \'42\', \'breadcrumb\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM `productfeeds` WHERE `id` = \'42\';');
        $this->addSql('DELETE FROM `productfeed_fields` WHERE `feed_id` = \'42\';');
    }
}
