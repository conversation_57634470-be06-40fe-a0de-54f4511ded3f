<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250617150353 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6616 - Add sell out premium to Adchieve feed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES
                (\'sellOut\', \'25\', \'sellOutPremium\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `productfeed_fields` WHERE `name` = \'sellOut\' and `feed_id` = 25;
        ');
    }
}
