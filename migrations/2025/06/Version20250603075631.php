<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250603075631 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6586 Add visibility column to faq_products';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('faq_products');
        $table->addColumn('visible', Types::BOOLEAN, [
            'notnull' => true,
            'default' => true,
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('faq_products');
        $table->dropColumn('visible');
    }
}
