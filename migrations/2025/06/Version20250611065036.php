<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250611065036 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6640 Store individual measurements for product availability';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage
            ADD COLUMN `measurement_1` TINYINT UNSIGNED DEFAULT NULL,
            ADD COLUMN `measurement_2` TINYINT UNSIGNED DEFAULT NULL,
            ADD COLUMN `measurement_3` TINYINT UNSIGNED DEFAULT NULL,
            ADD COLUMN `measurement_4` TINYINT UNSIGNED DEFAULT NULL,
            ADD COLUMN `measurement_5` TINYINT UNSIGNED DEFAULT NULL
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu.stock_availability_percentage
            DROP COLUMN `measurement_1`,
            DROP COLUMN `measurement_2`,
            DROP COLUMN `measurement_3`,
            DROP COLUMN `measurement_4`,
            DROP COLUMN `measurement_5`
        ');
    }
}
