<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250606073854 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6732 articles_stock_per_location table column for target WoS';
    }

    public function up(Schema $schema): void
    {
        $productsTable = $schema->getTable('articles_stock_per_location');
        $productsTable->addColumn('target_wos', 'integer', [
            'notnull' => false,
            'unsigned' => true,
        ]);
    }

    public function down(Schema $schema): void
    {
        $productsTable = $schema->getTable('articles_stock_per_location');
        $productsTable->dropColumn('target_wos');
    }
}
