<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Domain;
use Webdsign\GlobalBundle\Entity\Parking;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250613093412 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6633 new CBS parking folder and renaming te old one';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO cameranu.parkeren
            (id, domain_id, pos, omschrijving, info, flags)
            VALUES
            (:id, :domain_id, :pos, :description, :info, :flags)
        ', [
            'id' => Parking::CBS_TO_BE_JUDGED,
            'domain_id' => Domain::CAMERANU,
            'pos' => 19,
            'description' => 'Te controleren CBS-orders',
            'info' => '',
            'flags' => Parking::FLAG_ALLOW_PARKING,
        ]);

        $this->addSql('UPDATE cameranu.parkeren SET omschrijving = :description WHERE `id` = :id', [
            'id' => Parking::CBS_JUDGED,
            'description' => 'Gecontroleerde CBS-orders',
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM cameranu.parkeren where `id` = :id', ['id' => Parking::CBS_TO_BE_JUDGED]);

        $this->addSql('UPDATE cameranu.parkeren SET omschrijving = :description WHERE `id` = :id', [
            'id' => Parking::CBS_JUDGED,
            'description' => 'Binnenkomende CBS-orders',
        ]);
    }
}
