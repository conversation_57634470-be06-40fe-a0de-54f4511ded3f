<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250521122500 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6667 New columns for contact_acties table';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.content_acties');
        $table->addColumn('description_site_long', Types::TEXT, [
            'notnull' => false,
        ]);

        $table->addColumn('cta_text', Types::STRING, [
            'notnull' => true,
            'default' => 'Meer informatie',
        ]);
        $table->addColumn('title_listerpages', Types::STRING, [
           'notnull' => false,
        ]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.content_acties');
        $table->dropColumn('description_site_long');
        $table->dropColumn('cta_text');
        $table->dropColumn('title_listerpages');
    }
}
