<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Service\OpenAI\ProductImagesDescriber;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Constant\OpenAiConstants;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514124157 extends AbstractMigration
{
    public const array TABLE_ROW = [
        'class' => ProductImagesDescriber::class,
        'name' => 'Product afbeeldingen',
        'shortDescription' => 'Beschrijft de afbeeldingen van een product',
        'model' => OpenAiConstants::MODEL_GPT_4O_MINI,
        'prompt' => <<<PROMPT
        Beschrijf de afbeeldingen van het product in het kort in het Nederlands.
        In plaats van de product naam te gebruiken in je tekst, vervang deze met "%product.name%". Deze word dan later automatisch vervangen door de applicatie zelf.
        Deze tekst zal gebruikt worden voor de toegankelijkheid van applicaties, bijvoorbeeld voor screen readers, dus antwoord moet kort en bondig zijn.
        Begin je beschrijving altijd met de product naam.
        Schrijf de tekst met context dat de gebruiker al weet wat het product is, dus niet "Product is een...".
        Houd de focus op wat er in de afbeelding staat.
        Je bent altijd zeker van wat het product is.
        PROMPT,
    ];

    public function getDescription(): string
    {
        return 'CAM-6324 AI Assistant for describing product images';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'INSERT INTO `cameranu`.`ai_assistants` (class, name, short_description, model, prompt) VALUES (?, ?, ?, ?, ?)',
            [
                self::TABLE_ROW['class'],
                ...array_slice(array_values(self::TABLE_ROW), 1)
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            'DELETE FROM `cameranu`.`ai_assistants` WHERE class = ?',
            [
                self::TABLE_ROW['class'],
            ]
        );
    }
}
