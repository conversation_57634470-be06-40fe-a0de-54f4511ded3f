<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528082228 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6711 - Add specs references to pagebuilder pages';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('pages_specs');
        $table->addColumn('id', Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $table->addColumn('page_id', Types::INTEGER);
        $table->addColumn('spec_id', Types::INTEGER);
        $table->addColumn('value', Types::STRING, ['length' => 750]);

        $table->addOption('collate', 'utf8mb4_general_ci');
        $table->setPrimaryKey(['id']);
        $table->addForeignKeyConstraint('pages', ['page_id'], ['id'], ['onDelete' => 'CASCADE']);
        $table->addForeignKeyConstraint('specs_specifications', ['spec_id'], ['id'], ['onDelete' => 'CASCADE']);
        $table->addIndex(['page_id']);
        $table->addIndex(['spec_id', 'value']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('pages_specs');
    }
}
