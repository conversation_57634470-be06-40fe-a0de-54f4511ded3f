<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Type;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528083207 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6708 Correct rating column in powerreviews table';
    }

    public function up(Schema $schema): void
    {
        $schema
            ->getTable('powerreviews')
            ->modifyColumn('rating', [
                'type' => Type::getType(Types::DECIMAL),
                'precision' => 7,
                'scale' => 2,
            ]);
    }

    public function down(Schema $schema): void
    {
        $schema
            ->getTable('powerreviews')
            ->modifyColumn('rating', [
                'type' => Type::getType(Types::INTEGER),
                'length' => 11,
            ]);
    }
}
