<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250501074622 extends AbstractMigration
{
    private const string TABLE_NAME_SHIPPINGMETHOD_2 = 'verzendwijze2';
    private const string LANGUAGE = 'nl';
    private const int PARENT_ID = 64;

    public function getDescription(): string
    {
        return 'CAM-6508 - Checkout - Leveringsstap tekstfout (2x Gratis)';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            '
                UPDATE ' . self::TABLE_NAME_SHIPPINGMETHOD_2 . '
                SET verzendwijze = \'Bezorgd door PostNL\', verzendwijze_kort = \'PostNL aangetekend\'
                WHERE parent_id = :parentId and taal = :language
            ',
            [
                'parentId' => self::PARENT_ID,
                'language' => self::LANGUAGE
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            '
                UPDATE ' . self::TABLE_NAME_SHIPPINGMETHOD_2 . '
                SET verzendwijze = \'<strong>PostNL</strong> aangetekend gratis versturen\',
                verzendwijze_kort = \'PostNL aangetekend gratis\'
                WHERE parent_id = :parentId and taal = :language
            ',
            [
                'parentId' => self::PARENT_ID,
                'language' => self::LANGUAGE
            ]
        );
    }
}
