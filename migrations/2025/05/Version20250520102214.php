<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250520102214 extends AbstractMigration
{
    private const STOCK_ARTICLE_ORDER_IDX_NAME = 'idx_voorraad_artikel_id_bestelling_id';

    public function getDescription(): string
    {
        return 'CAM-6650 Add combined index for product ID and order ID to stock table';
    }

    public function up(Schema $schema): void
    {
        $stockTable = $schema->getTable('voorraad');
        $stockTable->addIndex([
            'artikel_id',
            'bestelling_id',
        ], self::STOCK_ARTICLE_ORDER_IDX_NAME);
    }

    public function down(Schema $schema): void
    {
        $stockTable = $schema->getTable('voorraad');
        $stockTable->dropIndex(self::STOCK_ARTICLE_ORDER_IDX_NAME);
    }
}
