<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250522134537 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6683 use products short name for PowerReviews product feed.';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`productfeed_fields` SET `key` = \'nameShort\' WHERE `id` = 778;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`productfeed_fields` SET `key` = \'name\' WHERE `id` = 778;
        ');
    }
}
