<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Service\OpenAI\ProductsSorter;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Constant\OpenAiConstants;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250526135924 extends AbstractMigration
{
    public const array TABLE_ROW = [
        'class' => ProductsSorter::class,
        'name' => 'Product sorteren',
        'shortDescription' => 'Sorteert de producten gebaseerd op een punten systeem',
        'model' => OpenAiConstants::MODEL_GPT_4O_MINI,
        'prompt' => <<<PROMPT
        Sorteer de lijst met producten.
        De Sortering wordt gedaan aan de hand van een punten systeem.
        De volgende categorieën kunnen punten geven aan een product:
            -Merk
            -Voorraadstatus
            -Soort product
        Hoe meer punten een product heeft hoe hoger hij in de lijst moet komen.
        Het punten systeem is als volgt:
            -Hoe bekender het merk hoe meer punten. Dit kan 1 tot 10 punten opleveren.
            -Hoe meer voorraad het product heeft, des te meer punten hij krijgt.
            Dit kan tussen de 1 tot 5 punten opleveren.
            -Camera\'s en lenzen moeten ook extra punten krijgen. Dit levert 2 punten op.
        Je mag niet zelf producten verzinnen en alleen de onderstaande informatie gebruiken.

        Het id van het product wordt ook weer gebruikt om het id te vullen.
        Zo ook met de naam.
        In sortingScore geef je terug hoeveel punten het product heeft gekregen, zonder uitleg.

        PROMPT,
    ];
    public const string TABLE_NAME_DASHBOARD_PRODUCTS_SORTING = 'dashboard_products_sorting';

    public function getDescription(): string
    {
        return 'CAM-6510 - Chatgpt assistent volgorde producten maken';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'INSERT INTO `cameranu`.`ai_assistants` (class, name, short_description, model, prompt)
                    VALUES (?, ?, ?, ?, ?)
                ',
            [
                self::TABLE_ROW['class'],
                ...array_slice(array_values(self::TABLE_ROW), 1)
            ]
        );

        $table = $schema->createTable(self::TABLE_NAME_DASHBOARD_PRODUCTS_SORTING);
        $table->addColumn('id', Types::INTEGER, ['autoincrement' => true]);
        $table->setPrimaryKey(['id']);
        $table->addColumn('productId', Types::INTEGER, ['notnull' => true]);
        $table->addColumn('sortingScore', Types::INTEGER, ['notnull' => true]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            'DELETE FROM `cameranu`.`ai_assistants` WHERE class = ?',
            [
                self::TABLE_ROW['class'],
            ]
        );

        $schema->dropTable(self::TABLE_NAME_DASHBOARD_PRODUCTS_SORTING);
    }
}
