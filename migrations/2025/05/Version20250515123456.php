<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250515123456 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add products relationship to ManufacturerGpsr entity';
    }

    public function up(Schema $schema): void
    {
        // Add products relationship to ManufacturerGpsr
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr ADD COLUMN IF NOT EXISTS `all_root_groups` BOOLEAN DEFAULT FALSE NOT NULL');
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr ADD COLUMN IF NOT EXISTS `brand` VARCHAR(255) NOT NULL');
        $this->addSql('
            CREATE TABLE IF NOT EXISTS cameranu.manufacturer_gpsr_root_group (
                id INT AUTO_INCREMENT NOT NULL,
                manufacturer_gpsr_id INT NOT NULL,
                root_group_id INT NOT NULL,
                PRIMARY KEY(id),
                INDEX IDX_MANUFACTURER_GPSR (manufacturer_gpsr_id),
                INDEX IDX_ROOT_GROUP (root_group_id),
                CONSTRAINT FK_MANUFACTURER_GPSR FOREIGN KEY (manufacturer_gpsr_id) REFERENCES cameranu.manufacturer_gpsr (id) ON DELETE CASCADE,
                CONSTRAINT FK_ROOT_GROUP FOREIGN KEY (root_group_id) REFERENCES cameranu.rootgroepen (id) ON DELETE CASCADE
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS cameranu.manufacturer_gpsr_root_group');
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr DROP COLUMN IF EXISTS `all_root_groups`');
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr DROP COLUMN IF EXISTS `brand`');
    }
}
