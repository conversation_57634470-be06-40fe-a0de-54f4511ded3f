<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250514090222 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6615 add blog_banner column to pages table';
    }

    public function up(Schema $schema): void
    {
        $pages = $schema->getTable('cameranu.pages');
        $pages->addColumn('blog_banner', 'string', [
            'length' => 255,
            'notnull' => true,
        ]);
    }

    public function down(Schema $schema): void
    {
        $pages = $schema->getTable('cameranu.pages');
        $pages->dropColumn('blog_banner');
    }
}
