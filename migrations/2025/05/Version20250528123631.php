<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250528123631 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6711 - Add expert review page columns';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('pages');
        $table->addColumn('expert_review', Types::BOOLEAN, ['default' => false]);
        $table->addColumn('expert_review_title', Types::STRING, ['notnull' => false]);
        $table->addColumn('expert_review_intro', Types::TEXT, ['notnull' => false]);
        $table->addColumn('expert_review_pros', Types::JSON, ['notnull' => false]);
        $table->addColumn('expert_review_cons', Types::JSON, ['notnull' => false]);
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('pages');
        $table->dropColumn('expert_review');
        $table->dropColumn('expert_review_title');
        $table->dropColumn('expert_review_intro');
        $table->dropColumn('expert_review_pros');
        $table->dropColumn('expert_review_cons');
    }
}
