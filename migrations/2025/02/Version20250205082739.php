<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Service\OpenAI\FaqWriter;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Constant\OpenAiConstants;

final class Version20250205082739 extends AbstractMigration
{
    public const string TABLE = '`cameranu`.`ai_assistants`';
    public const array DATA = [
        'class' => FaqWriter::class,
        'name' => 'FAQ-schrijver',
        'shortDescription' => 'Schrijft FAQ\'s voor producten',
        'model' => OpenAiConstants::MODEL_GPT_4O_MINI,
        'prompt' => <<<PROMPT
        Cameranu is een foto- en videowinkel voor de zakelijke gebruikers, professional, hobbyist en amateurfotograaf, videograaf en content creator.
        Schrijf als contentschrijver en copywriter een antwoord op veelgestelde vragen voor dit product.

        Mijn eerste bericht is een stuk tekst met productinformatie en specificaties. Daar hoef je niet op te antwoorden. Elk daaropvolgend bericht is een vraag over het product. Gebruik de productinformatie en specificaties en/of de bijgevoegde plaatjes om een goed antwoord te formuleren.

        Geef elke vraag en antwoord in een apart bericht terug.

        - Elk antwoord mag bestaan uit maximaal 5 zinnen.
        - Antwoorden kort en bondig formuleren: geen overbodige details die niet direct relevant zijn voor de vraag.
        - Duidelijk technische specificaties benoemen: bijvoorbeeld vermelden dat de lens geen optische stabilisatie heeft, maar zonder dit negatief te laten klinken.
        - Geen vage of subjectieve termen gebruiken: woorden zoals "bijzonder veelzijdig", "uitstekende prestaties" en "ideale keuze" moeten concreter geformuleerd worden.
        - Actieve schrijfstijl: de antwoorden moeten direct en to-the-point zijn zonder dubbelzinnige of herhalende zinsconstructies.

        Je antwoordt in het Nederlands en je levert alles aan in json-formaat.
        PROMPT
    ];

    public function getDescription(): string
    {
        return 'CAM-6077 faq writer';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                'insert into %s (class, name, short_description, model, prompt) values (?, ?, ?, ?, ?)',
                self::TABLE
            ),
            [
                self::DATA['class'],
                self::DATA['name'],
                self::DATA['shortDescription'],
                self::DATA['model'],
                self::DATA['prompt'],
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(sprintf('delete from %s where class = ?', self::TABLE), [
            self::DATA['class'],
        ]);
    }
}
