<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250213191621 extends AbstractMigration
{
    const string TABLE_POSTNL_TNT_STATUS_CODES_NAME = 'postnl_tnt_codes';
    const string TABLE_POSTNL_STATUS_NAME = 'postnl_status';
    const array TNT_STATUS_CODES = [
        1 => [1, 'Zending voorgemeld'],
        2 => [1, 'Zending in ontvangst genomen'],
        3 => [1, 'Zending afgehaald'],
        4 => [1, 'Zending niet afgehaald'],
        5 => [2, 'Zending gesorteerd'],
        6 => [2, 'Zending niet gesorteerd'],
        7 => [3, 'Zending in distributieproces'],
        8 => [3, 'Zending niet afgeleverd'],
        9 => [3, 'Zending bij douane'],
        11 => [4, 'Zending afgeleverd'],
        12 => [4, 'Zending beschikbaar op afhaallocatie'],
        13 => [1, 'Voorgemeld: nog niet aangenomen'],
        14 => [1, 'Voorgemeld: definitief niet aangenomen'],
        15 => [1, 'Manco collectie'],
        16 => [2, 'Manco sortering'],
        17 => [3, 'Manco distributie'],
        18 => [1, 'Definitief manco'],
        19 => [1, 'Zending afgekeurd'],
        20 => [3, 'Zending in inklaringsproces'],
        21 => [2, 'Zending in voorraad'],
        22 => [4, 'Zending afgehaald van Postkantoor'],
        23 => [4, 'Afhaalopdracht gecollecteerd'],
        99 => [99, 'Niet van toepassing'],
    ];

    public function getDescription(): string
    {
        return 'CAM-5645 - Add and fill Postnl Status & TNT Status Codes tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`'.self::TABLE_POSTNL_TNT_STATUS_CODES_NAME.'` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `phase_code` int(3) NOT NULL,
                `status_code` int(3) NOT NULL,
                `status_description` varchar(255) NOT NULL,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');

        $sql = 'INSERT INTO `cameranu`.`' . self::TABLE_POSTNL_TNT_STATUS_CODES_NAME . '` (`phase_code`, `status_code`, `status_description`) VALUES ';
        foreach (self::TNT_STATUS_CODES as $key => $code) {
            $sql .= $key !== 1 ? ',' : '';
            $sql .= '(' . $code[0] . ', ' . $key . ', \'' . $code[1] . '\')';
        }
        $this->addSql($sql);

        $this->addSql('
            CREATE TABLE IF NOT EXISTS `cameranu`.`'.self::TABLE_POSTNL_STATUS_NAME.'` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `postnl_id` int(11) NOT NULL,
                `tnt_status_code` int(11) NOT NULL,
                `timestamp` datetime NOT NULL,
                PRIMARY KEY (`id`),
                FOREIGN KEY (`postnl_id`) REFERENCES `cameranu`.`postnl` (`id`),
                FOREIGN KEY (`tnt_status_code`) REFERENCES `cameranu`.`'. self::TABLE_POSTNL_TNT_STATUS_CODES_NAME .'` (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu`.`' . self::TABLE_POSTNL_STATUS_NAME . '`');
        $this->addSql('DROP TABLE `cameranu`.`' . self::TABLE_POSTNL_TNT_STATUS_CODES_NAME . '`');
    }
}
