<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250224133231 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6216 Add extra terminal Eindhoven';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `betalingsmogelijkheden` (`id`, `pos`, `omschrijving`, `goedhart`, `flags`, `betaalwijze_id`)
            VALUES
                (\'476\', \'478\', \'EINDHOVEN A920 2\', \'pin_ein\', \'1\', \'3\');
        ');
        $this->addSql('
            INSERT INTO `pin_terminals` (`id`, `payment_type_id`, `terminal_id`, `psp`, `enabled`)
            VALUES
                (\'200\', \'476\', \'term_4eqo6RcRhcLW66X2HQ25J\', \'mollie\', \'1\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `pin_terminals` WHERE `id` = \'200\';
        ');
        $this->addSql('
            DELETE FROM `betalingsmogelijkheden` WHERE `id` = \'476\';
        ');
    }
}
