<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250218105244 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6096 stock snapshots';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
        <<<SQL
        CREATE TABLE IF NOT EXISTS `cameranu_analysis`.`stock_snapshots` (
            `id` int(11) NOT NULL,
            `snapshot_year` year(4) NOT NULL,
            `artikel_id` int(11) NOT NULL DEFAULT 0,
            `datum_in` date NOT NULL DEFAULT '0000-00-00',
            `date_out` datetime DEFAULT NULL,
            `tstamp_in` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
            `factuur_leverancier` varchar(50) NOT NULL,
            `barcode` varchar(30) NOT NULL DEFAULT '',
            `ordernummer` bigint(20) NOT NULL DEFAULT 0,
            `bestelling_id` int(11) NOT NULL DEFAULT 0,
            `prijs_verkoop` decimal(9,2) NOT NULL DEFAULT 0.00,
            `prijs_verkoop_ex` decimal(10,3) NOT NULL DEFAULT 0.000,
            `prijs_inkoop` decimal(8,2) NOT NULL DEFAULT 0.00,
            `prijs_korting` decimal(6,2) NOT NULL DEFAULT 0.00,
            `prijs_verwbijdrage` decimal(6,2) NOT NULL DEFAULT 0.00,
            `stock_value` decimal(12,4) NOT NULL,
            `btw` decimal(3,1) NOT NULL,
            `leverancier_id` int(11) NOT NULL DEFAULT 0,
            `retour_parent` int(11) DEFAULT NULL,
            `info` tinytext NOT NULL,
            `user_id` int(11) NOT NULL DEFAULT 0,
            `ingescand_door` int(11) NOT NULL DEFAULT 0,
            `inserted_stock_location_id` int(11) DEFAULT NULL,
            `locatie` char(1) NOT NULL,
            `bestelmemo_id` int(11) DEFAULT NULL,
            `flags` int(11) NOT NULL DEFAULT 0,
            `stock_location_id` int(11) DEFAULT NULL,
            `location_compartment_node_id` int(11) DEFAULT NULL,
            PRIMARY KEY (`id`, `snapshot_year`),
            KEY `voorraad_artikel_id` (`artikel_id`),
            KEY `voorraad_ordernummer` (`ordernummer`),
            KEY `voorraad_barcode` (`barcode`),
            KEY `voorraad_datum_in` (`datum_in`),
            KEY `voorraad_bestelling_id` (`bestelling_id`),
            KEY `voorraad_leverancier_id` (`leverancier_id`),
            KEY `voorraad_stock_location_id` (`stock_location_id`) USING BTREE,
            KEY `voorraad_bestelmemo_id` (`bestelmemo_id`),
            KEY `voorraad_inserted_stock_location_id` (`inserted_stock_location_id`),
            KEY `voorraad_date_out` (`date_out`) USING BTREE,
            KEY `voorraad_composite_for_pdascan` (`barcode`,`stock_location_id`,`ordernummer`,`bestelling_id`,`user_id`,`date_out`),
            KEY `voorraad_location_compartment_node_id` (`location_compartment_node_id`)
        )
        ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        PARTITION BY RANGE (snapshot_year) (
            PARTITION p9999 VALUES LESS THAN MAXVALUE
        )
        SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu_analysis`.`stock_snapshots`');
    }
}
