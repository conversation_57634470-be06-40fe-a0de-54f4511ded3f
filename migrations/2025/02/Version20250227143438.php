<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\StockLogAction;

final class Version20250227143438 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4147 remove stock that is sent back to suppliers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO cameranu.stock_log_action VALUES (:id, :title)', [
            'id' => StockLogAction::STOCK_RETURN_TO_SUPPLIER,
            'title' => 'Voorraad retour naar leverancier',
        ]);
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM cameranu.stock_log_action WHERE id = :id', [
            'id' => StockLogAction::STOCK_RETURN_TO_SUPPLIER,
        ]);
    }
}
