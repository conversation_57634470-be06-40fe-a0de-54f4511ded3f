<?php

declare(strict_types=1);

namespace App\Migrations;

use <PERSON><PERSON><PERSON>le\MigrationHelper\ManagesForeignKeys;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250203103950 extends AbstractMigration
{
    use ManagesForeignKeys;
    private const string ADDITIONAL_TRADE_IN_VALUE_TABLE = 'cameranu.quote_additional_trade_in_value';
    private const string ADDITIONAL_TRADE_IN_ACTION_TABLE = 'cameranu.quote_additional_trade_in_action';
    private const string ADDITIONAL_TRADE_IN_ACTION_OPTIONS_TABLE = 'cameranu.quote_additional_trade_in_action_options';

    public function getDescription(): string
    {
        return 'CAM-5163 Add tables for instant-quote actions';
    }

    public function up(Schema $schema): void
    {
        $valueTable = $schema->createTable(self::ADDITIONAL_TRADE_IN_VALUE_TABLE);
        $valueTable->addColumn('id',  Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $valueTable->addColumn('quote_id',  Types::INTEGER, ['notnull' => false, 'unsigned' => true]);
        $valueTable->addColumn('quote_product_id',  Types::INTEGER, ['notnull' => false, 'unsigned' => true]);
        $valueTable->addColumn('additional_value_customer', Types::FLOAT, ['notnull' => true, 'default' => 0]);
        $valueTable->addColumn('additional_value_definitive', Types::FLOAT, ['notnull' => true, 'default' => 0]);
        $valueTable->addColumn('additional_percentage', Types::INTEGER, ['notnull' => true, 'default' => 0]);
        $valueTable->addColumn('discountcode_id', Types::INTEGER, ['notnull' => false, 'default' => 0, 'unsigned' => false]);
        $valueTable->addColumn('quote_trade_in_action_id', Types::INTEGER, ['notnull' => false, 'default' => 0, 'unsigned' => true]);
        $valueTable->setPrimaryKey(['id']);

        $actionTable = $schema->createTable(self::ADDITIONAL_TRADE_IN_ACTION_TABLE);
        $actionTable->addColumn('id',  Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $actionTable->addColumn('description',  Types::STRING, ['notnull' => false]);
        $actionTable->addColumn('percentage', Types::INTEGER, ['notnull' => true, 'default' => 0]);
        $actionTable->addColumn('start_date', Types::DATETIME_IMMUTABLE);
        $actionTable->addColumn('end_date', Types::DATETIME_IMMUTABLE);
        $actionTable->setPrimaryKey(['id']);

        $optionsTable = $schema->createTable(self::ADDITIONAL_TRADE_IN_ACTION_OPTIONS_TABLE);
        $optionsTable->addColumn('id',  Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $optionsTable->addColumn('quote_trade_in_action_id',  Types::INTEGER, ['notnull' => true, 'unsigned' => true]);
        $optionsTable->addColumn('value',  Types::STRING, ['notnull' => false]);
        $optionsTable->addColumn('type',  Types::STRING, ['notnull' => true]);
        $optionsTable->addColumn('name',  Types::STRING, ['notnull' => false]);
        $optionsTable->addColumn('description',  Types::STRING, ['notnull' => false]);
        $optionsTable->setPrimaryKey(['id']);

        $valueTable->addForeignKeyConstraint(
            'cameranu.quotes',
            ['quote_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_additional_trade_in_value_quote'
        );

        $valueTable->addForeignKeyConstraint(
            'cameranu.quote_products',
            ['quote_product_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_additional_trade_in_value_quote_product'
        );

        $valueTable->addForeignKeyConstraint(
            'cameranu.discountcodes',
            ['discountcode_id'],
            ['discountId'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_additional_trade_in_action_discount_code'
        );

        $valueTable->addForeignKeyConstraint(
            self::ADDITIONAL_TRADE_IN_ACTION_TABLE,
            ['quote_trade_in_action_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_additional_trade_in_action'
        );

        $optionsTable->addForeignKeyConstraint(
            self::ADDITIONAL_TRADE_IN_ACTION_TABLE,
            ['quote_trade_in_action_id'],
            ['id'],
            [
                'onUpdate' => 'CASCADE',
                'onDelete' => 'CASCADE',
            ],
            'fk_option_additional_trade_in_action'
        );

        $actionTable->addIndex(['start_date'], 'idx_start_date');
        $actionTable->addIndex(['end_date'], 'idx_end_date');
        $optionsTable->addUniqueConstraint(['quote_trade_in_action_id', 'value', 'type'], 'unique_action_value_type');
    }

    public function down(Schema $schema): void
    {
        $optionsTable = $schema->getTable(self::ADDITIONAL_TRADE_IN_ACTION_OPTIONS_TABLE);
        foreach ($optionsTable->getIndexes() as $index) {
            $optionsTable->dropIndex($index->getName());
        }

        foreach ($optionsTable->getForeignKeys() as $foreignKey) {
            $optionsTable->removeForeignKey($foreignKey->getName());
        }

        $valueTable = $schema->getTable(self::ADDITIONAL_TRADE_IN_VALUE_TABLE);
        foreach ($valueTable->getIndexes() as $index) {
            $valueTable->dropIndex($index->getName());
        }

        foreach ($valueTable->getForeignKeys() as $foreignKey) {
            $valueTable->removeForeignKey($foreignKey->getName());
        }

        $schema->dropTable(self::ADDITIONAL_TRADE_IN_ACTION_OPTIONS_TABLE);
        $schema->dropTable(self::ADDITIONAL_TRADE_IN_VALUE_TABLE);
        $schema->dropTable(self::ADDITIONAL_TRADE_IN_ACTION_TABLE);
    }
}
