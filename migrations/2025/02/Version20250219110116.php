<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250219110116 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6098 stock movement';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
        <<<SQL
        CREATE TABLE IF NOT EXISTS `cameranu_analysis`.`stock_movement` (
            `stock_id` int(11) NOT NULL,
            `date_calculated` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `root_group_id` int(11) DEFAULT NULL,
            `main_group_id` int(11) DEFAULT NULL,
            `sub_group_id` int(11) DEFAULT NULL,
            `parent` varchar(255) DEFAULT NULL,
            `product_name` varchar(255) NOT NULL,
            `root_group_name` varchar(255) DEFAULT NULL,
            `main_group_name` varchar(255) DEFAULT NULL,
            `sub_group_name` varchar(255) DEFAULT NULL,
            `purchase_price` decimal(8,2) DEFAULT NULL,
            `selling_price_ex` decimal(10,3) DEFAULT NULL,
            `selling_price_inc` decimal(9,2) DEFAULT NULL,
            `vat` decimal(3,1) DEFAULT NULL,
            `stock_value` decimal(12,4) NOT NULL,
            -- fields marking stock going in
            `initial_stock` tinyint(1) DEFAULT NULL,
            `closing_stock` tinyint(1) DEFAULT NULL,
            `purchased` tinyint(1) DEFAULT NULL,
            `return_in_stock` tinyint(1) DEFAULT NULL,
            -- fields marking stock going out
            `sold` tinyint(1) DEFAULT NULL,
            `return_to_supplier` tinyint(1) DEFAULT NULL,
            `missing` tinyint(1) DEFAULT NULL,
            -- sum of in and out
            `total` tinyint(1) DEFAULT NULL,
            `difference` tinyint(1) DEFAULT NULL,
            PRIMARY KEY (`stock_id`, `date_calculated`),
            KEY `stock_id` (`stock_id`),
            KEY `product_id` (`product_id`),
            KEY `root_group_id` (`root_group_id`),
            KEY `main_group_id` (`main_group_id`),
            KEY `sub_group_id` (`sub_group_id`),
            KEY `purchase_price` (`purchase_price`),
            KEY `selling_price_ex` (`selling_price_ex`),
            KEY `selling_price_inc` (`selling_price_inc`),
            KEY `vat` (`vat`),
            KEY `stock_value` (`stock_value`),
            KEY `initial_stock` (`initial_stock`),
            KEY `closing_stock` (`closing_stock`),
            KEY `purchased` (`purchased`),
            KEY `return_in_stock` (`return_in_stock`),
            KEY `sold` (`sold`),
            KEY `return_to_supplier` (`return_to_supplier`),
            KEY `missing` (`missing`),
            KEY `total` (`total`),
            KEY `difference` (`difference`)
        )
        ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        PARTITION BY RANGE (date_calculated) (
            PARTITION p99999999 VALUES LESS THAN MAXVALUE
        )
        SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `cameranu_analysis`.`stock_movement`');
    }
}
