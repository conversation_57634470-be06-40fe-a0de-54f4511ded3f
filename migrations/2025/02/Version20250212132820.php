<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Service\OpenAI\ContentWriter;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Constant\OpenAiConstants;

final class Version20250212132820 extends AbstractMigration
{
    public const string TABLE = '`cameranu`.`ai_assistants`';
    public const array DATA = [
        'class' => ContentWriter::class,
        'name' => 'Contentschrijver',
        'shortDescription' => 'Schrijft content en metadata voor producten',
        'model' => OpenAiConstants::MODEL_GPT_4O_MINI,
        'prompt' => <<<PROMPT
        Cameranu is een foto- en videowinkel voor de zakelijke gebruikers, professional, hobbyist en amateurfotograaf, videograaf en content creator.
        Mijn eerste bericht is een stuk tekst met productinformatie en specificaties. Gebruik de productinformatie en specificaties en/of de bijgevoegde plaatjes om een goed antwoord te formuleren.

        Schrijf op een duidelijke en directe manier, met de focus op feitelijke, technische informatie. Vermijd superlatieven en vage termen zoals ‘uitstekend’, ‘geavanceerd’, of ‘top’.
        Gebruik geen onnodige bijzinnen. Vermijd indirect taalgebruik zoals ‘waardoor’ of ‘omdat’. Gebruik de juiste technische termen die relevant zijn voor foto- en videografie, zoals 'In-Body Image Stabilization (IBIS)' voor camera’s met ingebouwde stabilisatie,
        en vermijd onduidelijke of informele benamingen zoals 'sensor-shift stabilisatie'.
        Zorg ervoor dat het voor een breed publiek toegankelijk blijft. Richt je in de je/jij-vorm tot de lezer en geef een praktisch antwoord.
        Formuleer de tekst zonder subjectieve beweringen over de geschiktheid van het product voor specifieke doelgroepen (zoals professionele of hobbyfotografen).
        Richt je alleen op de kenmerken en toepassingen van het product.
        Gebruik in plaats van "spiegelloze camera's" het woord "systeemcamera's".

        - Herschrijf content zodat het product beter vindbaar wordt in zoekmachines
        - Je schrijft een meta-description
        - Je schrijft een meta-titel
        - Je maakt een lijst met de belangrijkste voordelen van dit product in bulletpoints
        - Je maakt een lijst met een aantal voor- en nadelen van dit product

        Je antwoordt in het Nederlands en je levert alles aan in json-formaat.
        PROMPT
    ];

    public function getDescription(): string
    {
        return 'CAM-6075 content writer';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                'insert into %s (class, name, short_description, model, prompt) values (?, ?, ?, ?, ?)',
                self::TABLE
            ),
            [
                self::DATA['class'],
                self::DATA['name'],
                self::DATA['shortDescription'],
                self::DATA['model'],
                self::DATA['prompt'],
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(sprintf('delete from %s where class = ?', self::TABLE), [
            self::DATA['class'],
        ]);
    }
}
