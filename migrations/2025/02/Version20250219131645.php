<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250219131645 extends AbstractMigration
{
    private const string TABLE_NAME_FRONTPAGE_USP = 'frontpage_usp';
    private const string COLUMN_NAME_CUSTOMER_TYPE = 'customer_type';
    private const string TABLE_NAME_PAGES_BLOCKTYPES = 'pages_blocktypes';
    private const string TYPE_NAME = 'customerTypeSwitch';
    private const string CATEGORY_NAME = 'Content';

    public function getDescription(): string
    {
        return 'CAM-5725 - Frontpagetool USPs voor zakelijke website';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_FRONTPAGE_USP)->addColumn(
            self::COLUMN_NAME_CUSTOMER_TYPE,
            Types::STRING,
            [
                'length' => 255,
                'notnull' => false,
            ]
        );

        $this->addSql(sprintf(
            'INSERT INTO %s (`created`, `modified`, `type`, `sort`, `category`)
            VALUES (NOW(), NOW(), \'%s\', 80, \'%s\')',
            self::TABLE_NAME_PAGES_BLOCKTYPES,
            self::TYPE_NAME,
            self::CATEGORY_NAME
        ));
    }

    public function down(Schema $schema): void
    {
        $schema->getTable(self::TABLE_NAME_FRONTPAGE_USP)->dropColumn(self::COLUMN_NAME_CUSTOMER_TYPE);

        $this->addSql(sprintf(
            'DELETE FROM %s
            WHERE category = \'%s\' AND type = \'%s\'',
            self::TABLE_NAME_PAGES_BLOCKTYPES,
            self::CATEGORY_NAME,
            self::TYPE_NAME
        ));
    }
}
