<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250220105407 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5549 Add new parking folder';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            INSERT INTO `cameranu`.`parkeren` (`id`, `domain_id`, `pos`, `omschrijving`, `info`, `flags`)
            VALUES
                (\'2439\', \'1\', \'140\', \'Later sturen Urk\', \'\', \'40\');
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            DELETE FROM `cameranu`.`parkeren` where `id` = \'2439\';`
        ');
    }
}
