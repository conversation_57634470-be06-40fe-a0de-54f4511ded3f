<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20250204125922 extends AbstractMigration
{
    private const string TABLE_FAQ_QUESTIONS = 'faq_questions';
    private const string TABLE_FAQ_PRODUCTS = 'faq_products';

    public function getDescription(): string
    {
        return 'CAM-6079 beheren van productfaqs';
    }

    public function up(Schema $schema): void
    {
        $questionsTable = $schema->createTable(self::TABLE_FAQ_QUESTIONS);
        $questionsTable->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $questionsTable->addColumn('specs_profile_id', Types::INTEGER);
        $questionsTable->addColumn('active', Types::BOOLEAN);
        $questionsTable->addColumn('question', Types::STRING);
        $questionsTable->setPrimaryKey(['id']);
        $questionsTable->addForeignKeyConstraint(
            'cameranu.specs_profiles',
            ['specs_profile_id'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE']
        );

        $productsTable = $schema->createTable(self::TABLE_FAQ_PRODUCTS);
        $productsTable->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $productsTable->addColumn('product_id', Types::INTEGER);
        $productsTable->addColumn('question_id', Types::INTEGER, [
            'unsigned' => true,
        ]);
        $productsTable->addColumn('answer', Types::TEXT);
        $productsTable->setPrimaryKey(['id']);
        $productsTable->addForeignKeyConstraint(
            'cameranu.faq_questions',
            ['question_id'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE']
        );
        $productsTable->addForeignKeyConstraint(
            'cameranu.artikelen',
            ['product_id'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE']
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE_FAQ_PRODUCTS);
        $schema->dropTable(self::TABLE_FAQ_QUESTIONS);
    }
}
