<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20250203151910 extends AbstractMigration
{
    private const STRING TABLE = 'ai_assistants';

    public function getDescription(): string
    {
        return 'CAM-6074 beheren van OpenAI prompts';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable(self::TABLE);

        $table->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('class', Types::STRING);
        $table->addColumn('name', Types::STRING);
        $table->addColumn('short_description', Types::TEXT, [
            'notnull' => false,
        ]);
        $table->addColumn('model', 'ai_assistant_model_enum');
        $table->addColumn('prompt', Types::TEXT);
        $table->addColumn('assistant_id', Types::STRING, [
            'notnull' => false,
        ]);

        $table->setPrimaryKey(['id']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE);
    }
}
