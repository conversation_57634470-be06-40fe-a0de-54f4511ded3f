<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250312144924 extends AbstractMigration
{
    private const string DISCOUNT_CODE_TABLE = 'discountcodes';
    private const string DISCOUNT_CODE_BATCH_TABLE = 'discount_code_batches';
    public function getDescription(): string
    {
        return 'CAM-5995 add only_one_per_order_line option to discountcodes and batches';
    }

    public function up(Schema $schema): void
    {
        $discountCodeTable = $schema->getTable(self::DISCOUNT_CODE_TABLE);
        $discountCodeTable->addColumn('only_one_per_order_line', Types::BOOLEAN, [
            'notnull' => true,
            'default' => false,
        ]);

        $discountCodeTable->addIndex(['only_one_per_order_line'], 'idx_only_one_per_order_line');

        $discountCodeBatchTable = $schema->getTable(self::DISCOUNT_CODE_BATCH_TABLE);
        $discountCodeBatchTable->addColumn('only_one_per_order_line', Types::BOOLEAN, [
            'notnull' => true,
            'default' => false,
        ]);

        $discountCodeBatchTable->addIndex(['only_one_per_order_line'], 'idx_only_one_per_order_line');
    }

    public function down(Schema $schema): void
    {
        $discountCodeTable = $schema->getTable(self::DISCOUNT_CODE_TABLE);
        $discountCodeTable->dropColumn('only_one_per_order_line');

        $discountCodeBatchTable = $schema->getTable(self::DISCOUNT_CODE_BATCH_TABLE);
        $discountCodeBatchTable->dropColumn('only_one_per_order_line');
    }
}
