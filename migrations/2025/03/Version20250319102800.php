<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250319102800 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6156 Rename mail_content types';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "levertijden" WHERE `type` = "standaardmail"');
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "reparatie" WHERE `type` = "schade"');
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "pakketten" WHERE `type` = "lease"');
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "standaardmail" WHERE `type` = "bestelling"');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "bestelling" WHERE `type` = "standaardmail"');
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "lease" WHERE `type` = "pakketten"');
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "schade" WHERE `type` = "reparatie"');
        $this->addSql('UPDATE `cameranu`.`mail_content` SET `type` = "standaardmail" WHERE `type` = "levertijden"');
    }
}
