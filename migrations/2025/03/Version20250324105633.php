<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250324105633 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6342 add table for stock factors';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `cameranu`.`second_hand_product_stock_factors` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `product_id` int(11) NOT NULL,
              `rank` int(11) NOT NULL,
              `stock` int(11) NOT NULL,
              `average_sales_rate` decimal(7,2) NOT NULL,
              `days_sales_in_inventory_quarter` decimal(7,2) NOT NULL,
              `days_sales_in_inventory_year` decimal(7,2) NOT NULL,
              `conversion_rate` int(11) NOT NULL,
              `last_out` datetime DEFAULT NULL,
              `last_in` datetime DEFAULT NULL,
              `timestamp` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `product_id_2` (`product_id`),
              KEY `product_id` (`product_id`),
              CONSTRAINT `second_hand_product_stock_factors_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `artikelen` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci'
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('cameranu.second_hand_product_stock_factors');
    }
}
