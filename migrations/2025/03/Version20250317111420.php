<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use <PERSON>B<PERSON>le\MigrationHelper\UrkMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250317111420 extends UrkMigration
{
    public function getDescription(): string
    {
        return 'CAM-6072 create serviceStatistics table';
    }

    public function upForUrk(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `cameranu_urk`.`service_statistics` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `finished_tickets` int(11) NOT NULL DEFAULT 0,
              `snoozed_tickets` int(11) NOT NULL DEFAULT 0,
              `sent_mails` int(11) NOT NULL DEFAULT 0,
              `placed_notes` int(11) NOT NULL DEFAULT 0,
              `planned_hours` decimal(3,1) NOT NULL DEFAULT 0.0,
              `task_id` int(11) NOT NULL,
              `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `task_type` (`task_id`),
              KEY `timestamp` (`timestamp`),
              CONSTRAINT `service_statistics_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
              CONSTRAINT `service_statistics_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `schedule_roleTasks` (`taskId`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ');
    }

    public function downForUrk(Schema $schema): void
    {
        $schema->dropTable('service_statistics');
    }
}
