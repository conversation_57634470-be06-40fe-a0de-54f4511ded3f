<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Service\OpenAI\SpecWriter;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Constant\OpenAiConstants;

final class Version20250328073150 extends AbstractMigration
{
    public const string TABLE = '`cameranu`.`ai_assistants`';
    public const array DATA = [
        'class' => SpecWriter::class,
        'name' => 'Specificatieschrijver',
        'shortDescription' => 'Schrijft specificaties voor producten',
        'model' => OpenAiConstants::MODEL_GPT_4O_MINI,
        'prompt' => <<<PROMPT
        Mijn eerste bericht is een stuk tekst met productinformatie en/of specificaties. Gebruik de productinformatie en specificaties en/of de bijgevoegde plaatjes om een goed antwoord te formuleren. Verder stuur ik een array mee in json formaat waarin alle specificaties die nu bekend zijn meegestuurd worden. Ik wil elke specificatie terugkrijgen van je.

        Dit is een voorbeeld hoe ik specificaties naar je opstuur:
        {
            "Algemene info": [
                {
                    "id": 267,
                    "name": "Merk",
                    "description": "",
                    "currentValue": "Canon"
                },
                {
                    "id": 26888,
                    "name": "Type product",
                    "description": "Digitale camera, Analoge camera",
                    "currentValue": "Digitale camera"
                },
                {
                    "id": 271,
                    "name": "Type camera",
                    "description": "Systeemcamera, Spiegelreflexcamera, etc",
                    "currentValue": "Compact camera"
                }
        }

        Ga elke specificatie die ik je geef langs. Gebruik "description" als instructie hoe het veld in te vullen. "currentValue" is de huidige ingevulde waarde. Controleer of deze klopt a.d.h.v. de meegegeven content. Gebruik deze om "value" te vullen. Een lege "currentValue" probeer je te vullen a.d.h.v. de meegegeven content. Het veld "name" mag je 1 op 1 doorgeven.

        Je antwoordt in het Nederlands en je levert alles aan in json-formaat.
        PROMPT
    ];

    public function getDescription(): string
    {
        return 'CAM-6076 spec writer';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            sprintf(
                'insert into %s (class, name, short_description, model, prompt) values (?, ?, ?, ?, ?)',
                self::TABLE
            ),
            [
                self::DATA['class'],
                self::DATA['name'],
                self::DATA['shortDescription'],
                self::DATA['model'],
                self::DATA['prompt'],
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(sprintf('delete from %s where class = ?', self::TABLE), [
            self::DATA['class'],
        ]);
    }
}
