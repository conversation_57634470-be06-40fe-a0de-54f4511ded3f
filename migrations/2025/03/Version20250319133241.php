<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Doctrine\DBAL\Types\Types;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250319133241 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6300 Add CompetitorData entities';
    }

    public function up(Schema $schema): void
    {
        $competitor = $schema->createTable('cameranu.competitor');
        $competitor->addColumn('key', Types::STRING);
        $competitor->addColumn('name', Types::STRING);
        $competitor->setPrimaryKey(['key']);

        $intakePrice = $schema->createTable('cameranu.competitor_product_buy_price');
        $intakePrice->addColumn('id', Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $intakePrice->addColumn('source_key', Types::STRING);
        $intakePrice->addColumn('product_id', Types::INTEGER);
        $intakePrice->addColumn('competitor_key', Types::STRING);
        $intakePrice->addColumn('price', Types::FLOAT);
        $intakePrice->addColumn('data_forest_indexed_report_id', Types::INTEGER, ['notnull' => false, 'unsigned' => true]);
        $intakePrice->addColumn('created_at', Types::DATETIME_IMMUTABLE, ['default' => 'CURRENT_TIMESTAMP']);
        $intakePrice->addColumn('indexed_at', Types::DATETIME_IMMUTABLE);
        $intakePrice->setPrimaryKey(['id']);
        $intakePrice->addForeignKeyConstraint('cameranu.artikelen', ['product_id'], ['id']);
        $intakePrice->addForeignKeyConstraint('cameranu.competitor', ['competitor_key'], ['key']);
        $intakePrice->addForeignKeyConstraint('cameranu.data_forest_indexed_report', ['data_forest_indexed_report_id'], ['id']);
        $intakePrice->addIndex(['indexed_at']);
        $intakePrice->addIndex(['product_id', 'competitor_key', 'indexed_at']);

        $sellPrice = $schema->createTable('cameranu.competitor_product_sell_price');
        $sellPrice->addColumn('id', Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $sellPrice->addColumn('source_key', Types::STRING);
        $sellPrice->addColumn('product_id', Types::INTEGER);
        $sellPrice->addColumn('competitor_key', Types::STRING);
        $sellPrice->addColumn('price', Types::FLOAT);
        $sellPrice->addColumn('competitor_monitor_product_id', Types::INTEGER, ['notnull' => false, 'unsigned' => true]);
        $sellPrice->addColumn('created_at', Types::DATETIME_IMMUTABLE, ['default' => 'CURRENT_TIMESTAMP']);
        $sellPrice->addColumn('indexed_at', Types::DATETIME_IMMUTABLE);
        $sellPrice->setPrimaryKey(['id']);
        $sellPrice->addForeignKeyConstraint('cameranu.artikelen', ['product_id'], ['id']);
        $sellPrice->addForeignKeyConstraint('cameranu.competitor', ['competitor_key'], ['key']);
        $sellPrice->addIndex(['competitor_monitor_product_id', 'indexed_at']);
        $sellPrice->addIndex(['indexed_at']);
        $sellPrice->addIndex(['product_id', 'competitor_key', 'indexed_at']);

        $stock = $schema->createTable('cameranu.competitor_product_stock');
        $stock->addColumn('id', Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $stock->addColumn('source_key', Types::STRING);
        $stock->addColumn('product_id', Types::INTEGER);
        $stock->addColumn('competitor_key', Types::STRING);
        $stock->addColumn('amount', Types::INTEGER);
        $stock->addColumn('competitor_monitor_product_id', Types::INTEGER, ['notnull' => false, 'unsigned' => true]);
        $stock->addColumn('created_at', Types::DATETIME_IMMUTABLE, ['default' => 'CURRENT_TIMESTAMP']);
        $stock->addColumn('indexed_at', Types::DATETIME_IMMUTABLE);
        $stock->setPrimaryKey(['id']);
        $stock->addForeignKeyConstraint('cameranu.artikelen', ['product_id'], ['id']);
        $stock->addForeignKeyConstraint('cameranu.competitor', ['competitor_key'], ['key']);
        $stock->addIndex(['competitor_monitor_product_id', 'indexed_at']);
        $stock->addIndex(['indexed_at']);
        $stock->addIndex(['product_id', 'competitor_key', 'indexed_at']);

        $dataforestIndexedReport = $schema->createTable('cameranu.data_forest_indexed_report');
        $dataforestIndexedReport->addColumn('id', Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $dataforestIndexedReport->addColumn('path', Types::STRING);
        $dataforestIndexedReport->addColumn('external_indexed_at', Types::DATETIME_IMMUTABLE);
        $dataforestIndexedReport->addColumn('indexed_at', Types::DATETIME_IMMUTABLE, ['default' => 'CURRENT_TIMESTAMP']);
        $dataforestIndexedReport->setPrimaryKey(['id']);
        $dataforestIndexedReport->addUniqueIndex(['path']);

        $competitorMonitorImports = $schema->createTable('cameranu.competitor_monitor_imports');
        $competitorMonitorImports->addColumn('id', Types::INTEGER, ['autoincrement' => true, 'unsigned' => true]);
        $competitorMonitorImports->addColumn('product_count', Types::INTEGER, ['unsigned' => true]);
        $competitorMonitorImports->addColumn('last_scanned_at', Types::DATETIME_IMMUTABLE);
        $competitorMonitorImports->addColumn('indexed_at', Types::DATETIME_IMMUTABLE, ['default' => 'CURRENT_TIMESTAMP']);
        $competitorMonitorImports->setPrimaryKey(['id']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('cameranu.competitor_monitor_imports');
        $schema->dropTable('cameranu.data_forest_indexed_report');
        $schema->dropTable('cameranu.competitor_product_stock');
        $schema->dropTable('cameranu.competitor_product_sell_price');
        $schema->dropTable('cameranu.competitor_product_buy_price');
        $schema->dropTable('cameranu.competitor');
    }
}
