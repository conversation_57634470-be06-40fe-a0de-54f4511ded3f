<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250310140812 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-5745 - Insert new mail_content templates';
    }

    public function up(Schema $schema): void
    {
        // Mailtekst 1
        $this->addSql('
            INSERT INTO `mail_content` (`id`, `domain_id`, `pos`, `type`, `titel`, `taal`, `parent_id`, `flags`, `origin`)
            VALUES
                (\'3350\', \'1\', \'106\', \'systeembericht\', \'Vertraging bestelling leverancier\', \'nl\', \'0\', \'1\', \'5\');
        ');
        $this->addSql('
            INSERT INTO `mail_content2` (`parent_id`, `onderwerp`, `bericht`, `info`, `taal`, `adres`, `image`)
            VALUES
                (\'3350\', \'Uw bestelling met ordernummer: [::ordernummer::]\', \'Beste [::naam::],\r\n\r\nDe bestelling bij onze leverancier heeft helaas vertraging opgelopen, waardoor we jouw order later dan gepland kunnen versturen. Onze excuses voor het ongemak.\r\n\r\nWe verwachten jouw bestelling binnen 3 tot uiterlijk 7 werkdagen te kunnen verzenden. Zodra de order is verstuurd, ontvang je hiervan een verzendbevestiging met trackinginformatie.\r\n\r\nMocht je vragen hebben naar aanleiding van dit bericht, dan kun je gerust reageren op deze e-mail. We helpen je graag verder.\', \'\', \'nl\', \'\', NULL);
        ');
        $this->addSql('
            INSERT INTO `mail_content2` (`parent_id`, `onderwerp`, `bericht`, `info`, `taal`, `adres`, `image`)
            VALUES
                (\'3350\', \'Your order with order number: [::ordernummer::]\', \'Dear [::naam::],\r\n\r\nThe order from our supplier has unfortunately been delayed, which means we will be able to ship your order later than planned. Our apologies for the inconvenience.\r\n\r\nWe expect to ship your order within 3 to a maximum of 7 business days. As soon as the order has been dispatched, you will receive a shipping confirmation with tracking information.\r\n\r\nIf you have any questions regarding this message, feel free to reply to this email. We are happy to assist you.\', \'\', \'en\', \'\', NULL);
        ');

        // Mailtekst 2
        $this->addSql('
            INSERT INTO `mail_content` (`id`, `domain_id`, `pos`, `type`, `titel`, `taal`, `parent_id`, `flags`, `origin`)
            VALUES
                (\'3353\', \'1\', \'106\', \'systeembericht\', \'Vertraging bestelling leverancier\', \'nl\', \'0\', \'1\', \'5\');
        ');
        $this->addSql('
            INSERT INTO `mail_content2` (`parent_id`, `onderwerp`, `bericht`, `info`, `taal`, `adres`, `image`)
            VALUES
                (\'3353\', \'Uw bestelling met ordernummer: [::ordernummer::]\', \'Beste [::naam::],\r\n\r\nDe bestelling bij onze leverancier heeft helaas vertraging opgelopen, waardoor we jouw order later dan gepland kunnen versturen. Onze excuses voor het ongemak.\r\n\r\nWe gaan voor je uitzoeken wat de nieuwe verwachte levertijd is, hiervoor hebben we even tijd nodig. We laten je binnen 3 tot 7 werkdagen weten wat de nieuwe verwachte levertijd is. Zodra we meer informatie hebben, houden we je meteen op de hoogte.\', \'\', \'nl\', \'\', NULL);
        ');
        $this->addSql('
            INSERT INTO `mail_content2` (`parent_id`, `onderwerp`, `bericht`, `info`, `taal`, `adres`, `image`)
            VALUES
                (\'3353\', \'Your order with order number: [::ordernummer::]\', \'Dear [::naam::],\r\n\r\nThe order from our supplier has unfortunately been delayed, which means we will be able to ship your order later than planned. Our apologies for the inconvenience.\r\n\r\nWe will look into the new expected delivery time for you, which will take some time. We will inform you of the new expected delivery time within 3 to 7 business days. As soon as we have more information, we will keep you updated.\', \'\', \'en\', \'\', NULL);
        ');

        //Mailtekst 3
        $this->addSql('
            INSERT INTO `mail_content` (`id`, `domain_id`, `pos`, `type`, `titel`, `taal`, `parent_id`, `flags`, `origin`)
            VALUES
                (\'3356\', \'1\', \'106\', \'systeembericht\', \'Nalevering mogelijk\', \'nl\', \'0\', \'1\', \'5\');
        ');
        $this->addSql('
            INSERT INTO `mail_content2` (`parent_id`, `onderwerp`, `bericht`, `info`, `taal`, `adres`, `image`)
            VALUES
                (\'3356\', \'Uw bestelling met ordernummer: [::ordernummer::]\', \'Beste [::naam::],\r\n\r\nBedankt voor je bestelling! Je hebt een of meer artikelen besteld waarbij "Nalevering mogelijk" staat. We bestellen deze artikelen bij onze leverancier als dat nog niet is gebeurd.\r\n\r\nBinnen 3 tot 7 werkdagen laten we je weten wat de verwachte levertijd is. Zodra we meer informatie hebben, brengen we je direct op de hoogte.\r\n\r\nHeb je vragen? Reageer gerust op deze e-mail!\', \'\', \'nl\', \'\', NULL);
        ');
        $this->addSql('
            INSERT INTO `mail_content2` (`parent_id`, `onderwerp`, `bericht`, `info`, `taal`, `adres`, `image`)
            VALUES
                (\'3356\', \'Your order with order number: [::ordernummer::]\', \'Dear [::naam::],\r\n\r\nThank you for your order! You have ordered one or more items marked as "Backorder possible." We will order these items from our supplier if this has not already been done.\r\n\r\nWithin 3 to 7 business days, we will inform you of the expected delivery time. As soon as we have more information, we will update you immediately.\r\n\r\nIf you have any questions, feel free to reply to this email.\', \'\', \'en\', \'\', NULL);
        ');
    }

    public function down(Schema $schema): void
    {
        // Mailtekst 1
        $this->addSql('
            DELETE FROM `mail_content` WHERE `id` = 3350;
        ');
        $this->addSql('
            DELETE FROM `mail_content2` WHERE `parent_id` = 3350;
        ');
        $this->addSql('
            DELETE FROM `mail_content2` WHERE `parent_id` = 3350;
        ');

        // Mailtekst 2
        $this->addSql('
            DELETE FROM `mail_content` WHERE `id` = 3353;
        ');
        $this->addSql('
            DELETE FROM `mail_content2` WHERE `parent_id` = 3353;
        ');
        $this->addSql('
            DELETE FROM `mail_content2` WHERE `parent_id` = 3353;
        ');

        // Mailtekst 3
        $this->addSql('
            DELETE FROM `mail_content` WHERE `id` = 3356;
        ');
        $this->addSql('
            DELETE FROM `mail_content2` WHERE `parent_id` = 3356;
        ');
        $this->addSql('
            DELETE FROM `mail_content2` WHERE `parent_id` = 3356;
        ');
    }
}
