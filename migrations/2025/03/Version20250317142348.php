<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250317142348 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6306 Add split column for stock out through kit splits';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu_analysis.stock_movement
            ADD COLUMN split TINYINT(1) DEFAULT NULL AFTER missing;
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            ALTER TABLE cameranu_analysis.stock_movement
            DROP COLUMN split;
        ');
    }
}
