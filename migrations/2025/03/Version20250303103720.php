<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250303103720 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6211 Add promotion form configuration and entry tables';
    }

    public function up(Schema $schema): void
    {
        $configuration = $schema->createTable('cameranu.photo_contest_configuration');
        $configuration->addColumn('id', Types::INTEGER, ['autoincrement' => true]);
        $configuration->addColumn('name', Types::STRING);
        $configuration->addColumn('max_upload_size', Types::INTEGER, ['default' => 20]);
        $configuration->addColumn('max_upload_amount', Types::INTEGER, ['default' => 1]);
        $configuration->addColumn('supported_file_formats', Types::SIMPLE_ARRAY);
        $configuration->addColumn('first_name_required', Types::BOOLEAN, ['default' => true]);
        $configuration->addColumn('last_name_required', Types::BOOLEAN, ['default' => true]);
        $configuration->addColumn('submit_button_text', Types::STRING, ['notnull' => false]);
        $configuration->addColumn('add_email_optin', Types::BOOLEAN, ['default' => true]);
        $configuration->setPrimaryKey(['id']);
        $configuration->addUniqueIndex(['name']);

        $entry = $schema->createTable('cameranu.photo_contest_entry');
        $entry->addColumn('id', Types::INTEGER, ['autoincrement' => true]);
        $entry->addColumn('configuration_id', Types::INTEGER);
        $entry->addColumn('email', Types::STRING);
        $entry->addColumn('first_name', Types::STRING, ['notnull' => false]);
        $entry->addColumn('last_name_prefix', Types::STRING, ['notnull' => false]);
        $entry->addColumn('last_name', Types::STRING, ['notnull' => false]);
        $entry->addColumn('email_optin', Types::BOOLEAN, ['default' => false]);
        $entry->addColumn('created_at', Types::DATETIME_IMMUTABLE, ['default' => 'CURRENT_TIMESTAMP']);
        $entry->addColumn('clang_send_at', Types::DATETIME_IMMUTABLE, ['notnull' => false]);
        $entry->setPrimaryKey(['id']);
        $entry->addForeignKeyConstraint('cameranu.photo_contest_configuration', ['configuration_id'], ['id'], ['onDelete' => 'CASCADE']);

        $entryFile = $schema->createTable('cameranu.photo_contest_entry_file');
        $entryFile->addColumn('id', Types::INTEGER, ['autoincrement' => true]);
        $entryFile->addColumn('cloudflare_id', Types::STRING);
        $entryFile->addColumn('url', Types::STRING);
        $entryFile->addColumn('entry_id', Types::INTEGER, ['notnull' => false]);
        $entryFile->addColumn('created_at', Types::DATETIME_IMMUTABLE, ['default' => 'CURRENT_TIMESTAMP']);
        $entryFile->setPrimaryKey(['id']);
        $entryFile->addForeignKeyConstraint('cameranu.photo_contest_entry', ['entry_id'], ['id'], ['onDelete' => 'CASCADE']);
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('cameranu.photo_contest_entry_file');
        $schema->dropTable('cameranu.photo_contest_entry');
        $schema->dropTable('cameranu.photo_contest_configuration');
    }
}
