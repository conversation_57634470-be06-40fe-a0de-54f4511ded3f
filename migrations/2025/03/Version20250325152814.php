<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20250325152814 extends AbstractMigration
{
    private const string TABLE = 'artikelen_pros_cons';

    public function getDescription(): string
    {
        return 'CAM-6078 store pros and cons for products';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable(self::TABLE);
        $table->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'unsigned' => true,
        ]);
        $table->addColumn('product_id', Types::INTEGER);
        $table->addColumn('type', 'product_pro_or_con_enum');
        $table->addColumn('content', Types::TEXT);
        $table->setPrimaryKey(['id']);
        $table->addForeignKeyConstraint(
            'cameranu.artikelen',
            ['product_id'],
            ['id'],
            ['onDelete' => 'CASCADE', 'onUpdate' => 'CASCADE']
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable(self::TABLE);
    }
}
