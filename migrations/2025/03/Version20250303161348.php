<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250303161348 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6153 - Add field to store maximum delivery time in the shopcart table';
    }

    public function up(Schema $schema): void
    {
        $schema
            ->getTable('shopcart')
            ->addColumn('max_delivery_time', 'integer', [
                'notnull' => false
            ]);
    }

    public function down(Schema $schema): void
    {
        $schema
            ->getTable('shopcart')
            ->dropColumn('max_delivery_time');
    }
}
