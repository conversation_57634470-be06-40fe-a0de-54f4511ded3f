<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250313151243 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6154 add logging tables for discountcodes';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            CREATE TABLE `discount_code_log_action` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `title` varchar(50) DEFAULT NULL,
                 PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ');

        $this->addSql('
            INSERT INTO `discount_code_log_action` (`id`, `title`)
            VALUES (1, \'Code aangemaakt\'),
                (2, \'Batch aangemaakt\'),
                (3, \'Code aangepast\'),
                (4, \'Batch aangepast\'),
                (5, \'Batch gearchiveerd\'),
                (6, \'Batch geactiveerd\');
        ');

        $this->addSql('
            CREATE TABLE `discount_code_log` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
              `user_id` int(11) DEFAULT NULL,
              `action_id` int(11) DEFAULT NULL,
              `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
              `discountcode_id` int(11) DEFAULT NULL,
              `batch_id` int(11) unsigned DEFAULT NULL,
              `info` text DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `action_id` (`action_id`),
              KEY `user_id` (`user_id`),
              KEY `discountcode_id` (`discountcode_id`),
              KEY `batch_id` (`batch_id`),
              KEY `timestamp` (`timestamp`),
              CONSTRAINT `discount_code_log_ibfk_1` FOREIGN KEY (`action_id`) REFERENCES `discount_code_log_action` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `discount_code_log_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `cameranu_urk`.`users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `discount_code_log_ibfk_3` FOREIGN KEY (`discountcode_id`) REFERENCES `discountcodes` (`discountId`) ON DELETE CASCADE ON UPDATE CASCADE,
              CONSTRAINT `discount_code_log_ibfk_4` FOREIGN KEY (`batch_id`) REFERENCES `discount_code_batches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ');
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('discount_code_log');
        $schema->dropTable('discount_code_log_action');
    }
}
