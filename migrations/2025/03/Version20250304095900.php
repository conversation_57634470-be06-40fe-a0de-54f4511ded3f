<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250304095900 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add manufacturer_email and representative_email fields to manufacturer_gpsr table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr ADD manufacturer_email VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr ADD representative_email VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr DROP manufacturer_email');
        $this->addSql('ALTER TABLE cameranu.manufacturer_gpsr DROP representative_email');
    }
}
