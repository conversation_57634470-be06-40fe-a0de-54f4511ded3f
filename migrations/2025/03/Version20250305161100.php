<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250305161100 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-4324 Creates the event_log table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'CREATE TABLE cameranu.event_log (
                id INT AUTO_INCREMENT NOT NULL,
                action INT NOT NULL,
                user_id INT NOT NULL,
                event_id INT NOT NULL,
                datetime DATETIME NOT NULL,
                registration_id INT DEFAULT NULL,
                PRIMARY KEY(id),
                INDEX IDX_EVENT_LOG_USER (user_id),
                INDEX IDX_EVENT_LOG_EVENT (event_id),
                INDEX IDX_event_log_registration_id (registration_id),
                CONSTRAINT FK_EVENT_LOG_EVENT FOREIGN KEY (event_id)
                    REFERENCES cameranu.event_products (id),
                CONSTRAINT FK_EVENT_LOG_USER FOREIGN KEY (user_id)
                    REFERENCES cameranu.users (id),
                CONSTRAINT FK_event_log_registration_id FOREIGN KEY (registration_id)
                    REFERENCES cameranu.event_registrations (id)
            ) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB'
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE cameranu.event_log');
    }
}
