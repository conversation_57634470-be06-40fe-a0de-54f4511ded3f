<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\IntegerType;
use Doctrine\DBAL\Types\Type;
use Doctrine\Migrations\AbstractMigration;

final class Version20250321144924 extends AbstractMigration
{
    private const TABLE_KSO = 'kit_split_orders';
    private const COL_KSO_ID = 'id';
    private const COL_KSO_PO_ID = 'stock_memo_id';
    private const FK_KSO_PO = 'fk_kit_split_orders_voorraad_memo';

    private const TABLE_KSOR = 'kit_split_orders_rows';
    private const COL_KSOR_KSO_ID = 'kit_split_order_id';
    private const COL_KSOR_PRODUCT_ID = 'product_id';
    private const COL_KSOR_STOCK_ID = 'stock_id';
    private const FK_KSOR_KSO = 'fk_kit_split_orders_rows_kit_split_orders';
    private const FK_KSOR_PRODUCT = 'fk_kit_split_orders_rows_artikelen';
    private const FK_KSOR_STOCK = 'fk_kit_split_orders_rows_voorraad';

    private const TABLE_PRODUCT = 'artikelen';
    private const COL_PRODUCT_ID = 'id';

    private const TABLE_STOCK = 'voorraad';
    private const COL_STOCK_ID = 'id';

    private const TABLE_PO = 'voorraad_memo';
    private const COL_PO_ID = 'id';

    public function getDescription(): string
    {
        return 'CAM-6060 Add foreign keys to kit split order tables';
    }

    public function up(Schema $schema): void
    {
        $ksoTable = $schema->getTable(self::TABLE_KSO);
        $ksoTable->addForeignKeyConstraint(
            self::TABLE_PO,
            [self::COL_KSO_PO_ID],
            [self::COL_PO_ID],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'RESTRICT',
            ],
            self::FK_KSO_PO,
        );

        $ksorTable = $schema->getTable(self::TABLE_KSOR);
        $ksorTable->modifyColumn(
            self::COL_KSOR_KSO_ID,
            [
                'type' => new IntegerType(),
                'unsigned' => true,
                'notnull' => true,
            ],
        );

        $ksorTable->addForeignKeyConstraint(
            self::TABLE_KSO,
            [self::COL_KSOR_KSO_ID],
            [self::COL_KSO_ID],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'RESTRICT',
            ],
            self::FK_KSOR_KSO,
        );

        $ksorTable->addForeignKeyConstraint(
            self::TABLE_PRODUCT,
            [self::COL_KSOR_PRODUCT_ID],
            [self::COL_PRODUCT_ID],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'RESTRICT',
            ],
            self::FK_KSOR_PRODUCT,
        );

        $ksorTable->addForeignKeyConstraint(
            self::TABLE_STOCK,
            [self::COL_KSOR_STOCK_ID],
            [self::COL_STOCK_ID],
            [
                'onDelete' => 'RESTRICT',
                'onUpdate' => 'RESTRICT',
            ],
            self::FK_KSOR_STOCK,
        );
    }

    public function down(Schema $schema): void
    {
        $ksoTable = $schema->getTable(self::TABLE_KSO);
        $ksoTable->removeForeignKey(self::FK_KSO_PO);

        $ksorTable = $schema->getTable(self::TABLE_KSOR);
        $ksorTable->removeForeignKey(self::FK_KSOR_KSO);
        $ksorTable->removeForeignKey(self::FK_KSOR_PRODUCT);
        $ksorTable->removeForeignKey(self::FK_KSOR_STOCK);
    }
}
