<?php

declare(strict_types=1);

namespace App\Migrations;

use <PERSON>B<PERSON>le\MigrationHelper\UrkMigration;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Schema\SchemaException;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250304085407 extends UrkMigration
{
    public function getDescription(): string
    {
        return 'CAM-6140 add primary key to location log table';
    }

    /**
     * @throws SchemaException
     */
    public function upForUrk(Schema $schema): void
    {
        $table = $schema->getTable('log_locaties');
        $table->addColumn('id', TYPES::INTEGER, [
            'unsigned' => true,
            'autoincrement' => true,
            'notnull' => true,
        ]);
        $table->setPrimaryKey(['id']);
    }

    /**
     * @throws SchemaException
     */
    public function downForUrk(Schema $schema): void
    {
        $table = $schema->getTable('log_locaties');
        $table->dropPrimaryKey();
        $table->dropColumn('id');
    }
}
