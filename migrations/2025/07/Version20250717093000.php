<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20250717093000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6732 Create stock_target_weeks table to store target weeks of stock by WEX main group and parent';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('cameranu.stock_target_weeks');

        $table->addColumn('id', Types::INTEGER, [
            'autoincrement' => true,
            'notnull' => true,
        ]);

        $table->addColumn('wex_maingroup_id', Types::INTEGER, [
            'notnull' => true,
            'unsigned' => true,
        ]);

        $table->addColumn('parent', Types::STRING, [
            'length' => 255,
            'notnull' => true,
        ]);

        $table->addColumn('target_weeks', Types::INTEGER, [
            'notnull' => true,
        ]);

        $table->setPrimaryKey(['id']);

        $table->addForeignKeyConstraint(
            'cameranu.wex_hoofdgroepen',
            ['wex_maingroup_id'],
            ['id'],
            ['onDelete' => 'CASCADE']
        );

        $table->addUniqueIndex(['wex_maingroup_id', 'parent'], 'unique_wex_maingroup_parent');
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('cameranu.stock_target_weeks');
    }
}
