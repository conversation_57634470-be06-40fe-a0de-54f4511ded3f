<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250728092816 extends AbstractMigration
{
    private const string TABLE = 'cameranu.event_themes';
    private const string NEW_COLUMN = 'tag_id';
    private const string FOREIGN_TABLE = 'cameranu.tags';
    private const string FOREIGN_COLUMN = 'tagId';
    private const string FK_TAGS = 'event_themes_tags_ibfk';
    private const string IDX_TAGS = 'IDX_EVENT_THEMES_TAG_ID';
    private const array TAGS_MAPPING = [
        'Architectuur' => 115953,
        'Avond' => 115956,
        'Basistechnieken' => 115962,
        'Beeldbewerking' => 115980,
        'Bruiloft' => 115989,
        'Canon' => 115974,
        'Creatief' => 115944,
        'Dieren' => 115971,
        'DJI' => 116055,
        'Drones' => 116013,
        'Fashion' => 116038,
        'Flitsen' => 115947,
        'Food' => 116052,
        'Fujifilm' => 116049,
        'Landschap' => 115941,
        'Leica' => 116061,
        'Lightpainting' => 115983,
        'Luchtvaart' => 116010,
        'Macro' => 115992,
        'Natuur' => 116001,
        'Nikon' => 115998,
        'Onderwater' => 116043,
        'Portfoliobespreking' => 116037,
        'Portret' => 115950,
        'Printen' => 115977,
        'Product' => 116058,
        'Reizen' => 116004,
        'Sony' => 116031,
        'Sport' => 116028,
        'Stilleven' => 115986,
        'Straat' => 116025,
        'Studio' => 115959,
        'Urbex' => 116022,
        'Verrekijkers' => 116016,
        'Video' => 115965,
        'Visuele Communicatie' => 115968,
        'Vogels' => 115995,
        'Wildlife' => 116007,
        'Zwart-wit' => 116046,
    ];

    public function getDescription(): string
    {
        return 'CAM-6925 add tag_id to event_themes and link existing tags';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(sprintf(
            'ALTER TABLE %s ADD %s INT DEFAULT NULL',
            self::TABLE,
            self::NEW_COLUMN
        ));

        $this->addSql(sprintf(
            'CREATE INDEX %s ON %s (%s)',
            self::IDX_TAGS,
            self::TABLE,
            self::NEW_COLUMN
        ));

        $this->addSql(
            sprintf(
                'ALTER TABLE %s ADD CONSTRAINT %s FOREIGN KEY (%s) REFERENCES %s (%s) ON DELETE CASCADE ON UPDATE CASCADE',
                self::TABLE,
                self::FK_TAGS,
                self::NEW_COLUMN,
                self::FOREIGN_TABLE,
                self::FOREIGN_COLUMN
            )
        );

        // Update tag_id values
        foreach (self::TAGS_MAPPING as $name => $tagId) {
            $this->addSql(sprintf(
                'UPDATE %s SET %s = %d WHERE `name` = %s',
                self::TABLE,
                self::NEW_COLUMN,
                $tagId,
                $this->connection->quote($name)
            ));
        }
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable(self::TABLE);
        $table->removeForeignKey(self::FK_TAGS);
        $table->dropColumn(self::NEW_COLUMN);
    }
}
