<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\WexMaingroup;

final class Version20250717103200 extends AbstractMigration
{
    private const DEFAULT_TARGETS = [
        WexMaingroup::ID_CAMERAS => 7,
        WexMaingroup::ID_CAMERA_LENSES => 7,
        WexMaingroup::ID_COMPUTING_OUTPUT => 5,
        WexMaingroup::ID_LIGHTING_STUDIO => 12,
        WexMaingroup::ID_OPTICS => 10,
        WexMaingroup::ID_CAMERA_ACCESSORIES => 6,
        WexMaingroup::ID_OTHER_ACCESSORIES => 6,
        WexMaingroup::ID_VIDEO_CONSUMER => 6,
        WexMaingroup::ID_VIDEO_PRO => 6,
    ];

    public function getDescription(): string
    {
        return 'CAM-6732 Populate stock_target_weeks table with default values (6 weeks) for all WEX main groups and specified parents';
    }

    public function up(Schema $schema): void
    {
        $wexMaingroups = $this->connection->fetchAllAssociative(
            'SELECT id FROM cameranu.wex_hoofdgroepen ORDER BY id'
        );

        foreach ($wexMaingroups as $wexMaingroup) {
            if (array_key_exists($wexMaingroup['id'], self::DEFAULT_TARGETS) === false) {
                continue;
            }

            foreach (Origin::CAMERANU_PARENTS as $parent) {
                $this->addSql(
                    'INSERT INTO cameranu.stock_target_weeks (wex_maingroup_id, parent, target_weeks) VALUES (?, ?, ?)',
                    [
                        $wexMaingroup['id'],
                        $parent,
                        self::DEFAULT_TARGETS[$wexMaingroup['id']],
                    ]
                );
            }
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM cameranu.target_stock_weeks WHERE target_weeks = 6');
    }
}
