<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

final class Version20250708091118 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6802 Add AFAS collect account option to users';
    }

    public function up(Schema $schema): void
    {
        $schema->getTable('webusers')->addColumn('afas_collect_account', Types::INTEGER, [
            'notnull' => false,
        ]);
    }

    public function down(Schema $schema): void
    {
        $schema->getTable('webusers')->dropColumn('afas_collect_account');
    }
}
