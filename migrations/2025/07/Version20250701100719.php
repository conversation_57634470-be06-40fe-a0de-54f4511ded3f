<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250701100719 extends AbstractMigration
{
    private const IDX = 'bestelling_naw_herkomst_verzendwijze_factuurnummer_IDX';

    public function getDescription(): string
    {
        return 'CAM-6819';
    }

    public function up(Schema $schema): void
    {
        $orderTable = $schema->getTable('bestelling_naw');
        $orderTable->addIndex(['factuurnummer', 'herkomst', 'verzendwijze', 'parkeren_id'], self::IDX);
    }

    public function down(Schema $schema): void
    {
        $orderTable = $schema->getTable('bestelling_naw');
        $orderTable->dropIndex(self::IDX);
    }
}
