<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Entity\ProductFeed;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250721124526 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-7008 add field for openPurchaseOrders to Adchieve feed';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('INSERT INTO `cameranu`.`productfeed_fields` (`name`, `feed_id`, `key`)
            VALUES (\'openstaande_bestellingen\', ' . ProductFeed::ADCHIEVE . ', \'amount_in_purchase_order\')
        ');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DELETE FROM  `cameranu`.`productfeed_fields`
            WHERE `feed_id` = ' . ProductFeed::ADCHIEVE . ' AND `key` = \'amount_in_purchase_order\''
        );
    }
}
