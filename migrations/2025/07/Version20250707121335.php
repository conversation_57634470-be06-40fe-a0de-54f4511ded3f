<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250707121335 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6935 Add columns for monetary_amount to rfm tables';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.customer_rfm_snapshot');
        $table->addColumn(
            'monetary_amount',
            Types::DECIMAL,
            [
                'precision' => 11,
                'scale' => 2,
                'notnull' => true,
                'default' => 0.
            ]
        );

        $table = $schema->getTable('cameranu.customer_rfm_history');
        $table->addColumn(
            'monetary_amount',
            Types::DECIMAL,
            [
                'precision' => 13,
                'scale' => 2,
                'notnull' => true,
                'default' => 0.
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.customer_rfm_snapshot');
        $table->dropColumn('monetary_amount');

        $table = $schema->getTable('cameranu.customer_rfm_history');
        $table->dropColumn('monetary_amount');
    }
}
