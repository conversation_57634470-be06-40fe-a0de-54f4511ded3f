<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250715142208 extends AbstractMigration
{
    public const TYPE_COURSE = 'Cursus';
    public const TYPE_LECTURE = 'Lezing';
    public const TYPE_EVENT = 'Evenement';
    public const TYPE_WORKSHOP = 'Workshop';
    public const TYPE_ROADSHOW = 'Roadshow';

    public const VALID_TYPES = [
        self::TYPE_COURSE,
        self::TYPE_LECTURE,
        self::TYPE_EVENT,
        self::TYPE_WORKSHOP,
        self::TYPE_ROADSHOW,
    ];

    public const DEFAULT_CATEGORY_ID = 2;

    public function getDescription(): string
    {
        return 'CAM-4213 - Migrate event type to category table';
    }

    public function up(Schema $schema): void
    {
        foreach (self::VALID_TYPES as $value) {
            $this->addSql('INSERT INTO cameranu.event_categories (name) VALUES (:name)', [
                'name' => $value,
            ]);

            $this->addSql(
                'UPDATE cameranu.events SET category_id = (SELECT id FROM cameranu.event_categories WHERE name = :name) WHERE type = :type',
                [
                    'name' => $value,
                    'type' => $value,
                ]
            );
        }
    }

    public function down(Schema $schema): void
    {
        foreach (self::VALID_TYPES as $value) {
            $this->addSql('UPDATE cameranu.events SET type = :type, category_id = :defaultCategoryId WHERE category_id = (SELECT id FROM cameranu.event_categories WHERE name = :name)', [
                'type' => $value,
                'name' => $value,
                'defaultCategoryId' => self::DEFAULT_CATEGORY_ID,
            ]);

            $this->addSql('DELETE FROM cameranu.event_categories WHERE name = :name', [
                'name' => $value,
            ]);
        }
    }
}
