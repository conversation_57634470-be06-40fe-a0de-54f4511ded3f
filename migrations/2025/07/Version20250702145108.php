<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250702145108 extends AbstractMigration
{
    private const int FOLDER = 4;
    private const int FLAGS_UP = 289;
    private const int FLAGS_DOWN = 33;

    public function getDescription(): string
    {
        return 'CAM-6929 Enable isPaid check for PostNL Betaald folder';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`parkeren` SET `flags` = ' . self::FLAGS_UP . ' WHERE `id` = ' . self::FOLDER
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('
            UPDATE `cameranu`.`parkeren` SET `flags` = ' . self::FLAGS_DOWN . ' WHERE `id` = ' . self::FOLDER
        );
    }
}
