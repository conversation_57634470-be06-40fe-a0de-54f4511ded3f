<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250729124722 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6987 Log secondhand intake price updates';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('second_hand_prices_update');

        $table->addColumn('id', 'integer', ['autoincrement' => true]);
        $table->setPrimaryKey(['id']);

        $table->addColumn('product_id', 'integer');
        $table->addColumn('user_id', 'integer');
        $table->addColumn('price', 'decimal', ['precision' => 12, 'scale' => 2]);
        $table->addColumn('date_changed', 'datetime');
        $table->addColumn('description', 'text');

        $table->addIndex(['product_id'], 'idx_product_id');
        $table->addIndex(['user_id'], 'idx_user_id');
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('second_hand_prices_update');
    }
}
