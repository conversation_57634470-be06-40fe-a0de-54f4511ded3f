<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250701074008 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6698 AFAS debtor status per order';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->createTable('afas_debtor_status');
        $table->addColumn('id', 'integer', ['unsigned' => true, 'autoincrement' => true]);
        $table->addColumn('order_id', 'integer');
        $table->addColumn('amount_invoiced', 'decimal', ['precision' => 11, 'scale' => 3, 'notnull' => false]);
        $table->addColumn('balance', 'decimal', ['precision' => 11, 'scale' => 3, 'notnull' => false]);
        $table->addColumn('settled', 'boolean', ['default' => false]);
        $table->addColumn('last_updated', 'datetime', ['default' => 'CURRENT_TIMESTAMP']);
        $table->setPrimaryKey(['id']);
        $table->addIndex(['order_id'], 'idx_afas_debtor_status_order_id');
        $table->addForeignKeyConstraint(
            'bestelling_naw',
            ['order_id'],
            ['id'],
            ['onUpdate' => 'CASCADE', 'onDelete' => 'CASCADE'],
            'afas_debtor_status_order_id'
        );
    }

    public function down(Schema $schema): void
    {
        $schema->dropTable('afas_debtor_status');
    }
}
