<?php

declare(strict_types=1);

namespace App\Migrations;

use CatBundle\Service\OpenAI\ContentRewriter;
use CatBundle\Service\OpenAI\ProductImagesDescriber;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Webdsign\GlobalBundle\Constant\OpenAiConstants;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250703081046 extends AbstractMigration
{
    public const array TABLE_ROW = [
        'class' => ContentRewriter::class,
        'name' => 'Contentschrijver',
        'shortDescription' => 'Herschrijf content voor producten',
        'model' => OpenAiConstants::MODEL_GPT_4O_MINI,
        'prompt' => <<<PROMPT
Je bent een copywriter met specialisatie in conversie optimalisatie. Herschrijf de content van het product met conversie gerichte elementen.

Je volgt de volgende verplichte regels:
- Gebruik het AIDA model voor de eerste alinea van de tekst
- Deel de gehele tekst opnieuw in op basis van het 6 Thinking Hats model
- Je benoemt nooit letterlijk de hoeden uit het 6 Thinking Hats model
- Behoud de essentie van de tekst, voeg geen nieuwe informatie toe
- Schrijf in de jij/je-vorm
- Gebruik tussenkoppen en opsommende lijsten zoals het origineel
- Het resultaat moet HTML zijn, geen markdown.
- Het resultaat bevat geen \'fenced code block\' markdown element
- Het resultaat mag geen <html>, <head> en <body> elementen bevatten
- Het resultaat mag absoluut geen <img> element bevatten
- Negeer de YouTube-link, deze hoeft niet te worden meegenomen in het resultaat
PROMPT,
    ];

    public function getDescription(): string
    {
        return 'CAM-6779 - Add new AI assistant';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            'INSERT INTO `cameranu`.`ai_assistants` (class, name, short_description, model, prompt) VALUES (?, ?, ?, ?, ?)',
            [
                self::TABLE_ROW['class'],
                ...array_slice(array_values(self::TABLE_ROW), 1)
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(
            'DELETE FROM `cameranu`.`ai_assistants` WHERE class = ?',
            [
                self::TABLE_ROW['class'],
            ]
        );
    }
}
