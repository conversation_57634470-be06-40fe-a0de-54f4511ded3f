<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\DBAL\Types\Types;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250708062452 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6934 add rfm_segment to order';
    }

    public function up(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.bestelling_naw');
        $table->addColumn(
            'rfm_segment',
            Types::STRING,
            [
                'length' => 255,
                'notnull' => false
            ]
        );
    }

    public function down(Schema $schema): void
    {
        $table = $schema->getTable('cameranu.bestelling_naw');
        $table->dropColumn('rfm_segment');
    }
}
