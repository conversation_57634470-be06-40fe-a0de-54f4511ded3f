<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

class Version20250812110423 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6129 - Create events_instructors junction table for many-to-many relationship between events and instructors';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            "
        CREATE TABLE cameranu.events_instructors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_id INT NOT NULL,
            instructor_id INT NOT NULL,
            CONSTRAINT FK_events_instructors_event_id FOREIGN KEY (event_id) REFERENCES cameranu.events (id),
            CONSTRAINT FK_events_instructors_instructor_id FOREIGN KEY (instructor_id) REFERENCES cameranu.event_instructors (id),
            UNIQUE KEY unique_event_instructor (event_id, instructor_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        "
        );

        // Migrate existing data from events.instructor_id to the new junction table
        $this->addSql(
            "
        INSERT INTO cameranu.events_instructors (event_id, instructor_id)
        SELECT id, instructor_id
        FROM cameranu.events
        WHERE instructor_id IS NOT NULL
        "
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE IF EXISTS cameranu.events_instructors');
    }
}
