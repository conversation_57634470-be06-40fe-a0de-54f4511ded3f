<?php

declare(strict_types=1);

namespace App\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

class Version20250812110424 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'CAM-6129 - Remove instructor_id column from events table after migrating to many-to-many relationship';
    }

    public function up(Schema $schema): void
    {
        // Drop the foreign key constraint first
        $this->addSql('ALTER TABLE cameranu.events DROP FOREIGN KEY FK_events_instructor_id');

        // Drop the instructor_id column
        $this->addSql('ALTER TABLE cameranu.events DROP COLUMN instructor_id');
    }

    public function down(Schema $schema): void
    {
        // Add the instructor_id column back
        $this->addSql('ALTER TABLE cameranu.events ADD COLUMN instructor_id INT NOT NULL');

        // Add the foreign key constraint back
        $this->addSql('ALTER TABLE cameranu.events ADD CONSTRAINT FK_events_instructor_id FOREIGN KEY (instructor_id) REFERENCES cameranu.event_instructors (id)');

        // Migrate data back from junction table (taking the first instructor for each event)
        $this->addSql(
            "
        UPDATE cameranu.events e
        SET instructor_id = (
            SELECT ei.instructor_id
            FROM cameranu.events_instructors ei
            WHERE ei.event_id = e.id
            LIMIT 1
        )
        WHERE EXISTS (
            SELECT 1
            FROM cameranu.events_instructors ei
            WHERE ei.event_id = e.id
        )
        "
        );
    }
}
