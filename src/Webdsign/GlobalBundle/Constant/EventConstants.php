<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Constant;

class EventConstants
{
    public const TYPE_COURSE = 'Cursus';
    public const TYPE_LECTURE = 'Lezing';
    public const TYPE_EVENT = 'Evenement';
    public const TYPE_WORKSHOP = 'Workshop';
    public const TYPE_ROADSHOW = 'Roadshow';
    public const LEVEL_BEGINNER = 'Beginner';
    public const LEVEL_HOBBYIST = 'Hobbyist';
    public const LEVEL_PROFESSIONAL = 'Professioneel';
    public const RESOURCE_EXPORT_EXTENDED = 'events-export-extended';
    public const RESOURCE_EXPORT = 'events-export';
    public const RESOURCE_CANCEL_REGISTRATION = 'events-cancel-registration';
    public const RESOURCE_ADD_REGISTRATION = 'events-add-registration';
    public const RESOURCE_REFUND_EVENTS = 'events-refund-events';
    public const RESOURCE_CANCEL_EVENTS = 'events-cancel-events';
    public const RESOURCE_ADD_EVENTS = 'events-add-events';
    public const RESOURCE_ADD_CATEGORIES = 'events-add-categories';
    public const RESOURCE_EVENTS = 'events';

    public const VALID_TYPES = [
        self::TYPE_COURSE,
        self::TYPE_LECTURE,
        self::TYPE_EVENT,
        self::TYPE_WORKSHOP,
        self::TYPE_ROADSHOW,
    ];
    public const VALID_LEVELS = [
        self::LEVEL_BEGINNER,
        self::LEVEL_HOBBYIST,
        self::LEVEL_PROFESSIONAL,
    ];
}
