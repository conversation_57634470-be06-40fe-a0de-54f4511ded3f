<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Constant\Afas;

use Webdsign\GlobalBundle\Entity\PaymentMethod;

final class OrderConstants
{
    // Administratienummers per locatie
    public const int ADMINISTRATION_AMSTERDAM = 3;
    public const int ADMINISTRATION_ANTWERPEN = 12;
    public const int ADMINISTRATION_APELDOORN = 4;
    public const int ADMINISTRATION_EINDHOVEN = 8;
    public const int ADMINISTRATION_GRONINGEN = 2;
    public const int ADMINISTRATION_ROTTERDAM_CENTRUM = 7;
    public const int ADMINISTRATION_URK = 1;
    public const int ADMINISTRATION_VENDIRO = 9;

    public const array ADMINISTRATION_PARENT_MAPPING = [
        'cameranu_amsterdam' => OrderConstants::ADMINISTRATION_AMSTERDAM,
        'cameranu_antwerpen' => OrderConstants::ADMINISTRATION_ANTWERPEN,
        'cameranu_apeldoorn' => OrderConstants::ADMINISTRATION_APELDOORN,
        'cameranu_eindhoven' => OrderConstants::ADMINISTRATION_EINDHOVEN,
        'cameranu_groningen' => OrderConstants::ADMINISTRATION_GRONINGEN,
        'cameranu_rotterdam' => OrderConstants::ADMINISTRATION_AMSTERDAM,
        'vendiro' => OrderConstants::ADMINISTRATION_VENDIRO,
        'vendiro_cameratools' => OrderConstants::ADMINISTRATION_VENDIRO,
        'cameranu' => OrderConstants::ADMINISTRATION_URK,
        'cameranu_utrecht' => OrderConstants::ADMINISTRATION_URK,
    ];

    // Dagboeknummers
    public const string JOURNAL_SALES_ADMIN = '01';
    public const string JOURNAL_SALES_ADMIN_ROTTERDAM = '04';
    public const int JOURNAL_PAYMENT_PLATFORMS_WRITE_OFF = 20;

    // Type rekening
    public const int ACCOUNT_TYPE_LEDGER = 1;
    public const int ACCOUNT_TYPE_DEBTOR = 2;
    public const int ACCOUNT_TYPE_CREDITOR = 3;

    // Debit of credit
    public const string DEBIT = 'debit';
    public const string CREDIT = 'credit';

    // Grootboekcodes
    public const int LEDGER_VAT_CREDIT = 1500;
    public const int LEDGER_VAT = 1510;
    public const int LEDGER_VAT_OSS = 1511;
    public const int LEDGER_REVENUE_REGULAR = 8000;
    public const int LEDGER_REVENUE_SECONDHAND = 8010;
    public const int LEDGER_REVENUE_RENTAL = 8020;
    public const int LEDGER_REVENUE_WORKSHOPS = 8030;
    public const int LEDGER_REVENUE_INSURANCE = 8040;
    public const int LEDGER_REVENUE_REPAIRS = 8050;
    public const int LEDGER_REVENUE_PRINTSHOP = 8060;
    public const int LEDGER_DISCOUNT_REGULAR = 8100;
    public const int LEDGER_DISCOUNT_SECONDHAND = 8105;
    public const int LEDGER_DISCOUNT_RENTAL = 8110;
    public const int LEDGER_DISCOUNT_WORKSHOPS = 8115;
    public const int LEDGER_DISCOUNT_INSURANCE = 8120;
    public const int LEDGER_EMPLOYEE_DISCOUNT = 8150;
    public const int LEDGER_SHIPPING_COSTS = 8910;
    public const int LEDGER_PAYMENT_COSTS = 8920;
    public const int LEDGER_MARGIN_SALES_EIG = 8440;
    public const int LEDGER_COMMERCIAL_SOLUTION_COMPLAINT_CAMERANU = 8700;
    public const int LEDGER_QUOTE_DISCOUNT_SUPPLIERS = 8710;
    public const int LEDGER_QUOTE_DISCOUNT_CAMERANU = 8790;
    public const int LEDGER_MISSING_STOCK = 8800;
    public const int LEDGER_DOA = 8810;
    public const int LEDGER_ONE_TIME_SALES = 8820;
    public const int LEDGER_INCORRECTLY_BOOKED_STOCK = 8830;
    public const int LEDGER_SALES_OTHER = 8900;
    public const int LEDGER_COUPONS = 2760;

    /**
     * Oude grootboekcodes vanuit Multivers mappen naar Afas
     */
    public const array LEDGER_OLD_MAPPING = [
        '1395' => 2715,
        '1605' => 1470,
        '1606' => 1485,
        '1607' => 1475,
        '1608' => 1450,
        '4621' => 4340,
        '7220' => 7020,
        '7859' => 7690,
        '7978' => 7520,
        '7979' => 7530,
        '7989.300' => 7010,
        '8095.700' => 2740,
        '8095.800' => self::LEDGER_REVENUE_RENTAL,
        '8451' => 7000,
        '8750' => 2750,
        '8751' => self::LEDGER_REVENUE_INSURANCE,
        '8870' => 2730,
        '8880' => self::LEDGER_REVENUE_REPAIRS,
        '8881.201' => 8090,
        '8890' => 8165,
        '8890.001' => 8140,
        '8890.002' => 8140,
        '8890.003' => 8610,
        '8890.008' => 8600,
        '8890.100' => 8160,
        '8891.000' => self::LEDGER_EMPLOYEE_DISCOUNT,
        '8891.005' => 8160,
        '8891.006' => 7190,
        '8893.001' => 8190,
        '8894' => self::LEDGER_DOA,
        '8900' => self::LEDGER_SHIPPING_COSTS,
        '8940' => self::LEDGER_MARGIN_SALES_EIG,
        '8989.005' => self::LEDGER_QUOTE_DISCOUNT_SUPPLIERS,
        '8989.006' => self::LEDGER_MISSING_STOCK,
        '8989.007' => self::LEDGER_COMMERCIAL_SOLUTION_COMPLAINT_CAMERANU,
        '8989.010' => 8930,
        '8989.021' => self::LEDGER_INCORRECTLY_BOOKED_STOCK,
        '8989.150' => self::LEDGER_QUOTE_DISCOUNT_SUPPLIERS,
        '8989.302' => self::LEDGER_DISCOUNT_SECONDHAND,
        '8990' => self::LEDGER_QUOTE_DISCOUNT_CAMERANU,
    ];
    /**
     * Verbijzonderingscodes marketplaces
     */
    public const string DEFAULT_DIFFERENTIATION_CODE = '01';
    public const array DIFFERENTIATION_CODES = [
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_BOL_NL => '02',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_BOL_BE => '03',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_AMAZON_DE => '04',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_AMAZON_FR => '05',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_AMAZON_IT => '06',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_AMAZON_ES => '07',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_AMAZON_NL => '08',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_AMAZON_BE => '09',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_CDISCOUNT => '10',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_REAL_KAUFLAND => '11',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_FNAC_FR => '12',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_FNAC_BE => '12',
        DebtorConstants::COLLECTIVE_DEBTOR_VENDIRO_DARTY => '13',
    ];

    public const array SHOP_DIFFERENTIATION_CODES = [
        'cameranu_amsterdam' => '01',
        'cameranu_antwerpen' => '90',
        'cameranu_apeldoorn' => '90',
        'cameranu_eindhoven' => '90',
        'cameranu_groningen' => '90',
        'cameranu_rotterdam' => '02',
        'cameranu_rotterdam_pro' => '90',
        'cameranu_utrecht' => '11',
        'cameranu' => '0',
        'vendiro' => '90',
        'vendiro_cameratools' => '90',
    ];

    /**
     * Tussenrekeningen betalingen
     */
    public const int LEDGER_SUSPENSE_ACCOUNT_ADYEN = 2101;
    public const int LEDGER_SUSPENSE_ACCOUNT_CC = 2110;
    public const int LEDGER_SUSPENSE_ACCOUNT_SPRAYPAY = 2120;
    public const int LEDGER_SUSPENSE_ACCOUNT_PAYPAL = 2130;
    public const int LEDGER_SUSPENSE_ACCOUNT_CASH = 2140;
    public const int LEDGER_SUSPENSE_ACCOUNT_PLATFORMS = 2180;
    public const array PAYMENT_LEDGER_MAPPING = [
        PaymentMethod::ID_ADYEN => self::LEDGER_SUSPENSE_ACCOUNT_ADYEN,
        PaymentMethod::CREDITCARD => self::LEDGER_SUSPENSE_ACCOUNT_CC,
        PaymentMethod::SPRAYPAY => self::LEDGER_SUSPENSE_ACCOUNT_SPRAYPAY,
        PaymentMethod::PAYPAL => self::LEDGER_SUSPENSE_ACCOUNT_PAYPAL,
        PaymentMethod::PIN_CASH => self::LEDGER_SUSPENSE_ACCOUNT_CASH,
        PaymentMethod::VENDIRO_CAMERATOOLS => self::LEDGER_SUSPENSE_ACCOUNT_PLATFORMS,
    ];
}
