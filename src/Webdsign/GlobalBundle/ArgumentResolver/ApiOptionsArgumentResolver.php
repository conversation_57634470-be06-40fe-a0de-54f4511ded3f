<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\ArgumentResolver;

use Generator;
use ReflectionClass;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;
use Webdsign\GlobalBundle\Form\DataObject\Api\ApiOptionsInterface;

class ApiOptionsArgumentResolver implements ValueResolverInterface
{
    public function resolve(Request $request, ArgumentMetadata $argument): Generator
    {
        $argumentType = $argument->getType();

        if ($argumentType !== null && class_exists($argumentType)) {
            $reflection = new ReflectionClass($argumentType);
            if ($reflection->implementsInterface(ApiOptionsInterface::class)) {
                $class = $argument->getType();
                yield new $class($request);
            }
        }

        return [];
    }
}
