<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\DTO\Event;

use DateTime;
use Doctrine\Common\Collections\Collection;
use Webdsign\GlobalBundle\Entity\Event\Category;
use Webdsign\GlobalBundle\Entity\Event\EventInstructor;
use Webdsign\GlobalBundle\Entity\Event\Instructor;
use Webdsign\GlobalBundle\Entity\Event\Location;
use Webdsign\GlobalBundle\Entity\StockLocation;

class EventDataObject
{
    // Velden gekoppeld aan een event entity
    public ?int $eventId = null;
    public ?bool $visible = null;
    public ?bool $canGoOffline = null;
    public string $title = '';
    public string $description = '';
    public ?string $metaTitle = null;
    public ?string $metaDescription = null;
    public ?string $programDescription = null;
    public ?string $programList = null;
    public ?string $practicalInformation = null;
    public ?string $includedList = null;
    public ?string $heroImage = null;
    public ?string $thumbnailImage = null;
    public ?array $impressionImages = null;
    public Category $category;
    public Collection $eventInstructors;
    public Collection $instructors;
    public string $level;
    public Collection $themes;
    public Collection $checklistItems;

    // Velden gekoppeld aan een (event)product entity
    public ?int $productId = null;
    public ?bool $productVisible = null;
    public ?DateTime $startDateAndTime = null; // niet immutable omdat DateTimeType dat niet ondersteunt in Symfony 3.4
    public ?DateTime $endDateAndTime = null;
    public ?int $maximumCapacity = null;
    public ?bool $needsRegistration = null;
    public ?bool $needsPayment = null;
    public ?bool $administrationCosts = null;
    public Location $location;
    public ?int $numberOfRegistrations;
    public ?string $externalLink = null;
    public ?string $externalName = null;
    public ?string $programListEvent = null;
    public ?int $adminProductId = null;

    public function getContentSummary(): string
    {
        return implode("\n", [
            $this->title,
            $this->description,
            $this->programDescription,
            $this->practicalInformation,
            $this->programListEvent,
        ]);
    }
}
