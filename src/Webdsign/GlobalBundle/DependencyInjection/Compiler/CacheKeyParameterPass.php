<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\DependencyInjection\Compiler;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class CacheKeyParameterPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        $projectDir = $container->getParameter('kernel.project_dir');

        if (false === is_string($projectDir)) {
            return;
        }

        $projectDirCacheKey = preg_replace('/[{}()\/\\\@:]/', '-', $projectDir);

        $container->setParameter('cat.path_cache_key', $projectDirCacheKey);
    }
}
