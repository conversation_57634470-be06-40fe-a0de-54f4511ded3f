<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Services;

use CatBundle\Enum\Mollie\Resource;
use CatBundle\Service\Product\MatchingProductsFinder;
use CatBundle\Service\ProductDiscountSet;
use CatBundle\Service\ProductUrlService;
use CatBundle\Service\Squeezely\PostCrossSell;
use DateTime;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use Exception;
use Firstred\PostNL\Exception\CifDownException;
use Firstred\PostNL\Exception\CifException;
use Firstred\PostNL\Exception\HttpClientException;
use Firstred\PostNL\Exception\InvalidArgumentException;
use Firstred\PostNL\Exception\NotFoundException;
use Firstred\PostNL\Exception\NotSupportedException;
use Firstred\PostNL\Exception\ResponseException;
use Firstred\PostNL\PostNL as PostNLApi;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Webdsign\GlobalBundle\DBAL\ShipmentMethodEnumType;
use Webdsign\GlobalBundle\DTO\WebsiteConfigurator\ActiveConfigDataObject;
use Webdsign\GlobalBundle\Entity\AdyenLog;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\CustomerAddress;
use Webdsign\GlobalBundle\Entity\CustomerRepository;
use Webdsign\GlobalBundle\Entity\DiscountSets\DiscountSetPosition;
use Webdsign\GlobalBundle\Entity\LedgerFieldCode;
use Webdsign\GlobalBundle\Entity\Maingroup;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\Postnl;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\CartItem;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\ProductImage;
use Webdsign\GlobalBundle\Entity\ProductMargin;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\ReferrerMail;
use Webdsign\GlobalBundle\Entity\Rootgroup;
use Webdsign\GlobalBundle\Entity\ShipmentMethod;
use Webdsign\GlobalBundle\Entity\Stock;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\Subgroup;
use Webdsign\GlobalBundle\Entity\WishListItemRepository;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Entity\PhotoContest\Entry;
use Webdsign\GlobalBundle\Entity\PhotoContest\EntryFile;
use Webdsign\GlobalBundle\Entity\PromotionsPaddington;
use Webdsign\GlobalBundle\Entity\PromotionsPaddingtonRepository;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Exception\NoDiscountSetException;
use Webdsign\GlobalBundle\Exception\QueueException;
use Webdsign\GlobalBundle\Services\WebsiteConfigurator\ConfigManager;

class ClangWebHook
{
    private array $data;
    private ActiveConfigDataObject $activeConfig;

    public function __construct(
        private readonly array $webhookTokens,
        private readonly array $webhookUrls,
        private readonly OrderInfoRepository $orderInfoRepository,
        private readonly string $imgPath,
        private readonly string $discountCode,
        private readonly TranslatorInterface $translator,
        private readonly ProductUrlService $productUrlService,
        private readonly CustomerRepository $customerRepository,
        private readonly ContainerInterface $container,
        private readonly EntityManagerInterface $entityManager,
        private readonly WishListItemRepository $wishListRepository,
        private readonly ProductRepository $productRepository,
        private readonly StockLocationRepository $stockLocationRepository,
        private readonly ConfigManager $configManager,
        private readonly PromotionsPaddingtonRepository $promotionsPaddingtonRepository,
        private readonly ProductDiscountSet $productDiscountSet,
        private readonly PostCrossSell $postCrossSell,
        private readonly MatchingProductsFinder $matchingProductsFinder,
    ) {
        $this->activeConfig = $configManager->getActive();

        $this->data = [];
    }

    /**
     * @throws CifDownException
     * @throws CifException
     * @throws HttpClientException
     * @throws InvalidArgumentException
     * @throws NotFoundException
     * @throws NotSupportedException
     * @throws ResponseException
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws \Psr\Cache\InvalidArgumentException
     * @throws Exception
     */
    public function handleData(array $data, string $type, bool $useBasicAuth = false): void
    {
        $send = true;
        switch ($type) {
            case 'order':
                $send = $this->buildOrderData($data);
                break;
            case 'wishlist':
            case 'wishlistNew':
                $this->buildWishlistData($data);
                break;
            case 'activateNewsletter':
            case 'customer':
                $send = $this->buildCustomerData($data);
                break;
            case 'passwordReset':
            case 'passwordResetNew':
            case 'lostShopcart':
            case 'eventsOrder':
            case 'eventsReminder':
            case 'eventsMessage':
            case 'eventsReview':
            case 'tradeInProcess':
                $this->data = $data;
                break;
            case 'productUpdate':
                $send = $this->buildProductUpdateData($data);
                break;
            case 'crossSell':
                $send = $this->buildCrossSellData($data);
                break;
            case 'promotionsPaddington':
                $send = $this->buildPromotionsPaddingtonData($data);
                break;
            case 'photoContestCampaignEntryCreate':
                $send = $this->buildPhotoContestCampaignEntryCreate($data['entry']);
                break;
        }

        if ($send) {
            $this->sendData($type, $useBasicAuth);
        }
    }

    public function buildCustomerData(array $data): bool
    {
        if (
            !array_key_exists('customer', $data) &&
            !array_key_exists('customerId', $data)
        ) {
            return false;
        }

        $customerId = $data['customer']['id'] ?? $data['customerId'];
        $customer = $this->customerRepository->find($customerId);
        if (!$customer instanceof Customer) {
            return false;
        }

        $customerAddresses = $customer->getAddresses();
        /** @var CustomerAddress $customerAddress */
        $customerAddress = $customerAddresses->current();
        $hasAddress = $customerAddress instanceof CustomerAddress;

        $segmentations = $data['segmentation'] ?? $customer->getSegmentations();
        $segmentationData = [];
        if (isset($data['segmentation'])) {
            $segmentationData = $segmentations;
        } else {
            foreach ($segmentations as $segmentation) {
                $segmentationData[$segmentation->getType()] = $segmentation->getValue();
            }
        }

        $clangData['06_nummer'] = $customer->getPhonenr();
        $clangData['aantal_orders_totaal'] = $segmentationData['orderCount'] ?? '';
        $clangData['aantal_kortingscodes_gebruikt'] = $segmentationData['discountCodes'] ?? '';
        $clangData['achternaam'] = $customer->getLastName();
        $clangData['bedrijfsnaam'] = $hasAddress ? $customerAddress->getCompany() : '';
        $clangData['bedrijfsemail'] = $hasAddress ? $customerAddress->getCompanyEmail() : '';
        $clangData['btw_nummer'] = $hasAddress ? $customerAddress->getTaxnr() : '';
        $clangData['emailadres'] = $customer->getEmail();
        $clangData['geboortedatum'] = $customer->getBirthday() !== null ? $customer->getBirthday()->format('d-m-Y') : '';
        $clangData['geslacht'] = $customer->getSex();
        $clangData['huisnummer'] = $hasAddress ? $customerAddress->getHousenr() : '';
        $clangData['huisnummer_toevoeging'] = $hasAddress ? $customerAddress->getHousenrext() : '';
        $clangData['klant_sinds'] = $customer->getTstamp();
        $clangData['klantnummer_cameranu'] = $customer->getId();
        $clangData['klantwaarde_totaal'] = number_format((float) ($segmentationData['totalValue'] ?? 0), 2, ',', '.');
        $clangData['land'] = $hasAddress ? $customerAddress->getCountry() : '';
        $clangData['marge_totaal'] = number_format((float) ($segmentationData['marge'] ?? 0), 2, ',', '.');
        $clangData['postcode'] = $hasAddress ? $customerAddress->getZipcode() : '';
        $clangData['retouren'] = $segmentationData['return'] ?? '';
        $clangData['straatnaam'] = $hasAddress ? $customerAddress->getAddress() : '';
        $clangData['telefoonnummer'] = $customer->getPhonenr();
        $clangData['type_klant'] = $segmentationData['customerType'] ?? '';
        $clangData['voornaam'] = $customer->getFirstName();
        $clangData['woonplaats'] = $hasAddress ? $customerAddress->getCity() : '';
        $clangData['nieuwsbrief'] = $customer->getMailable();
        $clangData['ingeschrevenNieuwsbrief'] = $data['newsletterSubscribed'] ?? false;
        $clangData['opt_in'] = $data['type'] ?? '';
        $clangData['cameranu_hash'] = $customer->getIdentifyHash() ?? '';

        $this->data = $clangData;
        return true;
    }

    /**
     * Bouw data op a.d.h.v ordernummer uit queue
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     * @throws CifDownException
     * @throws CifException
     * @throws HttpClientException
     * @throws InvalidArgumentException
     * @throws NotFoundException
     * @throws NotSupportedException
     * @throws ResponseException
     * @throws \Psr\Cache\InvalidArgumentException
     * @throws Exception
     */
    private function buildOrderData(array $data): bool
    {
        $orderNumber = (string)$data['OrderNumber'];
        $clangData = [];

        /** @var OrderInfo $orderInfo */
        $orderInfo = $this->orderInfoRepository->findOneByOrdernr($orderNumber);

        if (!$orderInfo instanceof OrderInfo) {
            throw (new QueueException('Clang Order Webhook Error #: No OrderInfo'))->setRequeueable();
        }

        if ($orderInfo->isVendiroOrder()) {
            return false;
        }

        if (str_starts_with($orderInfo->getCustomerComment(), 'Koerierslijst')) {
            return false;
        }

        $shipment = $orderInfo->getShippingMethod();
        $shipmentName = $shipment->getShipmentMethodShort();
        $paymentMethod = $orderInfo->getPaymentMethod();
        $paymentName = $paymentMethod !== null ? $paymentMethod->getName() : 'geen betaalwijze';

        // Betaalwijzenaam voor Adyen opzoeken
        if (
            $paymentMethod instanceof PaymentMethod &&
            in_array($paymentMethod->getId(),[PaymentMethod::ID_ADYEN, PaymentMethod::ID_MOLLIE])
        ) {
            $paymentNames = [];

            if ($paymentMethod->getId() === PaymentMethod::ID_ADYEN) {
                $adyenLogs = $orderInfo->getAdyenLogs();
                foreach ($adyenLogs as $adyenLog) {
                    if ($adyenLog->getType() === AdyenLog::TYPE_PAYMENT) {
                        $paymentNames[$adyenLog->getPaymentName()] = $this->translator->trans(
                            'adyen.paymentmethod.' . $adyenLog->getPaymentName()
                        );
                    }
                }
            }

            if ($paymentMethod->getId() === PaymentMethod::ID_MOLLIE) {
                $logs = $orderInfo->getPspLogs();
                foreach ($logs as $log) {
                    if (in_array($log->getType(), [Resource::PAYMENT->value, Resource::ORDER->value])) {
                        $method = $log->getData()['method'];
                        $paymentNames[$method] = $this->translator->trans(
                            'mollie.paymentmethod.' . $method
                        );
                    }
                }
            }

            // Meerdere betalingsmethoden aan elkaar plakken
            $paymentName = implode(' / ', $paymentNames);

            // Als we hier geen betaalwijze hebben terugvallen op Adyen/Mollie als naam
            if ($paymentName === '') {
                $paymentName = $paymentMethod->getName();
            }
        }

        $country = $orderInfo->getCountry();
        $cartItems = $orderInfo->getCartItems();
        $totalPurchaseValue = 0.0;
        $productAmount = 0;
        $containsOccasion = false;
        $clangCartItems = [];
        $totalDiscount = $data['TotalDiscount'];
        $containsDiscountcodes = $data['ContainsDiscountCode'];
        $containsCoupons = $data['Coupon'];
        $deliveryDate = $data['DeliveryDate'] ?? '';

        if (empty($deliveryDate)) {
            $deliveryDate = self::getExpectedDeliveryDate($orderInfo, $cartItems);
        }

        $pickupDate = '';
        if ($shipment->getType() === ShipmentMethodEnumType::OPTION_PICKUP) {
            if ($deliveryDate === "") {
                $pickupDate = (new DateTime())->format('Y-m-d');
            } else {
                $pickupDate = $deliveryDate;
            }
        }

        $ledgerFields = $orderInfo->getLedgerFields();
        $discountCodes = [];
        $lastChannel = '';
        $referrerMail = $orderInfo->getReferrerMail();
        if ($referrerMail instanceof ReferrerMail) {
            $lastChannel = $referrerMail->getType() ?? '';
        }

        if (count($cartItems) === 1) {
            if (
                in_array(
                    $cartItems[0]->getProduct()->getSubgroup()->getId(),
                    [
                    Subgroup::ADDITIONAL_CONTRIBUTION,
                    Subgroup::ADDITIONAL_CONTRIBUTION_CONDITIONAL
                    ]
                )
            ) {
                return false;
            }
        }

        foreach ($ledgerFields as $field) {
            if ($field->getCode() === $this->discountCode) {
                $discountCodes[] = $field->getInfo();
            } elseif ($field->getCode() === LedgerFieldCode::BACKORDER_LEDGER_CODE) {
                if (str_contains($field->getInfo(), 'nalevering van order')) {
                    $clangData['NaleveringOrder'] = 'ja';
                } else {
                    $clangData['NaleveringOrder'] = 'nee';
                }
            } elseif ($field->getCode() === LedgerFieldCode::ADDITIONAL_CONTRIBUTION_CODE) {
                return false;
            }
        }

        //Producten tellen, productdata ophalen & occassion check
        $deliveryTimes = $data['deliveryTimes'] ?? [];
        foreach ($cartItems as $cartItem) {
            $cartItemProduct = $cartItem->getProduct();

            if (array_key_exists($cartItemProduct->getId(), $clangCartItems)) {
                $clangCartItems[$cartItemProduct->getId()]['ProductAmount'] += $cartItem->getAmount();
            } else {
                $clangCartItems[$cartItemProduct->getId()] = $this->getCartItemData($cartItem, $country, $deliveryDate);

                //Top 3 bijpassende objectieven toevoegen aan camera's
                if ($this->matchingProductsFinder->canFindMatchingLenses($cartItemProduct)) {
                    $matchingLenses = $this->matchingProductsFinder->findMatchingLenses($cartItemProduct, limit: 3);

                    foreach ($matchingLenses as $matchingLens) {
                        $matchingLensData = $this->buildProductData($matchingLens);
                        $clangCartItems[$cartItemProduct->getId()]['matchingLenses'][$matchingLens->getId()] = $matchingLensData;
                    }
                }
            }

            $totalPurchaseValue += $cartItemProduct->getCurrentStockValue();

            if ($cartItemProduct->getCategory()->getName() === 'artikel') {
                $productAmount += (1 * $cartItem->getAmount());
            }

            if ($cartItemProduct->getSubgroup()->getMaingroup()->getRootgroupId() === 21) {
                $containsOccasion = true;
            }

            $deliveryTime = array_key_exists($cartItem->getId(), $deliveryTimes) ? $deliveryTimes[$cartItem->getId()] : '';
            $clangCartItems[$cartItemProduct->getId()]['Levertijd'] = $deliveryTime;
        }

        $customer = $orderInfo->getCustomer();
        $customerId = ($customer instanceof Customer) ? $customer->getId() : 0;
        $customerType = ($customer instanceof Customer) ? $customer->getCustomerType() : 'consument';

        // PostNL delivery data
        $inputStatus = null;
        $delayed = false;
        $trackingCode = null;

        $postnlRepository = $this->entityManager->getRepository(Postnl::class);
        $postnl = $postnlRepository->findOneBy(['order' => $orderInfo]);

        if ($postnl instanceof Postnl) {
            $postnlApi = new PostNLApi(
                new \Firstred\PostNL\Entity\Customer(
                    CustomerNumber: $this->container->getParameter('postnl.cameranu.customernumber'),
                    CustomerCode: $this->container->getParameter('postnl.cameranu.customercode'),
                ),
                $this->container->getParameter('postnl_token'),
                (bool)$this->container->getParameter('postnl.sandbox')
            );

            $trackingCode = $postnl->getTrackingcode();
            try {
                $status = $postnlApi->getShippingStatusByBarcode($trackingCode, true);
                $events = $status->getEvents();

                if ($status->getStatus() !== null) {
                    $inputStatus = $status->getStatus()->getPhaseDescription();
                } else {
                    $inputStatus = 'Geen status gevonden';
                }

                if (count($events) > 0) {
                    foreach ($events as $event) {
                        if ($event !== null && in_array($event->getCode(), Postnl::DELAY_EVENT_CODES)) {
                            $delayed = true;
                        }
                    }
                }
            } catch (NotFoundException $exception) {
                $inputStatus = 'Shipment niet gevonden bij PostNL';
            }
        }

        // order marge
        $totalValue = $orderInfo->getTotalValue(true, false, false, false, true, true);
        $totalMargin = $totalValue - $totalPurchaseValue;

        $clangData['OrderDate'] = $orderInfo->getOrderDate();
        $clangData['OrderTime'] = $orderInfo->getOrderTime();
        $clangData['OrderNumber'] = $orderNumber;
        $clangData['TotalPrice'] = $totalValue;
        $clangData['TotalPriceExVat'] = round($data['PriceEx'], 2);
        $clangData['SubTotal'] = round($orderInfo->getTotalValue(true, true, true, true, true, true), 2);
        $clangData['PaymentMethod'] = $paymentName;
        $clangData['ShippingMethod'] = $shipmentName;
        $clangData['CountryCode'] = $country;
        $clangData['TotalDiscount'] = $totalDiscount;
        $clangData['VatAmount'] = $data['VatAmount'];
        $clangData['Latest_entry_point'] = $data['Latest_entry_point'];
        $clangData['lastChannel'] = $lastChannel;
        $clangData['ShippingCosts'] = $orderInfo->getShippingCosts();
        $clangData['PaymentCosts'] = $orderInfo->getPaymentCosts();
        $clangData['Coupon'] = $containsCoupons;
        $clangData['ContainsDiscountCode'] = $containsDiscountcodes;
        $clangData['ShippingStatus'] = $data['ShippingStatus'];
        $clangData['ProductAmount'] = $productAmount;
        $clangData['ContainsOccassion'] = $containsOccasion ? 'ja' : 'nee';
        $clangData['DeliveryDate'] = $deliveryDate;
        $clangData['CustomerId'] = $customerId;
        $clangData['PhoneNumber'] = $orderInfo->getPhonenr();
        $clangData['Email'] = $orderInfo->getEmail();
        $clangData['DeliveryCompanyName'] = $orderInfo->getDeliveryCompany();
        $clangData['DeliveryCity'] = $orderInfo->getDeliveryCity();
        $clangData['DeliveryFirstName'] = $orderInfo->getDeliveryFirstName();
        $clangData['DeliveryNameInsertion'] = $orderInfo->getDeliveryNameInsertion();
        $clangData['DeliveryLastName'] = $orderInfo->getDeliveryLastName();
        $clangData['DeliveryZipCode'] = $orderInfo->getDeliveryZipcode();
        $clangData['DeliveryStreet'] = $orderInfo->getDeliveryAddress();
        $clangData['DeliveryHouseNumber'] = $orderInfo->getDeliveryHousenr();
        $clangData['DeliveryHouseNumberAddition'] = $orderInfo->getDeliveryHousenrext();
        $clangData['InvoiceFirstName'] = $orderInfo->getFirstName();
        $clangData['InvoiceDeliveryNameInsertion'] = $orderInfo->getNameInsertion();
        $clangData['InvoiceLastName'] = $orderInfo->getLastName();
        $clangData['InvoiceCompanyName'] = $orderInfo->getCompany();
        $clangData['InvoiceCity'] = $orderInfo->getCity();
        $clangData['InvoiceZipCode'] = $orderInfo->getZipcode();
        $clangData['InvoiceStreet'] = $orderInfo->getAddress();
        $clangData['InvoiceHouseNumber'] = $orderInfo->getHousenr();
        $clangData['InvoiceHouseNumberAddition'] = $orderInfo->getHousenrext();
        $clangData['InvoiceCompleted'] = $orderInfo->getInvoiceNumber() !== 0 ? 'ja' : 'nee';
        $clangData['OrderCompleted'] = $orderInfo->isFinishedOrder() ? 'ja' : 'nee';
        $clangData['CustomerType'] = $customerType;

        if ($customer instanceof Customer && !empty($customer->getVatIdentificationNumber())) {
            $clangData['CustomerVatNumber'] = $orderInfo->getCustomer()->getVatIdentificationNumber();
        }

        $clangData['Products'] = $clangCartItems;
        $clangData['DiscountCodes'] = $discountCodes;
        $clangData['OnlineOffline'] = $orderInfo->isWebOrder() ? 'Online' : 'Offline';
        $clangData['OrderStatus'] = $inputStatus ?? 'Nog niet verzonden';
        $clangData['TrackingCode'] = $trackingCode ?? '';
        $clangData['DeliveryStatus'] = $inputStatus ?? '';
        $clangData['Delayed'] = $delayed ? 'ja' : 'nee';
        $clangData['Margin'] = $totalMargin;
        $clangData['Afhaaldatum'] = $pickupDate;

        if ($shipment->getType() === ShipmentMethodEnumType::OPTION_PICKUP && $clangData['DeliveryCity'] === "") {
            if (in_array($orderInfo->getShippingMethod()->getId(), ShipmentMethod::PICK_UP_METHODS)) {
                if ($orderInfo->getShippingMethod()->getId() == ShipmentMethod::PICKUP) {
                    $clangData['DeliveryCity'] = 'Urk';
                } else {
                    $clangData['DeliveryCity'] = $orderInfo->getShippingMethod()->getChangeOrigin()->getStoreName();
                }
            }
        }

        $this->data = $clangData;

        return true;
    }

    private function buildWishlistData(array $data): void
    {
        $clangData = $productsInWishlist = $productsNotInWishlist = $wishList = [];
        $clangData['CustomerId'] = $data['customerId'];
        $clangData['Date'] = $data['date'];
        $clangData['Time'] = $data['time'];

        $customer = $this->customerRepository->find($clangData['CustomerId']);
        if (!$customer instanceof Customer) {
            return;
        }

        $clangData['CustomerEmail'] = $customer->getEmail();

        $this->entityManager->refresh($customer);
        $wishListItems = $customer->getWishListItems();

        foreach ($wishListItems as $wishListItem) {
            $this->entityManager->refresh($wishListItem);

            if (!$this->wishListRepository->itemHasValidProduct($wishListItem)) {
                continue;
            }

            $product = $wishListItem->getProduct();

            if ($product instanceof Product) {
                $productData = $this->buildproductData($product);
                $productData['Status'] = $wishListItem->getStatus() ? 'delete' : 'add';

                if ($wishListItem->getStatus() === false) {
                    $productsInWishlist[$product->getId()] = $productData;
                } else {
                    $productsNotInWishlist[$product->getId()] = $productData;
                }
            }
        }

        foreach ($productsInWishlist as $key => $inWishlist) {
            $wishList[$key] = $inWishlist;
        }

        foreach ($productsNotInWishlist as $key => $notInWishlist) {
            if (!array_key_exists($key, $wishList)) {
                $wishList[$key] = $notInWishlist;
            }
        }

        $clangData['Products'] = array_values($wishList);
        $this->data = $clangData;
    }

    /**
     * Send data to clang
     */
    private function sendData(string $type, bool $useBasicAuth = false): void
    {
        $clangWebhookUrl = $this->webhookUrls[$type];
        $clangToken = $this->webhookTokens[$type];
        try {
            $url = $clangWebhookUrl . $clangToken;
            $headers = [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ];

            if ($useBasicAuth === true) {
                $url = $clangWebhookUrl;
                $headers['Authorization'] = 'Basic ' . $clangToken;
            }

            $guzzleClient = new Client();
            $guzzleClient->request(
                'POST',
                $url,
                [
                    'headers' => $headers,
                    'json' => $this->data,
                ]
            );
        } catch (GuzzleException $e) {
//            throw (new QueueException('Clang ' . $type . ' Webhook Error #:' . $e->getMessage()))->setRequeueable();
        }
    }

    /**
     * Bouw cartItem array op
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function getCartItemData(CartItem $cartItem, string $country, string $deliveryDate): array
    {
        $cartItemData = [];
        $cartItemProduct = $cartItem->getProduct();
        $inStock = $cartItemProduct->getInStock();
        $stockState = $inStock >= $cartItem->getAmount() ? 'op voorraad' : 'niet op voorraad';
        $productMargin = $cartItemProduct->getMargin();
        $deliveryTime = 0;

        if ($inStock <= 0) {
            $productId = $cartItemProduct->getId();
            $deliveryTime = $cartItemProduct->getExpectedDeliveryTime();

            $deliveryInfo = $this
                ->productRepository
                ->getStockStatusData($this->activeConfig, $productId, false, 'datetime');

            $deliveryDate = array_key_exists($productId, $deliveryInfo) ? $deliveryInfo[$productId] : '';
            $deliveryDate = $deliveryDate instanceof DateTime ? $deliveryDate->format('Y-m-d') : $deliveryDate;
        }

        $barcode = null;
        $order = $cartItem->getOrder();
        if ($order->isFinishedOrder()) {
            $stockRepository = $this->entityManager->getRepository(Stock::class);
            $stock = $stockRepository->findOneBy([
                'order' => $order,
                'ordernumber' => $order->getOrdernr(),
                'product' => $cartItemProduct,
            ]);
            if ($stock instanceof Stock) {
                $barcode = $stock->getBarcode();
            }
        }

        $cartItemData['ProductId'] = $cartItemProduct->getId();
        $cartItemData['Rootgroup'] = $cartItemProduct->getSubgroup()->getMaingroup()->getRootgroup()->getName();
        $cartItemData['Hoofdgroup'] = $cartItemProduct->getSubgroup()->getMaingroup()->getName();
        $cartItemData['Subgroup'] = $cartItemProduct->getSubgroup()->getName();
        $cartItemData['Brand'] = $cartItemProduct->getBrand();
        $cartItemData['ProductAmount'] = $cartItem->getAmount();
        $cartItemData['ProductName'] = $cartItemProduct->getName();
        $cartItemData['Margin'] = $productMargin instanceof ProductMargin ? $productMargin->getValue() : 0.;
        $cartItemData['Specs'] = $cartItemProduct->getSpecs();
        $cartItemData['Weight'] = $cartItemProduct->getWeight();
        $cartItemData['Accessories'] = $cartItemProduct->getAccessories();
        $cartItemData['Combodeals'] = $cartItemProduct->getCombodeals();
        $cartItemData['PriceInclVat'] = (float)$cartItem->getPrice();
        $cartItemData['EAN/SKU'] = !empty($cartItemProduct->getEan()) ? $cartItemProduct->getEan() : '';
        $cartItemData['Barcode'] = $barcode ?? '-';
        $cartItemData['Stock'] = $stockState;
        $cartItemData['DeliveryTime'] = $deliveryTime;
        $cartItemData['DeliveryDate'] = $deliveryDate;
        $cartItemData['Image'] = $this->getImage($cartItemProduct);
        $cartItemData['Discount'] = $cartItem->getPriceDiscount();
        $cartItemData['PriceExclVat'] = $cartItem->getPriceEx();
        $cartItemData['Vat'] = $cartItem->getTax();
        $cartItemData['Url'] = $this->getProductUrl($cartItemProduct);
        $cartItemData['Visible'] = true;

        if ($cartItem->isParentWithoutOwnStock() || $cartItemProduct->isDynamicDiscountSet()) {
            $cartItemData['Visible'] = false;
        }

        return $cartItemData;
    }

    /**
     * @throws Exception
     */
    private function buildProductUpdateData(array $data): bool
    {
        $product = $this->productRepository->findOneBy(['id' => $data['productId']]);

        if (!$product instanceof Product) {
            return false;
        }

        $prices = $product->getPriceForCountry('NL');
        $stockLocation = $this->stockLocationRepository->findOneBy(['id' => StockLocation::MAGAZIJN_URK]);

        $clangData['ProductId'] = $product->getId();
        $clangData['PriceInc'] = $prices->getPrice();
        $clangData['PriceEx'] = $prices->getPriceEx();
        $clangData['Stock'] = count($product->getUnsoldStock($stockLocation));
        $clangData['Deliverable'] = $product->getExpectedDeliveryTime() !== Product::DISCONTINUED ? 'ja' : 'nee';
        $clangData['ReviewScore'] = $product->getReviewRating();

        $this->data = $clangData;

        return true;
    }

    private function getImage(Product $product): string
    {
        $images = $product->getImages();
        $imagePath = $this->imgPath . 'thumb_160/';
        $fullImagePath = '';
        /** @var ProductImage $image */
        foreach ($images as $image) {
            if (($image->getFlags() & 1) === 1 && $image instanceof ProductImage) {
                $fullImagePath = $imagePath . substr($image->getImage(), 0, 3) . '/' . $image->getImage();
                break;
            }
        }

        return $fullImagePath;
    }

    private function getProductUrl(Product $product): string
    {
        $this->productUrlService
            ->setName($product->getName())
            ->setSlug($product->getSlug())
            ->setId($product->getId())
        ;

        return $this->productUrlService->generateUrl(false, true);
    }

    private function buildCrossSellData(array $data): bool
    {
        $orderId = $data['orderId'];
        $order = $this->orderInfoRepository->find($orderId);

        if (!$order instanceof OrderInfo || !$order->isFinishedOrder()) {
            return false;
        }

        $crossSellingProducts = $this->productDiscountSet->getCrossSellingProductsForOrder($order);

        $crossSellingProductData = array_map(function ($crossSellingProduct) {
            return $this->buildProductData($crossSellingProduct);
        }, $crossSellingProducts);

        if (count($crossSellingProducts) < 3) {
            return false;
        }

        $clangData['CustomerId'] = $order->getCustomerid();
        $clangData['Email'] = $order->getEmail();
        $clangData['OrderNumber'] = $order->getOrdernr();
        $clangData['Products'] = array_values($crossSellingProductData);
        $this->data = $clangData;

        //also post to Squeezely
        $this->postCrossSell->postCrossSellProducts($order, $clangData['Products']);

        return true;
    }

    private function buildProductData(Product $product): array
    {
        $productData = [];
        $subGroup = $product->getSubgroup();
        $mainGroup = $subGroup instanceof Subgroup ? $subGroup->getMaingroup() : null;
        $rootGroup = $mainGroup instanceof Maingroup ? $mainGroup->getRootgroup() : null;
        $prices = $product->getPriceForCountry('NL');
        $productData['ProductId'] = $product->getId();
        $productData['RootGroup'] = $rootGroup instanceof Rootgroup ? $rootGroup->getName() : '';
        $productData['MainGroup'] = $mainGroup instanceof Maingroup ? $mainGroup->getName() : '';
        $productData['SubGroup'] = $subGroup instanceof Subgroup ? $subGroup->getName() : '';
        $productData['Brand'] = $product->getBrand(false);
        $productData['ProductName'] = $product->getName();
        $productData['PriceInclVat'] = (float)$prices->getPrice();
        $productData['EAN/SKU'] = !empty($product->getEan()) ? $product->getEan() : '';
        $productData['Stock'] = $product->getInStock();
        $productData['Image'] = $this->getImage($product);
        $productData['Url'] = $this->getProductUrl($product);

        return $productData;
    }

    private function buildPromotionsPaddingtonData(array $data) {
        $entry = $this->promotionsPaddingtonRepository->find($data['id']);

        if (!$entry instanceof PromotionsPaddington) {
            return false;
        }

        $this->data = [
            'id' => $entry->getId(),
            'voornaam' => $entry->getFirstName(),
            'tussenvoegsel' => $entry->getPrefix(),
            'achternaam' => $entry->getLastName(),
            'email' => $entry->getEmail(),
            'afbeelding_url' => $entry->getImageUrl(),
        ];

        return true;
    }

    private function buildPhotoContestCampaignEntryCreate(Entry $entry): bool
    {
        $this->data = [
            'id' => $entry->getId(),
            'campaign_id' => $entry->getConfiguration()->getId(),
            'campaign_name' => $entry->getConfiguration()->getName(),
            'emailaddress' => $entry->getEmail(),
            'firstname' => $entry->getFirstName(),
            'prefix' => $entry->getLastNamePrefix(),
            'lastname' => $entry->getLastName(),
            'email_opt_in' => $entry->isEmailOptin() ? 'Ja' : 'Nee',
            'images' => $entry->getFiles()->map(static fn (EntryFile $file) => $file->getUrl())->toArray(),
        ];

        return true;
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    private function getExpectedDeliveryDate(OrderInfo $orderInfo, Collection $cartItems): string
    {
        $deliveryTimestamp = $orderInfo->getOrderWait()?->getDeliveryDate() !== null &&
            $orderInfo->getOrderWait()?->getDeliveryDate() !== '' ?
            $orderInfo->getOrderWait()?->getDeliveryDate()->getTimestamp() : null;

        $deliveryTimestamps = [];
        if ($deliveryTimestamp === null) {
            $activeConfig = $this->configManager->getActive();

            /**
             * @var CartItem $cartItem
             */
            foreach ($cartItems as $cartItem) {
                $stockStatusData = $this->productRepository->getStockStatusData(
                    $activeConfig,
                    $cartItem->getProductId(),
                    false,
                    'datetime'
                );

                if (array_key_exists($cartItem->getProductId(), $stockStatusData)) {
                    $deliveryDatetime = $stockStatusData[$cartItem->getProductId()] ?? null;
                    $deliveryTimestamps[] = $deliveryDatetime?->getTimestamp() ?? 0;
                }
            }

            if (is_array($deliveryTimestamps) && $deliveryTimestamps !== []) {
                $deliveryTimestamp = max($deliveryTimestamps);
            }
        }

       if ($deliveryTimestamp > 0) {
           $deliveryDate = (new DateTime())->setTimestamp($deliveryTimestamp);
           return $deliveryDate->format('Y-m-d');
       }

       return '';
    }
}
