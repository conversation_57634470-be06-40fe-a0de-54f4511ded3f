<?php

namespace Webdsign\GlobalBundle\Services\Extension;

use DateTime;
use Doctrine\Common\Collections\Collection;
use Exception;
use IntlDateFormatter;
use Ob\HighchartsBundle\Highcharts\ChartInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Finder\Finder;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\CustomerRepository;
use Webdsign\GlobalBundle\Entity\Event\Product as EventProduct;
use Webdsign\GlobalBundle\Entity\Event\ProductRepository;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\StockInLocation;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\User;

class TwigExtension extends AbstractExtension
{
    public function __construct(
        private readonly ContainerInterface $container,
        private readonly TranslatorInterface $translator,
        private readonly CustomerRepository $customerRepository,
        private readonly ProductRepository $eventProductRepository,
    ) {
    }

    /**
     * Onze eigen twig functies.
     *
     * @return array
     */
    public function getFunctions(): array
    {
        return [
            'remote_image' => new TwigFunction('remote_image', [$this, 'remoteImage']),
            'file_exists' => new TwigFunction('file_exists', 'file_exists'),
            'all_mails_counter' => new TwigFunction('all_mails_counter', [$this, 'allMailsCounter']),
            'has_access' => new TwigFunction('has_access', [$this, 'hasAccess']),
            'has_role' => new TwigFunction('has_role', [$this, 'hasRole']),
            'is_loaded' => new TwigFunction('is_loaded', [$this, 'isLoaded']),
            'is_array' => new TwigFunction('is_array', [$this, 'isArray']),
            'readonly' => new TwigFunction('readonly', [$this, 'readonly']),
            'translate_attributes' => new TwigFunction('translate_attributes', [$this, 'translateAttributes']),
            'find_file' => new TwigFunction('find_file', [$this, 'findFile']),
            'is_cat' => new TwigFunction('is_cat', [$this, 'isCat']),
            'get_one_product_code' => new TwigFunction('get_one_product_code', [$this, 'getOneProductCode']),
            'orderLocations' => new TwigFunction('orderLocations', [$this, 'orderLocations']),
            'str_repeat' => new TwigFunction('str_repeat', [$this, 'strRepeat']),
            'path_active' => new TwigFunction('path_active', [$this, 'pathActive']),
            'event_website_url' => new TwigFunction('event_website_url', [$this, 'eventWebsiteUrl']),
            'chart' => new TwigFunction('chart', [$this, 'chart'], ['is_safe' => ['html']]),
            'is_customer_important' => new TwigFunction('is_customer_important', [$this, 'isCustomerImportant']),
            'instance_of' => new TwigFunction('instance_of', [$this, 'instanceOf']),
        ];
    }

    public function chart(ChartInterface $chart, $engine = 'jquery'): string
    {
        return $chart->render($engine);
    }

    /**
     * Onze eigen twig filters.
     *
     * @return array
     */
    public function getFilters(): array
    {
        return [
            'date_format' => new TwigFilter('date_format', [$this, 'dateFormat']),
            'initials' => new TwigFilter('initials', [$this, 'initials']),
            'json_decode' => new TwigFilter('json_decode', [$this, 'jsonDecode']),
            'transchoice' => new TwigFilter('transchoice', [$this, 'transChoice']),
        ];
    }

    /**
     * Bestaat een extern plaatje?
     *
     * @param $url
     * @return boolean
     */
    public function remoteImage($url): bool
    {
        $fileHeaders = @get_headers($url);

        return is_array($fileHeaders) && strpos($fileHeaders[0], '200 OK') !== false;
    }

    /**
     * Initialen van een naam ophalen
     *
     * @param $str
     * @return string
     */
    public function initials($str): string
    {
        $first = [$str[0], $str[1]];

        return implode('', $first);
    }

    /**
     * Hoeveel mails staan er in totaal open?
     *
     * @return string
     */
    public function allMailsCounter(): string
    {
        $mailCounter = $this->container->get('webdsignservice.mailcounter');
        $totals = $mailCounter->getTotals();
        if (array_key_exists('totalOpenMails', $totals)) {
            return $totals['totalOpenMails'];
        }

        return '-';
    }

    /**
     * @param User $user
     * @param $resource
     * @return bool
     */
    public function hasAccess(User $user, $resource): bool
    {
        return $user->hasAccess($resource);
    }

    public function hasRole(User $user, $role): bool
    {
        return $user->hasRole($role);
    }

    /**
     * Is een doctrine entity al geladen?
     *
     * @param $entity
     * @param $controlColumn
     * @return bool
     */
    public function isLoaded($entity, $controlColumn = 'name'): bool
    {
        $doctrineHelper = $this->container->get('webdsigncore.doctrinehelper');

        return $doctrineHelper->isEntityLoaded($entity, $controlColumn);
    }

    /**
     * Is een variable een array?
     *
     * @param $variable
     * @return bool
     */
    public function isArray($variable): bool
    {
        return is_array($variable);
    }

    /**
     * Draait de tool in readonly mode?
     *
     * @return bool
     */
    public function readonly(): bool
    {
        $statusChecker = $this->container->get('webdsign.statuschecker');
        return $statusChecker->isReadonly();
    }

    /**
     * Vertaal attributen van een formulierelement.
     *
     * @param array $attributes
     * @return array mixed
     */
    public function translateAttributes(array $attributes): array
    {
        $t = false;

        foreach ($attributes as $attributeName => $attributeValue) {
            if ((substr($attributeName, -strlen('|trans')) === '|trans') !== false) {
                if ($t === false) {
                    $t = $this->container->get('translator');
                }

                unset($attributes[$attributeName]);
                $attributeName = str_replace('|trans', '', $attributeName);
                $attributes[$attributeName] = $t->trans($attributeValue);
            }
        }

        return $attributes;
    }


    /**
     * @param $fileName
     * @param bool $wildcard
     * @param bool $extraPath
     * @return bool|string
     */
    public function findFile($fileName, $wildcard = false, $extraPath = false)
    {
        if ($wildcard) {
            $fileName .= '*';
        }
        $path = ($extraPath) ?
            $this->container->getParameter('web_path') :
            $this->container->getParameter('web_path') . $extraPath;


        $finder = new Finder();
        $finder->name($fileName)->files();

        foreach ($finder->in($path) as $file) {
            return '/' . $file->getRelativePathname();
        }

        return false;
    }

    /**
     * Laat een datum op verschillende tijden op verschillende manieren zien.
     *
     * @param $date
     * @param $locale
     * @param bool $fuzzy
     * @return mixed
     */
    public function dateFormat($date, $locale, $fuzzy = false)
    {
        if (!is_a($date, 'DateTime')) {
            return false;
        }
        $today = new DateTime();
        $interval = $date->diff($today);
        $t = $this->container->get('translator');

        $prepend = '';
        $format = '';
        $dateFormat = IntlDateFormatter::NONE;
        $timeFormat = IntlDateFormatter::NONE;

        // Om uit te rekenen of een datum gisteren is.
        $hoursAgo = $interval->format('%R%a') * 24 + $interval->h;
        $currentHour = (int) $today->format('H');

        // Om vorige week te kunnen bepalen.
        $hoursInAWeek = 7 * 24;
        $dayNumber = (int) $today->format('w');
        $hoursSinceNewWeek = $currentHour + $dayNumber * 24;

        switch (true) {
            case ($hoursAgo < 0):
                if ($fuzzy === true) {
                    return $t->trans('core.future');
                }
                break;
            // Het is nog vandaag.
            case ($hoursAgo < $currentHour):
                if ($fuzzy === true) {
                    return $t->trans('core.today');
                }

                $format = 'H:mm';
                $prepend = $t->trans('core.todayat');
                break;
            // Het aantal uren geleden is meer dan het huidige uur en minder dan
            // het huidige uur + een dag.
            case ($hoursAgo >= $currentHour && $hoursAgo < (24 + $currentHour)):
                if ($fuzzy === true) {
                    return $t->trans('core.yesterday');
                }

                $format = 'H:mm';
                $prepend = $t->trans('core.yesterdayat');
                break;
            // Zelfde als hierboven maar dan + twee dagen.
            case ($hoursAgo >= $currentHour && $hoursAgo < (48 + $currentHour)):
                if ($fuzzy === true) {
                    return $t->trans('core.daybeforeyesterday');
                }
            // Als het aantal uren geleden binnen deze week valt.
            case ($hoursAgo >= $currentHour && $hoursAgo < $hoursSinceNewWeek):
                if ($fuzzy === true) {
                    return $t->trans('core.thisweek');
                }
            // Hetzelfde maar dan in de vorige week.
            case (
                $hoursAgo >= $currentHour &&
                $hoursAgo > $hoursSinceNewWeek &&
                $hoursAgo < ($hoursSinceNewWeek + $hoursInAWeek)
            ):
                if ($fuzzy === true) {
                    return $t->trans('core.lastweek');
                }
            default:
                if ($fuzzy === true) {
                    return $t->trans('core.evenolder');
                }
                $dateFormat = IntlDateFormatter::MEDIUM;
                $timeFormat = IntlDateFormatter::SHORT;
        }

        $formatter = IntlDateFormatter::create(
            $locale,
            $dateFormat,
            $timeFormat,
            $date->getTimezone()->getName(),
            IntlDateFormatter::GREGORIAN,
            $format
        );

        if (!empty($prepend)) {
            $prepend .= ' ';
        }

        return $prepend . $formatter->format($date->getTimestamp());
    }

    /**
     * @param Product $product
     * @param string $type
     * @param StockLocation[] $stockLocations
     * @return string|null
     * @throws Exception
     */
    public function getOneProductCode(Product $product, string $type = 'ean', array $stockLocations = []): ?string
    {
        $productCodes = $product->getProductCodes();
        $firstProductCode = null;

        foreach ($productCodes as $productCode) {
            if ($productCode->getType() === $type && ($firstProductCode === null || $productCode->getFlags() & 2)) {
                $firstProductCode = $productCode->getCode();
                if ($productCode->getFlags() & 2) {
                    break;
                }
            }
        }

        if ($firstProductCode === null) {
            foreach ($stockLocations as $stockLocation) {
                $barcodes = [];
                $unsoldStock = $product->getUnsoldStock($stockLocation);

                foreach ($unsoldStock as $item) {
                    $barcodes[] = $item->getBarcode();
                }

                if (count($barcodes)) {
                    $firstProductCode = implode(', ', $barcodes);
                }
            }
        }

        return $firstProductCode;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return 'webdsign_twig_extension';
    }

    /**
     * @return bool
     */
    public function isCat(): bool
    {
        return getenv('SERVER_NAME') === $this->container->getParameter('cat_hostname');
    }

    /**
     * @param Collection $collection
     * @return array
     */
    public function orderLocations(Collection $collection): array
    {
        /** @var StockInLocation $item */
        foreach ($collection as $item) {
            $data[] = [
                'stock' => $item->getStock(),
                'color' => $item->getStockLocation()->getColor(),
                'code' => $item->getStockLocation()->getCode(),
                'location' => $item->getStockLocation(),
            ];
        }

        usort($data, static function ($a, $b) {
            return $a['code'] <=> $b['code'];
        });

        return $data;
    }

    /**
     * @param string $input
     * @param int $multiplier
     * @return string
     */
    public function strRepeat(string $input, int $multiplier): string
    {
        return str_repeat($input, $multiplier);
    }

    public function jsonDecode(string $string) {
        return json_decode($string);
    }

    public function pathActive(array $paths): bool
    {
        /** @var RequestStack */
        $requestStack = $this->container->get('request_stack');
        $currentRoute = $requestStack->getCurrentRequest()->get('_route');

        return in_array($currentRoute, $paths);
    }

    public function eventWebsiteUrl(int|EventProduct $eventProduct): string
    {
        $productUrlService = $this->container->get('product_url_service');
        if (is_int($eventProduct)) {
            $eventProduct = $this->eventProductRepository->find($eventProduct);
        }

        $event = $eventProduct->getEvent();

        if ($productUrlService !== null) {
            $productUrlService
                ->setLanguage(null)
                ->setName($event->getTitle())
                ->setId($event->getId());

            return $productUrlService->generateEventsUrl(true);
        }

        return '';
    }

    public function transChoice(string $message, int $count): string
    {
        $message = $this->translator->trans($message, ['%count%' => $count]);
        $choices = explode('|', $message);
        if (count($choices) === 1) {
            return $message;
        }

        return $count === 1
            ? $choices[0]
            : $choices[1];
    }

    public function isCustomerImportant(Customer $customer): bool
    {
        return $this->customerRepository->isCustomerImportant($customer);
    }

    public function instanceOf(object $instance, string $class): bool
    {
        $reflection = new \ReflectionClass($class);

        return $reflection->isInstance($instance);
    }
}
