<?php

namespace Webdsign\GlobalBundle\Services\RMA;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;
use Webdsign\GlobalBundle\Entity\Country;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\CustomerAddress;
use Webdsign\GlobalBundle\Entity\CustomerRepository;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Entity\OrderTickets;
use Webdsign\GlobalBundle\Entity\Rma;
use Webdsign\GlobalBundle\Entity\RmaOption;
use Webdsign\GlobalBundle\Entity\RmaProduct;
use Webdsign\GlobalBundle\Entity\RmaReason;
use Webdsign\GlobalBundle\Entity\RmaStatus;
use Webdsign\GlobalBundle\Entity\RmaStatusRepository;
use Webdsign\GlobalBundle\Entity\RmaType;
use Webdsign\GlobalBundle\Entity\ServiceTag;
use Webdsign\GlobalBundle\Entity\ServiceTagRepository;
use Webdsign\GlobalBundle\Entity\Stock;
use Webdsign\GlobalBundle\Entity\StockRepository;
use Webdsign\GlobalBundle\Entity\Ticket;
use Webdsign\GlobalBundle\Entity\TicketReply;
use Webdsign\GlobalBundle\Exception\NoMailboxException;
use Webdsign\GlobalBundle\Exception\NoProductsException;
use Webdsign\GlobalBundle\Services\RmaConfirmation;
use Webdsign\GlobalBundle\Services\TicketHash;

readonly class HandleNewRma
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private TicketHash $ticketHash,
        private RmaConfirmation $rmaConfirmation
    ) {
    }

    /**
     * @param array $data
     * @throws NoProductsException
     * @throws OptimisticLockException
     * @throws LoaderError
     * @throws RuntimeError
     * @throws SyntaxError
     * @return Ticket
     */
    public function handleNewRma(array $data): Ticket
    {
        $products = [];
        if (isset($data['products'])) {
            $products = $data['products'];
        } else {
            $products[] = [
                'barcode' => $data['barcode'] ?? '',
                'artikel' => $data['artikel'] ?? '',
                'voorraad_id' => $data['voorraad_id'] ?? '',
                'bestelling_id' => $data['bestelling_id'] ?? '',
            ];
        }

        if (0 === count($products)) {
            throw new NoProductsException('Products not found in data');
        }

        $title = '';
        $ticket = new Ticket();
        $ticket->setHash($this->ticketHash->getHash());
        $ticket->setState('open');
        $customer = $this->findCustomerForRma($data);
        $ticket->setCustomer($customer);

        if ($customer) {
            $ticket->setOrigin($customer->getOrigin());
        }

        foreach ($this->findTags($data['soort'], 'rma') as $tag) {
            $ticket->addTag($tag);
            $title .= $tag->getName();
        }

        $ticket->setSubject(''); // Deze kan leeg blijven, de mail naar de klant staat in de eerste reply
        if (!empty($data['emailadres'])) {
            $ticket->setFromAddress($data['emailadres']);
        } elseif ($customer) {
            $ticket->setFromAddress($customer->getEmail());
        }
        $rma = new Rma();
        $rma->setTicket($ticket);

        // Default type gebruiken. Workaround voor CAT-3014
        $rmaTypeRepository = $this->entityManager->getRepository(RmaType::class);
        $rmaType = $rmaTypeRepository->find(1);

        if ($rmaType !== null) {
            $rma->setType($rmaType);
        }

        /** @var RmaStatusRepository $rmaStatusRepository */
        $rmaStatusRepository = $this->entityManager->getRepository(RmaStatus::class);
        $rma->setStatus($rmaStatusRepository->getFirstStatusForRma($rma));

        $orderIds = [];
        foreach ($products as $productData) {
            $rmaProduct = new RmaProduct();
            $rmaProduct->setBarcode($productData['barcode'] ?? '');
            $rmaProduct->setTitle($productData['artikel'] ?? '');
            if (!empty($productData['voorraad_id'])) {
                $rmaProduct->setStockItem($this->findStockItem($productData['voorraad_id']));
            }
            $rma->addRmaProduct($rmaProduct);
            if (isset($productData['bestelling_id'])) {
                $orderIds[] = $productData['bestelling_id'];
            }
        }

        // gebruik in de title van het ticket alleen het eerste product
        $title .= ' - ' . $rma->getRmaProducts()->first()->getTitle();
        $ticket->setTitle($title);

        if ($data['rmaReason'] !== '') {
            $rmaReasonRepository = $this->entityManager->getRepository(RmaReason::class);
            $rmaReason = $rmaReasonRepository->find($data['rmaReason']);

            if ($rmaReason instanceof RmaReason) {
                $rma->setReason($rmaReason);
            }
        }

        $rma->setDescription($data['klachtomschrijving'] ?? '');
        $rma->setCustomerName($data['naam'] ?? '');
        $rma->setCustomerStreet($data['adres'] ?? '');
        $rma->setCustomerHouseNr($data['huisnr'] ?? '');
        $rma->setCustomerHouseNrExt($data['ext'] ?? '');
        $rma->setCustomerPostalcode($data['postcode'] ?? '');
        $rma->setCustomerCity($data['woonplaats'] ?? '');

        if (!empty($data['land'])) {
            $rma->setCustomerCountry($this->findCountry($data['land']));
        }

        if (!empty($data['oplossing'])) {
            foreach ($this->findSolutions($data['oplossing']) as $solution) {
                $rma->addSolution($solution);
            }
        }

        $this->entityManager->persist($ticket);
        $this->entityManager->persist($rma);

        if ($customer && $customer->getAddresses()->count() === 0) {
            $this->createAddressForRmaCustomer($rma, $customer);
        }

        $this->entityManager->flush(); // nodig om rmaId te verkrijgen

        $ticket->setTitle('RMA ' . $rma->getId() . ' - ' . $title);

        $orderIds = array_unique($orderIds);
        foreach ($orderIds as $orderId) {
            if ($orderInfo = $this->findOrder($orderId)) {
                $orderTicket = new OrderTickets();
                $orderTicket->setOrder($orderInfo);
                $orderTicket->setTicket($ticket);
                $this->entityManager->persist($orderTicket);
            }
        }

        $this->entityManager->flush();

        $this->sendMailForRma($rma, $data);

        $this->addInternalNote($ticket, 'Door klant ingevuld op website' . "\n" . 'IP: ' . $data['ipnr'] . "\n" . 'Aangemeld: ' . $data['aangemeld']);
        $this->entityManager->flush();

        return $ticket;
    }

    private function findCustomerForRma(array $data): ?Customer
    {
        /** @var CustomerRepository $customerRepository */
        $customerRepository = $this->entityManager->getRepository(Customer::class);

        $customer = null;
        if (!empty($data['user_id'])) {
            /** @var Customer $customer */
            $customer = $customerRepository->find($data['user_id']);
        }

        if (!$customer && !empty($data['bestelling_id'])) {
            $orderInfo = $this->findOrder($data['bestelling_id']);

            if ($orderInfo instanceof OrderInfo) {
                $customer = $orderInfo->getCustomer();
            }
        }

        if (!$customer && !empty($data['emailadres'])) {
            $customers = $customerRepository->findBy([
                'email' => $data['emailadres'],
                'mergeid' => 0
            ]);
            if (count($customers) === 1) {
                $customer = $customers[0];
            }
        }

        return $customer;
    }

    private function findTags(string $searchString, string $category = null): array
    {
        $tagMapping = [
            'schade' => 'reparatie',
        ];

        /** @var ServiceTagRepository $tagRepository */
        $tagRepository = $this->entityManager->getRepository(ServiceTag::class);
        $searchOptions = [
            'active' => 1,
            'name' => $tagMapping[$searchString] ?? $searchString
        ];

        if (null !== $category) {
            $searchOptions['category'] = $category;
        }

        return $tagRepository->findBy($searchOptions);
    }

    /**
     * @param int $stockId
     * @return null|Stock
     */
    private function findStockItem(int $stockId): ?Stock
    {
        /** @var StockRepository $stockRepository */
        $stockRepository = $this->entityManager->getRepository(Stock::class);
        /** @var Stock $stock */
        $stock = $stockRepository->find($stockId);
        return $stock;
    }

    /**
     * @param string $searchString
     * @return RmaOption[]
     */
    private function findSolutions(string $searchString): array
    {
        $optionRepository = $this->entityManager->getRepository(RmaOption::class);
        $queryBuilder = $optionRepository->createQueryBuilder('ro');
        $queryBuilder->select('ro')
            ->where($queryBuilder->expr()->like('ro.title', ':title'))
            ->andWhere("ro.type = 'solution'")
            ->orderBy('ro.sortOrder', 'asc')
            ->setParameter('title', $searchString . '%');

        return $queryBuilder->getQuery()->getResult();
    }

    private function findOrder(int $orderId): ?OrderInfo
    {
        /** @var OrderInfoRepository $orderInfoRepository */
        $orderInfoRepository = $this->entityManager->getRepository(OrderInfo::class);
        /** @var OrderInfo $orderInfo */
        $orderInfo = $orderInfoRepository->find($orderId);
        return $orderInfo;
    }

    /**
     * @param Rma $rma
     * @param array $data
     * @throws OptimisticLockException
     * @throws LoaderError
     * @throws RuntimeError
     * @throws SyntaxError
     * @throws NoMailboxException
     * @return void
     */
    private function sendMailForRma(Rma $rma, array $data): void
    {
        // TODO: klant gegevens aanpassen adv $data: email, telefoon (deze staan niet in RMA)
        $toAddress = $data['emailadres'];// $rma->getTicket()->getCustomer()->getEmail()

        $rmaConfirmation = $this->rmaConfirmation;

        $ticket = $rma->getTicket();
        $customer = $ticket instanceof Ticket ? $ticket->getCustomer() : null;

        //eerst kijken of nl/en is ingesteld anders naar land kijken niet nl is altijd en
        if ($customer instanceof Customer && in_array(strtolower($customer->getLanguage()), ['nl', 'en'])) {
            $language = $customer->getLanguage();
        } else {
            $language = (strtolower($data['land']) === 'nl') ? 'nl' : 'en';
        }

        $result = $rmaConfirmation->sendMail(
            $rma,
            $toAddress,
            $data['mailContentId'],
            $language,
            [
                'phone' => $data['telefoon'] ?? '-',
                'email' => $toAddress
            ],
            true
        );

        if (!$result) {
            $this->addInternalNote($rma->getTicket(), 'Let op! Er is waarschijnlijk iets mis gegaan met het versturen van de bevestigingsmail: ' . $toAddress);
        }
    }

    private function addInternalNote(Ticket $ticket, string $text): void
    {
        $ticketReply = new TicketReply();
        $ticketReply->setTicket($ticket);
        $ticketReply->setContent($text);
        $ticketReply->setReplyType('internal');
        $ticketReply->setCreated(new DateTime());
        $this->entityManager->persist($ticketReply);
    }

    private function findCountry(string $countryCode): ?Country
    {
        $countryRepository = $this->entityManager->getRepository(Country::class);
        /** @var Country $country */
        $country = $countryRepository->findOneBy([
            'code' => $countryCode
        ]);

        return $country;
    }

    public function createAddressForRmaCustomer(Rma $rma, Customer $customer): CustomerAddress
    {
        $address = new CustomerAddress();
        $address->setName($customer->getFirstName());
        $address->setFirstName($customer->getFirstName());
        $address->setLastNamePrefix('');
        $address->setLastName('');
        $address->setAddress($rma->getCustomerStreet());
        $address->setHousenr($rma->getCustomerHouseNr());
        $address->setHousenrext($rma->getCustomerHouseNrExt());
        $address->setZipcode($rma->getCustomerPostalcode());
        $address->setCity($rma->getCustomerCity());

        $country = $rma->getCustomerCountry();
        $countryCode = $country->getCode();
        $address->setCountry($countryCode);

        $address->setCustomer($customer);
        $address->setIsMainAddress(true);
        $address->setIsSendAddress(true);

        $this->entityManager->persist($address);
        $this->entityManager->flush();

        return $address;
    }
}
