<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Services\Afas;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use GuzzleHttp\Psr7\Uri;
use GuzzleHttp\TransferStats;
use JsonException;
use Throwable;
use Webdsign\GlobalBundle\Entity\Afas\Log;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Exception\Afas\InvalidLogException;
use Webdsign\GlobalBundle\Exception\Afas\InvalidRequestException;
use Webdsign\GlobalBundle\Form\Filter\Afas\AbstractFilter;
use Webdsign\GlobalBundle\Form\Filter\Afas\AfasFilterInterface;

class ApiClient
{
    private string $url;
    private string $token;
    private Logger $logger;
    private ?AfasFilterInterface $filter = null;
    private ?Uri $effectiveUrl = null;

    public function __construct(
        string $url,
        string $token,
        Logger $logger
    ) {
        $this->url = $url;
        $this->token = 'AfasToken ' . base64_encode($token);
        $this->logger = $logger;
    }

    /**
     * @throws InvalidLogException
     * @throws JsonException
     * @throws InvalidRequestException
     */
    public function execute(string $method, string $connector, ?array $payload = null, ?object $entity = null): string
    {
        $client = new Client([
            'base_uri' => $this->url,
        ]);

        // Workaround voor foutieve utf-8 karakters
        $encodedPayload = '{}';
        if ($payload !== null) {
            $payload = mb_convert_encoding($payload, 'UTF-8', 'UTF-8');
            $encodedPayload = json_encode($payload, JSON_THROW_ON_ERROR);
        }

        try {
            $options = [
                'headers' => [
                    'Authorization' => $this->token,
                    'Connection' => 'Keep-Alive',
                    'Content-Type' => 'Application/json'
                ],
                'on_stats' => function (TransferStats $stats) {
                    $this->effectiveUrl = $stats->getEffectiveUri();
                }
            ];

            if ($payload !== null) {
                $options['body'] = $encodedPayload;
            }

            if ($this->filter instanceof AfasFilterInterface) {
                $query = [];

                if ($this->filter->skip !== null) {
                    $query['skip'] = $this->filter->skip;
                }

                if ($this->filter->take !== null) {
                    $query['take'] = $this->filter->take;
                }

                if (count($this->filter->filters)) {
                    $filtersJson = [
                        'Filters' => [
                            'Filter' => []
                        ]
                    ];

                    foreach ($this->filter->filters as $i => $group) {
                        $groupFields = [];

                        foreach ($group as $fieldFilter) {
                            $groupFields[] = [
                                '@FieldId' => $fieldFilter['field'],
                                '@OperatorType' => (string)$fieldFilter['operator'],
                                '#text' => (string)$fieldFilter['value'],
                            ];
                        }

                        $filtersJson['Filters']['Filter'][] = [
                            '@FilterId' => 'Filter ' . ($i + 1),
                            'Field' => $groupFields,
                        ];
                    }

                    $query['filterjson'] = json_encode($filtersJson, JSON_UNESCAPED_SLASHES);
                }

                if (count($this->filter->sort)) {
                    $query['orderbyfieldids'] = implode(',', $this->filter->sort);
                }

                if (count($query)) {
                    $options['query'] = $query;
                }
            }

            $response = $client->request($method, $connector, $options);

            return $response->getBody()->getContents();
        } catch (Throwable $e) {
            /**
             * @var BadResponseException $e
             */
            $response = $e->getResponse();
            $content = $response->getBody()->getContents();

            if (empty($content)) {
                $content = json_encode($e->getMessage(), JSON_THROW_ON_ERROR);
            }

            $this->logger->add(
                $connector,
                $method,
                Log::TYPE_ERROR,
                $encodedPayload,
                $content,
                $entity instanceof Customer ? $entity : null,
                $entity instanceof OrderInfo ? $entity : null
            );

            $content = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
            throw new InvalidRequestException($content['externalMessage'] ?? $content);
        }
    }

    public function setFilter(AfasFilterInterface $filter): void
    {
        $this->filter = $filter;
    }

    public function getEffectiveUrl(): ?Uri
    {
        return $this->effectiveUrl;
    }
}
