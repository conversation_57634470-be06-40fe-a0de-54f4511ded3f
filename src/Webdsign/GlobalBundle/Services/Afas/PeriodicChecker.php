<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Services\Afas;

use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use JsonException;
use Webdsign\GlobalBundle\Constant\Afas\OrderConstants;
use Webdsign\GlobalBundle\Entity\Afas\InvoiceInfo;
use Webdsign\GlobalBundle\Entity\Afas\InvoiceInfoRepository;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Exception\Afas\InvalidLogException;
use Webdsign\GlobalBundle\Exception\Afas\InvalidRequestException;
use Webdsign\GlobalBundle\Form\Filter\Afas\AbstractFilter;
use Webdsign\GlobalBundle\Form\Filter\Afas\OrderFilter;

class PeriodicChecker
{
    private OrderInfoRepository $orderRepository;
    private OrderManager $orderManager;

    public function __construct(
        OrderInfoRepository $orderRepository,
        OrderManager $orderManager,
        private readonly InvoiceInfoRepository $invoiceInfoRepository
    ) {
        $this->orderRepository = $orderRepository;
        $this->orderManager = $orderManager;
    }

    /**
     * @throws InvalidLogException
     * @throws InvalidRequestException
     * @throws JsonException
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function compareMonth(int $administrationId, int $year, int $period): array
    {
        $parents = $this->findParentForAdministration($administrationId);

        $adminOrdersRaw = $this->orderRepository->shouldBeInAfas($parents, $year, $period);
        $adminOrders = [];

        foreach ($adminOrdersRaw as $adminOrder) {
            $adminOrder['totalIncAdmin'] = round(
                $adminOrder['extra_costs'] +
                $adminOrder['sold_stock'] +
                $adminOrder['ledger_fields'] -
                $adminOrder['returns'] -
                $adminOrder['coupons'] +
                $adminOrder['discount']
            , 2);

            $adminOrders[$adminOrder['invoiceNumber']] = $adminOrder;
        }

        unset($adminOrdersRaw);

        $afasOrders = $this->findAfasOrdersForPeriod($administrationId, $year, $period);

        return $this->compare($adminOrders, $afasOrders);
    }

    private function compare(array $adminOrders, array $afasOrders): array
    {
        $inAdminButNotInAfas = [];
        $inAfasButNotInAdmin = [];
        $inBothButDifferentTotals = [];
        $diffTotal = 0.;

        foreach ($adminOrders as $key => $adminOrder) {
            if (!array_key_exists($key, $afasOrders)) {
                $inAdminButNotInAfas[$key] = $adminOrder;
                continue;
            }

            $diff = abs($adminOrder['totalIncAdmin'] - $afasOrders[$key]);

            if ($diff > 0.05) {
                $adminOrder['totalIncAfas'] = $afasOrders[$key];
                $adminOrder['diff'] = round($diff, 2);
                $inBothButDifferentTotals[$key] = $adminOrder;
                $diffTotal += $diff;
            }
        }

        foreach ($afasOrders as $key => $totalInc) {
            if (!array_key_exists($key, $adminOrders)) {
                $inAfasButNotInAdmin[$key] = [
                    'invoiceNumber' => $key,
                    'totalIncAdmin' => 0.,
                    'totalIncAfas' => $totalInc
                ];
            }

            unset($adminOrders[$key]);
            unset($afasOrders[$key]);
        }

        return [
            'inAdminButNotInAfas' => $inAdminButNotInAfas,
            'inAfasButNotInAdmin' => $inAfasButNotInAdmin,
            'inBothButDifferentTotals' => $inBothButDifferentTotals,
            'diffTotal' => $diffTotal,
        ];
    }

    public function compareYear(int $administrationId, int $year): array
    {
        ini_set('memory_limit', '1024M');
        $parents = $this->findParentForAdministration($administrationId);

        $adminOrdersRaw = $this->orderRepository->shouldBeInAfas($parents, $year);
        $adminOrders = [];

        foreach ($adminOrdersRaw as $adminOrder) {
            $adminOrder['totalIncAdmin'] = round(
                $adminOrder['extra_costs'] +
                $adminOrder['sold_stock'] +
                $adminOrder['ledger_fields'] -
                $adminOrder['returns'] -
                $adminOrder['coupons'] +
                $adminOrder['discount']
                , 2);

            $adminOrders[$adminOrder['invoiceNumber']] = $adminOrder;
        }

        unset($adminOrdersRaw);

        $afasOrders = $this->findAfasOrdersForYear($administrationId, $year);

        $indexedAfasOrders = [];
        foreach ($afasOrders as $afasOrder) {
            $indexedAfasOrders[$afasOrder['invoiceNumber']] = $afasOrder['total'];
        }

        unset($afasOrders);

        return $this->compare($adminOrders, $indexedAfasOrders);
    }

    private function findAfasOrdersForYear(int $administrationId, int $year): array
    {
        return $this->invoiceInfoRepository->findForAdministrationByYear($administrationId, $year);
    }

    private function findParentForAdministration(int $administrationId): array
    {
        // Parents opzoeken bij de gekozen administratie
        return array_keys(
            array_filter(
                OrderConstants::ADMINISTRATION_PARENT_MAPPING,
                static function ($value) use ($administrationId) {
                    return $value === $administrationId || $administrationId === 0;
                }
            )
        );
    }

    /**
     * @throws OptimisticLockException
     * @throws InvalidRequestException
     * @throws ORMException
     * @throws JsonException
     * @throws InvalidLogException
     */
    public function findAfasOrdersForPeriod(int $administrationId, int $year, int $period): array
    {
        // AFAS-orders opzoeken
        $skip = 0;
        $take = 10000;
        $orderFilter = new OrderFilter();
        $orderFilter->take = $take;

        $shared = [
            'Year' => [$year, AbstractFilter::FILTER_IS_EQUAL_TO],
            'Period' => [$period, AbstractFilter::FILTER_IS_EQUAL_TO],
            'SeqNo' => [1, AbstractFilter::FILTER_IS_EQUAL_TO],
        ];

        if ($administrationId !== 0) {
            $shared['UnitId'] = [$administrationId, AbstractFilter::FILTER_IS_EQUAL_TO];
        }

        $variants = [
            ['JournalId' => [OrderConstants::JOURNAL_SALES_ADMIN, AbstractFilter::FILTER_IS_EQUAL_TO],],
            ['JournalId' => [OrderConstants::JOURNAL_SALES_ADMIN_ROTTERDAM, AbstractFilter::FILTER_IS_EQUAL_TO],],
        ];

        $orderFilter->addGroupedFilter($shared, $variants);

        $loop = true;
        $afasOrders = [];

        while ($loop === true) {
            $orderFilter->skip = $skip;
            $results = $this->orderManager->find($orderFilter);
            $skip += $take;

            if (!array_key_exists('rows', $results) || count($results['rows']) === 0) {
                $loop = false;
            } else {
                foreach ($results['rows'] as $result) {
                    $totalInc = 0;

                    $totalInc += $result['AmtDebit'];
                    $totalInc -= $result['AmtCredit'];

                    $afasOrders[$result['InvoiceId']] = $totalInc;
                }
            }
        }

        return $afasOrders;
    }
}
