<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Services\Event;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Webdsign\GlobalBundle\DTO\Event\EventDataObject;
use Webdsign\GlobalBundle\DTO\ProductDataObject;
use Webdsign\GlobalBundle\Entity\Event\Event;
use Webdsign\GlobalBundle\Entity\Event\EventRepository;
use Webdsign\GlobalBundle\Entity\Event\Product;
use Webdsign\GlobalBundle\Entity\Event\ProductRepository;
use Webdsign\GlobalBundle\Entity\Product as NormalProduct;
use Webdsign\GlobalBundle\Entity\ProductPrice;
use Webdsign\GlobalBundle\Entity\ProductTag;
use Webdsign\GlobalBundle\Entity\ProductWarranty;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Entity\Subgroup;
use Webdsign\GlobalBundle\Entity\Tag;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Form\DataTransformer\Event\EventTransformer;
use Webdsign\GlobalBundle\Services\ProductManager;

class EventManager
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private EventRepository $eventRepository,
        private ProductRepository $productRepository,
        private EventTransformer $eventTransformer,
        private ProductManager $normalProductManager,
        private TokenStorageInterface $tokenStorage,
        private StockLocationRepository $stockLocationRepository
    ) {
    }

    public function get(int $id): ?EventDataObject
    {
        $eventProduct = $this->productRepository->find($id);

        if ($eventProduct === null) {
            return null;
        }

        return $this->eventTransformer->transform($eventProduct);
    }

    public function save(EventDataObject $dto): Product
    {
        $token = $this->tokenStorage->getToken();
        $user = $token?->getUser();

        if (!$user instanceof User) {
            throw new AccessDeniedHttpException();
        }

        if ($dto->eventId === null) {
            $event = new Event();
            $event->setCreated(new DateTimeImmutable());
            $event->setCreator($user);
            $this->entityManager->persist($event);
        } else {
            $event = $this->eventRepository->find($dto->eventId);
        }

        $normalProductDto = new ProductDataObject();

        if ($dto->productId === null) {
            // We maken een eventproduct en een adminproduct aan en koppelen deze aan het event
            $product = new Product();
            $product->setEvent($event);
            $product->setCreated(new DateTimeImmutable());
            $this->entityManager->persist($product);
        } else {
            $product = $this->productRepository->find($dto->productId);
            $normalProductDto->id = $product->getProduct()->getId();
        }

        $normalProductDto->name = implode(' ', [
            $dto->title,
            '-',
            $dto->startDateAndTime->format('Y-m-d'),
        ]);
        $normalProductDto->subgroupId = Subgroup::SUBGROUP_EVENTS_ID;
        $normalProductDto->flags = NormalProduct::FLAG_SHOW;
        $normalProductDto->warrantyId = ProductWarranty::NONE;

        if ($dto->productVisible === true) {
            $normalProductDto->flags |= NormalProduct::FLAG_VISIBLE;
        } else {
            $normalProductDto->flags &= ~NormalProduct::FLAG_VISIBLE;
        }

        if ($dto->needsPayment !== true) {
            $normalProductDto->flags |= NormalProduct::FLAG_WITHOUT_PRICE_ONLINE;
        } else {
            $normalProductDto->flags &= ~NormalProduct::FLAG_WITHOUT_PRICE_ONLINE;
        }

        $normalProduct = $this->normalProductManager->save($normalProductDto, false);
        $product->setProduct($normalProduct);

        $event->setUpdated(new DateTimeImmutable());
        $event->setUpdater($user);

        $event->setVisible($dto->visible);
        $event->setCanGoOffline($dto->canGoOffline);
        $event->setTitle($dto->title);
        $event->setDescription($dto->description);
        $event->setMetaTitle($dto->metaTitle);
        $event->setMetaDescription($dto->metaDescription);
        $event->setProgramDescription($dto->programDescription);
        $event->setProgramList($dto->programList);
        $event->setPracticalInformation($dto->practicalInformation);
        $event->setIncludedList($dto->includedList);
        $event->setHeroImage($dto->heroImage);
        $event->setThumbnailImage($dto->thumbnailImage);
        $event->setImpressionImages($dto->impressionImages);
        $event->setLevel($dto->level);
        $event->setCategory($dto->category);
        $event->setEventInstructors($dto->eventInstructors);
        $event->setStockLocation($this->stockLocationRepository->find(StockLocation::WINKEL_URK));
        $event->setThemes($dto->themes);
        $event->setChecklistItems($dto->checklistItems);

        $product->setStartDateAndTime(DateTimeImmutable::createFromMutable($dto->startDateAndTime));
        $product->setEndDateAndTime(DateTimeImmutable::createFromMutable($dto->endDateAndTime));
        $product->setMaximumCapacity($dto->maximumCapacity);
        $product->setNeedsRegistration($dto->needsRegistration);
        $product->setNeedsPayment($dto->needsPayment);
        $product->setAdministrationCosts($dto->administrationCosts);
        $product->setLocation($dto->location);
        $product->setExternalLink($dto->externalLink);
        $product->setExternalName($dto->externalName);
        $product->setProgramListEvent($dto->programListEvent);
        $product->setSoftdelete(false);

        $productTags = $normalProduct->getProductTags();
        $existingTags = $productTags->map(fn($pt) => $pt->getTag())->toArray();

        foreach ($dto->themes as $theme) {
            $tag = $theme->getTag();

            if ($tag instanceof Tag && !in_array($tag, $existingTags, true)) {
                $productTag = new ProductTag();
                $productTag->setTag($tag);
                $productTag->setProduct($normalProduct);
                $this->entityManager->persist($productTag);

                $productTags->add($productTag);
                $existingTags[] = $tag;
            }
        }

        $this->entityManager->flush();

        return $product;
    }

    /**
     * @return Product[]
     */
    public function getAllEventProducts(int $eventId): array
    {
        return $this->productRepository->findBy([
            'event' => $eventId,
        ]);
    }

    public function findProduct(int $productId): ?Product
    {
        return $this->productRepository->find($productId);
    }

    public function findProductByAdminId(int $normalProductId): ?Product
    {
        return $this->productRepository->findOneBy([
            'product' => $normalProductId,
        ]);
    }

    public function copyProductPrices(Product $fromProduct, Product $toProduct): void
    {
        $productPrices = $fromProduct->getProduct()->getPrices();
        $newPrices = new ArrayCollection();

        foreach ($productPrices as $productPrice) {
            $newPrice = new ProductPrice();
            $newPrice
                ->setProduct($toProduct->getProduct())
                ->setCountry($productPrice->getCountry())
                ->setPrice($productPrice->getPrice())
                ->setPriceEx($productPrice->getPriceEx())
                ->setPriceEx($productPrice->getPriceEx())
                ->setPriceFromTo($productPrice->getPriceFromTo())
                ->setDiscountPrice($productPrice->getDiscountPrice())
            ;

            $this->entityManager->persist($newPrice);
            $newPrices->add($newPrice);
        }

        if (count($newPrices)) {
            $this->entityManager->flush();
        }
    }
}
