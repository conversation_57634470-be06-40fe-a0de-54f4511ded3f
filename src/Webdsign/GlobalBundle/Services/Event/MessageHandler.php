<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Services\Event;

use CatBundle\Service\ProductUrlService;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Throwable;
use Webdsign\GlobalBundle\DTO\Event\Queue\MessageDataObject;
use Webdsign\GlobalBundle\Entity\Event\Registration;
use Webdsign\GlobalBundle\Exception\Event\ClangException;
use Webdsign\GlobalBundle\Services\ClangWebHook;
use Symfony\Contracts\Translation\TranslatorInterface;

class MessageHandler
{
    public const CLANG_HOOK_ORDER = 'eventsOrder';
    public const CLANG_HOOK_REMINDER = 'eventsReminder';
    public const CLANG_HOOK_MESSAGE = 'eventsMessage';
    public const CLANG_HOOK_REVIEW = 'eventsReview';

    public function __construct(
        private readonly RegistrationManager $registrationManager,
        private readonly ClangWebHook $clangWebHook,
        private readonly ProductUrlService $productUrlService,
        private readonly TranslatorInterface $translator,
        #[Autowire('%env(WEBSITE_URL)%')]
        private readonly string $websiteUrl,
    ) {
    }

    /**
     * @throws ClangException
     */
    public function execute(MessageDataObject $dto): void
    {
        $registration = $this->registrationManager->getEntity($dto->registrationId);

        if ($registration === null) {
            throw new ClangException('Ongeldige registratie');
        }

        switch ($dto->type) {
            case MessageDataObject::TYPE_MESSAGE:
                $clangType = self::CLANG_HOOK_MESSAGE;
                $data = $this->message($registration);
                break;
            case MessageDataObject::TYPE_REGISTRATION:
                $clangType = self::CLANG_HOOK_ORDER;
                $data = $this->newRegistration($registration);
                break;
            case MessageDataObject::TYPE_CANCEL:
                $clangType = self::CLANG_HOOK_MESSAGE;
                $data = $this->cancel($registration, $dto->cancelReason ?? null);
                break;
            case MessageDataObject::TYPE_REFUND:
                $clangType = self::CLANG_HOOK_MESSAGE;
                $data = $this->refund($registration);
                break;
            case MessageDataObject::TYPE_REMINDER:
                $clangType = self::CLANG_HOOK_REMINDER;
                $data = $this->message($registration);
                break;
            case MessageDataObject::TYPE_REVIEW:
                $clangType = self::CLANG_HOOK_REVIEW;
                $data = $this->message($registration);
                break;
            default:
                $data = throw new ClangException('Ongeldig berichttype');
        }

        if ($dto->message !== null) {
            $data['message'] = $dto->message;
        }

        try {
            $this->clangWebHook->handleData($data, $clangType);
        } catch (Throwable) {
            throw new ClangException(
                sprintf('Fout bij versturen %s met registratie-id %d naar Clang', $clangType, $dto->registrationId)
            );
        }
    }

    private function message(Registration $registration): array
    {
        return $this->addBasicData($registration);
    }

    private function newRegistration(Registration $registration): array
    {
        $data = $this->addBasicData($registration);
        $data = $this->addEventData($registration, $data);
        return $this->addOrderData($registration, $data);
    }

    private function cancel(Registration $registration, ?string $cancelReason = null): array
    {
        return $this->addBasicData($registration, $cancelReason);
    }

    private function refund(Registration $registration): array
    {
        return $this->addBasicData($registration);
    }

    private function addBasicData(Registration $registration, ?string $cancelReason = null): array
    {
        $product = $registration->getProduct();
        $event = $product->getEvent();

        $data =  [
            'firstName' => $registration->getFirstName(),
            'lastName' => implode(
                ' ',
                array_filter([
                    $registration->getPrefix(),
                    $registration->getLastName(),
                ])
            ),
            'emailAddress' => $registration->getEmailAddress(),
            'cnuHash' => $registration->getAccessCode(),
            'event' => [
                'id' => $product->getId(),
                'name' => $event->getTitle(),
                'startDate' => $product->getStartDateAndTime()->format('d-m-Y'),
                'startTime' => $product->getStartDateAndTime()->format('H:i'),
                'endDate' => $product->getEndDateAndTime()->format('d-m-Y'),
                'endTime' => $product->getEndDateAndTime()->format('H:i'),
                'event_type' => $event->getCategory()->getName(),
            ],
        ];
        if ($cancelReason !== null) {
            $data['cancelReason'] = $this->translateCancelReason($cancelReason);
        }
        return $data;

    }

    private function addEventData(Registration $registration, array $data): array
    {
        $product = $registration->getProduct();
        $event = $product->getEvent();
        $location = $product->getLocation();

        $this->productUrlService->setLanguage(null)->setName($event->getTitle())->setId($event->getId());

        return array_merge_recursive([
            'event' => [
                'locationName' => $location->getName(),
                'locationAddress' => implode(
                    ' ',
                    array_filter([
                        $location->getStreet(),
                        $location->getHouseNumber(),
                        $location->getHouseNumberAddition(),
                    ])
                ),
                'locationPostalCode' => $location->getPostalCode(),
                'locationCity' => $location->getCity(),
                'instructor' => $event->getInstructor()->getFullName(),
                'practicalInformation' => $event->getPracticalInformation(),
                'checklistItems' => implode(
                    ', ',
                    $event->getChecklistItems()->toArray()
                ),
                'image' => $this->websiteUrl . $event->getHeroImage(),
                'url' => $this->productUrlService->generateEventsUrl(true),
            ]
        ], $data);
    }

    private function addOrderData(Registration $registration, array $data): array
    {
        $order = $registration->getOrder();

        if ($order === null) {
            return $data;
        }

        $cartItems = $order->getCartitems();
        $totalRegistrations = 0;

        foreach ($cartItems as $cartItem) {
            $totalRegistrations += $cartItem->getAmount();
        }

        return array_merge_recursive([
            'event' => [
                'price' => $registration->getCartItem() === null ? '' : $registration->getCartItem()->getPrice(),
                'amount' => $registration->getCartItem() === null ? '' : $registration->getCartItem()->getAmount(),
            ],
            'order' => [
                'id' => $order->getOrdernr(),
                'date' => $order->getOrderDate(),
                'paymentMethod' => $order->getPaymentMethod() === null ? '' : $order->getPaymentMethod()->getName(),
                'totalPrice' => $order->getTotalValue(),
                'totalAmount' => $totalRegistrations,
            ]
        ], $data);
    }

    private function translateCancelReason(string $cancelReason): string
    {
        return match ($cancelReason) {
            'cancelled_cameranu' => $this->translator->trans('event.cancel_reason.cancelled_cameranu'),
            'cancelled_customer' => $this->translator->trans('event.cancel_reason.cancelled_customer'),
            'cancelled_double_booking' => $this->translator->trans('event.cancel_reason.cancelled_double_booking'),
            default => $this->translator->trans('event.cancel_reason.unknown'),
        };
    }
}
