<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Services\Api;

use CatBundle\Constant\ShippingMethod;
use CatBundle\Service\CBS\DTO\Order;
use DateTime;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Throwable;
use Webdsign\GlobalBundle\DTO\Api\AddressDataObject;
use Webdsign\GlobalBundle\DTO\Api\ApiDataObjectInterface;
use Webdsign\GlobalBundle\DTO\Api\CustomerDataObject;
use Webdsign\GlobalBundle\DTO\Api\OrderDataObject;
use Webdsign\GlobalBundle\DTO\Api\OrderLineDataObject;
use Webdsign\GlobalBundle\DTO\Api\OrderOriginDataObject;
use Webdsign\GlobalBundle\DTO\Api\ParkingDataObject;
use Webdsign\GlobalBundle\DTO\Api\PaymentMethodDataObject;
use Webdsign\GlobalBundle\DTO\Api\ShippingMethodDataObject;
use Webdsign\GlobalBundle\DTO\Event\RegistrationDataObject;
use Webdsign\GlobalBundle\Entity\CartItem;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\CustomerRepository;
use Webdsign\GlobalBundle\Entity\OrderAnalyticsInfo;
use Webdsign\GlobalBundle\Entity\OrderHistory;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\OriginRepository;
use Webdsign\GlobalBundle\Entity\Parking;
use Webdsign\GlobalBundle\Entity\ParkingRepository;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\PaymentMethodRepository;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\Purchase\CalculatedPurchasePriceRepository;
use Webdsign\GlobalBundle\Entity\ShipmentMethod;
use Webdsign\GlobalBundle\Entity\ShipmentMethodRepository;
use Webdsign\GlobalBundle\Entity\StockInLocationRepository;
use Webdsign\GlobalBundle\Entity\Supplier;
use Webdsign\GlobalBundle\Entity\SupplierGroupRepository;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\UserRepository;
use Webdsign\GlobalBundle\Exception\Api\ApiException;
use Webdsign\GlobalBundle\Exception\Api\InvalidCustomerException;
use Webdsign\GlobalBundle\Exception\Api\InvalidOriginException;
use Webdsign\GlobalBundle\Exception\Api\InvalidParkingException;
use Webdsign\GlobalBundle\Exception\Api\InvalidPaymentMethodException;
use Webdsign\GlobalBundle\Exception\Api\InvalidShippingMethodException;
use Webdsign\GlobalBundle\Exception\Api\InvalidUserException;
use Webdsign\GlobalBundle\Form\DataTransformer\Api\OrderTransformer;
use Webdsign\GlobalBundle\Form\Filter\Api\ApiFilterInterface;

class OrderManager extends AbstractManager
{
    public function __construct(
        private readonly OrderInfoRepository $orderRepository,
        private readonly CustomerRepository $customerRepository,
        private readonly ShipmentMethodRepository $shipmentMethodRepository,
        private readonly PaymentMethodRepository $paymentMethodRepository,
        private readonly OriginRepository $originRepository,
        private readonly UserRepository $userRepository,
        private readonly CalculatedPurchasePriceRepository $calculatedPurchasePriceRepository,
        private readonly ParkingRepository $parkingRepository,
        private readonly OrderTransformer $orderTransformer,
        private readonly CustomerManager $customerManager,
        private readonly ProductManager $productManager,
        private readonly SupplierGroupRepository $supplierGroupRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly StockInLocationRepository $stockInLocationRepository,
        #[Autowire('%app.vat%')] private readonly array $vatPercentages,
    ) {
    }

    public function get(int $id): OrderDataObject
    {
        $order = $this->orderRepository->find($id);

        return $this->orderTransformer->transform($order);
    }

    public function find(ApiFilterInterface $filter): array
    {
        return array_map(fn($entity): OrderDataObject => $this->orderTransformer->transform($entity),
            $this->orderRepository->findByApiFilter($filter, self::DEFAULT_LIMIT));
    }

    /**
     * @param OrderDataObject $dto
     * @return OrderDataObject
     * @throws ApiException
     * @throws ORMException
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function save(ApiDataObjectInterface $dto): OrderDataObject
    {
        $customer = $this->customerRepository->find($dto->customerId);
        if (!$customer instanceof Customer) {
            throw new InvalidCustomerException();
        }

        $shippingMethod = $this->shipmentMethodRepository->find($dto->shippingMethod->id);
        if ($shippingMethod === null) {
            throw new InvalidShippingMethodException();
        }

        $paymentMethod = $this->paymentMethodRepository->find($dto->paymentMethod->id);
        if ($paymentMethod === null) {
            throw new InvalidPaymentMethodException();
        }

        $origin = $this->originRepository->find($dto->orderOrigin->id);
        if ($origin === null) {
            throw new InvalidOriginException();
        }

        $user = $this->userRepository->find($dto->handledBy);
        if ($user === null) {
            throw new InvalidUserException();
        }

        $invoiceAddressDto = $shippingAddressDto = new AddressDataObject();
        $orderDate = new DateTime();

        foreach ($dto->addresses as $address) {
            if ($address->type === AbstractManager::ADDRESS_TYPE_INVOICE || $address->isMainAddress === true) {
                $invoiceAddressDto = $address;
            }

            if ($address->type === AbstractManager::ADDRESS_TYPE_SHIPPING) {
                $shippingAddressDto = $address;
            }
        }

        $invoiceName = implode(' ', array_filter([
            $invoiceAddressDto->firstName,
            $invoiceAddressDto->prefix,
            $invoiceAddressDto->lastName,
        ]));

        $deliveryName = implode(' ', array_filter([
            $shippingAddressDto->firstName,
            $shippingAddressDto->prefix,
            $shippingAddressDto->lastName,
        ]));

        $flags = OrderInfo::FLAG_WAITING_FOR_PAYMENT;

        if ($dto->isZeroVat) {
            $flags += OrderInfo::FLAG_NO_VAT;
        }

        $orderEntity = (new OrderInfo())
            ->setCustomer($customer)
            ->setTaxNumber($invoiceAddressDto->vatNumber ?? '')
            ->setSex($customer->getSex())
            ->setFirstName($invoiceAddressDto->firstName ?? '')
            ->setLastName($invoiceAddressDto->lastName ?? '')
            ->setNameInsertion($invoiceAddressDto->prefix ?? '')
            ->setName($invoiceName)
            ->setCountry($invoiceAddressDto->country ?? '')
            ->setCity($invoiceAddressDto->city ?? '')
            ->setAddress($invoiceAddressDto->street ?? '')
            ->setAddress2('')
            ->setHousenr($invoiceAddressDto->houseNumber ?? '')
            ->setHousenrext('')
            ->setZipcode(
                $invoiceAddressDto->postcode !== null ? str_replace(' ', '', $invoiceAddressDto->postcode) :  ''
            )
            ->setZipcode2('')
            ->setCompany($invoiceAddressDto->company ?? '')
            ->setCompanyEmail($dto->companyEmail)
            ->setPhonenr($invoiceAddressDto->phoneNumber ?? '')
            ->setMobile('')
            ->setEmail($dto->email)
            ->setDeliveryFirstName($shippingAddressDto->firstName ?? '')
            ->setDeliveryLastName($shippingAddressDto->lastName ?? '')
            ->setDeliveryNameInsertion($shippingAddressDto->prefix ?? '')
            ->setDeliveryName($deliveryName)
            ->setDeliveryCountry($shippingAddressDto->country ?? '')
            ->setDeliveryCity($shippingAddressDto->city ?? '')
            ->setDeliveryAddress($shippingAddressDto->street ?? '')
            ->setDeliveryAddress2('')
            ->setDeliveryHousenr($shippingAddressDto->houseNumber ?? '')
            ->setDeliveryHousenrext('')
            ->setDeliveryZipcode($shippingAddressDto->postcode ?? '')
            ->setDeliveryZipcode2('')
            ->setDeliveryCompany('')
            ->setDeliveryPhonenr($shippingAddressDto->phoneNumber ?? '')
            ->setOurComment($dto->internalComment)
            ->setShippingMethod($shippingMethod)
            ->setPaymentMethod($paymentMethod)
            ->setOrderDate($orderDate->format('Y-m-d'))
            ->setOrderTime($orderDate->format('H:i:s'))
            ->setDateInvoice(new DateTime('0000-00-00'))
            ->setDateHandled(new DateTime('0000-00-00'))
            ->setLatlong('')
            ->setFlags($flags)
            ->setLanguage('nl')
            ->setHandledBy($user)
            ->setOrigin($origin)
            ->setCourierTime(new DateTime('00:00'))
            ->setPreventAutomaticCancel($dto->shippingMethod->id === ShippingMethod::CASH)
            ->setReference($dto->reference)
            ->setVatPercentage((string)$this->vatPercentages['nl']['high'])
            ->setIssuerId('')
            ->setIpNumber('0');

        $rfmSegment = $customer->getCustomerRFMScore()?->getSegment();
        $orderEntity->setRfmSegment($rfmSegment ?? 'Unknown');

        if ($dto->paymentCosts !== null) {
            $orderEntity->setPaymentCosts($dto->paymentCosts);
            $orderEntity->setPaymentCostsEx($dto->paymentCosts / (1 + ($this->vatPercentages['nl']['high'] / 100)));
        }

        // Only set the payment period if the payment method is bank transfer
        if ($paymentMethod->getId() === PaymentMethod::BANKTRANSFER) {
            $orderEntity->setPaymentPeriod($customer->getPaymentPeriod());
        }

        if ($dto->parking !== null) {
            $parking = $this->parkingRepository->find($dto->parking->id);

            if ($parking === null) {
                throw new InvalidParkingException();
            }

            $orderEntity->setParking($parking);
        }

        $this->entityManager->persist($orderEntity);

        /**
         * Create order history
         */
        $orderHistory = new OrderHistory();
        $orderHistory
            ->setOrder($orderEntity)
            ->setUser($user)
            ->setUserTeam($user->getUserTeam());
        $this->entityManager->persist($orderHistory);

        /**
         * Add order lines
         */
        foreach ($dto->orderLines as $orderLineDto) {
            if ($orderLineDto->parentOrderLine === null) {
                $parent = $this->entityManager->getReference(CartItem::class, 0);
            } else {
                $parent = $this->productManager->getEntity($orderLineDto->parentOrderLine->id);
            }

            $product = $this->productManager->getEntity($orderLineDto->product->id);

            if ($parent === null || $product === null) {
                continue;
            }

            $maxDeliveryTime = null;
            $stockStatusData = $this->productManager->getStockStatusData($orderLineDto->product->id, 'datetime');
            if (array_key_exists($orderLineDto->product->id, $stockStatusData)) {
                $stockStatusDateTime = $stockStatusData[$orderLineDto->product->id];
                $maxDeliveryTime = (int)(new DateTime('now'))->diff($stockStatusDateTime)->format('%a');
            }

            $orderLine = (new CartItem())
                ->setOrder($orderEntity)
                ->setParent($parent)
                ->setProduct($product)
                ->setAmount($orderLineDto->amount)
                ->setPrice($orderLineDto->price)
                ->setPriceEx($orderLineDto->priceEx)
                ->setPriceDiscount($orderLineDto->priceDiscount)
                ->setPriceRemovalCost($orderLineDto->priceRemovalCost)
                ->setTax($orderLineDto->taxRate)
                ->setStatus($orderLineDto->status)
                ->setFlags(0)
                ->setIpnr($orderLineDto->ipAddress)
                ->setSessionId((string) $orderEntity->getOrdernr())
                ->setMaxDeliveryTime($maxDeliveryTime)
                ->setUser($user);

            $this->entityManager->persist($orderLine);
        }

        $this->entityManager->flush();
        $this->entityManager->refresh($orderEntity);

        return $this->get($orderEntity->getId());
    }

    public function createFromCustomer(CustomerDataObject $customerDto): OrderDataObject {
        $orderDto = new OrderDataObject();

        $orderDto->email = $customerDto->email;
        $orderDto->customerId = $customerDto->id;

        return $orderDto;
    }

    public function createFromRegistration(RegistrationDataObject $registrationDataObject): OrderDataObject
    {
        $orderDto = new OrderDataObject();

        $orderDto->customerId = $registrationDataObject->customer->getId();
        $orderDto->email = $registrationDataObject->emailAddress;
        $orderDto->reference = '';
        $orderDto->internalComment = '';
        $orderDto->paymentMethod = new PaymentMethodDataObject();
        $orderDto->paymentMethod->id = PaymentMethod::ID_MOLLIE;
        $orderDto->shippingMethod = new ShippingMethodDataObject();
        $orderDto->shippingMethod->id = ShipmentMethod::ELECTRONIC;

        $origin = $registrationDataObject->product->getEvent()->getStockLocation()->getOrigin();

        if ($origin !== null) {
            $orderDto->orderOrigin = new OrderOriginDataObject();
            $orderDto->orderOrigin->id = $origin->getId();
        }

        if ($registrationDataObject->paymentMethodId !== null) {
            $orderDto->paymentMethod = new PaymentMethodDataObject();
            $orderDto->paymentMethod->id = $registrationDataObject->paymentMethodId;

            if ($registrationDataObject->paymentCosts !== null) {
                $orderDto->paymentCosts = $registrationDataObject->paymentCosts;
            }
        }

        $orderDto->handledBy = User::SYSTEM_USER_ADMIN;

        $address = new AddressDataObject();
        $address->type = AbstractManager::ADDRESS_TYPE_INVOICE;
        $address->firstName = $registrationDataObject->firstName;
        $address->lastName = $registrationDataObject->lastName;
        $address->prefix = $registrationDataObject->prefix;
        $address->phoneNumber = $registrationDataObject->phoneNumber;
        $address->country = $registrationDataObject->country;

        $orderDto->addresses[] = $address;

        return $orderDto;
    }

    /**
     * @throws ApiException
     * @throws ORMException
     *
     * @return OrderDataObject[]
     */
    public function createFromCbsOrder(Order $order, Customer $customer): array
    {
        $customerDto = $this->customerManager->get($customer->getId());
        $orderDto = new OrderDataObject();

        $orderDto->isZeroVat = true;
        $orderDto->handledBy = User::SYSTEM_USER_ADMIN;
        $orderDto->customerId = $customerDto->id;
        $orderDto->email = $customerDto->email;
        $orderDto->companyEmail = $customerDto->companyEmail;
        $orderDto->reference = $order->salesOrderId;
        $orderDto->internalComment = '';
        $orderDto->paymentMethod = new PaymentMethodDataObject();
        $orderDto->paymentMethod->id = PaymentMethod::BANKTRANSFER;
        $orderDto->shippingMethod = new ShippingMethodDataObject();
        $orderDto->shippingMethod->id = ShipmentMethod::UPS_STANDARD;
        $orderDto->orderOrigin = new OrderOriginDataObject();
        $orderDto->orderOrigin->id = Origin::CBS;
        $orderDto->parking = new ParkingDataObject();
        $orderDto->parking->id = Parking::CBS_TO_BE_JUDGED;

        foreach ($customerDto->addresses as $address) {
            if ($address->type === AbstractManager::ADDRESS_TYPE_INVOICE || $address->type === AbstractManager::ADDRESS_TYPE_SHIPPING || $address->isMainAddress === true) {
                $address->firstName = $customerDto->firstName;
                $address->lastName = $customerDto->lastName;
                $address->prefix = $customerDto->prefix;
                $address->phoneNumber = $customerDto->phoneNumber;

                $orderDto->addresses[] = $address;
            }
        }

        // In some cases NOTHING is in stock so we always create this
        $backorderDto = clone $orderDto;
        $backorderDto->parking = new ParkingDataObject();
        $backorderDto->parking->id = Parking::BACKORDER_CBS;

        // We need to look up the lowest price we can the product for, but have to exclude CBS suppliers
        $excludedSuppliers = [];
        foreach ($this->supplierGroupRepository->findBy(['usesCBS' => true]) as $cbsSupplierGroup) {
            $cbsSuppliers = $cbsSupplierGroup
                ->getSuppliers()
                ->map(static fn (Supplier $supplier) => $supplier->getId())
                ->toArray();
            /** @noinspection SlowArrayOperationsInLoopInspection */
            $excludedSuppliers = array_merge($excludedSuppliers, $cbsSuppliers);
        }
        $productIds = array_map(static function (Order\Line $line) {
            return $line->item->item->sellersIdentification->id;
        }, $order->orderLines);
        $lowestByProductId = $this
            ->calculatedPurchasePriceRepository
            ->lowestFl1ForProducts($productIds, $excludedSuppliers);

        $addLineToDTO = function (int $productId, OrderDataObject $orderDto, int $amount) use ($lowestByProductId) {
            try {
                $productDto = $this->productManager->get($productId);
                $calculatedPurchasePrice = (float)($lowestByProductId[$productId] ?? .0);

                $orderLineDto = new OrderLineDataObject();
                $orderLineDto->product = $productDto;
                $orderLineDto->amount = $amount;
                $orderLineDto->price = $calculatedPurchasePrice;
                $orderLineDto->priceEx = $calculatedPurchasePrice;
                $orderLineDto->priceDiscount = 0;
                $orderLineDto->priceRemovalCost = 0;
                $orderLineDto->taxRate = 0;
                $orderLineDto->status = 0;
                $orderLineDto->ipAddress = '0.0.0.0';

                $orderDto->orderLines[] = $orderLineDto;
            } catch (Throwable) {
            }
        };

        /** @var Order\Line $orderLine */
        foreach ($order->orderLines as $orderLine) {
            $productId = (int)$orderLine->item->item->sellersIdentification->id;
            $amount = $orderLine->item->quantity;
            $fakeProduct = $this->entityManager->getReference(Product::class, $productId);
            $available = $this->stockInLocationRepository->findAvailableForDispatchForProduct($fakeProduct);
            if ($available <= 0) {
                $nowAmount = 0;
                $backorderAmount = $amount;
            } else {
                // available = 10, amount = 15 --> 10 now, 5 backorder
                // available = 30, amount = 10, --> 10 now, 0 backorder
                $nowAmount = min($available, $amount);
                $backorderAmount = max($amount - $available, 0);
            }

            if ($nowAmount > 0) {
                $addLineToDTO($productId, $orderDto, $nowAmount);
            }

            if ($backorderAmount > 0) {
                $addLineToDTO($productId, $backorderDto, $backorderAmount);
            }
        }

        // twice same operation, but with foreach you can't make original ref null easily
        if (!empty($orderDto->orderLines)) {
            $orderDto = $this->save($orderDto);
        } else {
            $orderDto = null;
        }
        if (!empty($backorderDto->orderLines)) {
            $backorderDto = $this->save($backorderDto);
        } else {
            $backorderDto = null;
        }
        return [$orderDto, $backorderDto];
    }

    public function getRepository(): OrderInfoRepository
    {
        return $this->orderRepository;
    }
}
