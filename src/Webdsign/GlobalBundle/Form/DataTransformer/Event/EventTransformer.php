<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Form\DataTransformer\Event;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Form\DataTransformerInterface;
use Symfony\Component\Form\Exception\TransformationFailedException;
use Webdsign\GlobalBundle\DTO\Event\EventDataObject;
use Webdsign\GlobalBundle\Entity\Event\Event;
use Webdsign\GlobalBundle\Entity\Event\EventRepository;
use Webdsign\GlobalBundle\Entity\Event\Product;

class EventTransformer implements DataTransformerInterface
{
    private EventRepository $eventRepository;

    public function __construct(EventRepository $eventRepository)
    {
        $this->eventRepository = $eventRepository;
    }

    /**
     * @param Product $value
     */
    public function transform($value): EventDataObject
    {
        $dto = new EventDataObject();
        $event = $value->getEvent();

        // Event entity
        $dto->eventId = $event->getId();
        $dto->visible = $event->getVisible();
        $dto->canGoOffline = $event->getCanGoOffline();
        $dto->title = $event->getTitle();
        $dto->description = $event->getDescription();
        $dto->metaTitle = $event->getMetaTitle();
        $dto->metaDescription = $event->getMetaDescription();
        $dto->programDescription = $event->getProgramDescription();
        $dto->programList = $event->getProgramList();
        $dto->practicalInformation = $event->getPracticalInformation();
        $dto->includedList = $event->getIncludedList();
        $dto->heroImage = $event->getHeroImage();
        $dto->thumbnailImage = $event->getThumbnailImage();
        $dto->impressionImages = $event->getImpressionImages();
        $dto->category = $event->getCategory();
        $dto->eventInstructors = $event->getEventInstructors();
        $dto->instructors = new ArrayCollection($event->getInstructors());
        $dto->level = $event->getLevel();
        $dto->themes = $event->getThemes();
        $dto->checklistItems = $event->getChecklistItems();

        // Eventproduct entity
        $dto->productId = $value->getId();
        $dto->productVisible = $value->getProduct()->isVisible();
        $dto->startDateAndTime = DateTime::createFromImmutable($value->getStartDateAndTime());
        $dto->endDateAndTime = DateTime::createFromImmutable($value->getEndDateAndTime());
        $dto->maximumCapacity = $value->getMaximumCapacity();
        $dto->needsRegistration = $value->getNeedsRegistration();
        $dto->needsPayment = $value->getNeedsPayment();
        $dto->administrationCosts = $value->getAdministrationCosts();
        $dto->location = $value->getLocation();
        $dto->numberOfRegistrations = count($value->getRegistrations());
        $dto->externalLink = $value->getExternalLink();
        $dto->externalName = $value->getExternalName();
        $dto->programListEvent = $value->getProgramListEvent();
        $dto->adminProductId = $value->getProduct()->getId();

        return $dto;
    }

    /**
     * @param EventDataObject $value
     */
    public function reverseTransform($value): Event
    {
        $entity = $this->eventRepository->find($value->eventId);

        if ($entity === null) {
            throw new TransformationFailedException(sprintf(
                'Event with ID %s not found',
                $value->eventId
            ));
        }

        return $entity;
    }
}
