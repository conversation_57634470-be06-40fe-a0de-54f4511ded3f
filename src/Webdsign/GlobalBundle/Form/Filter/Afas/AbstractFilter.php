<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Form\Filter\Afas;

use Webdsign\GlobalBundle\Constant\Afas\OrderConstants;

class AbstractFilter implements AfasFilterInterface
{
    public const int FILTER_IS_EQUAL_TO = 1;
    public const int FILTER_IS_GREATER_OR_EQUAL_TO = 2;
    public const int FILTER_IS_LESS_OR_EQUAL_TO = 3;
    public const int FILTER_IS_GREATER_THAN = 4;
    public const int FILTER_IS_LESS_THAN = 5;
    public const int FILTER_IS_NOT_EQUAL_TO = 7;
    public const string SORT_ASC = '';
    public const string SORT_DESC = '-';
    public const string QUERY_AND_SEPARATOR = ',';
    public const string QUERY_OR_SEPARATOR = ';';

    public ?int $skip = null;
    public ?int $take = null;
    public array $filters = [];
    public array $sort = [];

    /**
     * <PERSON>aardes binnen de array $values zijn altijd OR. Voeg je een bepaald veld
     * meerdere keren toe wordt het tussen de verschillende values een AND:
     * Let op! je moet dan ook in alle andere filters een 2e filter value toevoegen.
     * Bijv.
     *
     * ->addFilter('UnitId', [
     *      [1, AbstractFilter::FILTER_IS_EQUAL_TO], <--filter1
     *      [3, AbstractFilter::FILTER_IS_EQUAL_TO], <--filter2
     * ])
     * ->addFilter('Year', [
     *      [2023, AbstractFilter::FILTER_IS_EQUAL_TO], <--filter1
     * ])
     *
     * Geeft dus: UnitId moet 1 OF 3 zijn OF Year 2023.
     * Dit geeft dus ook alles van het jaar 2023 terug
     *
     * Om bijv. dit te krijgen: UnitId moet 1 OF 3 zijn AND Year 2023
     * Moet je dus dit doen
     *  ->addFilter('UnitId', [
     *       [1, AbstractFilter::FILTER_IS_EQUAL_TO], <--filter1
     *       [3, AbstractFilter::FILTER_IS_EQUAL_TO], <--filter2
     *  ])
     *  ->addFilter('Year', [
     *       [2023, AbstractFilter::FILTER_IS_EQUAL_TO], <--filter1
     *       [2023, AbstractFilter::FILTER_IS_EQUAL_TO], <--filter2
     *  ])
     *
     * Gezien bovenstaande wat omslachtig is bij meerdere filters en velden kun je beter addGroupedFilter() gebruiken
     */
    public function addFilter(string $field, array $values): void
    {
        $numberOfValues = count($values);

        for ($i = 0; $i < $numberOfValues; $i++) {
            $currentValue = array_shift($values);

            $value = $currentValue[0] ?? '';
            $operator = $currentValue[1] ?? self::FILTER_IS_EQUAL_TO;

            $this->filters[$i][] = [
                'field' => $field,
                'value' => $value,
                'operator' => $operator,
            ];
        }
    }

    /**
     * Waardes binnen $sharedFilters worden toegepast op alle filters.
     * Waardes binnen $variants leveren elk een aparte OR filter op met de values van sharedFilters als AND
     *
     * Bijv.
     * $sharedFilters = [
     *     'Year' => [2023, AbstractFilter::FILTER_IS_EQUAL_TO],
     * ];
     *
     * $variants = [
     *      ['UnitId' => [1, AbstractFilter::FILTER_IS_EQUAL_TO],],
     *      ['UnitId' => [3, AbstractFilter::FILTER_IS_EQUAL_TO],],
     * ]
     *
     * ->addGroupedFilter($shared, $variants);
     *
     * Dit geeft: UnitId moet 1 OF 3 zijn AND Year 2023
     */
    public function addGroupedFilter(array $sharedFilters, array $variants): void
    {
        foreach ($variants as $variant) {
            $group = [];

            // Add shared filters first
            foreach ($sharedFilters as $field => [$value, $operator]) {
                $group[] = [
                    'field' => $field,
                    'value' => $value,
                    'operator' => $operator,
                ];
            }

            // Add variant filters for this group
            foreach ($variant as $field => [$value, $operator]) {
                $group[] = [
                    'field' => $field,
                    'value' => $value,
                    'operator' => $operator,
                ];
            }

            $this->filters[] = $group;
        }
    }

    public function addSort(string $field, string $direction = ''): void
    {
        $this->sort[] = $direction . $field;
    }
}
