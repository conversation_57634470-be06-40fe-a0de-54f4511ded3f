<?php

namespace Webdsign\GlobalBundle\Form\Type;

use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\CustomerAddress;
use Webdsign\GlobalBundle\Entity\Country;
use Webdsign\GlobalBundle\Entity\ExternalContact;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\Rma;
use Webdsign\GlobalBundle\Entity\RmaOption;
use Webdsign\GlobalBundle\Entity\RmaReason;
use Webdsign\GlobalBundle\Entity\RmaStatus;
use Webdsign\GlobalBundle\Entity\Ticket;
use Webdsign\GlobalBundle\Entity\ServiceTagRepository;

class RmaType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $newCustomer = array_key_exists('newCustomer', $options) && $options['newCustomer'] === true;
        $postalcodeUrl = array_key_exists('postalcodeUrl', $options) ? $options['postalcodeUrl'] : false;

        $builder
            ->add('type')
            ->add('depreciation', MoneyType::class, [
                'required' => false,
                'label' => 'service.rma.depreciation',
            ])
            ->add("status")
            ->add('rmaProducts')
            ->add('description', TextareaType::class, array(
                'required' => false,
                'label' => 'service.rma.repairDescription',
                'trim' => true,
                'attr' => array(
                    'rows' => 5
                ),
            ))
            ->add('accessories', EntityType::class, array(
                'class' => RmaOption::class,
                'required' => false,
                'multiple' => true,
                'label' => 'service.rma.accessories',
                'choice_label' => 'title',
                'attr' => array(
                    'class' => 'select2',
                ),
                'query_builder' => function(EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('o')
                        ->where("o.type = 'accessory'")
                        ->orderBy('o.sortOrder', 'ASC')
                        ;
                },
            ))
            ->add('solutions', EntityType::class, array(
                'class' => RmaOption::class,
                'required' => false,
                'multiple' => true,
                'label' => 'service.rma.solution',
                'choice_label' => 'title',
                'attr' => array(
                    'class' => 'select2',
                ),
                'query_builder' => function(EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('o')
                        ->where("o.type = 'solution'")
                        ->orderBy('o.sortOrder', 'ASC')
                        ;
                },
            ))
            ->add('otherOptions', EntityType::class, array(
                'class' => RmaOption::class,
                'required' => false,
                'multiple' => true,
                'label' => 'service.rma.otherOptions',
                'choice_label' => 'title',
                'attr' => array(
                    'class' => 'select2',
                ),
                'query_builder' => function(EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('o')
                        ->where("o.type = 'other'")
                        ->orderBy('o.sortOrder', 'ASC')
                        ;
                },
            ))
            ->add('reason', EntityType::class, [
                'class' => RmaReason::class,
                'required' => true,
                'multiple' => false,
                'label' => 'service.rma.returnDescription',
                'choice_label' => 'reason_nl',
                'attr' => [
                    'class' => 'select2',
                ],
            ])
            ->add('dropoffLocation', EntityType::class, [
                'class' => Origin::class,
                'required' => true,
                'placeholder' => '',
                'multiple' => false,
                'label' => 'service.rma.dropoffLocation',
                'choice_label' => 'stockLocationDescription',
                'attr' => [
                    'class' => 'select2',
                ],
                'query_builder' => function(EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('o')
                        ->where('o.physicalLocation = true')
                        ->andWhere('o.status = :active')
                        ->setParameter('active', Origin::STATUS_ACTIVE);
                },
                'data' => $options['defaultDropoffLocation'],
            ])
            ->add("customerName", TextType::class, array(
                'required' => $newCustomer,
                'label' => 'customer.address.label.name',
            ))
        ;

        if ($newCustomer) {
            $builder->add('customerEmail', TextType::class, [
                'required' => true,
                'label' => 'customer.form.email',
                'mapped' => false,
            ])
            ->add('customerPhonenr', TextType::class, [
                'required' => true,
                'label' => 'customer.form.phonenr',
                'mapped' => false,
            ]);
        }

        $builder
            ->add("customerStreet", TextType::class, array(
                'required' => true,
                'label' => 'customer.address.label.address',
            ))
            ->add("customerHouseNr", TextType::class, array(
                'required' => true,
                'label' => 'customer.address.label.housenr',
            ))
            ->add("customerHouseNrExt", TextType::class, array(
                'required' => false,
                'label' => 'customer.address.label.housenrext',
            ))
            ->add("customerPostalcode", TextType::class, array(
                'required' => true,
                'label' => 'customer.address.label.zipcode',
                'attr' => [
                    'data-url' => $postalcodeUrl
                ]
            ))
            ->add("customerCity", TextType::class, array(
                'required' => true,
                'label' => 'customer.address.label.city',
            ))
            ->add('customerCountry', EntityType::class, array(
                'class' => Country::class,
                'required' => false,
                'multiple' => false,
                'label' => 'customer.address.label.country',
                'choice_label' => 'title',
                'attr' => array(
                    'class' => 'select2',
                ),
                'query_builder' => function(EntityRepository $entityRepository) {
                    return $entityRepository->createQueryBuilder('c')
                        ->orderBy('c.title', 'ASC');
                },
            ))
            ->add('isVatFree', ChoiceType::class, [
                'label'  => 'service.rma.vat_free',
                'choices'  => [
                    'Ja' => true,
                    'Nee' => false,
                ],
                'empty_data' => false,
            ])
            ->add('mailLanguage', ChoiceType::class, [
                'required' => true,
                'label' => 'service.rma.form.mailLanguage',
                'empty_data' => 'NL',
                'attr' => [
                    'class' => 'select2',
                ],
                'placeholder' => '',
                // 'choices_as_values' => true,
                'choices' => [
                    'Nederlands' => 'NL',
                    'Engels' => 'EN',
                    'Duits' => 'DE',
                    'Frans' => 'FR',
                    'Portugees' => 'PT',
                    'Italiaans' => 'IT',
                ],
            ])
            ->add('save', SubmitType::class, array(
                'label' => 'core.save',
                'attr' => array(
                    'class' => 'btn-primary waves-effect',
                ),
            ))
            ->add('print', SubmitType::class, array(
                'label' => 'service.rma.letter.saveAndPrint',
                'attr' => array(
                    'class' => 'waves-effect',
                ),
            ));

        $builder->addEventListener(FormEvents::PRE_SET_DATA, function(FormEvent $event)
        {
            $form = $event->getForm();
            $data = $event->getData();
            $customer = $form->getConfig()->getOption("customer");

            if ($data instanceof Rma) {

                if (!$data->getTicket() instanceof Ticket) {
                    $tagIds = $form->getConfig()->getOption('tagIds');
                    $defaultTag = $form->getConfig()->getOption('defaultTag');
                    $form->add('tags', TagsType::class, array(
                        'mapped' => false,
                        'multiple' => true,
                        'required' => true,
                        'attr' => array(
                            'class' => 'select2-tags',
                        ),
                        'query_builder' => function (ServiceTagRepository $repository) use ($tagIds) {
                            $queryBuilder = $repository->createQueryBuilder('tags');

                            if (is_array($tagIds) && count($tagIds) > 0) {
                                $queryBuilder
                                    ->where('tags.id IN (:ids)')
                                    ->setParameter('ids', $tagIds)
                                ;
                            } else {
                                $queryBuilder
                                    ->where('tags.category = :category')
                                    ->setParameter('category', 'rma')
                                ;
                            }

                            $queryBuilder->addOrderBy('tags.name');

                            return $queryBuilder;
                        },
                        'data' => $defaultTag,
                        'label' => 'service.ticket.label.tags',
                    ));
                    $form->add('servicePartner', EntityType::class, array(
                        'mapped' => false,
                        'class' => ExternalContact::class,
                        'choice_label' => 'name',
                        'required' => false,
                        'attr' => array(
                            'class' => 'select2-ajax',
                            'data-ajax--type' => 'externalContact',
                            'data-ajax--filters' => '{"isServiceContact":true}',
                        ),
                        'label' => 'service.ticket.label.servicePartner',
                    ));
                }

                $form->add('type', EntityType::class, [
                    'class' => \Webdsign\GlobalBundle\Entity\RmaType::class,
                    'required' => true,
                    'multiple' => false,
                    'label' => 'service.rma.type',
                    'choice_label' => 'title',
                    'attr' => [
                        'class' => 'select2',
                    ],
                    'query_builder' => function (EntityRepository $entityRepository) use ($data) {
                        return $entityRepository->createQueryBuilder('t')
                            ->orderBy('t.id', 'ASC');
                    },
                ]);

                $form->add('status', EntityType::class, array(
                    'class' => RmaStatus::class,
                    'required' => false,
                    'multiple' => false,
                    'label' => 'service.rma.status',
                    'choice_label' => 'title',
                    'attr' => array(
                        'class' => 'select2',
                    ),
                    'query_builder' => function(EntityRepository $entityRepository) use ($data) {
                        $queryBuilder = $entityRepository->createQueryBuilder('s')
                            ->orderBy('s.sortOrder', 'ASC');
                        if ($data->getTicket() instanceof Ticket) {
                            $queryBuilder->join('s.tags', 't')
                                ->where('t IN (:tags)')
                                ->setParameters(
                                    array(
                                        'tags' => $data->getTicket()->getTags()
                                    )
                                );
                        }
                        return $queryBuilder;
                    },
                ));

                $ticket = $data->getTicket();
                if ($ticket instanceof Ticket) {
                    $customer = $ticket->getCustomer();
                }
            }

            if ($customer instanceof Customer) {
                if (empty($data->getCustomerName())) {
                    $data->setCustomerName($customer->getName());
                }
                $customerAddress = null;
                foreach ($customer->getAddresses() as $address) {
                    if ($address->getFlags() & 1) {
                        $customerAddress = $address;
                        break;
                    }
                }
                if ($customerAddress instanceof CustomerAddress)
                {
                    // alleen invullen vanuit klant-adres indien straatnaam niet ingevuld is!
                    if (empty($data->getCustomerStreet())) {
                        $data->setCustomerStreet($customerAddress->getAddress());
                        $data->setCustomerHouseNr($customerAddress->getHousenr());
                        $data->setCustomerHouseNrExt($customerAddress->getHousenrext());
                        $data->setCustomerPostalcode($customerAddress->getZipcode());
                        $data->setCustomerCity($customerAddress->getCity());

                        /** @var \Doctrine\Common\Persistence\ObjectManager $objectManager */
                        $objectManager = $form->getConfig()->getOption("objectManager");
                        $countryRepository = $objectManager->getRepository(Country::class);
                        $country = $countryRepository->findOneBy(array('code' => $customerAddress->getCountry()));
                        if ($country instanceof Country) {
                            $data->setCustomerCountry($country);
                        }
                    }
                }
            }

            $form->add('rmaProducts', CollectionType::class, array(
                    'entry_type' => RmaProductType::class,
                    'label' => false,
                    'allow_add' => true,
                    'allow_delete' => true,
                    'by_reference' => false,
                    'required' => true,
                    'entry_options' => array(
                        'orderIds' => $form->getConfig()->getOption("orderIds")
                    ),
                ));
        });


        $builder->addEventListener(FormEvents::PRE_SUBMIT, function(FormEvent $event)  {
            $data = $event->getData();
            $form = $event->getForm();


            if (isset($data['servicePartner'])) {
                $servicepartnerOptions = array();
                $servicepartnerOptions['query_builder'] = function(EntityRepository $repository) use ($data) {
                    $queryBuilder = $repository->createQueryBuilder('ec');
                    $queryBuilder->where('ec.id = :id')
                        ->setParameter('id', $data['servicePartner']);

                    return $queryBuilder;
                };
                $form->add('servicePartner', EntityType::class, array_merge(array(
                    'mapped' => false,
                    'class' => ExternalContact::class,
                    'choice_label' => 'name',
                    'required' => false,
                    'attr' => array(
                        'class' => 'select2-ajax',
                        'data-ajax--type' => 'externalContact',
                        'data-ajax--filters' => '{"isServiceContact":true}',
                    ),
                    'label' => 'service.ticket.label.servicePartner',
                ), $servicepartnerOptions));
            }
        });
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(array(
            'data_class' => Rma::class,
            'csrf_protection'  => false,
            'translate' => null,
            'orderIds' => array(),
            'objectManager' => null,
            'customer' => null,
            'defaultTagId' => null,
            'tagIds' => [],
            'defaultDropoffLocation' => null,
            'defaultTag' => null,
            'newCustomer' => null,
            'postalcodeUrl' => null,
        ));
    }

    /**
     * @return string
     */
    public function getBlockPrefix()
    {
        return 'rma';
    }
}
