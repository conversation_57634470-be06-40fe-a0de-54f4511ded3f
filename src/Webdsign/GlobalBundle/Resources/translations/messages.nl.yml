core.pagenotfound: Deze pagina bestaat niet
core.error.somethingwrong: Er is iets mis gegaan
core.error.notifydevelopers: Als deze fout zich blijft voordoen neem dan contact op met de programmeurs.
core.error.readonly: Er is iets mis. Hierd<PERSON> is de tool alleen-lezen. Neem contact op met de programmeurs.
core.error.forbidden: Je hebt geen toegang tot deze pagina
core.error.trylogin: Als je denkt dat dit niet klopt kun je proberen <a href="/logout">opnieuw in te loggen</a>.
core.save.success: De aanpassingen zijn opgeslagen
core.save.failure: Er is iets misgegaan bij het opslaan
core.actionbar.overview: Overzicht
core.actionbar.add: Toevoegen
core.actionbar.back: Terug
core.actionbar.count: item(s) geselecteerd
core.actionbar.selectall: Selecteer alles
core.actionbar.selectnone: Deselecteer alles

core.all: ''
core.menu.mytickets: Al mijn tickets
core.menu.following: Volgend
core.menu.admin: Administratie
core.menu.pending: In behandeling
core.menu.alltickets: Alle tickets
core.menu.otheropentickets: Overige open tickets
core.menu.allpendingtickets: Wachtende tickets
core.menu.customers: Klantenbeheer
core.menu.mailboxes: Mailboxbeheer
core.menu.tags: Tagbeheer
core.menu.tasks: Taakbeheer
core.menu.logs: Log
core.menu.blacklist: Blacklist
core.menu.mail: E-mail inbox
core.menu.mail.hold: Wachtende e-mail
core.menu.and: en
core.menu.assignedtickets: Toegewezen tickets
core.menu.assignedtodepartment: Toegewezen aan afdeling
core.menu.ticketstatistics: Ticket statistiek
core.menu.rma: RMA
core.menu.rma.open: Alle open RMA tickets
core.menu.rma.scan: Scan
core.menu.rma.open.tag: Open %title% tickets
core.menu.rma.open.user: Mijn RMA tickets
core.menu.rma.pending: Alle wachtende RMA tickets
core.menu.rma.withCourier: Met koerier mee
core.menu.rma.inHopper: Verzamelbak

core.list.options: Opties
core.list.delete: Verwijderen
core.list.archive: Archiveren
core.list.hold: Wachten
core.list.edit: Aanpassen
core.list.reply: Beantwoorden
core.list.filter: Filter
core.list.from: van
core.list.sort.default: Standaard
core.list.sort.name.asc: Op naam oplopend
core.list.sort.name.desc: Op naam aflopend
core.list.sort.subject.asc: Op onderwerp oplopend
core.list.sort.subject.desc: Op onderwerp aflopend
core.list.sort.date.asc: Op datum oplopend
core.list.sort.date.desc: Op datum aflopend
core.list.sort.lastContact.asc: Op laatste klantcontact oplopend
core.list.sort.lastContact.desc: Op laatste klantcontact aflopend
core.list.sort.lastCustomerReply.asc: Op laatste klantcontact oplopend
core.list.sort.lastCustomerReply.desc: Op laatste klantcontact aflopend
core.list.date: Datum
core.list.info: Informatie
core.list.makeactive: Maak actief
core.list.makeinactive: Maak inactief
core.logout: Uitloggen
core.save: Opslaan
core.close: Sluiten

core.selectable.addas: Toevoegen als
core.selectable.addto: Toewijzen aan
core.selectable.complete: Afronden
core.selectable.ticket: Ticket
core.selectable.user: Geen gebruiker

core.future: Toekomst
core.todayat: Vandaag om
core.yesterdayat: Gisteren om
core.today: Vandaag
core.yesterday: Gisteren
core.daybeforeyesterday: Eergisteren
core.thisweek: Deze week
core.lastweek: Vorige week
core.morethentwoweeksago: Meer dan twee weken geleden
core.evenolder: Eerder
core.days: dag(en)
core.businessdays: werkdag(en)

core.actionmenu.ticket: Voeg een ticket toe
core.actionmenu.ticketNote: Voeg een notitie toe bij dit ticket
core.actionmenu.customerNote: Voeg een notitie toe bij deze klant
core.actionmenu.contact: Voeg een contact mogelijkheid toe bij deze klant
core.actionmenu.customer: Voeg een nieuwe klant toe
core.actionmenu.order: Voeg een nieuwe contantbon toe
core.actionmenu.leaseorder: Lease contantbon aanmaken
core.actionmenu.newrma: Voeg nieuwe RMA toe voor deze klant
core.actionmenu.newuserrma: Maak nieuwe klant en voeg RMA toe
core.actionmenu.customerWarningAddress: Voeg een e-mailadres toe aan de "niet gebruiken"-lijst

core.actionmenu.address: Voeg een adres toe bij deze klant
core.actionmenu.mail: Stel een nieuwe mail op

core.pager.first: Eerste
core.pager.previous: Vorige
core.pager.next: Volgende
core.pager.last: Laatste

core.open: open
core.pending: wachtende
core.closed: gesloten
core.deleted: verwijderde

core.tooltip.extra.options: 'Extra opties'



log.change.values: '%name% is veranderd van %from% naar %to%'
log.change.values.small: '%name% veranderd naar %to%'
log.change: '%name% is veranderd van <span data-toggle="tooltip" data-container="body" title="Id: %from%"><b>%fromName%</b></span> naar <span data-toggle="tooltip" data-container="body" title="Id: %to%"><b>%toName%</b></span>'
log.change.small: 'heeft %name% veranderd in <span data-toggle="tooltip" data-container="body" title="Id: %to%"><b>%toName%</b></span>'
log.change.merged.ticket: 'Ticket %ticketOld% samengevoegd in %ticketNew%'
log.change.merged.ticket.small: 'Ticket %ticketOld% samengevoegd in %ticketNew%'
log.change.unmerged.ticket: 'Ticket %ticketOld% losgekoppeld van %ticketNew%'
log.change.unmerged.ticket.small: 'Ticket %ticketOld% losgekoppeld van %ticketNew%'
log.entity.add: '<span data-toggle="tooltip" data-container="body" title="Id: %id%">%name%</span> is aangemaakt'
log.entity.add.small: '%name% is aangemaakt'
log.entity.remove: '<span data-toggle="tooltip" data-container="body" title="Id: %id%">%name%</span> is verwijderd'
log.entity.remove.small: '%name% is verwijderd'

log.mail.failure: 'Fout bij het versturen van een mail voor <span data-toggle="tooltip" data-container="body" title="ID: %replyId%"><strong>antwoord</strong></span>.<br/>%failure%'

log.message.mergeid: MergeId
log.message.customer: Klantnaam
log.message.subject: Onderwerp
log.message.name: Naam
log.message.language: Taal
log.message.flags: Flags
log.message.birthday: Verjaardag

log.message.noName: Niemand
log.message.noId: Geen ID

log.message.state: Status
log.message.title: Titel
log.message.assignee: Toegewezen aan
log.message.lastReplyDate: Datum laatste reactie
log.message.mailSentDate: Mail verzonden op

log.message.servicePartner: Servicepartner
log.message.waitUntil: In behandeling tot
log.message.note: Notitie
log.message.description: Omschrijving
log.message.options: Opties
log.message.status: Status
log.message.RmaOption: RMA Optie
log.message.product: Product
log.message.barcode: Serienummer
log.message.customerName: naam
log.message.customerStreet: straat
log.message.customerHouseNr: huisnummer
log.message.customerHouseNrExt: huisnummer extentie
log.message.customerPostalcode: postcode
log.message.customerCity: stad
log.message.customerCountry: land
log.message.lastCustomerContact: Datum laatste klantcontact

log.message.collection.added: 'Toegevoegd aan collectie <span data-toggle="tooltip" data-container="body" title="IDs: %id%">%type%: %value%</span>'
log.message.collection.added.small: '%type%: %value% toegevoegd aan collectie'
log.message.collection.removed: 'Verwijderd uit collectie <span data-toggle="tooltip" data-container="body" title="IDs: %id%">%type%: %value%</span>'
log.message.collection.removed.small: '%type%: %value% verwijderd uit collectie'
log.message.collection.emptied: 'Collectie %name% is leeg gemaakt.'
log.message.collection.emptied.small: 'Collectie %name% is leeg gemaakt'
log.message.collection.call: 'Heeft telefonisch contact opgenomen met klant'
log.cron.name: "Cron"

core.message.assignnew: 'Automatisch toegewezen aan mij: %amount% tickets'
logger.message.assignnew: 'Automatisch tickets toegewezen: %values%'

mail-error.quota-issues: 'Mailbox zit vol, probeer het later nog eens.'
mail-error.policy-related: 'Policy-related, schakel een programmeur in.'
mail-error.bad-domain : 'Probleem met het domein, het e-mail adres klopt niet.'
mail-error.bad-mailbox: 'Probleem met de mailbox, het e-mail adres klopt niet.'
mail-error.inactive-mailbox: 'Dit email adres is niet meer in gebruik.'
mail-error.message-expired: 'Het bericht is verlopen. Klopt het e-mail adres?'
mail-error.no-answer-from-host: 'Het bericht is niet aangekomen, de ontvangende server reageert niet. Klopt het e-mail adres?'
mail-error.other messages: 'Het bericht is niet aangekomen, klopt het e-mail adres?'
mail-error.routing-errors: 'Er is een probleem met de ontvangende server. Klopt het e-mail adres?'
mail-error.spam-related: 'Onze mail werd gezien als spam, even contact opnemen met de programmeurs.'
mail-error.title: "E-mail niet aangekomen"

core.mail.no-order-mail.title: 'Ordermail niet verstuurd'
core.mail.no-order-mail.content: 'De gebruiker heeft geen e-mail gehad omdat het e-mail adres onjuist is.'

core.mail.mega.title: 'Mega-mail: %subject%'
core.mail.mega.content: 'Email van: %from% <br/>Onderwerp: %subject%'

core.expected-delivery-date.on: 'op'
core.expected-delivery-date.start-off: 'begin'
core.expected-delivery-date.end-off: 'eind'
core.expected-delivery-date.mid: 'medio'

core.day.monday: 'maandag'
core.day.tuesday: 'dinsdag'
core.day.wednesday: 'woensdag'
core.day.thursday: 'donderdag'
core.day.friday: 'vrijdag'
core.day.saturday: 'zaterdag'
core.day.sunday: 'zondag'

core.month.january: 'januari'
core.month.february: 'februari'
core.month.march: 'maart'
core.month.april: 'april'
core.month.may: 'mei'
core.month.june: 'juni'
core.month.july: 'juli'
core.month.august: 'augustus'
core.month.september: 'september'
core.month.october: 'oktober'
core.month.november: 'november'
core.month.december: 'december'

core.time.start: 'Start tijd'
core.time.end: 'Eind tijd'


service.dashboard.header: 'Dashboard'
service.task.header: 'Taakoverzicht'
service.task.add.header: 'Taak toevoegen'
service.task.edit.header: 'Taak aanpassen'
service.task.show.header: 'Alle tickets'
service.task.list.name: 'Naam'
service.ticket.sidebar.other: 'Overige gegevens'
service.ticket.header.all: 'Alle tickets'
service.ticket.header.following: 'Alle gevolgde tickets'
service.ticket.header.open: 'Alle open tickets'
service.ticket.header.closed: 'Alle gesloten tickets'
service.ticket.header.deleted: 'Alle verwijderde tickets'
service.ticket.header.pending: 'Alle wachtende tickets'
service.ticket.header: 'Alle %status% tickets'
service.ticket.add.header: 'Ticket toevoegen'
service.ticket.edit.header: 'Ticket aanpassen'
service.ticket.list.name: 'Naam'
service.ticket.list.tags: 'Tags'
service.ticket.list.created: 'Aangemaakt op'
service.ticket.list.assignee: 'Toegewezen aan'
service.ticket.list.customer: 'Klant'
service.ticket.assignee: 'Toegewezen aan'
service.ticket.created: 'Aangemaakt op'
service.ticket.createdby: 'Aangemaakt door'
service.ticket.updated: 'Aangepast op'
service.ticket.content: 'Beschrijving'
service.ticket.assign.tag: 'Toewijzen aan afdeling'
service.ticket.assign.to.me.tag: 'Toewijzen aan mij'
service.ticket.reply.type: 'Soort reactie'
service.ticket.reply.from: 'Van'
service.ticket.reply.to: 'Naar'
service.ticket.reply.saved: 'De reactie is opgeslagen'
service.ticket.reply.error: 'Het formulier bevat een fout'
service.ticket.reply.internal: 'Interne notitie door: '
service.ticket.reply.internal.dropdown: 'Interne notitie'
service.ticket.reply.customerContact: 'Deze reactie is gemarkeerd als klantcontact'
service.ticket.reply.log: 'Wijziging door: '
service.ticket.reply.tocustomer: 'Stuur reactie naar klant'
service.ticket.reply.toservicepartner: 'Stuur reactie naar servicepartner'
service.ticket.reply.email.notify: 'Reactie op'
service.ticket.reply.email.originalticket: 'Uw conversatie historie'
service.ticket.reply.email.invalidAddress: 'Ongeldig e-mailadres opgegeven, e-mail is niet verstuurd'
service.ticket.reply.time: 'op %date% om %time%'
service.ticket.reply.history: 'Stuur conversatie historie mee'
service.ticket.addfrommail: 'Ticket toegevoegd vanuit een e-mail'
service.ticket.completed: 'De ticket is afgerond'
service.ticket.pending: 'De ticket is in behandeling genomen'
service.ticket.deleted: 'De ticket is verwijderd'
service.ticket.merged: 'De ticket is samengevoegd'
service.ticket.customer: 'Klant'
service.ticket.servicePartner: 'Servicepartner'
service.ticket.follow: 'Meekijken'
service.ticket.unfollow: 'Niet meer volgen'
service.ticket.unfollowed: 'Je volgt dit ticket niet langer'
service.ticket.followed: 'Je volgt vanaf nu dit ticket'
service.ticket.removed.tag: 'Je hebt een tag verwijderd'
service.ticket.split: 'Afgesplitst van'
service.ticket.notitle: 'Geen onderwerp'
service.attachment.modal.header: 'Bijlage'

service.ticket.assign.tickets: 'Tickets toewijzen'
service.ticket.assign.to: 'Toewijzen aan'
service.ticket.assign: 'Toewijzen'
service.ticket.assignnew: 'Geef mij nieuwe tickets'
service.ticket.withtag: 'Eventueel met tag'
service.ticket.assigned: 'Toegewezen'
service.ticket.notAssigned: 'Niet toegewezen'
service.ticket.assign.type: 'Algemeen ticket'
service.ticket.assign.finduser: 'Zoek een gebruiker'
service.ticket.assign.create: 'Aanmaken'

service.ticket.order.add: 'Koppel order'
service.ticket.order.not.exist: 'Deze order bestaat niet.'
service.ticket.order.already.exist: 'Deze order is al gekoppeld.'
service.ticket.orderid: 'Ordernummer'
service.ticket.order.info.placeholder: 'Bestelling ID / Ordernummer / Factuurnummer'
service.ticket.order.added: 'Order is aan de ticket toegevoegd'
service.ticket.order.added.log: 'Order <b>%orderId%</b> gekoppeld'
service.ticket.order.added.log.small: 'Order %orderId% gekoppeld'
service.ticket.order.removed.log: 'Order <b>%orderId%</b> verwijderd'
service.ticket.order.removed.log.small: 'Order %orderId% verwijderd'
service.ticket.order.added.admin.log: 'Er is een ticket gemaakt voor deze order. <a href="%ticketLink%" target="_blank">Klik hier</a'
service.ticket.order.deleted: 'Order is verwijderd bij deze ticket'
service.ticket.order.added.newtitle: 'Uw bestelling met ordernummer %orderId%'
service.ticket.order.added.noCustomer: 'Let op! Order heeft geen klant, dus er is ook geen klant gekoppeld aan dit ticket!'

service.ticket.filter.all: 'Alle tickets'
service.ticket.filter.open: 'Open tickets'
service.ticket.filter.closed: 'Gesloten tickets'
service.ticket.filter.deleted: 'Verwijderde tickets'
service.ticket.filter.pending: 'Wachtende tickets'

service.ticket.filter.excludecategories: 'Categorie uitsluiten'
service.ticket.filter.tags: 'Tags selecteren'
service.ticket.filter.tasks: 'Taken selecteren'
service.ticket.filter.assignees: 'Toegewezen aan'
service.ticket.filter.state: 'Ticket status'
service.ticket.filter.rmaStatus: 'RMA status selecteren'
service.ticket.filter.servicePartner: 'Servicepartner'
service.ticket.filter.from: 'Van'
service.ticket.filter.to: 'Tot'

service.ticket.status.open: 'Open'
service.ticket.status.closed: 'Gesloten'
service.ticket.status.deleted: 'Verwijderd'
service.ticket.status.pending: 'In behandeling'

service.ticket.pending.nodate: 'In behandeling nemen zonder datum'
service.ticket.log.waituntil: 'Dit ticket is in behandeling en gaat automatisch terug naar open'

service.ticket.label.state: 'Status'
service.ticket.label.assignee: 'Toewijzen aan'
service.ticket.label.assign.tag: 'Toewijzen'
service.ticket.label.assign.button: 'Direct toewijzen aan afdeling'
service.ticket.label.title: 'Ticket Titel'
service.ticket.label.title.pattern: 'Vul een titel in'
service.ticket.label.subject: 'Ticket onderwerp'
service.ticket.label.tags: 'Gekoppelde tags'
service.ticket.label.followers: 'Volgers'
service.ticket.label.customer: 'Klant'
service.ticket.label.servicePartner: 'Servicepartner'
service.ticket.label.attachments: 'Bijlage(n) uploaden'
service.ticket.label.otherattachments: 'Eerdere bijlage(n) meesturen'
service.ticket.label.mergedIntoTicket: 'Samenvoegen NAAR ander ticket'
service.ticket.label.unmergeTicketFailure: 'Ticket was niet samengevoegd, loskoppelen dus niet mogelijk!'
service.ticket.label.unmerged: 'Ticket is losgekoppeld'
service.ticket.label.invoice: 'Stuur factuur mee'
service.ticket.label.offer: 'Stuur offerte mee'
service.ticket.label.proforma: 'Stuur proforma mee'
service.ticket.label.return: 'Stuur retour-label mee'
service.ticket.label.return.free: 'Stuur gratis retour-label mee'
service.ticket.label.customerContact: 'Markeer als klantcontact'
service.ticket.label.sendAsCNU: 'Verstuur met CameraNU Handtekening'

service.ticket.call.title: 'Telefoonnotitie'
service.ticket.call.subject: 'Ticket aangemaakt vanuit klantkaart.'

service.ticket.warning.emailaddress: 'Dit e-mailadres NIET gebruiken: <strong>[EMAIL]</strong>'
service.ticket.warning.emailaddress.description: 'Er is geen reden opgegeven waarom dit adres niet gebruikt mag worden'
service.customer.warning.emailaddress.heading: 'Niet gebruiken'
service.customer.warning.emailaddress.added: 'E-mailadres toegevoegd aan de "niet gebruiken"-lijst'
service.customer.warning.emailaddress.changed: 'E-mailadres opgeslagen'
service.customer.warning.emailaddress.replaced: 'E-mailadres stond al op de lijst, die is nu vervangen'
service.customer.warning.emailaddress.deleted: 'E-mailadres verwijderd van de "niet gebruiken"-lijst'
service.customer.warning.emailaddress.add: 'E-mailadres toevoege aan "niet gebruiken"-lijst'
service.customer.warning.emailaddress.address: 'E-mailadres'
service.customer.warning.emailaddress.description: 'Reden'
service.customer.warning.emailaddress.addedBy: 'Toegevoegd door'
service.customer.warning.emailaddress.datetime: 'Datum'

ticket.tooltip.show.full: 'Bekijk ticket'
ticket.tooltip.complete: 'Ticket afronden'
ticket.tooltip.pending: 'Ticket in behandeling'
ticket.tooltip.following: 'Volg of ontvolg dit ticket'
ticket.tooltip.options: 'Extra ticket opties'
ticket.tooltip.show.filters: 'Selecteer filters'
ticket.tooltip.show.assignnew: 'Mijzelf nieuwe tickets toewijzen'
ticket.tooltip.reply.convert: 'Zet antwoord om in een ticket'
ticket.tooltip.reply.view_mail: 'Bekijk volledige e-mail'
ticket.tooltip.merge: 'Huidig ticket samenvoegen NAAR deze'
ticket.tooltip.print: 'Printen'

ticket.multi.customer.other: "Overig"
ticket.multi.customer.addresses: "Addressen"
ticket.multi.customer.origin: "Herkomst"
ticket.multi.customer.orders: "Orders"
ticket.multi.customer.orders.last: "Laatste order"
ticket.multi.customer.reviews: "Reviews"
ticket.customer.add.checkbox: "Samenvoegen"
ticket.customer.add.radio: "Hoofd account"
ticket.customer.add: "Voeg aan deze klant toe"
ticket.customer.merge: "Samenvoegen"
ticket.customers.found.heading: "Er is een klant gevonden voor dit ticket|Meerdere klanten gevonden voor dit ticket"

ticket.reply.from.ticket: 'Deze ticket is gecreëerd op basis van een antwoord op deze <a href="%link%" target="_blank">ticket</a>'
ticket.reply.made.ticket: 'Voor dit antwoord is een <a href="%link%" target="_blank">nieuw ticket</a> aangemaakt.'
ticket.reply.limit.warning: 'Let op! Het bericht wordt erg groot, probeer styling en inline-plaatjes te beperken.'
ticket.reply.limit.max: 'Het bericht is te lang en kan niet verzonden worden. Verwijder styling en/of inline-plaatjes.'

ticket.added.tag: 'U heeft een tag toegevoegd bij een ticket <a href="%link%">klik hier de ticket te bekijken.</a>.'

ticket.merged.ticket: "Samengevoegde ticket"
ticket.notfound: 'Ticket niet gevonden'
ticket.404: 'Ticket pagina niet gevonden'
ticket.no.merge: 'Tickets zijn niet samengevoegd, ticket kan niet met zichzelf samengevoegd worden!'
ticket.merge: 'Tickets samenvoegen'

service.task.form.name: 'Naam'
service.task.form.description: 'Omschrijving'
service.task.form.icon: 'Icoon'
service.task.form.tags: 'Tags'
service.task.form.roles: 'Rollen'
service.task.form.show-total: 'Laat totaal aantal tickets zien bij gebruikers'

service.ticket.mail.added: 'De volgende tickets zijn aangemaakt:'
service.ticket.mail.existed: 'De volgende tickets bestonden al:'

service.ticket.order.update: 'Update'
service.ticket.order.id: 'Order ID:'
service.ticket.label.close: 'Afronden'
service.ticket.label.wait: 'Wachten'
service.ticket.label.send: 'Vesturen'
service.ticket.label.submit.close: 'Versturen en afronden'
service.ticket.label.submit.wait: 'In wacht zetten'
service.ticket.label.submit.wait.days: 'Dagen in de wacht zetten'
service.ticket.label.submit.send.finish: 'Versturen en afronden'
service.ticket.label.submit.wait.three: 'Versturen en in de wacht zetten (Drie dagen)'
service.ticket.label.submit.wait.seven: 'Versturen en in de wacht zetten (Zeven dagen)'
service.ticket.label.submit.wait.fourteen: 'Versturen en in de wacht zetten (Veertien dagen)'
service.ticket.label.submit.wait.twentyone: 'Versturen en in de wacht zetten (Eenentwintig dagen)'

service.ticket.notes: 'Notitie bij ticket'
service.ticket.customer.notes: 'Notities bij klant'

dashboard.pie.label.open: 'Open'
dashboard.pie.label.closed: 'Dicht'
dashboard.pie.label.deleted: 'Verwijderd'
dashboard.pie.label.other: 'Overig'
dashboard.pie.label.tickets.done: '%total% tickets van %date%'
dashboard.pie.label.tickets.tooltip: 'Tickets'

dashboard.relevant.tickets: 'Relevante tickets'
dashboard.week.statistics.heading: 'Statistieken van de afgelopen %count% dagen'

email.ticket.update: 'Er is een update voor ticket:'
email.ticket.click: 'Klik hier om er heen te gaan.'

ticket.replies.show-hidden: 'Laat %count% verborgen berichten zien'

service.ticket.findUser.title: 'Zoek gebruiker'
service.ticket.findUser.button: 'Zoek'

service.ticket.rma.title: RMA
service.ticket.rma.create: RMA aanmaken
service.ticket.rma.create.order: RMA aanmaken voor order
service.ticket.rma.create.new.user: Nieuwe klant en RMA aanmaken
service.ticket.rma.orderHelp: Koppel eerst een order aan dit ticket om daarna eenvoudig het product/serienummer te vinden
service.rma.form.mailLanguage: Mail taal
service.rma.description: Reden retour/klachtomschrijving
service.rma.repairDescription: Klachtomschrijving
service.rma.returnDescription: Reden retour
service.rma.productFromOrder: Selecteer product uit order
service.rma.productNotExistantError: Voorraad artikel bestaat niet meer en is verwijderd.
service.rma.product: Product
service.rma.barcode: Serienummer
service.rma.barcode.short: S/N
service.rma.price: Inkoopprijs
service.rma.status: Status
service.rma.type: Type
service.rma.vat_free: BTW vrij
service.rma.depreciation: Afwaardering
service.rma.accessories: Meegeleverde accessoires
service.rma.solution: Gewenste oplossing
service.rma.otherOptions: Extra
service.rma.updateTicketTitle: De titel van het ticket bijwerken
service.rma.warning.stockItemUpdate: Let op! Er was eerst geen order gekoppeld waardoor de productinformatie handmatig is ingevoerd. Bij het opslaan gaat deze informatie verloren!
service.rma.warning.otherStock: Originele waarde
service.rma.warning.productNotInOrder: 'Het product in deze RMA is niet gekoppeld aan de order!|De producten in deze RMA zijn niet gekoppeld aan de order!'
service.rma.warning.productNotInOrderButton: Koppelen
service.rma.invoice.number: Factuurnummer
service.rma.invoice.date: Factuurdatum
service.rma.lastCustomerContact: Laatste klantcontact
service.rma.noCustomerContact: Er is nog geen klantcontact geweest
service.rma.noRmaTicket: Dit is geen RMA ticket
service.rma.lastCustomerContact.days: '%count% dag geleden|%count% dagen geleden'
service.rma.orders: Orders van deze RMA
service.rma.export.list: Exporteer deze lijst met RMA's
service.rma.export.text: Deze export is gegeneerd op %date%
service.rma.error.invalidStockItem: 'Er is iets mis met artikel "%title%". Deze is niet (meer) correct gekoppeld, het kan zijn dat het eventuele factuurnummer hieronder niet klopt! Neem z.s.m. contact op met de programmeurs.'
service.rma.error.confirmationInfoMissing: 'De bevestiging kon niet gemaakt worden omdat er iets mis is met de volgende gegevens van de RMA: %fields%'
service.rma.error.noReturnLabelFound: 'Label voor type: %type% en land %country% is niet gevonden. Fallback label EN is verzonden'
service.rma.error.noCustomerFound: 'Order id: "%1$d" heeft geen of geen geldig klant nummer. Ga naar de admin en voorzie Order "%1$d" van een geldig klant nummer.'
service.rma.dropoffLocation: Afgeeflocatie


service.rma.letter.title: Brief naar servicepartner maken
service.rma.letter.saveAsPdf: Opslaan als PDF
service.rma.letter.saveAndPrint: Opslaan en printen
service.rma.letter.address: Adres
service.rma.letter.body: Briefinhoud
service.rma.letter.attention: t.a.v.
service.rma.letter.noServicePartnerError: Er is geen servicepartner ingevuld bij het ticket!
service.rma.letter.unknownServicePartnerError: Servicepartner Onbekend is ingevuld bij het ticket!
service.rma.letter.message: Brief naar servicepartner aangemaakt
service.rma.customerletter.message: Bevestigingsbrief voor klant gemaakt

service.rma.postnl.title: PostNL label maken
service.rma.postnl.number_of_packages: Aantal pakketten
service.rma.postnl.sender: Afzender
service.rma.postnl.target: Verzenden naar
service.rma.postnl.servicePartner: Servicepartner
service.rma.postnl.customer: Klant
service.rma.postnl.rmaStatus: RMA status direct aanpassen
service.rma.postnl.labeltype: Uitvoer naar
service.rma.postnl.exportA4: A4
service.rma.postnl.exportLabel: Label (10x15cm)
service.rma.postnl.create: Aanmaken
service.rma.postnl.printing: Label direct naar printer sturen?
service.rma.postnl.message: PostNL label (%trackcode%) gemaakt voor %to%
service.rma.postnl.message.print: PostNL label uitgeprint naar %printer%
service.rma.postnl.noServicePartner:  Kan niet verzenden naar servicePartner. Er is geen servicepartner ingevuld bij het ticket!
service.rma.postnl.unknownServicePartner: Kan niet verzenden naar servicePartner. Servicepartner Onbekend is ingevuld bij het ticket!
service.rma.postnl.track_and_trace_click_here: Klik hier om deze zending te volgen

service.rma.scan.label: RMA ID
service.rma.scan.button: Scan
service.rma.scan.openRma: Direct RMA openen

service.rma.actions: Acties
service.rma.action.process-return: Retour verwerken
service.rma.action.process-vendiro-return: Retour verwerken
service.rma.action.process-return.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel retour nemen</li></ul>"
service.rma.action.process-vendiro-return.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel retour nemen</li></ul>"
service.rma.action.money-return: Geld retour
service.rma.action.vendiro-money-return: Geld retour
service.rma.action.money-return.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel retour nemen</li><li>Parkeren naar \"Retour betaling klanten\"</li><li>Mail sturen naar de klant</li></ul>"
service.rma.action.vendiro-money-return.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel retour nemen</li><li>Parkeren naar \"RET. BETALING Vendiro\"</li></ul>"
service.rma.action.money-return-doa: Geld retour
service.rma.action.vendiro-money-return-doa: Geld retour
service.rma.action.money-return-doa.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel DOA nemen</li><li>Parkeren naar \"Retour betaling klanten\"</li><li>Mail sturen naar de klant</li></ul>"
service.rma.action.vendiro-money-return-doa.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel DOA nemen</li><li>Parkeren naar \"RET. BETALING Vendiro\"</li></ul>"
service.rma.action.product-return: Ander product
service.rma.action.product-vendiro-return: Ander product
service.rma.action.product-return.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel DOA nemen</li><li>Mail sturen naar de klant</li></ul>"
service.rma.action.product-vendiro-return.help: "<ul><li>Nieuwe order aanmaken indien nog niet aangemaakt</li><li>Artikel DOA nemen</li></ul>"
service.rma.action.process-rma: RMA verwerken
service.rma.action.process-vendiro-rma: RMA verwerken
service.rma.action.process-rma.help: "<ul><li>Brief aan servicepartner printen</li><li>Factuur printen</li><li>Mail naar klant (gebeurd niet bij DOA)</li><li>Ticket in behandeling (3 dagen)</li></ul>"
service.rma.action.process-vendiro-rma.help: "<ul><li>Brief aan servicepartner printen</li><li>Factuur printen</li><li>Mail naar klant (gebeurd niet bij DOA)</li><li>Ticket in behandeling (3 dagen)</li></ul>"
service.rma.action.request-rma: RMA aanvragen
service.rma.action.request-vendiro-rma: RMA aanvragen
service.rma.action.request-rma.help: "<ul><li>Mail naar servicepartner voor aanvraag RMA nummer</li></ul>"
service.rma.action.request-vendiro-rma.help: "<ul><li>Mail naar servicepartner voor aanvraag RMA nummer</li></ul>"
service.rma.action.error.no-order-bound: Deze automatische actie kan niet uitgevoerd worden. Er moet één order gekoppeld zijn aan de RMA.
service.rma.action.error.no-stock-items: Deze automatische actie kan niet uitgevoerd worden. De RMA producten moeten gekoppeld zijn aan voorraad.
service.rma.action.error.no-rma-order-bound: Deze automatische actie kan niet uitgevoerd worden. Er moet één retour-order gekoppeld zijn aan deze RMA.
service.rma.action.error.no-servicePartner-bound: Deze automatische actie kan niet uitgevoerd worden. Er is geen servicepartner gekoppeld aan het ticket.
service.rma.action.notice.unknown-servicePartner-bound: Let op servicepartner Onbekend gekoppeld. Bij RMA aanvragen aanpassen.
service.rma.action.error.unknown-servicePartner-bound: Deze automatische actie kan niet uitgevoerd worden. Service partner Onbekend is gekoppeld.
service.rma.action.error.max-one-rma-order-bound: Deze automatische actie kan niet uitgevoerd worden. Er is meer dan 1 order gekoppeld aan deze RMA, graag de retour handmatig afhandelen.
service.rma.action.error.invalid-payment-method: De originele order heeft geen geldige betaalwijze gevonden
service.rma.action.error.no-email: Deze automatische actie kan niet uitgevoerd worden. Er is geen geldig e-mailadres van de klant.
service.rma.action.error.no-paymentMethod: Deze automatische actie kan niet uitgevoerd worden. Er is geen geldige betaalwijze aan de originele order gekoppeld.

service.rma.servicepartner.error: Helaas is het niet mogelijk om de servicepartner automatisch te koppelen voor deze RMA
service.rma.servicepartner.error.non-stock-product: Een of meer producten van deze RMA heeft geen gekoppelde servicepartner
service.rma.servicepartner.error.multiple-partners: Er zijn meerdere servicepartners mogelijk voor deze RMA
service.rma.servicepartner.error.partner-not-found: Geen partner gevonden via de product(en) van deze RMA
service.rma.servicepartner.error.products-not-found: Deze RMA heeft geen producten
service.rma.action.error.no-printer: Er is geen printer ingesteld. Ga naar de admin, stel een printer in en log opnieuw in bij de servicetool.
service.rma.servicepartner.not-in-ticket: Er is nog geen servicepartner gekoppeld...

statistics.tickets.error.inputDate: 'Ongeldige datumreeks opgegeven, huidige weergave van %start% tot %end%'
statistics.tickets.assignChartHeading: "Ticket toewijzingen"
statistics.tickets.mailAndStateHeading: 'Mails en tickets van %start% tot %end%'
statistics.tickets.openTicketsPieHeading: "Open tickets"
statistics.tickets.count: "Aantal"
statistics.tickets.assignee: "Toegewezen aan"
statistics.tickets.assignedBy: "Toegewezen door"
statistics.tickets.transferred: "Overgedragen"
statistics.tickets.createdTickets: 'Aangemaakte tickets'
statistics.tickets.closedTickets: 'Afgeronde tickets'
statistics.tickets.newMail: 'Binnengekomen mail'
statistics.tickets.openUnassigned: 'Open en niet toegewezen'
statistics.tickets.openAssigned: 'Open en toegewezen'
statistics.tickets.openTagged: 'Open en toegewezen aan afdeling'
statistics.tickets.pendingUnassigned: 'In behandeling en niet toegewezen'
statistics.tickets.pendingAssigned: 'In behandeling en toegewezen'
statistics.tickets.pendingTagged: 'In behandeling en toegewezen aan afdeling'
statistics.tickets.activeUsers: 'Actieve gebruikers'
statistics.tickets.assignedCount: '%count% toegewezen'
statistics.tickets.user.replies: 'Tickets met reacties van %name%'
statistics.tickets.user.anwseredTicketCount: 'Aantal beantwoorde tickets'
statistics.tickets.user.anwseredTicketCount.help: 'Het totaal aantal tickets in de geselecteerde periode waarin deze gebruiker een of meerdere reacties heeft geplaatst.'
statistics.tickets.user.replyAverage: 'Gemiddeld aantal reacties per ticket'
statistics.tickets.user.replyAverage.help: 'Het gemiddeld aantal e-mails welke in de geselecteerde periode door deze gebruiker per ticket zijn verstuurd.'
statistics.tickets.user.replyCount: 'Aantal gestuurde reacties'
statistics.tickets.user.replyCount.help: 'Het totaal aantal e-mails in de geselecteerde periode die door deze gebruiker zijn gestuurd.'
statistics.tickets.user.noteCount: 'Aantal notities'
statistics.tickets.user.noteCount.help: 'Het totaal aantal interne notities die zijn geplaatst in de geselecteerde periode door deze gebruiker.'
statistics.tickets.user.closedCount: 'Aantal tickets afgerond'
statistics.tickets.user.closedCount.help: 'Het totaal aantal tickets dat deze gebruiker in de geselecteerde periode heeft afgesloten.'
statistics.tickets.user.mergedCount: 'Aantal tickets samengevoegd'
statistics.tickets.user.mergedCount.help: 'Het aantal tickets dat deze gebruiker in de geselecteerde periode heeft samengevoegd met een ander ticket.'
statistics.tickets.user.closedAndAssigned: 'Aantal toegewezen en afgeronde tickets'
statistics.tickets.user.closedAndAssigned.help: 'Het aantal tickets dat aan deze gebruiker in de geselecteerde periode is toegewezen en door deze gebruiker in diezelfde periode is afgesloten.'
statistics.tickets.user.transferredCount: 'Aantal doorgegeven tickets'
statistics.tickets.user.transferredCount.help: 'Het aantal tickets dat door deze gebruiker is toegewezen aan iemand of niemand in de geselecteerde periode.'
statistics.tickets.user.assignedToNobdy: 'Aantal toegewezen aan niemand'
statistics.tickets.user.assignedToNobdy.help: 'Het aantal tickets dat door deze gebruiker is toegewezen aan niemand in de geselecteerde periode.'
statistics.tickets.user.taggedTicketCount: 'Aantal toegewezen tickets met tag'
statistics.tickets.user.taggedTicketCount.help: 'Het aantal tickets dat in de geselecteerde periode zijn toegewezen aan deze gebruiker die in de geselecteerde periode een tag hadden.'
statistics.tickets.user.taggedReplyCount: 'Aantal reacties op tickets met tag'
statistics.tickets.user.taggedReplyCount.help: 'Het aantal e-mails dat in de geselecteerde periode zijn verstuurd door deze gebruiker bij tickets die in de geselecteerde periode een tag hadden.'
statistics.tickets.user.ticketsTaggedCount: 'Aantal tickets voorzien van een tag'
statistics.tickets.user.ticketsTaggedCount.help: 'Het aantal tickets dat door deze gebruiker voorzien zijn van een of meerdere tags in de geselecteerde periode.'
statistics.tickets.user.ticketAssignCount: 'Aantal zelf toegewezen tickets'
statistics.tickets.user.ticketAssignCount.help: 'Het totaal aantal tickets dat door deze gebruiker zijn toegewezen in de geselecteerde periode'
statistics.tickets.user.cherrypickCount: 'Aantal handmatig zelf toegewezen tickets'
statistics.tickets.user.cherrypickCount.help: 'Het aantal tickets dat handmatig door deze gebruiker aan zichzelf is toegewezen in de geselecteerde periode'
statistics.tickets.user.ticketTime: 'Verwerkingstijd per toegewezen ticket'
statistics.tickets.user.ticketTime.subtitle: 'Op basis van %count% tickets'
statistics.tickets.user.ticketTime.subtitle.quick: 'Op basis van %count% tickets (waarvan %quickTickets% snelle tickets)'
statistics.tickets.user.ticketTime.hours: 'uur'
statistics.tickets.user.ticketTime.minutes: 'minuten'
statistics.tickets.user.ticketTime.days: 'dagen'
statistics.tickets.user.ticketTime.label.low: 'Laagste'
statistics.tickets.user.ticketTime.label.q1: 'Q1'
statistics.tickets.user.ticketTime.label.median: 'Mediaan'
statistics.tickets.user.ticketTime.label.q3: 'Q3'
statistics.tickets.user.ticketTime.label.high: 'Hoogste'
statistics.tickets.user.ticketTime.label.average: 'Gemiddelde'
statistics.tickets.user.ticketTime.label.exception: 'Uitzondering'
statistics.tickets.user.ticketTime.label.extremeException: 'Extreme uitzondering'
statistics.tickets.user.ticketTimeCount: 'Aantal toegewezen tickets met daarna een reactie'
statistics.tickets.user.ticketTimeCount.help: 'Aantal tickets die zijn toegewezen aan deze gebruiker in de geselecteerde periode en daarna een e-mail is verstuurd door deze gebruiker in de geselecteerde periode.'
statistics.tickets.user.ticketTimeAvg: 'Gemiddelde verwerkingstijd per toegewezen ticket'
statistics.tickets.user.ticketTimeAvg.help: 'De tijd die deze gebruiker in de geselecteerde periode nodig had om een e-mail te sturen nadat deze gebruiker voor het eerst was toegewezen aan het ticket.'
statistics.tickets.user.ticketTimeQuick: 'Aantal snelle tickets'
statistics.tickets.user.ticketTimeQuick.help: 'Het aantal tickets in de geselecteerde periode waarbij binnen 1 minuut na het toewijzen aan deze gebruiker een e-mail is gestuurd.'
statistics.tickets.user.ticketTimeAvg.timeFormat.hours: '%count% uur'
statistics.tickets.user.ticketTimeAvg.timeFormat.minutes: '%count% minuut|%count% minuten'
statistics.tickets.user.ticketTimeAvg.timeFormat.text: 'tussen %from% en %till%'
statistics.tickets.everybody: 'Statistieken over iedereen weergeven'

auth.username: Gebruikersnaam
auth.password: Wachtwoord
auth.pin: Pincode
auth.login: Login
auth.loggedout: Je bent uitgelogd

auth.reply.signature: 'Kies een handtekening'
auth.reply.orders: 'Orders handtekening'
auth.reply.info: 'Info handtekening'
auth.reply.service: 'Service handtekening'
auth.reply.normal: 'Persoonlijke handtekening'
auth.reply.pro: 'Pro handtekening'
auth.reply.inkoop: 'Inkoop handtekening'
auth.reply.administratie: 'Administratie handtekening'
auth.reply.combodeals: 'Combodeals handtekening'
auth.reply.purchase: 'Purchase handtekening'
auth.reply.occasions: 'Occasions handtekening'
auth.reply.verhuur: 'Verhuur handtekening'
auth.reply.default: 'Maak uw keuze'

customer.addresses.heading: Adressen
customer.orders.heading: Orders
customer.returns.heading: Retouren
customer.reviews.heading: Reviews
customer.open.invoices.heading: Openstaande posten
customer.tickets.heading: Tickets
customer.rmas.heading: RMA's
customer.serviceuser.warning: Deze klant is samengevoegd met een andere klant.
customer.merge.isparent.warning: De volgende klanten zijn samengevoegd met deze klant. Die worden door deze actie ook geanonimiseerd!
customer.merge.heading: Klanten samenvoegen
customer.merge: Geselecteerde klanten samenvoegen NAAR deze klant
customer.automaticuser: Service klant, let op!
customer.merge.serviceuser.warning: Let op! Deze klant is automatisch aangemaakt en kan niet optimaal gebruik maken van de website.
customer.merge.realuser.warning: Let op! U staat op het punt een echt account onbruikbaar te maken! De gebruiker kan na het samenvoegen niet meer inloggen.
customer.merge.customer.to: Nieuw hoofdaccount
customer.merge.customer.from: Geselecteerde klanten samenvoegen
customer.merge.id: Klantnummer
customer.merge.name: Naam
customer.merge.contact: Adres(sen)
customer.merge.lastorders: Laatste bestelling(en)
customer.merge.origin.warning: Let op! klant van
customer.merge.origin.error: Let op! Klanten met verschillende herkomsten kunnen niet samengevoegd worden.
customer.merge.origin.access_denied_error: Het samenvoegen van klanten is niet gelukt omdat u niet gemachtigd bent.
customer.headacount: Gebruik als hoofdaccount
customer.add: Nieuwe gebruiker toevoegen
customer.edit: Gebruiker wijzigen
customer.list.header: Gebruikers beheer
customer.search.name: Naam
customer.search.email: E-mail
customer.search.adres: Adres
customer.search.submit: Zoek
customer.changes.success: De aanpassingen zijn opgeslagen
customer.404: Deze pagina bestaat niet
customer.user.notfound: Geen gebruiker opgegeven voor dit adres.
customer.user.notexist: De gebruiker bestaat niet.
customer.address.saved: Nieuw adres is toegevoegd.
customer.address.add: Nieuw adres toevoegen
customer.address.edit: Adres aanpassen
customer.address.noid: U hebt geen klant opgegeven.
customer.overview.title: Klanten overzicht
customer.table.id: ID
customer.table.name: Naam
customer.table.address: Adres
customer.table.email: E-mail
customer.table.company: Bedrijf
customer.table.options: Opties
customer.table.total.searchresults: Totale zoekresultaten
customer.contact.add: Contact optie toevoegen
customer.contact.edit: Contact optie wijzigen
customer.contact.delete: Contact optie verwijderd
customer.contact.saved: Nieuwe contact optie is toegevoegd
customer.contact.title: Contact
customer.return.type: Type
customer.return.description: Omschrijving
customer.return.startdate: Aangeleverd
customer.return.startdate.servicePartner: Aangeleverd Servicepartner
customer.return.lastupdate: Laatste update
customer.send.password.reset: Stuur wachtwoord reset
customer.header.address: Adres
customer.header.contact: Contact
customer.ticket.lastupdater: Laatst geupdate door
customer.ticket.handler: Word behandeld door
customer.tickets.replies.header: Laat reacties zien
customer.note.heading: Notities
customer.note.added: Notitie is toegevoegd
customer.note.changed: Notitie is gewijzigd
customer.note.deleted: Notitie is verwijderd
customer.note.add: Nieuwe notitie toevoegen
customer.note.edit: Notitie wijzigen
customer.note.placeholder: Notitie
customer.note.creator: Aangemaakt door
customer.note.updater: Laatst geupdate door
customer.note.confirm: Notitie verwijderen?
customer.order.search: Bestelling zoeken
customer.order.recent: Recente bestelling(en)
customer.label.heading: Algemene Zaken
customer.label.active: Account <span class="c-green">actief</span>
customer.label.blocked: Account geblokkeerd
customer.label.warehouse: Groothandel
customer.label.business: Zakelijk
customer.label.business.small: Klein Zakelijk
customer.label.business.category.educatie: Educatie
customer.label.business.category.healthcare: Healthcare
customer.label.business.category.facilitair: Facilitair
customer.label.business.category.beroepsmatig: Beroepsmatig
customer.label.business.category.industrial: Industrieel
customer.label.business.category.semiprof: Semi-professionele zelfstandige
customer.label.business.category.supplier: Leverancier
customer.label.business.account_manager: Accountmanager
customer.label.cnu_groep: CNU-groep
customer.label.consumer: Consument
customer.label.mailable: E-mail opt-in
customer.label.servicecustomer: <b>Service klant!</b> Alleen voor ons zichtbaar
customer.ticket.search: Ticket zoeken
customer.ticket.recent: Recente Ticket(s)
customer.form.active: Account actief
customer.form.blocked: Account geblokkeerd
customer.form.warehouse: Groothandel
customer.form.mailable: E-mail opt-in
customer.form.servicecustomer: Service klant! Alleen voor ons zichtbaar
customer.form.additional_contribution_supplier: Additionele bijdragen leverancier
customer.form.ignore_in_claims_and_fees_export: Orders niet meenemen in Claims&Vergoedingen
customer.form.importantuser: Speciale klant! Notificatie sturen bij bestelling
customer.form.use_id_as_debtor: Klantnummer gebruiken voor administratie.
customer.form.mailingtypes: E-mail voorkeuren
customer.form.readonly: Readonly (Klant mag alleen door bevoegde medewerkers aangepast worden)
customer.form.iban: IBAN
customer.form.bic: BIC
customer.form.language: Taal
customer.form.birthday: Verjaardag
customer.form.email: E-mail
customer.form.businessEmail: Zakelijk e-mail
customer.form.name: Naam
customer.form.phonenr: Telefoonnummer
customer.form.sex: Geslacht
customer.form.type: Klantsoort
customer.form.categoryType: KlantSubcategorie
customer.form.segment: Klantsegment
customer.form.segments: Klantsegmenten
customer.form.segment_show: Klantsegmenten weergeven
customer.form.payment_period: Betalingstermijn
customer.form.afas_collect_account: AFAS verzamelrekening (standaard 1400)
customer.form.segment.placeholder: Geen
customer.form.add.ticket: Voeg klant toe aan ticket
customer.form.save: Opslaan
customer.form.business_account_manager: Accountmanager zakelijk
customer.form.lost_shipment: Waarschuwing vermiste zending

customer.review.onsite: Bekijk review op de website
customer.order.canceled: Geannuleerd
customer.order.finished: Afgerond
customer.order.open: Open
customer.order.offer: Offerte
customer.order.visability.yes: Deze bestelling is zichtbaar voor de klant
customer.order.visability.no: Deze bestelling is niet zichtbaar voor de klant
customer.order.nocameranu: Dit is geen Cameranu bestelling!
customer.address.label.name: Naam
customer.address.label.firstname: Naam
customer.address.label.lastnameprefix: Tussenvoegsel
customer.address.label.lastname: Achternaam
customer.address.label.sex: Geslacht
customer.address.label.address: Adres
customer.address.label.housenr: Huis nr.
customer.address.label.housenrext: Huis nr. extentie
customer.address.label.zipcode: Postcode
customer.address.label.zipcode2: Oude Postcode extentie
customer.address.label.city: Plaats
customer.address.label.country: Land
customer.address.label.phonenr: Telefoonnummer
customer.address.label.company: Bedrijfsnaam
customer.address.label.coc_number: KVK-nummer
customer.address.label.taxnr: BTW-nummer
customer.address.label.flags: Flags
customer.address.label.mainaddress: Maak dit het factuuradres
customer.address.label.sendaddress: Maak dit het verzendadres verzendadres
customer.address.mainaddress: Factuuradres
customer.address.sendaddress: Verzendadres
customer.address.deleted: Adres is verwijderd
customer.form.title: 'Titel'
customer.form.contact.type: 'Type'
customer.form.contact.value: 'Vul in'
customer.form.comment: 'Opmerking'
customer.email.used: 'E-mail adres is leeg of al in gebruik'
customer.zipcode.error: 'Postcode niet gevonden'
customer.isimportant: Dit is een belangrijke klant
customer.origin.nocameranu: "Let op: Dit is een %name% klant!"
customer.origin.cameranu: Cameranu
customer.origin.vendiro: Vendiro
customer.hasnoname: Let op! De naam van deze klant is niet (volledig) ingevuld
customer.gotocard: Ga naar de klantenkaart
customer.form.origin.parent: Herkomst
customer.type.consument: Consument
customer.type.groothandel: Groothandel
customer.type.klein_zakelijk: Klein Zakelijk
customer.type.zakelijk: Zakelijk
customer.type.CNU-groep: CNU-groep

tag.list.header: 'Tagbeheer'
tag.add.header: 'Tag toevoegen'
tag.edit.header: 'Tag aanpassen'
tag.list.name: 'Naam'
tag.list.category: 'Categorie'
tag.list.mail: 'Notificatie per e-mail'
tag.list.defaultmail: 'Standaard antwoord e-mail'

tickets.form.active: 'Actief'
tickets.form.category: 'Categorie'
tickets.form.name: 'Naam'
ticket.form.choices.algemeen: 'Algemeen'
ticket.form.choices.afdeling: 'Afdeling'
ticket.form.choices.bestelling: 'Bestelling'
ticket.form.choices.klant: 'Klant'
ticket.form.choices.ticket: 'Ticket'
ticket.form.choices.rma: 'Schade & Retouren'
tickets.form.mail: 'Notificatie per e-mail'
tickets.form.defaultmail: 'Standaard antwoord e-mail'

mailbox.list.header: 'Mailboxbeheer'
mailbox.add.header: 'Mailbox toevoegen'
mailbox.edit.header: 'Mailbox aanpassen'
mailbox.list.name: 'Naam'
mail.list.mailbox: 'Mailbox'
mail.list.subject: 'Onderwerp'
mail.list.from: 'Van'
mail.list.to: 'Aan'
mail.list.date: 'Datum'
mail.list.attachment: '%count% Bijlage|%count% Bijlages'
mail.list.delete-attachment: 'Verwijder bijlage'
mail.list.add-to-ticket: 'Voeg toe als ticket'
mail.list.delete-email: 'Verwijder e-mail'
mail.attachment-deleted: 'Bijlage is verwijderd'

mail.form.compose: 'Nieuwe e-mail opstellen'
mail.form.sent: 'Uw e-mail is verzonden'
mail.form.name: 'Naam'
mail.form.alternativeSendAddress: 'Alternatief verzend adres'
mail.form.server: 'Server'
mail.form.port: 'Poort'
mail.form.username: 'Gebruikersnaam'
mail.form.password: 'Wachtwoord'
mail.form.importableWithImap: 'Importeer mailbox in de Servicetool'

mail.form.from: 'Van'
mail.form.to: 'Naar'
mail.form.subject: 'Onderwerp'
mail.form.message: 'Bericht'
mail.form.attachments: 'Bijlage(n) uploaden'
mail.form.otherattachments: 'Eerdere bijlage(n) meesturen'
mail.form.send: 'Versturen'
mail.form.replyfrom: 'Bericht verstuurd door'
mail.form.replyon: 'op'

mail.state.archived: 'De e-mail is gearchiveerd'
mail.state.hold: 'De e-mail is verplaatst naar wachtende e-mail'
mail.state.deleted: 'De e-mail is verwijderd'

mail.tooltip.show.full: 'Bekijk volledige e-mail'
mail.tooltip.archive: 'Zet mail in archief'
mail.tooltip.hold: 'Verplaats naar wachtende e-mail'
mail.tooltip.delete: 'Verwijder mail'
mail.tooltip.print: 'Print email'

mail.forward.heading: 'Doorgestuurd bericht'
mail.forward.subject: 'Onderwerp'
mail.forward.date: 'Datum'
mail.forward.from: 'Van'
mail.forward.to: 'Aan'
mail.forward.cc: 'CC'
mail.type.forward: 'Doorsturen'
mail.type.reply: 'Beantwoorden'
mail.type.choice: 'Type e-mail'

searchupdater.ordercomment.title: 'Opmerking bij order %id%'
search.autocomplete: Zoeken
search.placeholder: Zoek op...
search.noresults: Geen resultaten gevonden

search.header.customer: Klanten
search.header.user: Gebruikers
search.header.ticket: Tickets
search.header.other: Overig

search.filter.query: Zoekterm
search.filter.type: Type
search.filter.state: Status
search.filter.date: Datum
search.filter.dateFrom: Vanaf datum
search.filter.dateTo: Tot datum
search.filter.tags: Tags
search.filter.button: Zoeken

search.date.today: Vandaag
search.date.thisweek: Deze week
search.date.thismonth: Deze maand
search.date.choose: Zelf kiezen

search.filter.recent.title: Recent gebruikte filters
search.filter.recent.none: Nog geen filters gebruikt...

order.state.canceled: Geannuleerd
order.state.completed: Afgerond
order.state.offer: Offerte

packingslip.from.date: Vanaf datum
packingslip.to.date: Tot datum
packingslip.filter: Laat pakbon(nen) zien
packingslip.addnotes: Notities toevoegen
packingslip.btn.showselected : Toon geselecteerde pakbonnen
packingslip.orderNumber: Ordernummer
packingslip.reference: Referentie
packingslip.packingslip: Pakbon
packingslip.sentby: Verzonden door
packingslip.sentto: Verzonden naar
packingslip.receivedat: Binnengekomen op
packingslip.addnotes.filtered: Notitie bij pakbonnen
packingslip.error.selectatleastone: Selecteer tenminste een pakbon
packingslip.error.nopackingslips: Er zijn geen pakbonnen om notities aan toe te voegen
packingslip.error.somethingwentwrong: Er is iets fout gegaan
packingslip.showmessage: Geef een notitie op voor deze pakbonregel
packingslip.hasnotes: Op deze pakbon zijn notities geplaatst, open de pakbon om de notities te bekijken
packingslip.error.empty.code: Ongeldige barcode.
packingslip.error.invalid.cart: Geen geldige bak gekozen.
packingslip.error.invalid.amount: Je hebt geen geldig aantal gekozen zorg dat dit een getal boven 0 is.
packingslip.scan.completed: '%sX voorraad ingescand voor product: <b>%s</b> met barcode: <b>%s</b>'
packingslip.error.multiple.lines.found: 'Er zijn meerdere pakbon regels gevonden voor dit serienummer: %s'
packingslip.error.max.amount.exceeded: 'Let op: het maximum aantal is al bereikt. Er is geen extra voorraad ingeboekt. <br>Tel de producten na en leg het teveel apart. Neem hierover contact op met Inkoop.'
packingslip.error.ean.mismatch: 'Kon geen product matchen op EAN: %s'
packingslip.error.multiple.products.for.barcode: 'Meerdere artikelen met deze barcode: <br> %s'
packingslip.error.multiple.stock.for.barcode: 'Er bestaat al een voorraad regel met serienummer: <b>%s</b>'
packingslip.error.multiple.products.for.ean: 'Je scant een EAN van product %s als serienummer voor %s'
packingslip.error.ean.as.serial: 'Code <b>%s</b> is de EAN van dit product deze kun je niet als serienummer inboeken'
packingslip.error.ean.invalid: 'Code: <b>%s</b> is geen geldige barcode voor dit product.<br>Controleer of je de juiste code hebt gescand. Of neem contact op met magazijn beheer.'
packingslip.error.product.mismatch: 'Kon geen producten matchen op code: %s voor de geselecteerde pakbon(nen)'
packingslip.error.purchaseorder.mismatch: 'Geen open inkooporder voor referentie: %s'
packingslip.error.ean.needs.to.be.serial: 'Je probeert een product in te scannen op EAN %s. Dit product moet op serienummer gescand worden.'
packingslip.error.surplus.exceeded: 'Er is al genoeg voor dit artikel. Er is geen extra voorraad ingeboekt.<br>Tel de producten na en leg het teveel apart.<br> Neem hierover contact op met Inkoop.'
packingslip.error.surplus.amount.exceeded: 'Let op! Je wilt meer voorraad toevoegen dan wordt verwacht.<br>Er staan %s producten op de pakbon, er zijn er %s ingescand en je wilt er %s toevoegen.<br>'
packingslip.error.surplus.amount.exceeded.extra: 'Als er meer is geleverd, tel dan de producten dan na en leg het teveel apart.<br> Neem hierover contact op met Inkoop.<br>Er is nog geen voorraad toegevoegd! Je moet het aantal dus opnieuw invoeren, en de barcode scannen.<br>'
packingslip.modal.first.scan.info: 'Let op! Je gaat voorraad aanmaken voor het volgend product: <br><b>%s</b><br>Voorraad komt in geselecteerde transit bak.<br>De volgende scan boekt de voorraad in.<br>'

blacklist.success.add: Blacklist E-mail succesvol toegevoegd!
blacklist.success.update: Blacklist E-mail succesvol geupdate!
blacklist.success.delete: Blacklist E-mail succesvol verwijderd!
blacklist.delete.message: 'Weet u zeker dat u de blacklist voor het e-mailadres <strong>%mail%</strong> wilt verwijderen?'
pda.error.showticket: Geen actie mogelijk met de PDA voor deze RMA

priceupdate.title: Automatische prijzen
priceupdate.report.button: Rapportage
priceupdate.maxdiff.button: Overall maximale prijsafwijking aanpassen
priceupdate.margin.button: Overall minimale marge aanpassen
priceupdate.maxdiff.groupbutton: Maximale prijsverschillen aanpassen voor deze groep
priceupdate.margin.groupbutton: Minimale marge aanpassen voor deze groep
priceupdate.back: Terug
priceupdate.importdisabled.button: Automatische updates uitschakelen
priceupdate.importdisabled.groupbutton: Automatische updates uitschakelen op groepsniveau
priceupdate.omnia.reportnotfound: De rapportage kon niet worden gevonden
priceupdate.omnia.top50.button: TOP50
priceupdate.omnia.camera.button: Digitale camera's en objectieven

priceupdate.report.title: Rapportage
priceupdate.report.import: Import
priceupdate.report.country: Land
priceupdate.report.product: Product
priceupdate.report.site: Site
priceupdate.report.price: Prijs
priceupdate.report.current_price: Huidige prijs
priceupdate.report.log: Log
priceupdate.report.productoverview: Overzicht van dit product weergeven

priceupdate.importdisabled.title: Automatische prijzen uitschakelen
priceupdate.importdisabled.subgroup.title: Automatische prijzen uitschakelen op groepsniveau
priceupdate.importdisabled.product.title: Automatische prijzen uitschakelen op product
priceupdate.importdisabled.source: Bron
priceupdate.importdisabled.source.all: Alle
priceupdate.importdisabled.from: Vanaf
priceupdate.importdisabled.till: Tot
priceupdate.importdisabled.till.with_hint: Tot (Optioneel, bij leeg laten blijft de regel oneindig gelden)
priceupdate.importdisabled.till.endless: geen eindtijd
priceupdate.importdisabled.reason: Reden
priceupdate.importdisabled.changedBy: Laatste mutatie door
priceupdate.importdisabled.inherit: Overgenomen van globale instelling
priceupdate.importdisabled.subgroup.inherit: Overgenomen van bovenliggende groep
priceupdate.importdisabled.subgroup.none: Er zijn geen uitzonderingen van toepassing op deze groep
priceupdate.importdisabled.product.none: Er zijn geen uitzonderingen van toepassing op dit product
priceupdate.importdisabled.create: Toevoegen
priceupdate.importdisabled.edit: Aanpassen
priceupdate.importdisabled.productOrGroup: Product/Groep
priceupdate.importdisabled.allProducts: Alle producten
priceupdate.importdisabled.fromGroupOrProduct: Aanpassen kan alleen bij de groep/het product

priceupdate.maxdiff.title: 'Automatische prijzen: Maximaal prijsverschil'
priceupdate.maxdiff.source: Bron
priceupdate.maxdiff.source.all: Alle
priceupdate.maxdiff.subOrMainGroup: 'Sub-/Hoofdgroep'
priceupdate.maxdiff.percentage: Percentage
priceupdate.maxdiff.subgroup.title: Maximaal prijsverschil voor subgroep
priceupdate.maxdiff.fromGroup: Van groep
priceupdate.maxdiff.maingroup.title: Maximaal prijsverschil voor hoofdgroep
priceupdate.maxdiff.create: Toevoegen
priceupdate.maxdiff.edit.title: 'Automatische prijzen: Maximaal prijsverschil aanpassen'
priceupdate.maxdiff.noGroup: Geen groepen
priceupdate.maxdiff.subgroup.none: Er zijn geen uitzonderingen van toepassing op deze groep
priceupdate.maxdiff.maingroup.none: Er zijn geen uitzonderingen van toepassing op deze groep
priceupdate.maxdiff.edit: Aanpassen
priceupdate.maxdiff.delete: Verwijderen
priceupdate.maxdiff.deleted: Uitzondering verwijderd
priceupdate.maxdiff.subgroup.inherit: Overgenomen van bovenliggende groep
priceupdate.maxdiff.changedBy: Laatste mutatie door

priceupdate.margin.title: 'Automatische prijzen: minimale marge'
priceupdate.margin.source: Bron
priceupdate.margin.source.all: Alle
priceupdate.margin.group: 'Groep'
priceupdate.margin.percentage: Percentage
priceupdate.margin.rootgroup.title: Minimale marge voor rootgroep
priceupdate.margin.subgroup.title: Minimale marge voor subgroep
priceupdate.margin.maingroup.title: Minimale marge voor hoofdgroep
priceupdate.margin.fromGroup: Van groep
priceupdate.margin.create: Toevoegen
priceupdate.margin.edit.title: 'Automatische prijzen: Minimale marge aanpassen'
priceupdate.margin.noGroup: Geen groepen
priceupdate.margin.group.none: Er zijn geen uitzonderingen van toepassing op deze groep
priceupdate.margin.edit: Aanpassen
priceupdate.margin.delete: Verwijderen
priceupdate.margin.deleted: Uitzondering verwijderd
priceupdate.margin.subgroup.inherit: Overgenomen van bovenliggende groep
priceupdate.margin.changedBy: Laatste mutatie door

courierlist.fromlocation: Van locatie
courierlist.tolocation: Naar locatie
courierlist.forstate: Naar status
courierlist.addproduct: Product toevoegen
courierlist.addstock: Voorraad inscannen
courierlist.choosetransit: Deze voorraad moet in een transitbak geplaatst worden
courierlist.state: Status
courierlist.state.choosestate: Maak een keuze
courierlist.state.open: Producten toevoegen
courierlist.state.scannable: Voorraad inscannen
courierlist.state.transit: Voorraad onderweg
courierlist.state.transitIssue: Voorraad problemen
courierlist.state.done: Afgerond
courierlist.state.backorder: Backorder
courierlist.search.with.product: Zoek koerierslijst met product
courierlist.new.postnl.list: 'Nieuwe lijst: %id% aangemaakt voor producten die niet met PostNL mee konden'
courierlist.postnl.list.warning: De volgende producten kunnen niet met PostNL mee.
courierlist.postnl.list.info: Deze worden in een nieuwe koerierslijst gezet.
courierlist.postnl.list.no.courier.products: Deze lijst bevat geen producten die niet met PostNL mee kunnen, er is geen nieuwe lijst aangemaakt. \n Je kunt verder gaan met inscannen.
courierlist.stock.scan.max.amount.exceeded: Voor dit product is het maximum aantal al bereikt. Het laatst gescande product is niet toegevoegd.
courierlist.stock.scan.multiple.threshold.exceeded: Aantal is hoger dan 5 kies hoeveel je er wilt scannen
courierlist.stock.scan.amount.scanned: Er is %amount% voorraad ingescand
courierlist.stock.scan.scanned: Voorraad ingescand
courierlist.stock.scan.not.found: Geen uitleverbare voorraad gevonden voor ingescande barcode.
courierlist.fetch.from.other.location.no.stock: Er hoeft geen voorraad van andere locaties te komen.
courierlist.transitissue.to.done: Voorraadproblemen lijst %list% gesloten omdat alle voorraad op locatie is.
courierlist.type: Type
courierlist.type.fresh: Nieuw
courierlist.type.minimalStock: Minimale voorraad
courierlist.type.moveForOrder: Order
courierlist.type.moveForBackorder: Backorder
courierlist.error.delete.products.invalid.type: Deze optie mag alleen gebruikt worden voor backorder lijsten.
courierlist.btn.delete.all.backorder_products: Verwijder alle openstaande backorders
courierlist.btn.delete.dispatchable.backorder_products: Verwijder uitleverbare backorders
courierlist:
    picking_list:
        title: Koerier pick-lijst
        table:
            amount: Aantal picken
            location: Locatie
            product_stock: Artikel / aantal in voorraad op alle locaties
            other_locations: Andere locaties
        no_dispatchable_stock: Geen uitleverbare producten in lijst
courierlist.courierlist_id: Koerierslijst ID

internalinvoice.created: Factuurdatum
internalinvoice.finished: Datum afgerond
internalinvoice.numberoflines: Aantal factuurregels
internalinvoice.show: Bekijk factuur
internalinvoice.invoice: Factuur
internalinvoice.invoicefor: Factuur voor
internalinvoice.total: Totaal
internalinvoice.date: Factuurdatum
internalinvoice.invoicenumber: Factuurnummer
internalinvoice.reference: Referentie
internalinvoice.description: Omschrijving
internalinvoice.amount: Aantal
internalinvoice.pieceprice: Prijs
internalinvoice.price: Totaal
internalinvoice.vat: BTW %
internalinvoice.totalvat: BTW
internalinvoice.totalprice: Totaal te betalen
internalinvoice.tolocation: Factuur naar locatie
internalinvoice.forlocation: Informatie voor locatie
internalinvoice.name: Naam ontvanger
internalinvoice.emailaddress: E-mailadres ontvanger
internalinvoice.addressinformation: Adresinformatie
internalinvoice.footeraddressinformation: Footerinformatie als verzender
internalinvoice.email: E-mailadres
internalinvoice.telephone: Telefoonnummer
internalinvoice.address: Adres
internalinvoice.postcode: Postcode
internalinvoice.city: Plaats
internalinvoice.coc: KVK informatie
internalinvoice.vatnumber: BTW-nummer
internalinvoice.sepa: Sepa
internalinvoice.sepaname: Sepa naam
internalinvoice.sepaiban: Sepa IBAN
internalinvoice.sepabic: Sepa BIC
internalinvoice.sepaid: Sepa ID
internalinvoice.mandates: Mandaten
internalinvoice.mandateid: Mandaat ID
internalinvoice.signaturedate: Mandaat datum
internalinvoice.filter.type: Type
internalinvoice.filter.type.mixed_stock: Klantorder
internalinvoice.filter.type.courier_list: Koerierslijst
internalinvoice.filter.type.rma: RMA
internalinvoice.filter.type.credit: Credit
internalinvoice.filter.type.stock_transfer: Goederenverplaatsingen
internalinvoice.filter.fromlocation: Factuur vanaf
internalinvoice.filter.tolocation: Factuur naar
internalinvoice.filter.location.urk: Urk
internalinvoice.filter.location.amsterdam: Amsterdam
internalinvoice.filter.location.printshop_amsterdam: Printshop Amsterdam
internalinvoice.filter.location.pro_amsterdam: Pro Amsterdam
internalinvoice.filter.location.apeldoorn: Apeldoorn
internalinvoice.filter.location.groningen: Groningen
internalinvoice.filter.location.eindhoven: Eindhoven
internalinvoice.filter.location.rotterdam: Rotterdam
internalinvoice.filter.location.pro_rotterdam: Pro Rotterdam
internalinvoice.filter.location.antwerpen: Antwerpen
internalinvoice.filter.datetype: Type voor datumrange
internalinvoice.filter.datetype.created: Datum aangemaakt
internalinvoice.filter.datetype.invoice: Factuurdatum
internalinvoice.filter.datefrom: Vanaf datum
internalinvoice.filter.dateto: Tot en met datum
internalinvoice.filter.finished: Laat ook betaalde facturen zien
internalinvoice.filter: Filteren
internalinvoice.createcredit: Creditfactuur aanmaken
internalinvoice.createsepa: Sepa bestanden downloaden
internalinvoice.manualpayment: Handmatige betaling
internalinvoice.manualpayment.warning: Alle geselecteerde facturen worden gemarkeerd als betaald. Doorgaan?
internalinvoice.checks.pricediff: Max prijs verschil in euro's
internalinvoice.checks.pricediffpercentage: Max prijs verschil in %
internalinvoice.checks.stockvaluediff: Max verschil voorraadwaarde in euro's
internalinvoice.checks.stockvaluediffpercentage: Max verschil voorraadwaarde in %

adyenscope.filter.datetype: Type voor datumrange
adyenscope.filter.datetype.order: Datum besteld
adyenscope.filter.datetype.invoice: Factuurdatum
adyenscope.filter.datefrom: Vanaf datum
adyenscope.filter.dateto: Tot en met datum
adyenscope.filter.finished: Laat ook afgeronde order zien
adyenscope.filter: Filteren
adyenscope.filter.groupedByOrder: Groepeer betalingen op order niveau

ledgerfield.visible: Zichtbaar in lijst
ledgerfield.visibleRma: Alleen zichtbaar bij RMA's
ledgerfield.vatAllowed: BTW toestaan
ledgerfield.position: Positie
ledgerfield.code: Code
ledgerfield.name: Naam
ledgerfield.roles: Actief bij rollen

claimsandfees.form.type: Type
claimsandfees.form.from: Van
claimsandfees.form.to: Voor
claimsandfees.form.by: Vergoed door
claimsandfees.form.suppliers: Leveranciers
claimsandfees.form.supplier.groups: Leverancier groepen
claimsandfees.form.ledger.code: Grootboekcode
claimsandfees.form.discount.type: Soort korting
claimsandfees.form.value.type: Waarde type
claimsandfees.form.value: Waarde
claimsandfees.form.flMargin: FL marge
claimsandfees.form.kind: Soort vergoeding
claimsandfees.form.origin: Omzet herkomst
claimsandfees.form.tags: Gekoppelde tags
claimsandfees.form.date.from: Datum van
claimsandfees.form.date.till: Datum tot
claimsandfees.form.attachment: Bijlage
claimsandfees.form.action.name: Actienaam

claimsandfees.report.form.name: Naam van rapportage
claimsandfees.report.form.type: Type rapportage
claimsandfees.report.form.emails: E-mailadres(sen)
claimsandfees.report.form.date.from: Periode start
claimsandfees.report.form.date.till: Periode eind
claimsandfees.report.form.supplier.group: Hoofdleverancier

adyen.pos.terminalid: Adyen terminal ID
adyen.pos.pay: Betalen
adyen.pos.refund: Terugbetalen
adyen.pos.pin: Pinautomaat
adyen.pos.amount: Bedrag
adyen.pos.success: De betaling is gelukt
adyen.pos.type: Pin/QR
adyen.pos.type.qr: QR
adyen.pos.type.pin: Pinnen
adyen.pos.notice: Het minimale bedrag is 0.10. Bedragen invoeren met een punt.
adyen.pos.error.messageformat: Wacht even en probeer het opnieuw
adyen.pos.error.deviceout: Er zijn momenteel netwerkproblemen, probeer het opnieuw. Krijg je deze melding 3 keer achter elkaar, herstart dan de pin terminal.
adyen.pos.error.notallowed: Kaart geblokkeerd, ongeldig bedrag of onbekende betaalmethode.
adyen.pos.error.unavailableservice: Er is een probleem met de pinautomaat. Probeer een ander apparaat
adyen.pos.error.inprogress: Wacht even en probeer het opnieuw
adyen.pos.error.aborted: De klant heeft de transactie verlaten nadat hij een contactloze betaling had geprobeerd en werd gevraagd om een andere kaartinvoermethode te proberen (PIN of swipe)
adyen.pos.error.cancel: Klant heeft transactie afgebroken
adyen.pos.error.invalidcard: De kaart van de klant wordt niet geaccepteerd
adyen.pos.error.wrongpin: Ingevulde PIN is fout of ongeldig
adyen.pos.error.unreachablehost: Er zijn momenteel netwerkproblemen, probeer het opnieuw. Krijg je deze melding 3 keer achter elkaar, herstart dan de pin terminal.
adyen.pos.error.refusal: De kaart van de klant wordt niet geaccepteerd
adyen.pos.error.notfound: Er is een probleem met de pinautomaat. Probeer een ander apparaat
adyen.pos.error.busy: Er is al een verzoek verstuurd aan pin terminal %terminal%. Rond de betaling af op pin terminal %terminal%, stop de transactie op terminal %terminal% of wacht enkele momenten tot het scherm op pin %terminal% vanzelf sluit (max. 120 sec.).
adyen.pos.refund.success: De terugbetaling is gelukt
adyen.pos.refund.error.aborted: Er is iets misgegaan
adyen.pos.refund.error.busy: Er is iets misgegaan
adyen.pos.refund.error.cancel: Er is iets misgegaan
adyen.pos.refund.error.deviceout: Er zijn momenteel netwerkproblemen, probeer het opnieuw. Krijg je deze melding 3 keer achter elkaar, herstart dan de pin terminal.
adyen.pos.refund.error.insertedcard: Er is iets misgegaan
adyen.pos.refund.error.inprogress: Er is iets misgegaan
adyen.pos.refund.error.loggedout: Er is iets misgegaan
adyen.pos.refund.error.messageformat: Er is iets misgegaan
adyen.pos.refund.error.notallowed: Er is iets misgegaan
adyen.pos.refund.error.notfound: Er is iets misgegaan
adyen.pos.refund.error.paymentrestriction: Er is iets misgegaan
adyen.pos.refund.error.refusal: Er is iets misgegaan
adyen.pos.refund.error.unavailabledevice: Er is iets misgegaan
adyen.pos.refund.error.unavailableservice: Er is iets misgegaan
adyen.pos.refund.error.invalidcard: Er is iets misgegaan
adyen.pos.refund.error.unreachablehost: Er is iets misgegaan
adyen.pos.refund.error.wrongpin: Er is iets misgegaan

terminal.terminalid: Adyen terminal ID
terminal.pay: Betalen
terminal.refund: Terugbetalen
terminal.pin: Pinautomaat
terminal.amount: Bedrag
terminal.success: De betaling is gelukt
terminal.type: Pin/QR
terminal.type.qr: QR
terminal.type.pin: Pinnen
terminal.notice: Het minimale bedrag is 0.10. Het maximale bedrag is %max_amount%. Bedragen invoeren met een punt.
terminal.notice.shippingmethod: Er is geen verzendwijze ingesteld voor deze order.
terminal.error.payment: 'De betaling kon niet naar de terminal worden verstuurd. Dit zijn de meeste voorkomende oorzaken. Controleer of deze dingen kloppen en probeer het dan nog een keer:  Staat de herkomst van de order goed voor de winkel waar je probeert te pinnen? Staan de verzend- en betaalwijze goed voor de winkel waar je probeert te pinnen?'

adyen.refund: Terugbetalen
adyen.refund.amount: Bedrag
adyen.refund.success: De terugbetaling is aangevraagd. Let op! Het kan even duren voordat de terugbetaling verwerkt is (controleer orderlog en fact)
adyen.refund.error.order: Kan de originele order van deze credit order niet vinden
adyen.refund.error.orderpayment: Kan de originele betaling niet vinden voor deze credit order
adyen.refund.error.adyenlog: Kan de Adyen log data niet ophalen voor verificatie van de betaling
adyen.refund.error.amountexceeded: Maximaal mogelijk terug te betalen bedrag overschreden
adyen.refund.error.validation: Er is iets mis gegaan in de validatie
adyen.refund.warning.amountalreadyrefunded: Let op! er is al %amount% terugbetaald voor deze order

psp.refund: Terugbetalen
psp.refund.error: Er is iets mis gegaan bij het terugbetalen. Zie de order log voor meer informatie.
psp:
    refund:
        amount: Bedrag
        enteramount: Bedrag invullen
        selectpayment: Selecteer een betaling
        success: De terugbetaling is aangevraagd. Let op! Het kan even duren voordat de terugbetaling verwerkt is (controleer orderlog en fact)
        warning:
            norefundpossible: Er is voor deze order geen terugbetaling mogelijk
            amountalreadyrefunded: Let op! er is al %amount% terugbetaald voor deze order
        error:
            ordernotfound: Kan de originele order van deze credit order niet vinden
            orderpayment: Kan de originele betaling niet vinden
            log: Kan de log data niet ophalen voor verificatie van de betaling
            amountexceeded: De aanvraag kon niet worden verwerkt. Waarschijnlijk is het ingevoerde bedrag te hoog.
            validation: Er is iets mis gegaan in de validatie
            handlernotimplemented: Kan geen terugbetaling uitvoeren voor deze betaalmethode

adyenordercheck.filter.states: Status
adyenordercheck.filter.state.1: Betaalmethode Adyen
adyenordercheck.filter.state.4: Betaalmethode geen Adyen
adyenordercheck.filter.state.2: Goedhart OK
adyenordercheck.filter.state.3: Goedhart niet OK
adyenordercheck.filter.state.5: Wel in batch
adyenordercheck.filter.state.6: Niet in batch
adyenordercheck.filter.datefrom: Datum vanaf
adyenordercheck.filter.dateto: Datum tot
adyenordercheck.filter: Filteren
adyenordercheck.orderid: Ordernummer
adyenordercheck.invoicedate: Factuurdatum
adyenordercheck.customer: Klantnaam
adyenordercheck.totalcnu: Totaalbedrag CNU
adyenordercheck.totaladyen: Totaalbedrag in batch
adyenordercheck.payedwithadyen: Betaalmethode Adyen
adyenordercheck.goedhartok: Goedhart OK

adyen.paymentmethod.directEbanking: Sofort
adyen.paymentmethod.giropay: Giropay
adyen.paymentmethod.ideal: iDeal
adyen.paymentmethod.paypal: Paypal
adyen.paymentmethod.afterpay_default_NL: AfterPay
adyen.paymentmethod.afterpay_default: AfterPay
adyen.paymentmethod.amex: Creditcard
adyen.paymentmethod.maestro: Maestro
adyen.paymentmethod.mc: Creditcard
adyen.paymentmethod.visa: Creditcard
adyen.paymentmethod.bcmc: Bancontact
adyen.paymentmethod.klarna: Klarna
adyen.paymentmethod.klarna_account: Klarna
adyen.paymentmethod.applepay: Apple Pay
adyen.paymentmethod.scheme: Creditcard
adyen.paymentmethod.bankTransfer: Bankoverschrijving
adyen.paymentmethod.bankTransfer_DE: Bankoverschrijving
adyen.paymentmethod.bankTransfer_BE: Bankoverschrijving
adyen.paymentmethod.bankTransfer_GB: Bankoverschrijving
adyen.paymentmethod.bankTransfer_IBAN: Bankoverschrijving
adyen.paymentmethod.bankTransfer_NL: Bankoverschrijving
adyen.paymentmethod.bankTransfer_PL: Bankoverschrijving

adyen.klarna.paymentmethod.klarna_account: Klarna

mollie.paymentmethod:
    applepay: 'Apple Pay'
    bancontact: 'Bancontact'
    banktransfer: 'Bankoverschrijving'
    belfius: 'Belfius'
    billie: 'Billie'
    creditcard: 'Creditcard'
    directdebit: 'Automatische incasso'
    eps: 'EPS'
    giftcard: 'Cadeaubon'
    giropay: 'Giropay'
    ideal: 'iDeal'
    in3: 'in3'
    kbc: 'KBC'
    klarna: 'Klarna'
    klarnapaylater: 'Klarna: Acteraf betalen'
    klarnapaynow: 'Klarna: Betaal nu'
    klarnasliceit: 'Klarna: Gespreid betalen'
    mybank: 'MyBank'
    paypal: 'PayPal'
    paysafecard: 'paysafecard'
    przelewy24: 'Przelewy24'
    sofort: 'Sofort'
    voucher: 'Tegoedbon'

websiteconfigurator.date_changer.date: Datum
websiteconfigurator.date_changer.use_ntp: NTP gebruiken (overschrijft gekozen datum)

monday: maandag
tuesday: dinsdag
wednesday: woensdag
thursday: donderdag
friday: vrijdag
saturday: zaterdag
sunday: zondag

websiteconfigurator.field.parent: Koppel aan
websiteconfigurator.field.name: Naam
websiteconfigurator.field.label: Label
websiteconfigurator.field.description: Omschrijving
websiteconfigurator.field.read_only: Read only
websiteconfigurator.field.helper_text: Hulp tekst
websiteconfigurator.field.field_type: Soort veld
websiteconfigurator.field.field_type_option: Veldoptie
websiteconfigurator.field.actions: Opties

websiteconfigurator.config.active: Actief
websiteconfigurator.config.base_config: Basisconfiguratie
websiteconfigurator.config.enabled: Permanent aan
websiteconfigurator.config.enabled_day: Alleen op dag
websiteconfigurator.config.enabled_date_start: Vanaf datum en tijd
websiteconfigurator.config.enabled_date_stop: Tot en met datum en tijd
websiteconfigurator.config.name: Naam
websiteconfigurator.config.fields_and_values: Velden binnen deze configuratie
websiteconfigurator.config.actions: Opties
websiteconfigurator.config.edit: Aanpassen
websiteconfigurator.config.clone: Dupliceren
websiteconfigurator.config.delete: Verwijderen
websiteconfigurator.config.clone.success: Dupliceren van configuratie is gelukt
websiteconfigurator.config.clone.failed: Fout in dupliceren van configuratie, probeer het opnieuw

orderswap.form.filter: Filteren

coupons.generate.success: De cadeaubonnen zijn succesvol gegenereerd
coupons.generate.amount: Aantal
coupons.generate.prefix: Barcode prefix (voorvoegsel)
coupons.generate.suffix: Barcode suffix (achtervoegsel)
coupons.generate.download_csv_export: Csv export downloaden?
coupons.generate.generate: Genereren
coupons.delete.confirm: Weet je zeker dat je deze cadeaubon wilt verwijderen?
coupons.delete.success: De cadeaubon is verwijderd
coupons.delete.error_finished_order_linked: Deze cadeaubon mag niet worden verwijderd, omdat de gekoppelde order afgerond is.
coupons.add.barcode: Barcode
coupons.add.value: Waarde
coupons.add.save: Opslaan
coupons.add.error: Er is iets fout gegaan, probeer het opnieuw
coupons.add.values_error: Onjuiste waarden ingevuld voor het toevoegen van deze cadeaubon, probeer het opnieuw
coupons.add.duplicate_barcode_error: Deze barcode is al in gebruik, probeer het opnieuw
coupons.add.success: De cadeaubon is succesvol toegevoegd
coupons.assign.barcode: Barcode
coupons.assign.value: Waarde
coupons.assign.save: Opslaan
coupons.assign.ordernumber: Ordernummer
coupons.assign.success: De cadeaubon is succesvol uitgegeven
coupons.assign.stock_exists: Deze cadeaubon is al uitgegeven
coupons.assign.order_not_found: Geen order gevonden voor ordernummer
coupons.edit.success: De cadeaubon is succesvol aangepast
coupons.edit.not_issued: Deze cadeaubon is nog niet uitgegeven
coupons.edit.error_finished_order_linked: Deze cadeaubon mag niet meer worden aangepast, omdat de gekoppelde order afgerond is.
coupons.export_open.from.label: Van
coupons.export_open.to.label: Tot en met
coupons.export_open.title: Cadeaubonnen - Export openstaand
coupons.export_open.back: Terug naar overzicht
coupons.export_open.export: Exporteer

parking.index.header1: Parkeer map overzicht
parking.index.button_add: Parkeer map toevoegen
parking.index.table.th.pos: Pos
parking.index.table.th.description: Omschrijving
parking.index.table.th.shipment_method: Verzendwijze
parking.index.table.th.supervak: Supervak (mag niet worden hernoemd)
parking.index.table.th.vretend: Vretend
parking.index.table.th.active: Actief
parking.index.table.th.allowParking: Parkeren toestaan
parking.index.table.th.allowBackorderCheck: Backorder check uitvoeren
parking.index.table.th.allowOrdersToZoHo: Meenemen in Open Orders (ZoHo)
parking.index.table.th.nonPushingOriginStock: Niet meenemen in voorraad berekening voor locatie
parking.index.table.th.actions: Acties
parking.edit.header1: Parkeer map bewerken
parking.add.header1: Parkeer map toevoegen

orderparker.not_parked.stock_available: Order niet geparkeerd omdat er nu wel voorraad is maar tijdens bestellen niet

kit_split_order.save_order.title: Splitten van kit bestelling
kit_split_order.save_order.success_message: Kit split order instellingen succesvol opgeslagen!
kit_split_order.save_order.order_quantity: Hoeveel kit split bestellingen wil je doen?
kit_split_order.save_order.mail_to: Bij binnenkomst mail versturen naar
kit_split_order.scan_order.title: Scannen van kit split bestelling
kit_split_order.scan_order.success_message: Kit split order regels succesvol opgeslagen!
kit_split_order.scan_order.order_quantity: Aantal bestellingen
kit_split_order.split_stock.title: Splitten van kit voorraad item
kit_split_order.split_stock.success_message: Kit Voorraad item succesvol gesplit!
kit_split_order.table_caption: Inhoud van de kit
kit_split_order.order_index: Ordernummer
kit_split_order.product_quantity: Aantal
kit_split_order.product_number: Artikelnummer
kit_split_order.product_name: Naam
kit_split_order.product_ean: EAN
kit_split_order.purchase_price: Inkoopprijs
kit_split_order.barcode: Barcode
kit_split_order.save_button_text: Opslaan
kit_split_order.stock_memo_text: Kit split bestelling
kit_split_order.stock_info_text: Toegevoegd via kit split bestelling
kit_split_stock.stock_info_text: Toegevoegd via het splitten van kit voorraad
kit_split_order.error.product_not_a_kit: Dit artikel is geen kit!
kit_split_order.error.supplier_not_able_to_split: Bij deze leverancier mag je geen kit orders splitsen!
kit_split_order.error.stock_has_date_out: Dit voorraad item is niet meer beschikbaar!
kit_split_order.error.no_split_rows_found: Geen split order regels kunnen vinden, probeer het opnieuw!
kit_split_order.error.something_went_wrong: Whoops! er is iets mis gegaan tijdens het opslaan... probeer het opnieuw.
kit_split_order.error.check_barcode_results: Bekijk het resultaat en vul de lege barcodes opnieuw in.
kit_split_order.error.no_transit: Er is geen geldige transitbak gekozen.
kit_split_order.save_order.remark: Opmerking

stockanalysis.overview.page_title: Voorraadoverzicht
stockanalysis.overview.form.referencedate: Voorraadwaarde op
stockanalysis.overview.form.stocklocations: Voorraadlocaties
stockanalysis.overview.form.rentalproduct: Verhuur
stockanalysis.overview.form.rentalproduct.include: Inclusief verhuur producten
stockanalysis.overview.form.rentalproduct.exclude: Exclusief verhuur producten
stockanalysis.overview.form.rentalproduct.exclusive: Alleen verhuur producten

stocklog.search_logs.query: Voer een product ID, productnaam, voorraad ID of barcode in.
stocklog.search_logs.submit: Zoeken
stocklog.internal_invoice_type_rma: RMA
stocklog.internal_invoice_type_courier_list: koerierslijst
stocklog.internal_invoice_type_credit: credit
stocklog.internal_invoice_type_mixed_stock: klantorder
stocklog.internal_invoice_type_stock_transfer: voorraadverplaatsing

additional_contribution.list.fact_tooltip: Dit veld is dynamisch; als er een factuur verstuurd moet worden is dit het factuurnummer van de aangemaakte order, als het een ontvangen credit betreft dan wordt dit nr getoond

additional_contribution.form.type: Type
additional_contribution.form.submit: Opslaan
additional_contribution.form.date: Datum in
additional_contribution.form.period_start: Periode start
additional_contribution.form.period_end: Periode eind
additional_contribution.form.supplier: Klant/leverancier (Zoekveld)
additional_contribution.form.customer_number: Klantnummer
additional_contribution.form.fl_margin: FL Marge
additional_contribution.form.fl_margin_sub: FL Marge subcategorie
additional_contribution.form.in_case_receive: Indien ontvangst
additional_contribution.form.invoice_number: Factuurnummer
additional_contribution.form.description: Omschrijving
additional_contribution.form.po_number: PO nummer
additional_contribution.form.po_unknown: PO nummer onbekend
additional_contribution.form.amount_ex: Bedrag Ex. BTW
additional_contribution.form.additional_contribution_group: Valt onder (groep)
additional_contribution.form.group_name: Groepsnaam
additional_contribution.form.supplier_agreement: Leverancier akkoord
additional_contribution.form.success: 'De bijdrage is succesvol aangemaakt (Order: %s)'
additional_contribution.form.success_edit: 'De bijdrage is succesvol bijgewerkt (Order: %s)'
additional_contribution.form.error: Er is een fout opgetreden (%s)
additional_contribution.form.error.new_invoice: De nieuwe factuur bevat meer dan één product.
additional_contribution.form.error.invoice_not_found: Factuur met nr %s kon niet worden gevonden
additional_contribution.form.error.customer_not_found: Er kon geen klant worden gevonden
additional_contribution.form.error.cant_finish_without_invoice: Je kan de bijdrage niet afronden zonder een factuurnr in te voeren

additional_contribution.delete.confirm: Weet je zeker dat je deze bijdrage wilt verwijderen?
additional_contribution.delete.succes: De bijdrage is verwijderd

additional_contribution.rights.insufficient: Je hebt niet de voldoende rechten om deze actie uit te kunnen voeren
additional_contribution.rights.finished_order: Deze regel is al afgerond en mag niet meer worden gewijzigd
additional_contribution.rights.order_finished: Afgeronde regels kunnen niet worden verwijderd

additional_contribution.export.submit: Exporteer

additional_contribution.legend.unfinished_reception: Nog niet afgeronde (geen factuurnr) bijdrage van type ontvangst
additional_contribution.legend.unfinished_contribution: Nog niet afgeronde bijdrage van elk ander type
additional_contribution.legend.finished: Afgeronde bijdrage

additional_contribution.order.created: Order aangemaakt vanuit additionele bijdrage tool

api.error.invalid_address: Ongeldig adres of adres niet gevonden
api.error.invalid_customer: Ongeldige klant of klant niet gevonden
api.error.invalid_order: Ongeldige bestelling of bestelling niet gevonden
api.error.invalid_origin: Ongeldige herkomst of herkomst niet gevonden
api.error.invalid_payment_method: Ongeldige betaalmethode of betaalmethode niet gevonden
api.error.invalid_shipping_method: Ongeldige verzendmethode of verzendmethode niet gevonden
api.error.invalid_user: Ongeldige gebruiker of gebruiker niet gevonden
api.error.invalid_parking: Ongeldige parkeermap of parkeermap niet gevonden
api.error.customer_non_unique_email_address: Het e-mailadres voor een nieuwe klant moet uniek zijn
api.error.purchase_order_failed: De inkooporder is niet verstuurd
api.error.invalid_purchase_price_for_product: Er is geen geldige inkoopprijs gevonden voor dit product
api.error.invalid_subgroup: Ongeldige subgroep of subgroep niet gevonden

api.order.frontend.create_message: Gebruiker maakte contantbon aan %s (%s)
api.order.frontend.for_second_hand: vanuit tweedehands tool

purchase_order_mail.base_text: Wilt u zo vriendelijk zijn de ontvangst van de order te bevestigen en de verwachte levertijden aan ons te mailen?
purchase_order_mail.greeting: Goedendag
purchase_order_mail.please_order: Gaarne bestellen
purchase_order_mail.questions: Indien u verdere vragen heeft, neemt u dan gerust contact met ons op
purchase_order_mail.reference: Referentie
purchase_order_mail.amount: Aantal
purchase_order_mail.product.id: Id
purchase_order_mail.product: Artikel
purchase_order_mail.product_code: Artikelnummer
purchase_order_mail.price: Prijs
purchase_order_mail.out_of_stock.base_text: Deze producten konden wij niet via de api bestellen

subgroup.edit_wex_group.form.label.sub: Wex subgroep
subgroup.edit_wex_group.form.label.main: Wex hoofdgroep
subgroup.edit_wex_group.form.save: Opslaan
subgroup.edit_wex_Group.form.saved: Wijziging opgeslagen
subgroup.edit_wex_Group.form.error: Er is een fout opgetreden (%s)

customer.segment.title: Klantsegmenten
customer.segment.info.level_limit: Bestaande segmenten kunnen alleen binnen hun eigen niveau worden verplaatst. Een segment direct onder een hoofdsegment kan bijvoorbeeld dus alleen naar een ander hoofdsegment worden verplaatst.
customer.segment.info.delete: Bij het verwijderen van een segment worden alle onderliggende segmenten ook verwijderd. Klanten gelinkt aan het segment of onderliggende segmenten hebben dan geen segment meer.
customer.segment.form.name_nl.label: Naam (NL)
customer.segment.form.name_en.label: Naam (EN)
customer.segment.form.parent_segment.label: Is subgroep van
customer.segment.form.parent_segment.placeholder: Geen, maak aan als hoofdsegment
customer.segment.form.cancel: Wijziging annuleren
customer.segment.form.new: Nieuw segment
customer.segment.form.update: Segment wijzigen
customer.segment.saved: Segment succesvol opgeslagen
customer.segment.deleted: Segment succesvol verwijderd
customer.segment.error.max_level_exceeded: Segmenten mogen maximaal %s niveau(s) diep

discountset.form.name: Naam combodeal
discountset.form.has_price_range: Hoofdproduct moet binnen prijs bereik liggen
discountset.form.price_range_min: Minimale prijs
discountset.form.price_range_max: Maximale prijs
discountset.discount.0: Korting % bij één product
discountset.discount.1: Korting % bij twee producten
discountset.discount.2: Korting % bij drie producten
discountset.discount.3: Korting % bij vier producten
discountset.position.0: Positie één
discountset.position.1: Positie twee
discountset.position.2: Positie drie
discountset.position.3: Positie vier
discountset.brand.0: Merk 1
discountset.brand.1: Merk 2

claims_and_fees:
    page_title: Claims en Vergoedingen
    report:
        overlap_title: Er is overlap met de volgende rapportage(s)
        form:
            error:
                no_claims: Kon geen claims vinden voor deze rapportage.
                no_report: Kon geen rapportage maken voor deze claims.
                overlap: Er zijn claims die al een rapportage hebben in de opgegeven periode.
            success: Claim en vergoeding rapportage toegevoegd.
afas:
    export_overview:
        title: AFAS - overzicht van exports
        invoice_table_title: Problemen bij exporteren van facturen
        debtor_table_title: Problemen bij exporteren van debiteuren
        view_export_upcoming: Alle aankomende exports bekijken
        periodic_check: Periodieke controle
        view_invoice_problems: Problemen bij exporteren van facturen
        view_debtor_problems: Problemen bij exporteren van debiteuren
        requeue_all: Alle fouten opnieuw indienen
    export_upcoming:
        title: AFAS - aankomende exports
        view_export_overview: Terug naar foutmeldingen
    table:
        order_id: Order ID
        customer: Klant
        customer_number: Debiteurnummer
        invoice_number: Factuurnummer
        invoice_date: Factuurdatum
        error_message: Foutmelding
        origin: Herkomst
        actions: Actie(s)
        no_problems_found: Er zijn geen problemen gevonden
        orders_empty: Alle orders staan in AFAS
        search_debtor_error: zoek klantfoutmelding
    form:
        period_month: Periode maand
        period_year: Periode jaar
        administration: Administratie
        compare: Vergelijken

package_label:
    title:
        overview: Verzendlabels overzicht
        create: Verzendlabel toevoegen
    table:
        address: Adres
        transporter: Transporteur
        created_info: Aanmaak gegevens
        track_and_trace: Track&Trace
        notes: Notitie
        actions: Actie(s)
        nothing_found: Geen verzendlabels gevonden
    button:
        add_label: Label toevoegen
        print_label: Label printen
        download_label_in_pdf: Label openen in PDF
        return_to_overview: Terug naar overzicht
    form:
        transporter: Transporteur
        company: Bedrijfsnaam
        name: Volledige naam
        street: Straat
        house_number: Huisnummer
        house_number_addition: Toevoeging
        postal_code: Postcode
        residence: Plaats
        country: Land
        notes: Notities
        save: Opslaan
    state:
        label_added_and_printed: Verzendlabel toegevoegd en uitgeprint!
        label_printed: Verzendlabel uitgeprint!


core:
  button:
    back: Terug
  table:
    name: Naam
    quality: Kwaliteit
    stock: Voorraad
    price: Prijs
    visibility: Zichtbaarheid
    accessories: Accessoires
    performance_targets: Prestatietargets
    actions: Acties

product:
  search:
    no_results: Er zijn geen resultaten gevonden.

second_hand:
  page_title: Tweedehands
  search:
    no_results: Er zijn geen resultaten gevonden. Pas je zoekopdracht aan of voeg een nieuw product toe.
    new_product: Product aanmaken
  btn:
    show:
      new:
        stock: Nieuwe voorraad
        products: Nieuwe producten
    edit:
      accessories: Accessoires instellen
      prices: Prijzen overzicht
    delete:
      stock: Annuleren
    move:
      stock: Verplaats producten naar deze inname
    add:
      order: Toevoegen aan order
    filter: Filteren
    take_stock: Voorraad innemen
    info: Informatie
  take_stock:
    title: Voorraad innemen
    cart:
      current:
        title: Producten in deze inname
      open:
        title: Lopende inname
    form:
      btn:
        take: Innemen
      label:
        quality: Wat is de kwaliteit van het product
        quality_explanation: Verklaring voor gekozen kwaliteit (deze verklaring krijgt de klant te zien)
        location: Inname locatie
        vat_state: BTW status
        type: Soort
        serial_number: Serienummer
        accessories: Accessoires
      placeholder:
        choose_location: Kies inname locatie
        quality: Kies kwaliteit
      option:
        vat:
          margin: Particulier
          cnu: Zakelijk
    success: Voorraad ingenomen voor %s
  create:
    page_title: Nieuw product
  edit:
    page_title: Product aanpassen
  return_confirm: Weet je zeker dat je terug wilt keren? Hiermee gaan alle gegevens verloren
  form:
    product_finder_title: Product gebaseerd op
    already_exists: Dit product heeft al een tweedehands product!
    already_exists_confirm: Druk op OK om toch door te gaan
    delete_connection: Koppeling verwijderen
    title: Product gegevens
    name: Productnaam
    price: Prijs (indien een 10)
    original_product: Origineel product
    accessories: Accessoires
    to_dashboard: Dashboard taak aanmaken
    save: Opslaan
  product:
    child_products: Onderliggende producten
    no_products_found: Geen onderliggende producten gevonden
    messages:
      creation_failed: Tweedehands product niet kunnen toevoegen, probeer het nogmaals
      updated: Tweedehands product succesvol opgeslagen
  product_accessory:
    no_accessories_found: Geen accessoires gevonden voor dit product
    delete_confirm: Weet je zeker dat je deze accessoire wilt verwijderen?
    input:
      new:
        placeholder: Vul de naam in van de accessoire om deze toe te voegen
    messages:
      created: Accessoire %s is toegevoegd
      creation_failed: Accessoire niet kunnen toevoegen, probeer het nogmaals
      removed: Accessoire %s is verwijderd

  order:
    log:
      intake:
        product: 'Artikel: %s  ingenomen vanuit tweedehands tool'
        accessory: 'Accessoire: %s  ingenomen vanuit tweedehands tool'
  new_stock_list:
    pagination:
      results: Resultaten
      limit:
        prefix: Laat er
        suffix: Zien
    accept_stock:
      error:
        no_parent_found: 'Kon geen hoofd locatie vinden voor: %s'
      success: 'Voorraad verplaats naar: %s'
  table:
    item:
      date_in: Datum ingenomen
      date_created: Aangemaakt
      date_changed: Laatst aangepast
      product: Product
      barcode: Barcode
      location: Locatie
      quality: Kwaliteit
      intake_price: Inname prijs
      intake_formula: Inname formule
      sales_price: Verkoop prijs
      sales_formula: Verkoop formule
      intakeable: Innemen?
      accessories:
        title: Accessoires
        delivery:
          delivered: meegeleverd
          relation: gekoppeld aan product of profiel
          not_delivered: niet meegeleverd
      original_product_id: Origineel artikelnummer
      new_product_id: Nieuw artikelnummer
      price_difference: Prijsverschil
      serialNumber: Serienummer
      date: Datum
      user: Gebruiker

    btn:
      accept: Goedkeuren
  action_list:
      title: Acties overzicht
      create:
          title: Nieuwe actie aanmaken
      edit:
          title: Actie aanpassen
      btn:
          new: Nieuwe Actie
      form:
          description: Omschrijving
          percentage: Percentage
          start_date: Startdatum
          end_date: Einddatum
  price_list:
    title: Prijzen overzicht
    sub-title: Laatst aangepast op %s
    btn:
      save: Prijswijzigingen opslaan
      save_generic_formula: Aanpassen
    filter:
      brand: Merk
      product_type: Type product
  exception:
    intake_product_not_found: Er is geen product gevonden voor deze kwaliteit
    intake_price_not_found: Er is geen (geldige) inname prijs gevonden voor deze kwaliteit
    something_went_wrong: Er is iets misgegaan
    price_list_warning: Let op! je past de inname / verkoop formule aan. Deze prijsverandering heeft invloed op alle producten.
  request_review:
    questions: Vragen
    customer_answers: Antwoorden klant

main_group:
  name: Productgroep
  accessory:
    name: Accessoire
    page_title: Accessoires overzicht
    page_edit:
      title: Accessoires bewerken
      sub-title: Laatst aangepast op %s
    edit_warning: Let op! Je past accessoires aan binnen "%s". Deze verandering heeft invloed op alle producten binnen deze groep.
    subtitle: Stel hier de standaard accessoires in per hoofdgroep. Bij verandering van accessoires binnen een groep, verandert dit ook per product binnen de groep.
    no_main_groups_found: Geen hoofdgroepen gevonden
    no_main_group_accessories_found: Geen accessoires gevonden voor deze groep
    delete_confirm: Weet je zeker dat je deze accessoire wilt verwijderen?
    input:
      placeholder:
        name: Vul de naam in van de accessoire om deze toe te voegen
        price: Vul de prijs in van de accessoire
    messages:
      created: Accessoire %s is toegevoegd
      creation_failed: Accessoire niet kunnen toevoegen, probeer het nogmaals
      removed: Accessoire %s is verwijderd
  performance_target:
    name: Prestatietarget
    target: Target
    page_title: Prestatietargets overzicht
    page_edit:
      title: Prestatietargets bewerken
      sub-title: Laatst aangepast op %s
    edit_warning: Let op! Je past prestatietargets aan binnen "%s".
    subtitle: Stel hier de standaard prestatietargets in per hoofdgroep.
    no_main_groups_found: Geen hoofdgroepen gevonden
    no_main_group_performance_targets_found: Geen prestatietargets gevonden voor deze groep


minimum_stock:
    tab:
        all: All
    save_purchase_orders: Bestellingen opslaan
    save_minimum_stock: Min. vrd opslaan

analytics_page_types:
    save: Opslaan
    confirm_delete: Weet je zeker dat je dit type wilt verwijderen?
    type: Type
    results: Resultaten
    overview: Analytics page types
    add: Nieuw
    edit: Wijzigen

pick_list:
    title:
        orders: Order picklijsten
        courier_lists: Koerier picklijsten
        available_lists: Klaar om te picken
        in_progress_lists: In behandeling
        cart_chooser: Kies karren of rolcontainer
        scanned_cleanup: P-producten opruimen
        pick: Voorraad picken
        move: Voorraad verplaatsen
        move_stock: Voorraad verplaatsen tussen locaties
        list_stock: Oude P-voorraad
    button:
        combine: Karren kiezen
        pick: Picken
        finish: Klaar met picken
        problem: Probleem
        down: Naar beneden
        move: Voorraad verplaatsen
        refresh: Ververs
    search:
        location: Scan een plank of bak
        product: Scan een barcode of serienummer
        new_location: Nieuwe locatie
    result:
        location_not_found: Locatie niet gevonden
        product_not_found: Product niet gevonden
        product_only_one_location: Product heeft maar 1 locatie
        move_same_location: Van- en naarlocatie moet verschillen
        move_success: Voorraad succesvol verplaatst
        move_failed: Voorraad verplaatsen mislukt
    error:
        not_current_owner: Deze lijst is al opgepakt door %s

ai_assistants:
    title:
        list: Ai-assistenten
        edit: Aanpassen
    table:
        name: Naam
        short_description: Korte omschrijving
        model: Model
        prompt: Prompt
        date: Datum
        user: Gebruiker
        field: Veld
        edit: Aanpassen
    form:
        product_id: Artikelnummer
        save: Opslaan
        success: Assistent succesvol aangepast
    modal:
        close: Sluiten

faq:
    title:
        list: FAQ's
        edit: FAQ aanpassen
        create: FAQ toevoegen
    table:
        active: Actief
        specs_profile: Specificatieprofiel
        question: Vraag
        edit: Aanpassen
        delete: Verwijderen
    form:
        save: Opslaan
        test: Testen
        success: FAQ succesvol aangepast
        removed: FAQ succesvol verwijderd

stock_snapshot:
    title:
        movement: Verplaatsingscalculaties
    form:
        dateCalculated: Datum calculatie
        difference: Verschil
        parent: Entiteit
        rootGroup: Rootgroep
        mainGroup: Hoofdgroep
        subGroup: Subgroep
