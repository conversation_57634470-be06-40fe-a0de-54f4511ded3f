<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: \Webdsign\GlobalBundle\Entity\PowerReviewRepository::class)]
#[ORM\Table(name: 'cameranu.powerreviews')]
class PowerReview
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private int $id;
    #[ORM\ManyToOne(targetEntity: Product::class)]
    #[ORM\JoinColumn(name: 'product_id', referencedColumnName: 'id', nullable: false)]
    private Product $product;
    #[ORM\Column(name: 'amount', type: 'integer', nullable: false)]
    private int $amount;
    #[ORM\Column(name: 'rating', type: 'decimal', precision: 7, scale: 2, nullable: false)]
    private float $rating;

    public function getId(): int
    {
        return $this->id;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function setProduct(Product $product): PowerReview
    {
        $this->product = $product;
        return $this;
    }

    public function getAmount(): int
    {
        return $this->amount;
    }

    public function setAmount(int $amount): PowerReview
    {
        $this->amount = $amount;
        return $this;
    }

    public function getRating(): float
    {
        return $this->rating;
    }

    public function setRating(float $rating): PowerReview
    {
        $this->rating = $rating;
        return $this;
    }
}
