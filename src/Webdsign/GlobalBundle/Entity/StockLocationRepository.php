<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\NonUniqueResultException as NonUniqueResultExceptionAlias;
use Doctrine\Persistence\ManagerRegistry;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\StockLocation;

/**
 * @method StockLocation|null find($id, $lockMode = null, $lockVersion = null)
 * @method StockLocation|null findOneBy(array $criteria, array $orderBy = null)
 */
class StockLocationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, StockLocation::class);
    }

    /**
     * @param string $code
     * @return StockLocation|null
     */
    public function findOneByCode(string $code): ?StockLocation
    {
        return $this->findOneBy([
            'code' => $code,
        ]);
    }

    /**
     * @param array $stockLocationIds
     * @param array $order
     * @return StockLocation[]
     */
    public function findByIds(array $stockLocationIds, array $order = ['id' => 'ASC'], bool $excludeArchivedOrigins = false): array
    {
        $queryBuilder = $this->createQueryBuilder('sl', 'sl.id');
        $queryBuilder
            ->andWhere('sl.id IN (:stockLocationIds)')
            ->setParameter('stockLocationIds', array_values($stockLocationIds), Connection::PARAM_INT_ARRAY);

        if ($excludeArchivedOrigins === true) {
            $queryBuilder
                ->innerJoin('sl.origin', 'origin')
                ->andWhere('origin.status != :archived')
                ->setParameter('archived', Origin::STATUS_ARCHIVED);
        }

        foreach ($order as $column => $sort) {
            $queryBuilder->addOrderBy('sl.' . $column, $sort);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param Origin $origin
     * @return StockLocation|null
     */
    public function findOneByOrigin(Origin $origin): ?StockLocation
    {
        return $this->findOneBy([
            'parent' => $origin->getParent(),
        ]);
    }

    /**
     * @param Origin $origin
     * @return StockLocation|null
     * @throws NonUniqueResultExceptionAlias
     */
    public function findOneByOriginWhereContactInformationIsAvailable(Origin $origin): ?StockLocation
    {
        $queryBuilder = $this->createQueryBuilder('sl');
        $queryBuilder->innerJoin('sl.internalInvoiceStockLocationInformation', 'sli')
            ->where('sl.parent = :parent')
            ->setParameter('parent', $origin->getParent())
            ->setMaxResults(1);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @param StockLocation $stockLocation
     * @param bool $hasMinimalStock
     * @return array
     */
    public function findBySameParent(
        StockLocation $stockLocation,
        bool $hasMinimalStock = false,
        bool $excludeIgnoredInCalculation = false,
        bool $allowAllCameranuLocations = false
    ): array {
        $parent = $stockLocation->getParent();
        $criteria = ['parent' => $stockLocation->getParent()];

        // Work around om voor CameraNU niet alle locaties mee te nemen
        if ($parent === 'cameranu' && $allowAllCameranuLocations === false) {
            $criteria['availableForDispatch'] = 1;
        }

        if ($hasMinimalStock === true) {
            $criteria['hasMinimalStock'] = 1;
        }

        if ($excludeIgnoredInCalculation === true) {
            $criteria['ignoreInCalculation'] = 0;
        }

        return $this->findBy($criteria);
    }

    /**
     * @param StockLocation $stockLocation
     * @return array
     */
    public function findMainLocationForParent(StockLocation $stockLocation): array
    {
        $parent = $stockLocation->getParent();
        $criteria = [
            'parent' => $parent,
            'isMainLocation' => 1
        ];

        return $this->findBy($criteria);
    }

    public function findShopLocationForParent(StockLocation $stockLocation): ?StockLocation
    {
        $queryBuilder = $this->createQueryBuilder('sl');
        $queryBuilder
            ->where('sl.parent = :parent')
            ->andWhere($queryBuilder->expr()->isNotNull('sl.shop'))
            ->setParameter('parent', $stockLocation->getParent());

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findMainLocationForOrigin(Origin $origin): ?StockLocation
    {
        $parent = $origin->getParent();
        $criteria = [
            'parent' => $parent,
            'isMainLocation' => 1
        ];

        return $this->findOneBy($criteria);
    }

    /**
     * @param string $parent
     * @return array
     */
    public function findMainLocationsByParent(string $parent): array
    {
        $criteria = [
            'parent' => $parent,
            'isMainLocation' => 1
        ];

        return $this->findBy($criteria);
    }

    /**
     * @throws NonUniqueResultExceptionAlias
     */
    public function findBStockLocationForParent(StockLocation $stockLocation): ?StockLocation
    {
        $queryBuilder = $this->createQueryBuilder('sl');
        $queryBuilder
            ->where('sl.parent = :parent')
            ->andWhere('sl.id in (:ids)')
            ->setParameters([
                'parent' => $stockLocation->getParent(),
                'ids' => StockLocation::B_LOCATION_IDS,
            ]);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @param StockLocation $stockLocation
     * @return StockLocation
     */
    public function findCourierLocationForParent(StockLocation $stockLocation): StockLocation
    {
        $parent = $stockLocation->getParent();
        $criteria = [
            'parent' => $parent,
            'isCourierLocation' => 1
        ];

        return $this->findOneBy($criteria);
    }

    /**
     * @param array $exclude
     * @return array
     */
    public function findStores(array $exclude = []): array
    {
        $queryBuilder = $this->createQueryBuilder('sl');
        $queryBuilder
            ->where($queryBuilder->expr()->isNotNull('sl.shop'))
            ->andWhere($queryBuilder->expr()->notIN('sl.id', ':excludeIds'))
            ->setParameter('excludeIds', $exclude)
        ;

        return $queryBuilder->getQuery()->execute();
    }

    /**
     * @param array $exclude
     * @return array
     */
    public function findAllExcept(array $exclude): array
    {
        $queryBuilder = $this->createQueryBuilder('sl');
        $queryBuilder
            ->where($queryBuilder->expr()->notIn('sl.id', ':excludeIds'))
            ->setParameter('excludeIds', $exclude)
        ;

        return $queryBuilder->getQuery()->execute();
    }

    /**
     * @return array
     */
    public function findAllParents(): array
    {
        $queryBuilder = $this->createQueryBuilder('sl');
        $queryBuilder
            ->select('sl.parent')
            ->distinct()
        ;

        return $queryBuilder->getQuery()->execute();
    }

    public function findByCodeLetter(string $letter): array
    {
        $queryBuilder = $this->createQueryBuilder('sl');
        $queryBuilder
            ->where('sl.code LIKE :letter')
            ->setParameter('letter', '%' . $letter)
        ;

        return $queryBuilder->getQuery()->getResult();
    }
}
