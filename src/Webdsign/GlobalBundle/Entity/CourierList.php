<?php

namespace Webdsign\GlobalBundle\Entity;

use CatBundle\Service\CourierListBins;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Webdsign\GlobalBundle\Exception\InvalidCourierListStateException;
use Webdsign\GlobalBundle\Exception\InvalidCourierListTypeException;

#[ORM\Entity(repositoryClass: CourierListRepository::class)]
#[ORM\EntityListeners(['Webdsign\GlobalBundle\Listener\CourierListListener'])]
#[ORM\Table('cameranu.courier_lists')]
class CourierList
{
    public const STATE_OPEN = 'open';
    public const STATE_SCANNABLE = 'scannable';
    public const STATE_TRANSIT = 'transit';
    public const STATE_TRANSIT_ISSUE = 'transitIssue';
    public const STATE_DONE = 'done';
    public const STATE_BACKORDER = 'backorder';
    public const STATE_FORCE_CLOSED = 'forceClosed';
    public const STATE_CANCELLED = 'cancelled';
    public const TYPE_FRESH = 'fresh';
    public const TYPE_MINIMALSTOCK = 'minimalStock';
    public const TYPE_MOVEFORORDER = 'moveForOrder';
    public const TYPE_MOVEFORBACKORDER = 'moveForBackOrder';
    public const VALID_STATES
        = [
            self::STATE_OPEN,
            self::STATE_SCANNABLE,
            self::STATE_TRANSIT,
            self::STATE_TRANSIT_ISSUE,
            self::STATE_DONE,
            self::STATE_BACKORDER,
            self::STATE_FORCE_CLOSED,
            self::STATE_CANCELLED,
        ];
    public const VALID_TYPES
        = [
            self::TYPE_FRESH,
            self::TYPE_MINIMALSTOCK,
            self::TYPE_MOVEFORORDER,
            self::TYPE_MOVEFORBACKORDER,
        ];
    /**
     * @var int $id
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;
    /**
     * @var DateTime $created
     */
    #[ORM\Column(name: 'created', type: 'datetime')]
    private $created;
    /**
     * @var DateTime|null $updated
     */
    #[ORM\Column(name: 'updated', type: 'datetime')]
    private $updated;
    /**
     * @var User|null $creator
     */
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'createdBy', referencedColumnName: 'id')]
    private $creator;
    /**
     * @var User|null $updater
     */
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'stateChangedBy', referencedColumnName: 'id')]
    private $updater;
    /**
     * @var string $state
     */
    #[ORM\Column(name: 'state', type: 'string', length: 255)]
    private $state;
    #[ORM\ManyToOne(targetEntity: StockLocation::class)]
    #[ORM\JoinColumn(name: 'fromLocation', referencedColumnName: 'id')]
    private ?StockLocation $fromLocation = null;
    #[ORM\ManyToOne(targetEntity: StockLocation::class)]
    #[ORM\JoinColumn(name: 'toLocation', referencedColumnName: 'id')]
    private ?StockLocation $toLocation = null;
    /**
     * @var CourierListProduct[] $courierListProducts
     */
    #[ORM\OneToMany(mappedBy: 'courierList', targetEntity: CourierListProduct::class)]
    private $courierListProducts;
    /**
     * @var InternalInvoice[] $internalInvoices
     */
    #[ORM\OneToMany(mappedBy: 'courierList', targetEntity: InternalInvoice::class, fetch: 'LAZY')]
    private $internalInvoices;
    /**
     * @var string|null type
     */
    #[ORM\Column(name: 'type', type: 'string', length: 255)]
    private $type;
    /**
     * @var CourierListStatusLog[] $statusLogs
     */
    #[ORM\OneToMany(mappedBy: 'courierList', targetEntity: CourierListStatusLog::class)]
    private $statusLogs;

    /**
     * @var TransitCart $transitCart
     */
    #[ORM\OneToMany(mappedBy: 'courierList', targetEntity: TransitCart::class)]
    private $transitCart;

    /**
     * @var CourierListBins[] $courierListBins
     */
    #[ORM\ManyToMany(targetEntity: CourierListBin::class, mappedBy: 'courierLists')]
    private $courierListBins;

    #[ORM\OneToOne(mappedBy: 'courierList', targetEntity: ShipmentItem::class)]
    private ?ShipmentItem $shipmentItem = null;

    public function __construct()
    {
        $this->created = new DateTime();
        $this->courierListProducts = new ArrayCollection();
        $this->internalInvoices = new ArrayCollection();
        $this->statusLogs = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return DateTime
     */
    public function getCreated(): DateTime
    {
        return $this->created;
    }

    /**
     * @param DateTime $created
     * @return CourierList
     */
    public function setCreated(DateTime $created): CourierList
    {
        $this->created = $created;

        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getUpdated(): ?DateTime
    {
        return $this->updated;
    }

    /**
     * @param DateTime|null $updated
     * @return CourierList
     */
    public function setUpdated(?DateTime $updated): CourierList
    {
        $this->updated = $updated;

        return $this;
    }

    /**
     * @return User|null
     */
    public function getCreator(): ?User
    {
        return $this->creator;
    }

    /**
     * @param User|null $creator
     * @return CourierList
     */
    public function setCreator(?User $creator): CourierList
    {
        $this->creator = $creator;

        return $this;
    }

    /**
     * @return User|null
     */
    public function getUpdater(): ?User
    {
        return $this->updater;
    }

    /**
     * @param User|null $updater
     * @return CourierList
     */
    public function setUpdater(?User $updater): CourierList
    {
        $this->updater = $updater;

        return $this;
    }

    /**
     * @return string
     */
    public function getState(): string
    {
        return $this->state;
    }

    /**
     * @param string $state
     * @return CourierList
     * @throws InvalidCourierListStateException
     */
    public function setState(string $state): CourierList
    {
        if (!in_array($state, self::VALID_STATES, true)) {
            throw new InvalidCourierListStateException();
        }
        $this->state = $state;

        return $this;
    }

    public function getFromLocation(): ?StockLocation
    {
        return $this->fromLocation;
    }

    public function setFromLocation(StockLocation $fromLocation): CourierList
    {
        $this->fromLocation = $fromLocation;

        return $this;
    }

    public function getToLocation(): ?StockLocation
    {
        return $this->toLocation;
    }

    /**
     * @param StockLocation $toLocation
     * @return CourierList
     */
    public function setToLocation(StockLocation $toLocation): CourierList
    {
        $this->toLocation = $toLocation;

        return $this;
    }

    /**
     * @param string $type
     * @return CourierListProduct[]|Collection
     */
    public function getCourierListProducts(string $type = 'active'): Collection
    {
        $criteria = Criteria::create();

        if ($type !== 'all') {
            $criteria->where(Criteria::expr()->eq('deleted', $type === 'inactive' ? 1 : 0));
        }

        return $this->courierListProducts->matching($criteria);
    }

    /**
     * @param CourierListProduct[] $courierListProducts
     * @return CourierList
     */
    public function setCourierListProducts(array $courierListProducts): CourierList
    {
        $this->courierListProducts = $courierListProducts;

        return $this;
    }

    /**
     * @param CourierListProduct $product
     * @return CourierList
     */
    public function addProduct(CourierListProduct $product): CourierList
    {
        $this->courierListProducts->add($product);

        return $this;
    }

    /**
     * @return InternalInvoice[]
     */
    public function getInternalInvoices(): array
    {
        return $this->internalInvoices;
    }

    /**
     * @param InternalInvoice[] $internalInvoices
     * @return CourierList
     */
    public function setInternalInvoices(array $internalInvoices): CourierList
    {
        $this->internalInvoices = $internalInvoices;

        return $this;
    }

    /**
     * @param InternalInvoice $internalInvoice
     * @return CourierList
     */
    public function addInternalInvoice(InternalInvoice $internalInvoice): CourierList
    {
        $this->internalInvoices->add($internalInvoice);

        return $this;
    }

    /**
     * @return string|null
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * @param string|null $type
     * @return CourierList
     * @throws InvalidCourierListTypeException
     */
    public function setType(?string $type): CourierList
    {
        if (!in_array($type, self::VALID_TYPES, true)) {
            throw new InvalidCourierListTypeException();
        }
        $this->type = $type;

        return $this;
    }

    /**
     * @return CourierListStatusLog[]
     */
    public function getStatusLogs(): Collection
    {
        return $this->statusLogs;
    }

    /**
     * @param CourierListStatusLog[] $statusLogs
     * @return CourierList
     */
    public function setStatusLogs($statusLogs): CourierList
    {
        $this->statusLogs = $statusLogs;
        return $this;
    }

    /**
     * @return TransitCart
     */
    public function getTransitCart(): TransitCart
    {
        return $this->transitCart;
    }

    /**
     * @param TransitCart $transitCart
     * @return CourierList
     */
    public function setTransitCart(TransitCart $transitCart): CourierList
    {
        $this->transitCart = $transitCart;
        return $this;
    }

    /**
     * @return int
     */
    public function getTotalAmount(): int
    {
        $totalAmount = 0;
        $courierListProducts = $this->getCourierListProducts();

        foreach ($courierListProducts as $courierListProduct) {
            $totalAmount += $courierListProduct->getAmount();
        }

        return $totalAmount;
    }

    public function getTotalStock(): int
    {
        $totalStock = 0;

        foreach ($this->getCourierListProducts() as $courierListProduct) {
            $totalStock += $courierListProduct->countCourierListProductStock();
        }

        return $totalStock;
    }

    /**
     * @param CourierListBin $courierListBin
     * @return CourierList
     */
    public function addCourierListBins(CourierListBin $courierListBin): CourierList
    {
        $courierListBin->addCourierList($this);
        $this->courierListBins[] = $courierListBin;

        return $this;
    }

    /**
     * @return Collection
     */
    public function getCourierListBins(): Collection
    {
        return $this->courierListBins;
    }

    /**
     * @return bool
     */
    public function hasMissingStock(): bool
    {
        $locations = [
            $this->getToLocation()->getId(),
            $this->getFromLocation()->getId(),
            StockLocation::SEARCH_LOCATIONS[$this->getToLocation()->getParent()],
            StockLocation::SEARCH_LOCATIONS[$this->getFromLocation()->getParent()],
        ];

        foreach ($this->getCourierListProducts() as $courierListProduct) {
            foreach ($courierListProduct->getCourierListProductStock() as $courierListProductStock) {
                if ($courierListProductStock->getReceived() !== null) {
                    continue;
                }

                $currentLocation = $courierListProductStock->getStock()->getLocation();
                if (in_array($currentLocation->getId(), $locations) === false) {
                    return true;
                }
            }
        }

        return false;
    }

    public function containsProductsWithOnlyCourierShipping(): bool
    {
        foreach ($this->getCourierListProducts() as $courierListProduct) {
            $onlyHasCourierShipping = $courierListProduct->getProduct()->onlyHasCourierShipping();
            if ($onlyHasCourierShipping === true) {
                return true;
            }
        }

        return false;
    }

    public function getShipmentItem(): ?ShipmentItem
    {
        return $this->shipmentItem;
    }
}
