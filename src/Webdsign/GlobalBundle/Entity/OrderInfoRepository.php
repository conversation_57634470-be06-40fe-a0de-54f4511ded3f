<?php

namespace Webdsign\GlobalBundle\Entity;

use CatBundle\Constant\ShippingMethod;
use CatBundle\Form\Filter\AdyenOrderCheckFilter;
use CatBundle\Service\Filter\AdyenScopeFilter;
use DateTime;
use DateTimeInterface;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Webdsign\GlobalBundle\DQL\UseIndexWalker;
use Webdsign\GlobalBundle\Entity\Event\Registration;
use Webdsign\GlobalBundle\Exception\InvalidPaymentMethodException;
use Webdsign\GlobalBundle\Exception\OrderInfoCustomerNotFoundException;
use Webdsign\GlobalBundle\Form\Filter\Api\ApiFilterInterface;
use Webdsign\GlobalBundle\Form\Filter\Api\OrderFilter;

/**
 * @method OrderInfo|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrderInfo|null findOneBy(array $criteria, array $orderBy = null)
 * @method OrderInfo[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderInfoRepository extends WebdsignRepository
{

    public function __construct(
        ManagerRegistry $registry,
        private readonly CartItemRepository $cartItemRepository,
        private readonly OriginRepository $orderOriginRepository,
        private readonly CustomerRepository $customerRepository,
    ) {
        parent::__construct($registry, OrderInfo::class);
    }

    public function save(OrderInfo $entity): void
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();
    }

    /**
     * Haal alle orders op voor een klant
     *
     * @param int $id
     * @return array
     */
    public function getOrdersWithShopcartProductsByCustomer(int $id): array
    {
        $queryBuilder = $this->getQueryBuilder();
        $queryBuilder->from(OrderInfo::class, 'o')->select('o');
        $queryBuilder->andWhere('o.customerid = :id')->setParameter('id', $id);
        $queryBuilder->addSelect('c')->join('o.cartitems', 'c');
        $queryBuilder->addSelect('p')->join('c.product', 'p');
        return $queryBuilder
            ->getQuery()
            ->setHint(Query::HINT_CUSTOM_OUTPUT_WALKER, UseIndexWalker::class)
            ->setHint(UseIndexWalker::HINT_USE_INDEX, 'klantnummer')
            ->getResult();
    }

    /**
     * Haal order op met een ordernummer/id
     *
     * @param int $id
     * @return mixed
     * @throws NonUniqueResultException
     */
    public function getOrdersByNumber($id)
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder->where('o.ordernr = :id')
            ->orWhere('o.invoiceNumber = :id')
            ->orWhere('o.id = :id')
            ->setParameter('id', $id);

        return $queryBuilder
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function getInfoForSavingAfasInvoices(array $invoiceNumbers): array
    {
        $queryBuilder = $this->createQueryBuilder('o');
        return $queryBuilder
            ->select('o.id', 'o.invoiceNumber', 'IDENTITY(o.origin) as originId')
            ->where('o.invoiceNumber IN (:invoiceNumbers)')
            ->setParameter('invoiceNumbers', $invoiceNumbers)
            ->getQuery()
            ->getArrayResult();
    }

    /**
     * @param Customer $customer
     * @param int $orderId
     * @return OrderInfo|null
     * @throws NonUniqueResultException
     */
    public function getByCustomerAndOrder(Customer $customer, int $orderId): ?OrderInfo
    {
        return $this->createQueryBuilder('o')
            ->where('o.ordernr = :id')
            ->orWhere('o.invoiceNumber = :id')
            ->orWhere('o.id = :id')
            ->andWhere('o.customer = :customer')
            ->setParameter('id', $orderId)
            ->setParameter('customer', $customer)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Haal alleen actieve orders op
     *
     * @return $this
     */
    public function activeOrder(): self
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        $queryBuilder->andWhere('BIT_AND(oi.flags, 4) = 4');
        return $this;
    }

    /**
     * Haal orders op tussen een bepaalde datum
     *
     * @param bool|FALSE $startDate
     * @param bool|FALSE $endDate
     * @return $this
     */
    public function setDate($startDate = false, $endDate = false): self
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        if (false !== $startDate) {
            $queryBuilder->andWhere('oi.orderDate >= :startdate')->setParameter(':startdate', $startDate);
        }
        if (false !== $endDate) {
            $queryBuilder->andWhere('oi.orderDate <= :endDate')->setParameter(':endDate', $endDate);
        }
        return $this;
    }

    /**
     * Haal orders op die op een bepaalde plek besteld zijn
     *
     * @param string $origin
     * @return $this
     */
    public function setOrigin($origin): self
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        if ($origin === 'website') {
            $queryBuilder->addSelect('sm')->leftJoin('oi.shippingMethod', 'sm')->andWhere(
                'BIT_AND(sm.flags, 144) != 144'
            );
        } elseif ($origin === 'winkel') {
            $queryBuilder->addSelect('sm')->leftJoin('oi.shippingMethod', 'sm')->andWhere(
                'BIT_AND(sm.flags, 144) != 0'
            );
        }
        return $this;
    }

    /**
     * Haal orders op uit een rootgroep
     *
     * @param int $rootId
     * @return $this
     */
    public function fromRootgroep($rootId): self
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        if ($rootId) {
            $queryBuilder
                ->leftJoin('oi.cartitems', 'c')
                ->leftJoin('c.product', 'p')
                ->leftJoin('p.subgroup', 'sg')
                ->leftJoin('sg.maingroup', 'hg')
                ->leftJoin('hg.rootgroup', 'rg')
                ->andWhere('rg.id = :rootId')->setParameter(':rootId', $rootId);
        }
        return $this;
    }

    /*
     * haal gegevens op voor de pakketen overzicht/statistiek
     */
    public function getEpsOrders()
    {
        $queryBuilder = $this->getQueryBuilder('oi');

        $queryBuilder
            ->select('date_format(oi.orderDate, \'%Y%m\') AS month')
            ->addSelect('count(oi.id) AS cnt')
            ->leftJoin('oi.shippingMethod', 'v')
            ->andWhere('v.id in (4,21,74)')
            ->andWhere('BIT_AND(oi.flags, 4) = 4')
            ->groupBy('month')
            ->orderBy('month', 'DESC');

        return $this;
    }

    /**
     * Haal Order informatie op.
     *
     * @return array|Query
     */
    public function get()
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        if ($this->getPaginate() === true) {
            return $queryBuilder->getQuery();
        }

        return $queryBuilder
            ->getQuery()
            ->getResult();
    }

    /**
     * Used for communication with Robin HQ
     *
     * @param string $param
     * @return null|array
     * @throws NonUniqueResultException
     */
    public function getCustomersByEmailAddressOrPhoneNumber(string $param): ?array
    {
        if (false !== strpos($param, '@')) {
            $field = 'email';
        } else {
            $field = 'phonenr';
        }

        $queryBuilder = $this->getQueryBuilder('oi');
        $queryBuilder
            ->select([
                'oi.name',
                'oi.phonenr AS phone_number',
                'oi.email AS email_address',
                'MIN(oi.orderDate) AS customer_since',
                'SUM(op.amount) AS total_spent',
                'COUNT(oi.' . $field . ') AS order_count',
                'c.id AS customer_number',
                'c.customerType AS customer_type',
            ])
            ->join('oi.customer', 'c')
            ->leftJoin('oi.orderPayments', 'op')
            ->where('oi.' . $field . ' = :' . $field)
            ->setParameter($field, $param)
            ->groupBy('oi.' . $field);
        if ($field === 'phonenr') {
            $queryBuilder
                ->orWhere('oi.' . $field . ' = :partOne' . ucfirst($field))
                ->setParameter('partOne' . ucfirst($field), str_replace('+', '', $param))
                ->orWhere('oi.' . $field . ' = :partTwo' . ucfirst($field))
                ->setParameter('partTwo' . ucfirst($field), '0' . substr($param, -9));
        }
        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * Used for communication with Robin HQ
     *
     * @param string $emailAddress
     * @return array
     */
    public function getOrdersByEmailAddress(string $emailAddress): array
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        $queryBuilder
            ->select([
                'oi.id AS id',
                'oi.ordernr AS order_number',
                'oi.orderDate AS date',
                'CASE
                    WHEN BIT_AND(oi.flags, 4) = 4 THEN \'Afgehandeld\'
                    ELSE \'Open\'
                END AS status',
            ])
            ->where('oi.email = :email')
            ->setParameter('email', $emailAddress)
            ->orderBy('oi.ordernr', 'DESC');
        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * Used for communication with Robin HQ
     *
     * @param int $orderNumber
     * @return null|array
     * @throws NonUniqueResultException
     */
    public function getOrderDetailsByOrderNumber(int $orderNumber): ?array
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        $queryBuilder
            ->select([
                'oi.orderDate AS date',
                'CASE
                    WHEN BIT_AND(oi.flags, 4) = 4 THEN \'Afgehandeld\'
                    ELSE \'Open\'
                END AS status',
                'CASE
                    WHEN BIT_AND(oi.flags, 2) = 2 OR BIT_AND(oi.flags, 8) = 8 THEN \'Betaald\'
                    ELSE \'Betaling (nog) niet voldaan\'
                END AS payment_status
                ',
                'CASE
                    WHEN BIT_AND(v.flags, 1) = 1 THEN \'Verzonden\'
                    ELSE \'(Nog) niet verzonden\'
                END AS shipment_status
                ',
            ])
            ->join(Stock::class, 'v', 'WITH', 'oi.id = v.order')
            ->where('oi.ordernr = :orderNumber')
            ->setParameter('orderNumber', $orderNumber);
        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * Used for communication with Robin HQ
     *
     * @param string $emailAddress
     * @return array|null
     * @throws NonUniqueResultException
     */
    public function getCustomerLifetime(string $emailAddress): ?array
    {
        $queryBuilder = $this->getQueryBuilder('oi');
        $queryBuilder
            ->select([
                'oi.email AS email_address',
                'MIN(oi.orderDate) AS customer_since',
                'COUNT(oi.email) AS order_count',
                '\'EUR\' AS currency',
                'SUM(op.amount) AS total_revenue',
                'MAX(oi.orderDate) AS last_order_date',
            ])
            ->leftJoin('oi.orderPayments', 'op')
            ->where('oi.email = :email')
            ->setParameter('email', $emailAddress)
            ->groupBy('oi.email');
        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @param int $orderId
     * @return OrderInfo|null
     * @throws NonUniqueResultException
     */
    public function getOriginalOrderTotalByReturnOrder(int $orderId): ?OrderInfo
    {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select([
                'ordertickets.orderId'
            ])
            ->join(OrderRma::class, 'orderrma', 'WITH', 'orderrma.order = oi.id')
            ->join(Rma::class, 'rma', 'WITH', 'rma.id = orderrma.rma')
            ->join(OrderTickets::class, 'ordertickets', 'WITH', 'ordertickets.ticketId = rma.ticket')
            ->where('oi.id = :orderId')
            ->setParameter('orderId', $orderId);
        $originalOrder = $queryBuilder->getQuery()->getOneOrNullResult();

        if (empty($originalOrder) || $originalOrder['orderId'] === null) {
            return null;
        }

        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->where('oi.id = :orderId')
            ->setParameter('orderId', $originalOrder['orderId']);
        try {
            $orderInfo = $queryBuilder->getQuery()->getOneOrNullResult();
            if (!$orderInfo instanceof OrderInfo) {
                return null;
            }
        } catch (NonUniqueResultException $nure) {
            return null;
        }
        return $orderInfo;
    }

    /**
     * @param OrderInfo $order
     * @param UserInterface $user
     * @param Rma|null $rma
     * @return OrderInfo
     * @throws OptimisticLockException
     */
    public function createOrderFromOrder(OrderInfo $order, UserInterface $user, Rma $rma = null): OrderInfo
    {
        $entityManager = $this->getEntityManager();
        $originRepository = $entityManager->getRepository(Origin::class);

        /** @var Rma $rma */
        if ($order->isEventOrder() || in_array($order->getOrigin()->getParent(), ['vendiro', 'vendiro_cameratools'])) {
            $origin = $order->getOrigin();
        } else {
            $origin = $rma->getDropoffLocation()
                ? $originRepository->find($rma->getDropoffLocation())
                : $order->getOrigin();
        }

        $newOrder = new OrderInfo();
        $this->getEntityManager()->persist($newOrder);

        if (!$order->getPaymentMethod() instanceof PaymentMethod) {
            throw new InvalidPaymentMethodException();
        }

        $customer = $order->getCustomer();
        if (!$customer instanceof Customer) {
            throw new OrderInfoCustomerNotFoundException($order);
        }

        $newOrder->setCustomer($customer)
            ->setSex($order->getSex())
            ->setName($order->getName())
            ->setFirstName($order->getFirstName())
            ->setNameInsertion($order->getNameInsertion())
            ->setLastName($order->getLastName())
            ->setAddress($order->getAddress())
            ->setAddress2($order->getAddress2())
            ->setHousenr($order->getHousenr())
            ->setHousenrext($order->getHousenrext())
            ->setZipcode($order->getZipcode())
            ->setZipcode2($order->getZipcode2())
            ->setCity($order->getCity())
            ->setCountry($order->getCountry())
            ->setPhonenr($order->getPhonenr())
            ->setMobile($order->getMobile())
            ->setEmail($order->getEmail())
            ->setCompany($order->getCompany())
            ->setTaxNumber($order->getTaxNumber())
            ->setDeliveryName($order->getDeliveryName())
            ->setDeliveryFirstName($order->getDeliveryFirstName())
            ->setDeliveryNameInsertion($order->getDeliveryNameInsertion())
            ->setDeliveryLastName($order->getDeliveryLastName())
            ->setDeliveryAddress($order->getDeliveryAddress())
            ->setDeliveryAddress2($order->getDeliveryAddress2())
            ->setDeliveryHousenr($order->getDeliveryHousenr())
            ->setDeliveryHousenrext($order->getDeliveryHousenrext())
            ->setDeliveryZipcode($order->getDeliveryZipcode())
            ->setDeliveryZipcode2($order->getDeliveryZipcode2())
            ->setDeliveryCity($order->getDeliveryCity())
            ->setDeliveryCountry($order->getDeliveryCountry())
            ->setDeliveryPhonenr($order->getDeliveryPhonenr())
            ->setDeliveryCompany($order->getDeliveryCompany())
            ->setLatlong($order->getLatlong())
            ->setLanguage($order->getLanguage())
            ->setOrigin($origin)
            ->setOrderDate(date('Y-m-d'))
            ->setOrderTime(date('H:i:s'))
            ->setDateInvoice(new DateTime())
            ->setDateHandled(new DateTime())
            ->setShippingMethod($order->getShippingMethod())
            ->setPaymentMethod($order->getPaymentMethod())
            ->setParking($order->getParking())
            ->setHandledBy($user)
            ->setFlags(1)
            ->setCourierTime(new DateTime('00:00'))
            ->setVatPercentage($order->getVatPercentage())
            ->setIpNumber('')
            ->setPreventAutomaticCancel($order->getPreventAutomaticCancel())
            ->setIssuerId('');

        if ($rma?->isVatFree()) {
            $newOrder->setVatFree();
        }

        $rfmSegment = $customer->getCustomerRFMScore()?->getSegment();
        $newOrder->setRfmSegment($rfmSegment ?? 'Unknown');

        // Only set the payment period if the payment method is bank transfer
        if ($order->getPaymentMethod()->getId() === PaymentMethod::BANKTRANSFER) {
            $newOrder->setPaymentPeriod($customer->getPaymentPeriod());
        }

        if ($rma) {
            /** @var Parking $rmaParking */
            if (in_array($order->getOrigin()->getParent(), ['vendiro', 'vendiro_cameratools'])) {
                $rmaParking = $entityManager->getRepository(Parking::class)->find(1305); // 1305 = S,S&R Vendiro
            } else {
                $rmaParking = $entityManager->getRepository(Parking::class)->find(16); // 16 = Service, Schade & Retour
            }

            $newOrder->setParking($rmaParking);

            // koppelen order aan RMA
            $orderRma = new OrderRma();
            $orderRma->setOrder($newOrder);
            $orderRma->setRma($rma);
            $entityManager->persist($orderRma);
        }

        $newOrder->setReference('');

        if (!$order->isEventOrder()) {
            $orderParking = new OrderParking();
            $orderParking->setOrder($newOrder)
                ->setParking($newOrder->getParking());
            $newOrder->setOrderParking($orderParking);
            $entityManager->persist($orderParking);
        }

        /**
         * Create Order History
         */
        $orderHistory = new OrderHistory();
        $orderHistory
            ->setOrder($newOrder)
            ->setUser($user)
            ->setUserTeam($user->getUserTeam());
        $entityManager->persist($orderHistory);

        $entityManager->flush();
        return $newOrder;
    }

    /**
     * @param Customer $customer
     * @param CustomerAddress $address
     * @param ShipmentMethod $shippingMethod
     * @param PaymentMethod $paymentMethod
     * @param UserInterface $user
     * @param Origin $origin
     * @param Parking|null $parking
     * @param string $ourComment
     * @param string $customerComment
     * @return OrderInfo
     * @throws OptimisticLockException
     */
    public function createOrderForCustomer(
        Customer $customer,
        CustomerAddress $address,
        ShipmentMethod $shippingMethod,
        PaymentMethod $paymentMethod,
        UserInterface $user,
        Origin $origin,
        ?Parking $parking,
        string $ourComment = '',
        string $customerComment = '',
        string $vatPercentage = ''
    ): OrderInfo {
        $entityManager = $this->getEntityManager();
        $orderDate = new DateTime();

        $order = (new OrderInfo())
            ->setCustomer($customer)
            ->setTaxNumber($customer->getVatIdentificationNumber())
            ->setSex($customer->getSex())
            ->setFirstName($address->getFirstName())
            ->setLastName($address->getLastName())
            ->setNameInsertion($address->getLastNamePrefix())
            ->setName($address->getName())
            ->setCountry($address->getCountry())
            ->setCity($address->getCity())
            ->setAddress($address->getAddress())
            ->setAddress2('')
            ->setHousenr($address->getHousenr())
            ->setHousenrext($address->getHousenrext() ?? '')
            ->setZipcode($address->getZipcode())
            ->setZipcode2('')
            ->setCompany($address->getCompany() ?? '')
            ->setPhonenr($address->getPhonenr())
            ->setMobile('')
            ->setEmail($customer->getEmail())
            ->setDeliveryFirstName('')
            ->setDeliveryLastName('')
            ->setDeliveryNameInsertion('')
            ->setDeliveryName('')
            ->setDeliveryCountry('')
            ->setDeliveryCity('')
            ->setDeliveryAddress('')
            ->setDeliveryAddress2('')
            ->setDeliveryHousenr('')
            ->setDeliveryHousenrext('')
            ->setDeliveryZipcode('')
            ->setDeliveryZipcode2('')
            ->setDeliveryCompany('')
            ->setDeliveryPhonenr('')
            ->setOurComment($ourComment)
            ->setCustomerComment($customerComment)
            ->setShippingMethod($shippingMethod)
            ->setPaymentMethod($paymentMethod)
            ->setOrderDate($orderDate->format('Y-m-d'))
            ->setOrderTime($orderDate->format('H:i:s'))
            ->setDateInvoice($orderDate)
            ->setDateHandled($orderDate)
            ->setLatlong('')
            ->setFlags(OrderInfo::FLAG_WAITING_FOR_PAYMENT)
            ->setLanguage('nl')
            ->setHandledBy($user)
            ->setOrigin($origin)
            ->setCourierTime(new DateTime('00:00'))
            ->setReference('')
            ->setChecked(false)
            ->setVatPercentage($vatPercentage)
            ->setIpNumber('')
            ->setPreventAutomaticCancel($shippingMethod->getId() === 5)
            ->setIssuerId('');

        $rfmSegment = $customer->getCustomerRFMScore()?->getSegment();
        $order->setRfmSegment($rfmSegment ?? 'Unknown');

        // Only set the payment period if the payment method is bank transfer
        if ($paymentMethod->getId() === PaymentMethod::BANKTRANSFER) {
            $order->setPaymentPeriod($customer->getPaymentPeriod());
        }

        if ($vatPercentage === '0') {
            $order->setVatFree();
        }

        if ($parking instanceof Parking) {
            $order->setParking($parking);
            $entityManager->persist($order);

            $orderParking = new OrderParking();
            $orderParking->setOrder($order)
                ->setParking($parking);

            $order->setOrderParking($orderParking);
            $entityManager->persist($orderParking);
        } else {
            $entityManager->persist($order);
        }

        /**
         * Create Order History
         */
        $orderHistory = new OrderHistory();
        $orderHistory
            ->setOrder($order)
            ->setUser($user)
            ->setUserTeam($user->getUserTeam());
        $entityManager->persist($orderHistory);

        $entityManager->flush();

        return $order;
    }

    /**
     * @return OrderInfo[]
     */
    public function getOrderByNumber(string $searchValue): array
    {
        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder
            ->where('o.id = :number')
            ->orWhere('o.invoiceNumber = :number')
            ->orWhere('o.ordernr = :number')
            ->setParameter(':number', $searchValue);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param Parking $parking
     * @return array
     */
    public function findByParking(Parking $parking): array
    {
        $queryBuilder = $this->getQueryBuilder('o');
        $queryBuilder
            ->select('o')
            ->andWhere('o.parking = :parking')
            ->setParameter('parking', $parking);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param Parking $parking
     * @return OrderInfo[]
     */
    public function getOrdersByParking(Parking $parking): array
    {
        return $this->findBy([
            'parking' => $parking,
        ]);
    }

    /**
     * @param string $from
     * @param string|null $to
     * @return OrderInfo[]
     */
    public function getBetweenOrderDates(string $from, $to = null): array
    {
        $to = $to ?: $from;

        $queryBuilder = $this->getQueryBuilder('o');
        $queryBuilder
            ->select('o')
            ->andWhere($queryBuilder->expr()->between('o.orderDate', ':from', ':to'))
            ->setParameter('from', $from)
            ->setParameter('to', $to);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param bool $onlyWebsite
     * @param array $ipAddresses
     * @param array $origins
     * @return OrderInfo[]
     */
    public function getNotInAnalytics(bool $onlyWebsite = true, array $ipAddresses = [], array $origins = []): array
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->select('o')
            ->andWhere('o.origin IN (:origins)')
            ->andWhere('o.importedInAnalytics IS NULL OR o.importedInAnalytics = :zeroDate')
            ->andWhere('BIT_AND(o.flags, :paymentOkay) = :paymentOkay')
            ->setParameter('origins', $origins)
            ->setParameter('paymentOkay', OrderInfo::FLAG_PAYMENT_OKAY)
            ->setParameter('zeroDate', '0000-00-00 00:00:00')
        ;

        if ($onlyWebsite === true) {
            $ipAddresses[] = '';

            $queryBuilder
                ->andWhere('o.ipNumber NOT IN (:ipAdresses)')
                ->andWhere('o.domainOrigin IS NOT NULL')
                ->setParameter('ipAdresses', $ipAddresses);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param DateTime $fromDateTime
     * @param DateTime|null $toDateTime
     * @param Origin[]|null $origins
     * @param bool $checked
     *
     * @return array
     */
    public function getSimpleOrderForOriginBetweenOrderDates(
        DateTime $fromDateTime,
        DateTime $toDateTime = null,
        $origins = null,
        bool $checked = false
    ): array {
        $excludeParents = [
            'vendiro',
            'vendiro_cameratools',
        ];
        $toDateTime = $toDateTime ?: $fromDateTime;

        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select([
                'oi.id',
                'oi.orderDate',
                'oi.deliveryName',
                'oi.flags',
                'oi.checked',
                'c.name as customer',
                'a.name as assignee',
                'a.location as assignee_origin',
                'o.parent as origin_parent',
                'o.source as origin_source',
                'o.description as origin_description',
                's.id as shippingId',
            ])
            ->leftJoin('oi.customer', 'c')
            ->leftJoin('oi.handledBy', 'a')
            ->leftJoin('oi.origin', 'o')
            ->leftJoin('oi.shippingMethod', 's')
            ->andWhere($queryBuilder->expr()->between('oi.orderDate', ':from', ':to'))
            ->andWhere('o.parent NOT IN (:exclude)')
            ->andWhere('o.status = :active')
            ->andWhere('oi.checked = (:checked)')
            ->orderBy('oi.checked', 'ASC')
            ->addOrderBy('oi.orderDate', 'DESC')
            ->setParameter('from', $fromDateTime->format('Y-m-d'))
            ->setParameter('to', $toDateTime->format('Y-m-d'))
            ->setParameter('exclude', $excludeParents)
            ->setParameter('active', Origin::STATUS_ACTIVE)
            ->setParameter('checked', $checked);

        if ($origins !== null) {
            $queryBuilder
                ->andWhere('o.parent IN (:parents)')
                ->setParameter(
                    'parents',
                    $this->getOriginParents($origins)
                );
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param $origins
     * @return array
     */
    private function getOriginParents($origins): array
    {
        return $origins
            ->map(
                function (Origin $origin) {
                    return $origin->getParent();
                }
            )->getValues();
    }

    /**
     * @param AdyenOrderCheckFilter $filter
     * @param bool $returnQueryBuilder
     * @return QueryBuilder|OrderInfo[]
     */
    public function findByAdenOrderCheckFilter(AdyenOrderCheckFilter $filter, $returnQueryBuilder = false)
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->addSelect('o, ow, op, opt, ci')
            ->leftJoin('o.orderWait', 'ow')
            ->leftJoin('o.orderParking', 'op')
            ->leftJoin('o.orderPayments', 'opt')
            ->leftJoin('o.cartitems', 'ci')
            ->leftJoin('o.adyenBatchReports', 'abr');

        $states = $filter->getStates();

        if (is_array($states)) {
            $payedWithAdyen = in_array(AdyenOrderCheckFilter::STATE_PAYED_WITH_ADYEN, $states, true) ?
                AdyenOrderCheckFilter::STATE_PAYED_WITH_ADYEN : null;
            $goedhartOk = in_array(AdyenOrderCheckFilter::STATE_GOEDHART_OK, $states, true) ?
                AdyenOrderCheckFilter::STATE_GOEDHART_OK : null;
            $goedhartNotOk = in_array(AdyenOrderCheckFilter::STATE_GOEDHART_NOT_OK, $states, true) ?
                AdyenOrderCheckFilter::STATE_GOEDHART_NOT_OK : null;
            $notPayedWithAdyen = in_array(AdyenOrderCheckFilter::STATE_NOT_PAYED_WITH_ADYEN, $states, true) ?
                AdyenOrderCheckFilter::STATE_NOT_PAYED_WITH_ADYEN : null;
            $inBatch = in_array(AdyenOrderCheckFilter::STATE_IN_BATCH, $states, true) ?
                AdyenOrderCheckFilter::STATE_IN_BATCH : null;
            $notInBatch = in_array(AdyenOrderCheckFilter::STATE_NOT_IN_BATCH, $states, true) ?
                AdyenOrderCheckFilter::STATE_NOT_IN_BATCH : null;

            if ($payedWithAdyen !== null) {
                $queryBuilder->andWhere('opt.paymentType = :payedWithAdyen');
                $queryBuilder->setParameter('payedWithAdyen', AdyenOrderCheckFilter::ADYEN_PAYMENT_TYPE);
            }

            if ($goedhartOk !== null) {
                $queryBuilder->andWhere('BIT_AND(o.flags, :goedhartOk) = :goedhartOk');
                $queryBuilder->setParameter('goedhartOk', OrderInfo::FLAG_GOEDHART_OK);
            }

            if ($goedhartNotOk !== null) {
                $queryBuilder->andWhere('BIT_AND(o.flags, :goedhartOk) != :goedhartOk');
                $queryBuilder->setParameter('goedhartOk', OrderInfo::FLAG_GOEDHART_OK);
            }

            if ($notPayedWithAdyen !== null) {
                $queryBuilder->andWhere('opt.paymentType != :payedWithAdyen');
                $queryBuilder->setParameter('payedWithAdyen', AdyenOrderCheckFilter::ADYEN_PAYMENT_TYPE);
            }

            if ($inBatch !== null) {
                $queryBuilder->andWhere('abr.id IS NOT null');
            }

            if ($notInBatch !== null) {
                $queryBuilder->andWhere('abr.id IS null');
            }
        }

        if ($filter->getDateFrom() !== null) {
            $queryBuilder->andWhere('o.dateHandled >= :dateFrom');
            $queryBuilder->setParameter('dateFrom', $filter->getDateFrom());
        }

        if ($filter->getDateTo() !== null) {
            $queryBuilder->andWhere('o.dateHandled <= :dateTo');
            $queryBuilder->setParameter('dateTo', $filter->getDateTo());
        }

        if ($returnQueryBuilder === true) {
            return $queryBuilder;
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param DateTime|null $fromDate
     * @param DateTime|null $toDate
     * @return OrderInfo[]
     */
    public function findByNeedsNewPayment(?DateTime $fromDate = null, ?DateTime $toDate = null): array
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->addSelect('ci')
            ->leftJoin('o.cartitems', 'ci')
            ->leftJoin('o.origin', 'oo')
            ->leftJoin(OrderRma::class, 'orderrma', 'WITH', 'orderrma.order = o.id')
            ->andWhere('o.paymentMethod IN (:paymentMethod)')
            ->andWhere('o.shippingMethod != :cancelled')
            ->andWhere('BIT_AND(o.flags, :paymentComplete) = 0')
            ->andWhere('BIT_AND(o.flags, :orderComplete) = 0')
            ->andWhere('BIT_AND(o.flags, :paymentOkay) = 0')
            ->andWhere('BIT_AND(o.flags, :waitingForPayment) = :waitingForPayment')
            ->andWhere('oo.parent like :originParent')
            ->andWhere('oo.status = :active')
            ->andWhere('orderrma.order IS NULL')
            ->setParameter('paymentMethod', [PaymentMethod::ID_ADYEN, PaymentMethod::SPRAYPAY])
            ->setParameter('cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('paymentComplete', OrderInfo::FLAG_PAYMENT_COMPLETE)
            ->setParameter('orderComplete', OrderInfo::FLAG_ORDER_COMPLETE)
            ->setParameter('paymentOkay', OrderInfo::FLAG_PAYMENT_OKAY)
            ->setParameter('waitingForPayment', OrderInfo::FLAG_WAITING_FOR_PAYMENT)
            ->setParameter('originParent', 'cameranu' . '%')
            ->setParameter('active', Origin::STATUS_ACTIVE)
            ->groupBy('o.id');

        if ($fromDate instanceof DateTime && $toDate instanceof DateTime) {
            $queryBuilder
                ->andWhere(
                    '
                    UNIX_TIMESTAMP(CONCAT_WS(:separator, o.orderDate, o.orderTime)) > UNIX_TIMESTAMP(:fromTimestamp)'
                )
                ->andWhere(
                    '
                    UNIX_TIMESTAMP(CONCAT_WS(:separator, o.orderDate, o.orderTime)) <= UNIX_TIMESTAMP(:toTimestamp)'
                )
                ->setParameter('separator', ' ')
                ->setParameter('fromTimestamp', $fromDate->format('Y-m-d H:i:s'))
                ->setParameter('toTimestamp', $toDate->format('Y-m-d H:i:s'));
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param array $customerIds
     * @param null|DateTimeInterface $orderStartDate
     * @return array
     */
    public function getSegmentationDataForCustomers(
        array $customerIds,
        ?DateTimeInterface $orderStartDate = null
    ): array {
        $orderData = [];
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select('partial c.{id}')
            ->addSelect('partial oi.{id, orderDate, paymentCosts, paymentCostsEx, shippingCosts, shippingCostsEx}')
            ->addSelect('partial o.{id, source}')
            ->addSelect('partial coupons.{id}')
            ->addSelect('partial si.{id, purchasePrice}')
            ->addSelect('partial ci.{id, parentId, amount, price, priceEx, priceDiscount, status, tax}')
            ->addSelect('partial lf.{id, amount, discountCode}')
            ->join('oi.customer', 'c')
            ->join('oi.origin', 'o')
            ->leftJoin(
                'oi.coupons',
                'coupons',
                Query\Expr\Join::WITH,
                $queryBuilder->expr()->andX(
                    'BIT_AND(coupons.flags, :couponProcessedFlag) = :couponProcessedFlag'
                )
            )
            ->leftJoin('oi.stockItems', 'si')
            ->leftJoin('oi.cartitems', 'ci')
            ->leftJoin('oi.ledgerFields', 'lf')
            ->andWhere($queryBuilder->expr()->in('c.id', ':customerIds'))
            ->andWhere('BIT_AND(oi.flags, :orderCompleteFlag) = :orderCompleteFlag')
            ->setParameter('customerIds', $customerIds)
            ->setParameter('orderCompleteFlag', OrderInfo::FLAG_ORDER_COMPLETE)
            ->setParameter('couponProcessedFlag', Coupon::FLAG_PROCESSED)
            ->groupBy('oi.id')
            ->addGroupBy('si.id')
            ->addGroupBy('ci.id');

        if ($orderStartDate !== null) {
            $queryBuilder
                ->andWhere('DATE(oi.orderDate) >= DATE(:orderStartDate)')
                ->setParameter('orderStartDate', $orderStartDate);
        }

        $result = $queryBuilder->getQuery()->getArrayResult();

        foreach ($result as $order) {
            $orderData[$order['customer']['id']][] = $order;
        }

        return $orderData;
    }

    /**
     * @param CourierList $courierList
     * @param int $shippingMethod
     * @param string $catUrl
     * @return OrderInfo|null
     */
    public function findByCourierList(CourierList $courierList, int $shippingMethod, string $catUrl): ?OrderInfo
    {
        $comment = 'Koerierslijst <a href="javascript:wopen2(\'' . $catUrl . '/courier-list/' . $courierList->getId(
            ) . '\', \'bwin\', 1, 900, 450)">' . $courierList->getId() . '</a>';
        $queryBuilder = $this->createQueryBuilder('o');
        $orders = $queryBuilder
            ->select('o')
            ->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->eq('o.ourComment', ':courierComment'),
                    $queryBuilder->expr()->eq('o.customerComment', ':courierComment')
                )
            )
            ->andWhere('o.shippingMethod = :shippingMethod')
            ->setParameter('courierComment', $comment)
            ->setParameter('shippingMethod', $shippingMethod)
            ->getQuery()
            ->getResult();

        $order = current($orders);

        return ($order !== false) ? $order : null;
    }

    /**
     * @param DateTime|null $fromDate
     * @param array $origins
     * @param array $ipAddresses
     * @return OrderInfo[]
     */
    public function getWebsiteOrdersNotPaid(
        ?DateTime $fromDate = null,
        array $origins = [],
        array $ipAddresses = []
    ): array {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select('oi')
            ->innerJoin('oi.origin', 'ori')
            ->innerJoin('oi.shippingMethod', 'sm')
            ->innerJoin('oi.paymentMethod', 'pm')
            ->where('ori.id IN (:webOrigins)')
            ->andWhere('BIT_AND(oi.flags, :hasBeenPaid) = 0')
            ->andWhere('BIT_AND(oi.flags, :orderCompleted) = 0')
            ->andWhere('BIT_AND(oi.flags, :paymentOk) = 0')
            ->andWhere('sm.id != :cancelled')
            ->andWhere('oi.ipNumber NOT IN (:ipAddresses)')
            ->setParameter('webOrigins', $origins)
            ->setParameter('hasBeenPaid', OrderInfo::FLAG_PAYMENT_COMPLETE)
            ->setParameter('orderCompleted', OrderInfo::FLAG_ORDER_COMPLETE)
            ->setParameter('paymentOk', OrderInfo::FLAG_PAYMENT_OKAY)
            ->setParameter('cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('ipAddresses', $ipAddresses)
            ->orderBy('oi.orderDate', 'DESC');

        if ($fromDate instanceof DateTime) {
            $queryBuilder
                ->andWhere('oi.orderDate >= :fromDate')
                ->setParameter('fromDate', $fromDate->format('Y-m-d'));
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return OrderInfo[]
     */
    public function findAllBackorders(): array
    {
        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder
            ->addSelect('o')
            ->innerJoin('o.parking', 'p')
            ->where('BIT_AND(p.flags, :flag) = :flag')
            ->andWhere('p.domainId IN (:domains)')
            ->andWhere('o.shippingMethod != :quotation')
            ->setParameter(':flag', Parking::FLAG_BACKORDER_CHECK)
            ->setParameter(':domains', Domain::CNU_DOMAINS)
            ->setParameter(':quotation', ShipmentMethod::QUOTATION)
            ->orderBy('o.orderDate, o.orderTime');

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param DateTime $from
     * @param DateTime $to
     * @return OrderInfo[]
     */
    public function getBetweenDateTimes(DateTime $from, DateTime $to): array
    {
        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder
            ->select('o')
            ->andWhere(' UNIX_TIMESTAMP(CONCAT_WS(:separator, o.orderDate, o.orderTime)) > UNIX_TIMESTAMP(:from)')
            ->andWhere(' UNIX_TIMESTAMP(CONCAT_WS(:separator, o.orderDate, o.orderTime)) <= UNIX_TIMESTAMP(:to)')
            ->setParameter('separator', ' ')
            ->setParameter('from', $from->format('Y-m-d H:i:s'))
            ->setParameter('to', $to->format('Y-m-d H:i:s'));

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param OrderInfo $order
     * @return void
     */
    public function refresh(OrderInfo $order): void
    {
        $entityManager = $this->getEntityManager();
        $entityManager->refresh($order);
    }

    /**
     * @param string $from
     * @param string $to
     * @param array $origins
     * @param array $ipAddresses
     * @return array
     */
    public function getWrongInAnalytics(
        string $from,
        string $to,
        array $origins = [],
        array $ipAddresses = []
    ): array {
        $ipAddresses[] = '';
        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder
            ->select('o')
            ->andWhere('o.origin IN (:origins)')
            ->andWhere('BIT_AND(o.flags, :paymentOkay) = :paymentOkay')
            ->andWhere('o.gaClientId IS NOT NULL')
            ->andWhere('o.ipNumber NOT IN (:ipAdresses)')
            ->andWhere($queryBuilder->expr()->between('o.orderDate', ':from', ':to'))
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->setParameter('origins', $origins)
            ->setParameter('paymentOkay', OrderInfo::FLAG_PAYMENT_OKAY)
            ->setParameter('ipAdresses', $ipAddresses);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param int[] $productIds
     * @param int[] $locationIds
     * @param DateTimeInterface $start
     * @param DateTimeInterface $end
     * @return OrderInfo[]
     */
    public function getSalesForProductsAndLocationsInDateRange(
        array $productIds,
        array $locationIds,
        DateTimeInterface $start,
        DateTimeInterface $end
    ): array {
        $queryBuilder = $this->createQueryBuilder('oi')
            ->select('COUNT(oi) AS sales')
            ->addSelect('IDENTITY(si.product) AS product')
            ->addSelect('IDENTITY(si.location) AS location')
            ->join('oi.stockItems', 'si')
            ->where('IDENTITY(si.product) IN (:productIds)')
            ->andWhere('IDENTITY(si.location) IN (:locationIds)')
            ->andWhere('BIT_AND(oi.flags, :orderCompleteFlag) = :orderCompleteFlag')
            ->andWhere('oi.orderDate BETWEEN DATE(:start) AND DATE(:end)')
            ->groupBy('si.product')
            ->addGroupBy('si.location')
            ->setParameters([
                'productIds' => $productIds,
                'locationIds' => $locationIds,
                'start' => $start,
                'end' => $end,
                'orderCompleteFlag' => OrderInfo::FLAG_ORDER_COMPLETE,
            ]);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param string $from
     * @param string|null $to
     * @return OrderInfo[]
     */
    public function findAdyenWithReturnsBetweenInvoiceDates(string $from, ?string $to = null): array
    {
        $to = $to ?: $from;

        $queryBuilder = $this->getQueryBuilder('o');
        $queryBuilder
            ->select('o')
            ->innerJoin('o.articlesReturn', 'ar')
            ->andWhere($queryBuilder->expr()->between('o.dateInvoice', ':from', ':to'))
            ->andWhere('o.paymentMethod IN (:paymentMethod)')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->setParameter('paymentMethod', PaymentMethod::ID_ADYEN);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param DateTime $from
     * @param DateTime $to
     * @param string|null $originParent
     * @return array|int|string
     */
    public function getNotInAdministration(DateTime $from, DateTime $to, ?string $originParent = null)
    {
        $queryBuilder = $this->getQueryBuilder('o');
        $queryBuilder
            ->select('o.id')
            ->join('o.shippingMethod', 'sm')
            ->where('BIT_AND(o.flags, :goedhartOkFlag) = 0')
            ->andWhere('BIT_AND(o.flags, :completedFlag) = :completedFlag')
            ->andWhere('sm.id != :shippingMethodCancelled')
            ->andWhere('DATE(o.dateHandled) BETWEEN DATE(:from) AND DATE(:to)')
            ->setParameters([
                'goedhartOkFlag' => OrderInfo::FLAG_GOEDHART_OK,
                'completedFlag' => OrderInfo::FLAG_ORDER_COMPLETE,
                'shippingMethodCancelled' => OrderInfo::SHIPPING_METHOD_CANCELLED,
                'from' => $from,
                'to' => $to,
            ]);

        if ($originParent !== null) {
            $queryBuilder
                ->join('o.origin', 'oo')
                ->andWhere('oo.parent = :parent')
                ->andWhere('oo.status = :active')
                ->setParameter('parent', $originParent)
                ->setParameter('active', Origin::STATUS_ACTIVE);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return OrderInfo[]
     */
    public function findNotCompletedQuotations(): array
    {
        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder
            ->select('o')
            ->join('o.shippingMethod', 's')
            ->where('BIT_AND(o.flags, :flag_order_complete) != :flag_order_complete')
            ->andWhere('s.id = :shipping_method_quotation')
            ->setParameters([
                'flag_order_complete' => OrderInfo::FLAG_ORDER_COMPLETE,
                'shipping_method_quotation' => ShipmentMethod::QUOTATION,
            ]);

        return $queryBuilder->getQuery()->getResult();
    }

    public function getSalesForProductSuppliersAndOriginInDateRange(
        int $productId,
        string $origin,
        DateTime $start,
        DateTime $end,
        bool $includeOpenOrders = false
    ): array {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select('COUNT(si) AS sales')
            ->leftJoin('oi.stockItems', 'si')
            ->leftJoin('oi.origin', 'oo')
            ->innerJoin('oi.customer', 'c')
            ->where('IDENTITY(si.product) = :productId')
            ->andWhere('oo.parent = :origin')
            ->andWhere('oi.orderDate BETWEEN DATE(:start) AND DATE(:end)')
            ->andWhere('c.customerType != :excludeCustomerType')
            ->andWhere('BIT_AND(c.flags, :customerFlags) != :customerFlags')
            ->andWhere('oi.shippingMethod NOT IN (:cancelled, :quotation)')
            ->groupBy('si.product')
            ->addGroupBy('oo.parent')
            ->setParameters([
                'productId' => $productId,
                'origin' => $origin,
                'start' => $start,
                'end' => $end,
                'cancelled' => ShipmentMethod::CANCELLED,
                'quotation' => ShipmentMethod::QUOTATION,
                'excludeCustomerType' => Customer::TYPE_CNU_GROUP,
                'customerFlags' => Customer::FLAG_IGNORE_IN_CLAIMS_AND_FEES_EXPORT,
            ]);

        if ($includeOpenOrders === false) {
            $queryBuilder
                ->andWhere('BIT_AND(oi.flags, :flag_order_complete) = :flag_order_complete')
                ->setParameter('flag_order_complete', OrderInfo::FLAG_ORDER_COMPLETE);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param int $reference
     * @return OrderInfo|null
     * @throws NonUniqueResultException
     */
    public function findByReference(int $reference): ?OrderInfo
    {
        $queryBuilder = $this->createQueryBuilder('o');

        return $queryBuilder
            ->select('o')
            ->join('o.orderReference', 'r')
            ->where($queryBuilder->expr()->eq('r.reference', ':reference'))
            ->setParameter('reference', $reference)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param Customer $customer
     * @return array
     */
    public function getOpenInvoicesByCustomer(Customer $customer): array
    {
        $queryBuilder = $this->createQueryBuilder('o');

        return $queryBuilder
            ->select('o.id')
            ->addSelect('o.name')
            ->addSelect('o.ordernr')
            ->addSelect('o.invoiceNumber')
            ->addSelect('o.orderDate')
            ->addSelect('o.dateHandled')
            ->addSelect('op.amount')
            ->addSelect('op.date')
            ->join('o.customer', 'c')
            ->join('o.shippingMethod', 's')
            ->leftJoin('o.orderPayments', 'op')
            ->where('op.amount <> 0')
            ->andWhere('o.issuerId = :emptyString')
            ->andWhere('c = :customer')
            ->andWhere('op.date = :zeroDate')
            ->andWhere('BIT_AND(o.shippingMethod, :quotation) != :quotation')
            ->andWhere('BIT_AND(o.shippingMethod, :cancelled) != :cancelled')
            ->setParameter(':quotation', ShipmentMethod::QUOTATION)
            ->setParameter(':cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('customer', $customer)
            ->setParameter('zeroDate', '0000-00-00')
            ->setParameter('emptyString', '')
            ->groupBy('o')
            ->orderBy('o.invoiceNumber')
            ->addOrderBy('o.dateInvoice')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param DateTime $date
     * @return array
     */
    public function getSalesPerProduct(DateTime $date): array
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->select('p.id')
            ->addSelect('o.dateInvoice')
            ->addSelect('COUNT(o) as amount')
            ->join('o.stockItems', 's')
            ->join('s.product', 'p')
            ->where('o.dateInvoice = :date')
            ->setParameter('date', $date)
            ->groupBy('p.id')
            ->addGroupBy('o.dateInvoice');

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function findWithoutStockLog(
        ?DateTime $dateFrom = null,
        ?int $offset = null,
        ?int $rowCount = null,
        bool $iterable = false
    ) {
        $dateQuery = '';

        if ($dateFrom !== null) {
            $dateFrom->modify('-1 day');
            $dateQuery = ' AND o.dateInvoice >= :dateInvoice ';
        }

        $query = $this->getEntityManager()->createQuery(
        /** @lang DQL */ '
            SELECT
                o
            FROM ' . OrderInfo::class . ' o
            INNER JOIN ' . Stock::class . ' s WITH s.order = o.id
            WHERE (
                SELECT count(sl)
                FROM ' . StockLogN::class . ' sl
                WHERE sl.stockLogAction = 48
                AND sl.order = o.id
            ) = 0' . $dateQuery . '
            GROUP BY o.id
        '
        );

        if ($dateFrom !== null) {
            $query->setParameter('dateInvoice', $dateFrom);
        }

        if ($offset !== null) {
            $query->setFirstResult($offset);
        }

        if ($rowCount !== null) {
            $query->setMaxResults($rowCount);
        }

        if ($iterable === true) {
            return $query->iterate();
        }

        return $query->getResult();
    }

    /**
     * @param OrderInfo $order
     * @return string
     */
    public function findOriginParentForBackorder(OrderInfo $order): string
    {
        $parking = $order->getParking();
        if (!$parking instanceof Parking) {
            return $order->getOrigin()->getParent();
        }

        $parentSql = '
            SELECT
                sl.parent
            FROM cameranu.bestelling_parkeren AS op
            INNER JOIN cameranu.parkeren AS p ON op.parkeren_id = p.id
            INNER JOIN cameranu.stock_locations AS sl ON p.domain_id = sl.domain_id AND sl.is_main_location = :isMainLocation
            WHERE op.bestelling_id = :orderId
            GROUP BY sl.parent
        ';

        $query = $this->getEntityManager()->getConnection()->prepare($parentSql);
        $query->bindValue('isMainLocation', 1);
        $query->bindValue('orderId', $order->getId());

        $result = $query->executeQuery()->fetchFirstColumn();

        return count($result) > 0 ? current($result) : $order->getOrigin()->getParent();
    }

    /**
     * @param AdyenScopeFilter $filter
     * @return array
     */
    public function getAdyenScopeByFilter(AdyenScopeFilter $filter): array
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->select('o.id as orderId')
            ->addSelect('o.ordernr as orderNumber')
            ->addSelect('o.customerid as customerId')
            ->addSelect('o.orderDate as orderDate')
            ->addSelect('case when BIT_AND(o.flags, :flags) = :flags then \'ja\' else \'nee\' end AS isFinished')
            ->addSelect('case when sm.id = 6 then \'ja\' else \'nee\' end AS isCancelled')
            ->addSelect('o.dateInvoice as invoiceDate')
            ->addSelect('o.invoiceNumber as invoiceNumber')
            ->addSelect('oo.parent as parentOrigin')
            ->addSelect('opm.name as paymentName')
            ->addSelect('sm.name as deliveryMethodName')
            ->addSelect(
                '
                (
                    SELECT
                        SUM(s.salesPrice)
                    FROM ' . Stock::class . ' s
                    WHERE s.order = o
                ) AS stockValueInc'
            )
            ->addSelect(
                '
                (
                    SELECT
                        SUM(lf.amount)
                    FROM ' . LedgerField::class . ' lf
                    WHERE lf.orderId = o.id
                ) AS ledgerValueInc'
            )
            ->addSelect(
                '(
                    SELECT
                        SUM(ops.amount)
                    FROM ' . OrderPayments::class . ' ops
                    WHERE ops.order = o
                ) AS totalPaymentInc'
            )
            ->addSelect(
                '(
                    SELECT
                        COUNT(op.id)
                    FROM ' . OrderPayments::class . ' op
                    WHERE op.order = o
                ) AS payments'
            );

        if ($filter->isGroupedByOrder()) {
            $queryBuilder
                ->addSelect('GROUP_CONCAT(pt.goedhart) AS paymentMethod')
                ->addSelect('SUM(opt.amount) AS paymentAmount')
                ->addSelect('GROUP_CONCAT(opt.paymentDate) AS paymentDate')
                ->addSelect('GROUP_CONCAT(opt.adyenType) AS paymentAdyenType')
                ->addSelect('GROUP_CONCAT(opt.adyenPspReference) as paymentAdyenReference')
                ->groupBy('o');
        } else {
            $queryBuilder
                ->addSelect('pt.goedhart AS paymentMethod')
                ->addSelect('opt.amount AS paymentAmount')
                ->addSelect('opt.paymentDate AS paymentDate')
                ->addSelect('opt.adyenType AS paymentAdyenType')
                ->addSelect('opt.adyenPspReference as paymentAdyenReference');
        }

        $queryBuilder
            ->innerJoin('o.origin', 'oo')
            ->leftJoin('o.paymentMethod', 'opm')
            ->leftJoin('o.shippingMethod', 'sm')
            ->leftJoin('o.orderPayments', 'opt')
            ->leftJoin('opt.paymentType', 'pt');

        $dateColumn = $filter->getDateType() === 'order' ? 'orderDate' : 'dateInvoice';

        if (!$filter->getFinished()) {
            $conditions[] = 'BIT_AND(o.flags, :flags) != :flags';
        }

        $conditions[] = 'o.' . $dateColumn . ' >= :fromDate';

        switch ($filter->getDateType()) {
            case 'order':
                $conditions[] = 'o.' . $dateColumn . ' <= :toDate';
                break;
            case 'invoice':
                $conditions[] = '(o.' . $dateColumn . ' <= :toDate OR o.' . $dateColumn . ' = \'0000-00-00\')';
                break;
        }

        $queryBuilder->andWhere($queryBuilder->expr()->andX()->addMultiple($conditions));

        $dateFrom = $filter->getDateFrom()->format('Y-m-d');
        $dateTo = $filter->getDateTo()->format('Y-m-d');
        $queryBuilder->setParameter('flags', OrderInfo::FLAG_ORDER_COMPLETE);
        $queryBuilder->setParameter('fromDate', $dateFrom);
        $queryBuilder->setParameter('toDate', $dateTo);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param OrderFilter $filter
     * @param int $defaultLimit
     * @return OrderInfo[]
     */
    public function findByApiFilter(ApiFilterInterface $filter, int $defaultLimit): array
    {
        $limit = $filter->limit ?? $defaultLimit;
        $offset = $filter->page ? $limit * ($filter->page - 1) : 0;

        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder->setMaxResults($limit);
        $queryBuilder->setFirstResult($offset);

        if ($filter->id !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->eq('o.id', ':id')
            );

            $queryBuilder->setParameter('id', $filter->id);
        }

        if ($filter->orderNumber !== null && $filter->orderNumber !== 0) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->eq('o.ordernr', ':orderNumber')
            );

            $queryBuilder->setParameter('orderNumber', $filter->orderNumber);
        }

        if ($filter->email !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->eq('o.email', ':email')
            );

            $queryBuilder->setParameter('email', $filter->email);
        }

        if ($filter->customer !== null) {
            $queryBuilder->join('o.customer', 'c');
            $queryBuilder->andWhere(
                $queryBuilder->expr()->eq('c.id', ':customer')
            );

            $queryBuilder->setParameter('customer', $filter->customer);
        }

        if ($filter->orderDate !== null) {
            if (count($filter->orderDate) === 1) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->eq('o.orderDate', ':orderDate')
                );

                $queryBuilder->setParameter('orderDate', current($filter->orderDate));
            } else {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->between('o.orderDate', ':from', ':to')
                );

                $queryBuilder->setParameter('from', current($filter->orderDate));
                $queryBuilder->setParameter('to', end($filter->orderDate));
            }
        }

        if ($filter->invoiceDate !== null) {
            if (count($filter->invoiceDate) === 1) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->eq('o.dateInvoice', ':invoiceDate')
                );

                $queryBuilder->setParameter('invoiceDate', current($filter->invoiceDate));
            } else {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->between('o.dateInvoice', ':from', ':to')
                );

                $queryBuilder->setParameter('from', current($filter->invoiceDate));
                $queryBuilder->setParameter('to', end($filter->invoiceDate));
            }
        }

        if ($filter->completedDate !== null) {
            if (count($filter->completedDate) === 1) {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->eq('o.dateHandled', ':completedDate')
                );

                $queryBuilder->setParameter('completedDate', current($filter->completedDate));
            } else {
                $queryBuilder->andWhere(
                    $queryBuilder->expr()->between('o.dateHandled', ':from', ':to')
                );

                $queryBuilder->setParameter('from', current($filter->completedDate));
                $queryBuilder->setParameter('to', end($filter->completedDate));
            }
        }

        if ($filter->sort !== null && $filter->direction !== null) {
            $queryBuilder->orderBy('o.' . $filter->sort, $filter->direction);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param DateTime $from
     * @param DateTime $till
     * @return OrderInfo[]
     */
    public function getIdsBetweenInvoiceDates(DateTime $from, DateTime $till): array
    {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select('oi.id')
            ->join('oi.origin', 'o')
            ->andWhere('oi.invoiceNumber != :zero')
            ->andWhere('oi.invoiceNumber IS NOT NULL')
            ->andWhere(
                $queryBuilder->expr()->between('oi.dateInvoice', ':from', ':till')
            )
            ->setParameters([
                'zero' => 0,
                'from' => $from,
                'till' => $till
            ]);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findWithDuplicateInvoiceNumber(): array
    {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select('COUNT(oi.id) as cnt')
            ->addSelect('oi.domainId')
            ->addSelect('oi.invoiceNumber')
            ->andWhere('oi.invoiceNumber != :zero')
            ->andWhere('oi.invoiceNumber IS NOT NULL')
            ->andWhere('oi.id > :startId')
            ->join('oi.origin', 'o')
            ->groupBy('oi.invoiceNumber')
            ->addGroupBy('o.parent')
            ->having('cnt > 1')
            ->setParameters([
                'zero' => 0,
                'startId' => OrderInfo::START_NEW_INVOICE_NUMBERS_ID
            ]);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function findOpenNotParkedByPaymentMethod(int $paymentMethod): array
    {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->leftJoin('oi.orderParking', 'op')
            ->where('oi.paymentMethod = :paymentMethod')
            ->andWhere('BIT_AND(oi.flags, :flag_order_complete) != :flag_order_complete')
            ->andWhere('oi.shippingMethod != :quotation')
            ->andWhere('oi.shippingMethod != :cancelled')
            ->andWhere('op IS NULL')
            ->setParameter(':quotation', ShipmentMethod::QUOTATION)
            ->setParameter(':cancelled', ShipmentMethod::CANCELLED)
            ->setParameters([
                'quotation' => ShipmentMethod::QUOTATION,
                'cancelled' => ShipmentMethod::CANCELLED,
                'flag_order_complete' => OrderInfo::FLAG_ORDER_COMPLETE,
                'paymentMethod' => $paymentMethod,
            ]);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @throws Exception
     */
    public function findAllByYear(int $year, ?int $limit = null, ?int $offset = null): array
    {
        $startOfYear = new DateTime('01-01-' . $year);
        $startOfYear->setTime(0, 0);

        $endOfYear = new DateTime('31-12-' . $year);
        $endOfYear->setTime(23, 59, 59);

        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->andWhere('oi.orderDate BETWEEN :startOfYear AND :endOfYear')
            ->setParameters([
                'startOfYear' => $startOfYear->format('Y-m-d'),
                'endOfYear' => $endOfYear->format('Y-m-d'),
            ]);

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        if ($offset !== null) {
            $queryBuilder->setFirstResult((($limit ?? 0) * $offset));
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return OrderInfo[]
     */
    public function findNoVatOrdersBetweenInvoiceDates(
        DateTime $startDate,
        DateTime $endDate
    ): array {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->andWhere('o.dateInvoice BETWEEN :startDate AND :endDate')
            ->andWhere('o.invoiceNumber > 0')
            ->andWhere('o.dateInvoice > 0')
            ->andWhere('BIT_AND(o.flags, :flags) = :flags');
        $queryBuilder->setParameter('startDate', $startDate, Types::DATE_MUTABLE);
        $queryBuilder->setParameter('endDate', $endDate, Types::DATE_MUTABLE);
        $queryBuilder->setParameter('flags', OrderInfo::FLAG_NO_VAT);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * Business Rules
     * Accessoire gekocht samen met product in afgelopen 2 jaar
     * Order herkomst parent CameranuIsh
     * Klanttype consument/klein_zakelijk
     * Verkoopprijs tussen 10 en 300 euro
     * Filter gratis producten uit
     *
     * Extra regels voor objectieven
     * Filter digitale camera’s uit
     * Filter accu’s uit
     * Filter objectieven uit
     * Filter geheugenkaarten uit
     *
     * @return OrderInfo[]
     */
    public function getSalesForAccessoriesByProduct(
        int $productId,
        int $accessoryId,
        int $group
    ): array {
        $from = new DateTime();
        $from->modify('-2 year');
        $to = new DateTime();

        $productCartBuilder = $this->cartItemRepository->createQueryBuilder('sc');
        $productCartBuilder
            ->select('1')
            ->where('sc.productId = :productId')
            ->andWhere('sc.orderId = oi.id');

        $orderBuilder = $this->orderOriginRepository->createQueryBuilder('oo');
        $orderBuilder
            ->select('1')
            ->where('oo.parent IN (:origins)')
            ->andWhere('oo.id = oi.origin');

        $webusersBuilder = $this->customerRepository->createQueryBuilder('c');
        $webusersBuilder
            ->select('1')
            ->where('c.id = oi.customer')
            ->andWhere('c.customerType IN (:customerTypes)');

        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select('SUM(sc_accessory.amount) AS sales')
            ->innerJoin('oi.cartitems', 'sc_accessory')
            ->where('oi.invoiceNumber > 0')
            ->andWhere($queryBuilder->expr()->between('oi.orderDate', ':from', ':to'))
            ->andWhere('oi.shippingMethod != :cancelled')
            ->andWhere('IDENTITY(sc_accessory.product) = :accessoryId')
            ->andWhere('sc_accessory.price BETWEEN :min AND :max')
            ->andWhere('BIT_AND(sc_accessory.status, :free_flag) != :free_flag')
            ->andWhere($productCartBuilder->expr()->exists($productCartBuilder->getDQL()))
            ->andWhere($orderBuilder->expr()->exists($orderBuilder->getDQL()))
            ->andWhere($webusersBuilder->expr()->exists($webusersBuilder->getDQL()))
            ->setParameters([
                'from' => $from,
                'to' => $to,
                'min' => 10,
                'max' => 300,
                'productId' => $productId,
                'accessoryId' => $accessoryId,
                'free_flag' => CartItem::FLAG_IS_FREE,
                'origins' => Origin::CAMERANU_PARENTS,
                'cancelled' => ShipmentMethod::CANCELLED,
                'customerTypes' => [
                    Customer::TYPE_CUSTOMER,
                    Customer::TYPE_SMALL_BUSINESS,
                ],
            ]);

        if ($group === Rootgroup::LENSES_ID) {
            $queryBuilder
                ->innerJoin('sc_accessory.product', 'accessory')
                ->innerJoin('accessory.subgroup', 'sg')
                ->innerJoin('sg.maingroup', 'mg')
                ->innerJoin('mg.rootgroup', 'rg')
                ->andWhere('rg.id NOT IN (:excludedGroups)')
                ->setParameter('excludedGroups', [
                    Rootgroup::DIGITAL_CAMERAS_ID,
                    Rootgroup::MEMORY_ID,
                    Rootgroup::ACCU_ID,
                    Rootgroup::LENSES_ID
                ]);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function getSalesForCombinedProductsQuery(): Query
    {
        $from = new DateTime('-2 years');

        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->distinct()
            ->select('IDENTITY(c1.product) AS product_id')
            ->addSelect('IDENTITY(c2.product) AS additional_product_id')
            ->addSelect('SUM(c2.amount) AS sales')
            ->join('oi.cartitems', 'c1')
            ->join('oi.cartitems', 'c2', Expr\Join::WITH, 'c2.product != c1.product')
            ->join('c1.product', 'p1')
            ->join('p1.subgroup', 'sg1')
            ->join('sg1.maingroup', 'mg1')
            ->join('mg1.rootgroup', 'rg1')
            ->join('c2.product', 'p2')
            ->join('p2.subgroup', 'sg2')
            ->join('sg2.maingroup', 'mg2')
            ->join('mg2.rootgroup', 'rg2')
            ->join('oi.origin', 'oo')
            ->join('oi.customer', 'c')
            ->where('oo.parent IN (:origins)')
            ->andWhere('oi.orderDate > :from')
            ->andWhere('oi.invoiceNumber > 0')
            ->andWhere('oi.shippingMethod != :cancelled')
            ->andWhere('c.customerType IN (:customerTypes)')
            ->andWhere('c2.price >= :min')
            ->andWhere('BIT_AND(c1.status, :freeFlag) != :freeFlag')
            ->andWhere('BIT_AND(c2.status, :freeFlag) != :freeFlag')
            ->andWhere('BIT_AND(p1.flags, :productFlags) = :productFlags')
            ->andWhere('BIT_AND(p2.flags, :productFlags) = :productFlags')
            ->andWhere('BIT_AND(sg1.flags, :subGroupFlags) = :subGroupFlags')
            ->andWhere('BIT_AND(sg2.flags, :subGroupFlags) = :subGroupFlags')
            ->andWhere('BIT_AND(mg1.flags, :mainGroupFlags) = :mainGroupFlags')
            ->andWhere('BIT_AND(mg2.flags, :mainGroupFlags) = :mainGroupFlags')
            ->andWhere('BIT_AND(rg1.flags, :rootGroupFlags) = :rootGroupFlags')
            ->andWhere('BIT_AND(rg2.flags, :rootGroupFlags) = :rootGroupFlags')
            ->groupBy('c1.product')
            ->addGroupBy('c2.product')
            ->setParameters([
                'from' => $from,
                'min' => 10,
                'origins' => Origin::CAMERANU_PARENTS,
                'cancelled' => ShipmentMethod::CANCELLED,
                'customerTypes' => [
                    Customer::TYPE_CUSTOMER,
                    Customer::TYPE_SMALL_BUSINESS,
                ],
                'freeFlag' => CartItem::FLAG_IS_FREE,
                'productFlags' => Product::FLAG_SHOW + Product::FLAG_VISIBLE,
                'subGroupFlags' => Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE,
                'mainGroupFlags' => Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE,
                'rootGroupFlags' => Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU,
            ]);

        return $queryBuilder->getQuery();
    }

    public function findOrdersForOriginByShippingType(Origin $origin, string $shippingType): array
    {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->join('oi.shippingMethod', 'sm')
            ->join('oi.origin', 'oo')
            ->andWhere('sm.type = :type')
            ->andWhere('oo = :origin')
            ->andWhere('sm != :cancelled')
            ->andWhere('sm != :quotation')
            ->andWhere('oi.invoiceNumber = 0')
            ->setParameter(':quotation', ShipmentMethod::QUOTATION)
            ->setParameter('cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('type', $shippingType)
            ->setParameter('origin', $origin);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findProductAmountInOpenOrdersByOrigins(array $origins): array
    {
        $queryBuilder = $this->createQueryBuilder('oi');
        $queryBuilder
            ->select('cip.id')
            ->addSelect('SUM(ci.amount) as amount')
            ->join('oi.shippingMethod', 'sm')
            ->join('oi.origin', 'oo')
            ->join('oi.cartitems', 'ci')
            ->join('ci.product', 'cip')
            ->leftJoin('oi.stockItems', 's', Join::WITH, 'ci.product = s.product')
            ->andWhere('oo in (:origins)')
            ->andWhere('sm != :cancelled')
            ->andWhere('sm != :quotation')
            ->andWhere('oi.invoiceNumber = 0')
            ->andWhere('BIT_AND(ci.status, :flagParentWithoutStock) != :flagParentWithoutStock')
            ->andWhere('s.id IS NULL')
            ->setParameter(':quotation', ShipmentMethod::QUOTATION)
            ->setParameter('cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('flagParentWithoutStock', CartItem::FLAG_IS_PARENT_WITHOUT_OWN_STOCK)
            ->setParameter('origins', $origins)
            ->groupBy('cip.id');

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function readyForAfas(): array
    {
        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->select('o.id, o.name, o.invoiceNumber, o.dateInvoice, og.id as originId, og.source as origin')
            ->join('o.shippingMethod', 'sm')
            ->join('o.origin', 'og')
            ->andWhere('o.invoiceNumber != 0')
            ->andWhere('sm != :cancelled')
            ->andWhere('BIT_AND(o.flags, :goedhartOk) != :goedhartOk')
            ->andWhere('BIT_AND(o.flags, :goedhartError) != :goedhartError')
            // In het verleden staan nog veel orders fout m.b.t. Goedhartvinkjes. We gaan
            // daarom een datumselectie toevoegen
            ->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->gte('o.dateInvoice', ':invoiceDateNonVendiro'),
                        $queryBuilder->expr()->neq('og.parent', ':parentVendiro'),
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->gte('o.dateInvoice', ':invoiceDateVendiro'),
                        $queryBuilder->expr()->eq('og.parent', ':parentVendiro'),

                    )
                )
            )
            ->setParameter('cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('goedhartOk', OrderInfo::FLAG_GOEDHART_OK)
            ->setParameter('goedhartError', OrderInfo::FLAG_GOEDHART_ERROR)
            ->setParameter('invoiceDateNonVendiro', '2023-01-01')
            ->setParameter('invoiceDateVendiro', '2023-09-01')
            ->setParameter('parentVendiro', 'vendiro_cameratools')
            ->addOrderBy('og.id', 'ASC')
            ->addOrderBy('o.dateInvoice', 'ASC');

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function findMarketplaceOrders(DateTimeInterface $startDate, DateTimeInterface $endDate): array
    {
        $this->_em->clear();
        $this->_em->getConnection()->getConfiguration()->setSQLLogger(null);

        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->join('o.origin', 'og')
            ->join('o.shippingMethod', 'sm')
            ->andWhere('o.invoiceNumber != 0')
            ->andWhere('sm != :cancelled')
            ->andWhere(
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->gte('o.dateInvoice', ':startDate'),
                    $queryBuilder->expr()->lt('o.dateInvoice', ':endDate'),
                    $queryBuilder->expr()->in('og.parent', ':vendiroParents'),
                ),
            )
            ->setParameter('cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('vendiroParents', ['vendiro_cameratools', 'vendiro'])
            ->setParameter('startDate', $startDate->format('Y-m-d'))
            ->setParameter('endDate', $endDate->format('Y-m-d'));

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @throws DBAL\Exception
     * @throws DBAL\Driver\Exception
     */
    public function shouldBeInAfas(array $parents, int $year, ?int $month = null): array
    {
        $monthQuery = $month !== null ? ' and month(bn.datum_factuur) = ? ' : ' ';

        $sql = '
        select
            bn.id as bestelling_id,
            bn.factuurnummer as invoiceNumber,
            if ((bn.flags&?) != ?, sum(bn.verzendkosten + bn.betaalkosten), sum(bn.verzendkosten_ex + bn.betaalkosten_ex)) as extra_costs,
            # Voorraad
            (select if ((bn.flags&?) != ?, sum(v.prijs_verkoop), sum(v.prijs_verkoop_ex)) from cameranu.voorraad as v
                where v.bestelling_id = bn.id
            ) as sold_stock,
            # Grootboekvelden
            (select if ((bn.flags&?) != ?, sum(bv.bedrag), sum(bv.bedrag_ex)) from cameranu.bestelling_velden as bv
                where bv.bestelling_id = bn.id
            ) as ledger_fields,
            # Retouren
            (select if ((bn.flags&?) != ?, sum(ar.prijs), sum(ar.prijs_ex)) from cameranu.artikelen_retour as ar
                where ar.credit_bestelling_id = bn.id
            ) as returns,
            # Cadeaubonnen
            (select sum(c.bedrag) from cameranu.coupons as c
                where c.bestelling_id = bn.id
            ) as coupons,
            # Kortingen
            (select if ((bn.flags&?) != ?, sum(s.aantal * s.prijs_korting), sum(s.aantal * (s.prijs_korting / (1 + (s.btw / 100))))) from cameranu.shopcart as s
                left join cameranu.artikelen a on s.artikel_id = a.id
                where s.bestelling_id = bn.id
                and ((a.flags&?) != ? or (a.flags&?) = ? or (s.status&?) = ?)
            ) as discount
        from cameranu.bestelling_naw as bn
        inner join cameranu.bestelling_herkomst as bh on bh.id = bn.herkomst and bh.status = ?
        where bh.parent in (?)
          and bn.factuurnummer != 0
          and bn.verzendwijze != ?
          and year(bn.datum_factuur) = ? '
            . $monthQuery .
            'group by bn.id
        ';

        $parameters = [
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            OrderInfo::FLAG_NO_VAT,
            Product::FLAG_WEEK_OFFER,
            Product::FLAG_WEEK_OFFER,
            Product::FLAG_KIT_OR_SET_CHOICE,
            Product::FLAG_KIT_OR_SET_CHOICE,
            CartItem::FLAG_IS_FREE,
            CartItem::FLAG_IS_FREE,
            Origin::STATUS_ACTIVE,
            $parents,
            ShipmentMethod::CANCELLED,
            $year,
        ];

        $parameterTypes = [
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::STRING,
            Connection::PARAM_STR_ARRAY,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
            ParameterType::INTEGER,
        ];

        if ($month !== null) {
            $parameters[] = $month;
            $parameterTypes[] = ParameterType::INTEGER;
        }

        $query = $this->getEntityManager()->getConnection()->executeQuery($sql, $parameters, $parameterTypes);

        return $query->fetchAllAssociative();
    }

    public function readyForAdchieve(
        array $ipAddresses = [],
        array $origins = [],
        ?DateTime $from = null,
        ?DateTime $to = null
    ): array {
        $ipAddresses[] = '';
        $ignoreCustomerTypes = [Customer::TYPE_CNU_GROUP, Customer::TYPE_WAREHOUSE];

        if ($from === null) {
            $from = new DateTime('-1 week');
        }

        $from->setTime(0, 0);

        if ($to === null) {
            $to = new DateTime('yesterday');
        }

        $to->setTime(23, 59, 59);

        $queryBuilder = $this->createQueryBuilder('o');
        $queryBuilder
            ->join('o.origin', 'og')
            ->join('o.shippingMethod', 'sm')
            ->join('o.customer', 'c')
            ->andWhere('o.orderDate BETWEEN :fromDate AND :endDate')
            ->andWhere('o.invoiceNumber != 0')
            ->andWhere('o.ipNumber NOT IN (:ipAddresses)')
            ->andWhere('o.origin IN (:origins)')
            ->andWhere('sm != :cancelled')
            ->andWhere('c.customerType NOT IN (:ignoreCustomerTypes)')
            ->setParameter('fromDate', $from->format('Y-m-d'))
            ->setParameter('endDate', $to->format('Y-m-d'))
            ->setParameter('ipAddresses', $ipAddresses)
            ->setParameter('origins', $origins)
            ->setParameter('cancelled', ShipmentMethod::CANCELLED)
            ->setParameter('ignoreCustomerTypes', $ignoreCustomerTypes)
            ->addOrderBy('og.id', 'ASC')
            ->addOrderBy('o.orderDate', 'ASC');

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return OrderInfo[]
     */
    public function getPaidUnfinishedWithEventRegistrations(): array
    {
        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder
            ->select('o')
            ->innerJoin(Registration::class, 'r', Join::WITH, 'r.order = o.id')
            ->andWhere($queryBuilder->expr()->eq('BIT_AND(o.flags, :orderPaid)', ':orderPaid'))
            ->andWhere('r.isCancelled = :isCancelled')
            ->andWhere('o.invoiceNumber = :invoiceNumber')
            ->setParameter('orderPaid', OrderInfo::FLAG_PAYMENT_COMPLETE + OrderInfo::FLAG_PAYMENT_OKAY)
            ->setParameter('isCancelled', 0)
            ->setParameter('invoiceNumber', 0);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return OrderInfo[]
     */
    public function getEventOrdersWithPossibleWrongPaymentStatus(): array
    {
        $queryBuilder = $this->createQueryBuilder('o');

        $queryBuilder
            ->select('o')
            ->innerJoin(Registration::class, 'r', Join::WITH, 'r.order = o.id')
            ->innerJoin(ExternalPayment::class, 'ep', Join::WITH, 'ep.order = o.id')
            ->andWhere($queryBuilder->expr()->eq('BIT_AND(o.flags, :orderPaid)', ':orderNotPaid'))
            ->andWhere('r.isCancelled = :isCancelled')
            ->andWhere('o.invoiceNumber = :invoiceNumber')
            ->setParameter('orderPaid', OrderInfo::FLAG_PAYMENT_COMPLETE + OrderInfo::FLAG_PAYMENT_OKAY)
            ->setParameter('orderNotPaid', 0)
            ->setParameter('isCancelled', 0)
            ->setParameter('invoiceNumber', 0);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function getEventRegistrationsPaidWithLedgers(): array
    {
        $sql = <<<SQL
        select
            bn.id,
            ifnull(sum(bb.bedrag), 0) as total_paid,
            ifnull(sum(s.aantal * s.prijs) + bn.verzendkosten + bn.betaalkosten, 0) as total_due,
            ifnull(sum(bv.bedrag), 0) as total_ledger
        from cameranu.bestelling_naw as bn
        inner join cameranu.event_registrations as er on er.order_id = bn.id
        left join cameranu.shopcart as s on s.bestelling_id = bn.id
        left join cameranu.bestelling_betaling as bb on bb.bestelling_id = bn.id
        left join cameranu.bestelling_velden as bv on bv.bestelling_id = bn.id
        where bn.factuurnummer = 0 -- nog geen factuurnummer
        and (bn.flags&:orderPaidFlags)=0 -- betaling niet binnen
        and er.is_cancelled = 0 and bn.verzendwijze != :shippingMethod -- niet geannuleerd
        and bn.betaalwijze_id = :paymentMethod -- mollie
        group by bn.id
        having total_paid >= total_due + total_ledger
        SQL;

        $result = $this->getEntityManager()->getConnection()->executeQuery($sql, [
            'orderPaidFlags' => OrderInfo::FLAG_PAYMENT_COMPLETE + OrderInfo::FLAG_PAYMENT_OKAY,
            'shippingMethod' => ShippingMethod::CANCELLED,
            'paymentMethod' => PaymentMethod::ID_MOLLIE,
        ]);

        return $result->fetchAllAssociative();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function getBoughtAccessoriesTotalForAllProductWithinDate
    (
        DateTime $start,
        DateTime $end,
        ?OutputInterface $output = null
    ): array {
        $productTotals = [];
        $output?->writeln('Start getBoughtAccessoriesTotalForAllProductWithinDate');

        $output?->writeln('Start SQL minID shopcart');
        $sql = '
            SELECT MIN(id)
            FROM cameranu.shopcart
            WHERE tstamp >= :start
              AND tstamp < :end;
        ';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);

        $query->bindValue('start', $start->format('Y-m-d H:i:s'));
        $query->bindValue('end', $end->format('Y-m-d H:i:s'));

        $id = $query->executeQuery()->fetchOne();
        $output?->writeln('End SQL minID shopcart');

        // Set SQL in arrays
        $customer_type = sprintf('\'%s\',\'%s\'',
            Customer::TYPE_CUSTOMER,
            Customer::TYPE_SMALL_BUSINESS
        );

        $parent = '\''. implode('\',\'',Origin::CAMERANU_PARENTS) .'\'';
        $excludedGroups = implode(',',[
            Rootgroup::DIGITAL_CAMERAS_ID,
            Rootgroup::MEMORY_ID,
            Rootgroup::ACCU_ID,
            Rootgroup::LENSES_ID
        ]);

        $output?->writeln('Start SQL get total for sets');

        $sql = '
            WITH filtered_shopcart AS (
                SELECT
                    artikel_id,
                    bestelling_id,
                    prijs,
                    aantal,
                    shopcart.status as status
                FROM
                    cameranu.shopcart as shopcart
                INNER JOIN cameranu.bestelling_naw on bestelling_naw.id = shopcart.bestelling_id
                INNER JOIN cameranu.bestelling_herkomst on bestelling_herkomst.id = bestelling_naw.herkomst
                INNER JOIN cameranu.webusers on bestelling_naw.klantnummer = webusers.id
                WHERE
                    shopcart.id >= :minId
                    AND bestelling_id > 0
                    AND bestelling_naw.verzendwijze != :cancelled
                    AND bestelling_herkomst.parent in ('. $parent .')
                    AND webusers.customer_type in ('. $customer_type .')
            ), product_types AS (
                 SELECT
                     a.id,
                     sas.value
                 FROM cameranu.specs_article_profile sap
                 INNER JOIN cameranu.artikelen a on sap.articleId = a.id
                 INNER JOIN cameranu.specs_article_specifications sas On a.id = sas.articleId and sas.specId IN (27116, 26266)
                 WHERE sas.value != \'\'
            )

            SELECT
                product_id,
                setArticles.id as accessory_id,
                (
                    SELECT sap.profileId
                    FROM cameranu.specs_article_profile sap
                    WHERE sap.articleId = setArticles.id
                    LIMIT 1
                ) as spec_profile,
                (
                    SELECT product_types.value
                    FROM product_types
                    WHERE product_types.id = setArticles.id
                    LIMIT 1
                ) as product_type,
                sum(ass_sh.aantal) as total
            FROM
                cameranu.artikelen

            INNER JOIN cameranu.subgroepen subgroep ON subgroep.id = artikelen.subgroep_id
            INNER JOIN cameranu.hoofdgroepen hoofdgroep ON hoofdgroep.id = subgroep.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen rootgroep ON rootgroep.id = hoofdgroep.rootgroep_id
            INNER JOIN cameranu.product_accessoryset ON artikelen.id = product_accessoryset.product_id
            INNER JOIN cameranu.accessoryset_position ON product_accessoryset.accessoryset_id = accessoryset_position.tag_id
            INNER JOIN cameranu.tags_products ON accessoryset_position.tags_products_id = tags_products.tags_products_id
            INNER JOIN filtered_shopcart as ar_sh ON ar_sh.artikel_id = artikelen.id

            INNER JOIN cameranu.artikelen AS setArticles ON tags_products.itemId = setArticles.id
            INNER JOIN cameranu.subgroepen ac_subgroep ON ac_subgroep.id = setArticles.subgroep_id
            INNER JOIN cameranu.hoofdgroepen ac_hoofdgroep ON ac_hoofdgroep.id = ac_subgroep.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen ac_rootgroep ON ac_rootgroep.id = ac_hoofdgroep.rootgroep_id
            INNER JOIN filtered_shopcart as ass_sh ON ass_sh.artikel_id = setArticles.id
                AND ass_sh.bestelling_id = ar_sh.bestelling_id
                AND ass_sh.prijs BETWEEN :min AND :max
                AND (ass_sh.status & :free_flag ) != :free_flag
            WHERE (setArticles.flags & :productFlags) = :productFlags
            AND (ac_subgroep.flags & :subGroupFlags) = :subGroupFlags
            AND (ac_hoofdgroep.flags & :mainGroupFLags) = :mainGroupFLags
            AND (ac_rootgroep.flags & :rootGroupFlags) = :rootGroupFlags
            AND (artikelen.flags & :productFlags) = :productFlags
            AND (subgroep.flags & :subGroupFlags) = :subGroupFlags
            AND (hoofdgroep.flags & :mainGroupFLags) = :mainGroupFLags
            AND (rootgroep.flags & :rootGroupFlags) = :rootGroupFlags
            AND
            (
                (rootgroep.id = :lensesId AND ac_rootgroep.id NOT IN ('. $excludedGroups .'))
                OR
                (rootgroep.id != :lensesId)
            )

            GROUP BY product_id, accessory_id
        ';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);

        $query->bindValue('min', 10);
        $query->bindValue('max', 300);
        $query->bindValue('minId', $id);
        $query->bindValue('free_flag', CartItem::FLAG_IS_FREE);
        $query->bindValue('cancelled', ShipmentMethod::CANCELLED);
        $query->bindValue('lensesId', Rootgroup::LENSES_ID);
        $query->bindValue('productFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFLags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);

        foreach ($query->executeQuery()->fetchAllAssociative() as $product){
            if (!key_exists($product['product_id'], $productTotals)) {
                $productTotals[$product['product_id']] = [];
            }

            $productTotals[$product['product_id']][$product['accessory_id']]['amount'] = $product['total'];
            $productTotals[$product['product_id']][$product['accessory_id']]['spec_profile'] = $product['spec_profile'];
            $productTotals[$product['product_id']][$product['accessory_id']]['product_type'] = $product['product_type'];
        }


        $output?->writeln('End SQL get total for sets');

        $output?->writeln('Start SQL get total for old accessory');
        $sql = '
            WITH filtered_shopcart AS (
                SELECT
                    artikel_id,
                    bestelling_id,
                    prijs,
                    aantal,
                    shopcart.status as status
                FROM
                    cameranu.shopcart as shopcart
                INNER JOIN cameranu.bestelling_naw on bestelling_naw.id = shopcart.bestelling_id
                INNER JOIN cameranu.bestelling_herkomst on bestelling_herkomst.id = bestelling_naw.herkomst
                INNER JOIN cameranu.webusers on bestelling_naw.klantnummer = webusers.id
                WHERE
                    shopcart.id >= :minId
                    AND bestelling_id > 0
                    AND bestelling_naw.verzendwijze != :cancelled
                    AND bestelling_herkomst.parent in ('. $parent .')
                    AND webusers.customer_type in ('. $customer_type .')
            ), product_types AS (
                 SELECT
                     a.id,
                     sas.value
                 FROM cameranu.specs_article_profile sap
                 INNER JOIN cameranu.artikelen a on sap.articleId = a.id
                 INNER JOIN cameranu.specs_article_specifications sas On a.id = sas.articleId and sas.specId IN (27116, 26266)
                 WHERE sas.value != \'\'
            )

             SELECT
                ar_sh.artikel_id AS product_id,
                ass_sh.artikel_id as accessory_id,
                (
                    SELECT sap.profileId
                    FROM cameranu.specs_article_profile sap
                    WHERE sap.articleId = ass_sh.artikel_id
                    LIMIT 1
                ) as spec_profile,
                (
                    SELECT product_types.value
                    FROM product_types
                    WHERE product_types.id = ass_sh.artikel_id
                    LIMIT 1
                ) as product_type,
                sum(ass_sh.aantal) as total
            FROM
                cameranu.extra_opties
            INNER JOIN filtered_shopcart AS ar_sh
                ON ar_sh.artikel_id = extra_opties.parent_artikel_id
            INNER JOIN filtered_shopcart AS ass_sh
                ON ass_sh.artikel_id = extra_opties.eo_artikel_id and ass_sh.bestelling_id = ar_sh.bestelling_id
            INNER JOIN cameranu.artikelen as ar on ar_sh.artikel_id = ar.id
            INNER JOIN cameranu.artikelen as ass on ass_sh.artikel_id = ass.id
            INNER JOIN cameranu.subgroepen subgroep ON subgroep.id = ar.subgroep_id
            INNER JOIN cameranu.hoofdgroepen hoofdgroep ON hoofdgroep.id = subgroep.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen rootgroep ON rootgroep.id = hoofdgroep.rootgroep_id
            WHERE
                ass_sh.prijs BETWEEN :min AND :max
                AND (ass_sh.status & :free_flag ) != :free_flag
                AND (ass.flags & :productFlags) = :productFlags
                AND (subgroep.flags & :subGroupFlags) = :subGroupFlags
                AND (hoofdgroep.flags & :mainGroupFLags) = :mainGroupFLags
                AND (rootgroep.flags & :rootGroupFlags) = :rootGroupFlags

            group by product_id, accessory_id;
        ';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);

        $query->bindValue('min', 10);
        $query->bindValue('max', 300);
        $query->bindValue('minId', $id);
        $query->bindValue('free_flag', CartItem::FLAG_IS_FREE);
        $query->bindValue('cancelled', ShipmentMethod::CANCELLED);
        $query->bindValue('productFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFLags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);

        foreach ($query->executeQuery()->fetchAllAssociative() as $product) {
            if (!key_exists($product['product_id'], $productTotals)) {
                $productTotals[$product['product_id']] = [];
            }

            if (key_exists($product['accessory_id'], $productTotals[$product['product_id']])) {
                continue;
            }

            $productTotals[$product['product_id']][$product['accessory_id']]['amount'] = $product['total'];
            $productTotals[$product['product_id']][$product['accessory_id']]['spec_profile'] = $product['spec_profile'];
            $productTotals[$product['product_id']][$product['accessory_id']]['product_type'] = $product['product_type'];
        }

        $output?->writeln('End SQL get total for old accessory');

        return $productTotals;
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function findStrayOrders(DateTime $from, bool $waitingForPayment = false): array
    {
        if ($waitingForPayment === true) {
            $paymentFlag = OrderInfo::FLAG_WAITING_FOR_PAYMENT;
        } else {
            $paymentFlag = OrderInfo::FLAG_PAYMENT_COMPLETE;
        }

        $sql = <<<'SQL'
        select
            bn.id as bestelling_id,
            (
                select count(s.id) from cameranu.shopcart as s
                inner join cameranu.artikelen as a on a.id = s.artikel_id
                where s.bestelling_id = bn.id and (a.flags2&:dividedByTeamSales)=:dividedByTeamSales
            ) as verdeling_team_sales
        from cameranu.bestelling_naw as bn
        inner join cameranu.bestelling_betaling as bb on bb.bestelling_id = bn.id
        inner join cameranu.verzendwijze as v on v.id = bn.verzendwijze
        inner join cameranu.shopcart as s on s.bestelling_id = bn.id
        inner join cameranu.artikelen as a on a.id = s.artikel_id
        where bn.factuurnummer = 0
        and bn.parkeren_id = 0
        and bn.besteldatum >= :fromDate
        and (bn.flags&:paymentStatus)=:paymentStatus
        and (bn.flags&:allowPartialDelivery)!=:allowPartialDelivery
        and bn.verzendwijze not in (
            :shippingMethodCancelled,
            :shippingMethodCash,
            :shippingMethodCourier,
            :shippingMethodCourierFree,
            :shippingMethodQuote
        )
        and bn.verzendwijze not in (:shippingMethodsPickup)
        group by bn.id
        having verdeling_team_sales = 0
        order by bn.id
        SQL;

        $result = $this->getEntityManager()->getConnection()->executeQuery($sql, [
            'wasInStock' => CartItem::FLAG_WAS_IN_STOCK,
            'dividedByTeamSales' => Product::FLAG2_DIVIDE_BY_SALES_TEAM,
            'fromDate' => $from->format('Y-m-d'),
            'paymentStatus' => $paymentFlag,
            'allowPartialDelivery' => OrderInfo::FLAG_PARTIAL_DELIVERY,
            'shippingMethodCancelled' => ShipmentMethod::CANCELLED,
            'shippingMethodCash' => ShipmentMethod::CASH,
            'shippingMethodCourier' => ShipmentMethod::ID_COURIER,
            'shippingMethodCourierFree' => ShipmentMethod::COURIER_FREE,
            'shippingMethodPickup' => ShipmentMethod::PICKUP,
            'shippingMethodQuote' => ShipmentMethod::QUOTATION,
            'shippingMethodsPickup' => ShipmentMethod::PICK_UP_METHODS,
        ], [
            'shippingMethodsPickup' => ArrayParameterType::INTEGER,
        ]);

        $orderIds = array_keys($result->fetchAllKeyValue());

        return $this->findBy(['id' => $orderIds]);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function findDebtorsToCheck(DateTime $from): array
    {
        $sql = <<<'SQL'
        select bn.id, bn.factuurnummer from cameranu.bestelling_naw bn
        left join cameranu.afas_debtor_status ads on ads.order_id = bn.id
        left join cameranu.afas_invoice_info aii on aii.order_id = bn.id
        inner join cameranu.betaalwijze1 b on b.id = bn.betaalwijze_id
        where (ads.id is null or ads.settled = 0)
        and bn.besteldatum >= :fromDate
        and bn.betaalwijze_id not in (
            :paymentMethodCash,
            :paymentMethodMollie
        )
        and bn.verzendwijze != :shippingMethodCancelled
        and bn.factuurnummer > 0
        SQL;

        $result = $this->getEntityManager()->getConnection()->executeQuery($sql, [
            'fromDate' => $from->format('Y-m-d'),
            'paymentMethodCash' => PaymentMethod::PIN_CASH,
            'paymentMethodMollie' => PaymentMethod::ID_MOLLIE,
            'shippingMethodCancelled' => ShipmentMethod::CANCELLED,
        ]);

        $orderIds = array_keys($result->fetchAllKeyValue());

        return $this->findBy(['id' => $orderIds]);
    }
}
