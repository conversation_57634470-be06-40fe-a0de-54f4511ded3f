<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\Common\Collections\ArrayCollection;

/**
 * UblLine
 *
 *
 */
#[ORM\Entity(repositoryClass: UblLineRepository::class)]
#[ORM\InheritanceType('SINGLE_TABLE')]
#[ORM\DiscriminatorColumn(name: 'line_type', type: 'string')]
#[ORM\DiscriminatorMap(['cac' => 'UblCacLine', 'cbc' => 'UblCbcLine'])]
#[ORM\Table(name: 'cameranu.ubl_line')]
abstract class UblLine
{
    /**
     * @var integer
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'name', type: 'text')]
    private $name;

    /**
     * @var string
     */
    #[ORM\Column(name: 'path', type: 'text')]
    private $path;

    #[ORM\ManyToOne(targetEntity: UblLine::class)]
    #[ORM\JoinColumn(name: 'invoice_line_id', referencedColumnName: 'id')]
    private $invoiceLine;

    #[ORM\ManyToOne(targetEntity: UblCacLine::class, inversedBy: 'children')]
    #[ORM\JoinColumn(name: 'parent_id', referencedColumnName: 'id')]
    private $parent;

    #[ORM\ManyToOne(targetEntity: Ubl::class, inversedBy: 'ublLines')]
    #[ORM\JoinColumn(name: 'ubl_id', referencedColumnName: 'id')]
    private $ubl;

    #[ORM\OneToMany(mappedBy: 'ublLine', targetEntity: UblAttribute::class)]
    protected $attributes;

    #[ORM\Column(name: 'content', type: 'text')]
    protected $content;

    /**
     * @return mixed
     */
    public function getContent()
    {
        return $this->content;
    }

    /**
     * @param mixed $content
     */
    public function setContent($content)
    {
        $this->content = $content;
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getParent()
    {
        return $this->parent;
    }

    /**
     * @param mixed $parent
     */
    public function setParent($parent)
    {
        $this->parent = $parent;
    }

    /**
     * @return mixed
     */
    public function getUbl()
    {
        return $this->ubl;
    }

    /**
     * @param mixed $ubl
     */
    public function setUbl($ubl)
    {
        $this->ubl = $ubl;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getAttributes()
    {
        return $this->attributes;
    }

    /**
     * @param mixed $attributes
     */
    public function setAttributes($attributes)
    {
        $this->attributes = $attributes;
    }

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->attributes = new ArrayCollection();
    }

    /**
     * Add attribute
     *
     * @param UblAttribute $attribute
     *
     * @return UblLine
     */
    public function addAttribute(UblAttribute $attribute)
    {
        $this->attributes[] = $attribute;

        return $this;
    }

    /**
     * Remove attribute
     *
     * @param UblAttribute $attribute
     */
    public function removeAttribute(UblAttribute $attribute)
    {
        $this->attributes->removeElement($attribute);
    }

    /**
     * @return mixed
     */
    public function getPath()
    {
        return $this->path;
    }

    /**
     * @param mixed $path
     */
    public function setPath($path)
    {
        $this->path = $path;
    }

    /**
     * @return mixed
     */
    public function getInvoiceLine()
    {
        return $this->invoiceLine;
    }

    /**
     * @param mixed $invoiceLine
     */
    public function setInvoiceLine($invoiceLine)
    {
        $this->invoiceLine = $invoiceLine;
    }

    abstract protected function getPrefix();

    abstract protected function generateXML(\SimpleXMLElement &$xml);

    public function getPrefixedName()
    {
        return $this->getPrefix() . $this->getName();
    }

    public function generateXMLAttributes(\SimpleXMLElement &$xml)
    {
        foreach ($this->getAttributes() as $attribute) {
            $xml->addAttribute($attribute->getName(), $attribute->getValue());
        }
    }
}
