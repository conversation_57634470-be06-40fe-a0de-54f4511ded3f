<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: StockInLocationRepository::class)]
#[ORM\Table('cameranu.articles_stock_per_location')]
class StockInLocation
{
    /**
     * @var int $id
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private int $id;
    /**
     * @var Product $product
     */
    #[ORM\ManyToOne(targetEntity: Product::class, inversedBy: 'stockInLocations')]
    #[ORM\JoinColumn(name: 'product_id', referencedColumnName: 'id')]
    private Product $product;
    /**
     * @var StockLocation $stockLocation
     */
    #[ORM\ManyToOne(targetEntity: StockLocation::class)]
    #[ORM\JoinColumn(name: 'stock_location_id', referencedColumnName: 'id')]
    private StockLocation $stockLocation;
    /**
     * @var int|null $minimalStock
     */
    #[ORM\Column(name: 'minimal_stock', type: 'integer')]
    private ?int $minimalStock;
    /**
     * @var int|null $stock
     */
    #[ORM\Column(name: 'stock', type: 'integer')]
    private ?int $stock;
    /**
     * @var int|null $virtualStock
     */
    #[ORM\Column(name: 'virtual_stock', type: 'integer')]
    private ?int $virtualStock;
    /**
     * @var int|null $reservedStock
     */
    #[ORM\Column(name: 'reserved_stock', type: 'integer', nullable: true)]
    private ?int $reservedStock;

    #[ORM\Column(name: 'reserved_stock_for_route', type: 'integer', nullable: true)]
    private ?int $reservedStockForRoute;

    #[ORM\Column(name: 'target_wos', type: 'integer', nullable: true, options: ['unsigned' => true])]
    private ?int $targetWoS;

    /**
     * @var string|null $minimalStockLog
     */
    #[ORM\Column(name: 'minimal_stock_log', type: 'string')]
    private ?string $minimalStockLog;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return Product
     */
    public function getProduct(): Product
    {
        return $this->product;
    }

    /**
     * @param Product $product
     * @return StockInLocation
     */
    public function setProduct(Product $product): StockInLocation
    {
        $this->product = $product;

        return $this;
    }

    /**
     * @return StockLocation
     */
    public function getStockLocation(): StockLocation
    {
        return $this->stockLocation;
    }

    /**
     * @param StockLocation $stockLocation
     * @return StockInLocation
     */
    public function setStockLocation(StockLocation $stockLocation): StockInLocation
    {
        $this->stockLocation = $stockLocation;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getMinimalStock(): ?int
    {
        return $this->minimalStock;
    }

    /**
     * @param int|null $minimalStock
     * @return StockInLocation
     */
    public function setMinimalStock(?int $minimalStock): StockInLocation
    {
        $this->minimalStock = $minimalStock;

        //Winkel Urk en magazijn ook nog op de ouderwetse manier updaten
        if (
            $this->getStockLocation() instanceof StockLocation &&
            $this->getProduct() instanceof Product &&
            $this->getProduct()->getId() !== 0
        ) {
            if (
                $this->getStockLocation()->getId() === StockLocation::WINKEL_URK &&
                $this->getMinimalStock() !== $this->getProduct()->getMinStockW()
            ) {
                $this->getProduct()->setMinStockW($minimalStock);
            }

            if (
                $this->getStockLocation()->getId() === StockLocation::MAGAZIJN_URK &&
                $this->getMinimalStock() !== $this->getProduct()->getMinStock()
            ) {
                $this->getProduct()->setMinStock($minimalStock);
            }
        }

        return $this;
    }

    /**
     * @return int|null
     */
    public function getStock(): ?int
    {
        return $this->stock;
    }

    /**
     * @param int|null $stock
     * @return StockInLocation
     */
    public function setStock(?int $stock): StockInLocation
    {
        $this->stock = $stock;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getVirtualStock(): ?int
    {
        return $this->virtualStock;
    }

    /**
     * @param int|null $virtualStock
     * @return StockInLocation
     */
    public function setVirtualStock(?int $virtualStock): StockInLocation
    {
        $this->virtualStock = $virtualStock;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getMinimalStockLog(): ?string
    {
        return $this->minimalStockLog;
    }

    /**
     * @param string|null $minimalStockLog
     * @return StockInLocation
     */
    public function setMinimalStockLog(?string $minimalStockLog): StockInLocation
    {
        $this->minimalStockLog = $minimalStockLog;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getReservedStock(): ?int
    {
        return $this->reservedStock;
    }

    /**
     * @param int|null $reservedStock
     * @return StockInLocation
     */
    public function setReservedStock(?int $reservedStock): StockInLocation
    {
        $this->reservedStock = $reservedStock;

        return $this;
    }

    public function getReservedStockForRoute(): ?int
    {
        return $this->reservedStockForRoute;
    }

    public function setReservedStockForRoute(?int $reservedStockForRoute): StockInLocation
    {
        $this->reservedStockForRoute = $reservedStockForRoute;
        return $this;
    }

    public function getTargetWoS(): ?int
    {
        return $this->targetWoS;
    }

    public function setTargetWoS(?int $targetWoS): StockInLocation
    {
        $this->targetWoS = $targetWoS;
        return $this;
    }
}
