<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * Wex hoofdgroep
 */
#[ORM\Entity(repositoryClass: WexMaingroupRepository::class)]
#[ORM\Table('cameranu.wex_hoofdgroepen')]
class WexMaingroup
{
    public const ID_CAMERAS = 1;
    public const ID_CAMERA_LENSES = 2;
    public const ID_CAMERA_ACCESSORIES = 3;
    public const ID_COMPUTING_OUTPUT = 4;
    public const ID_OTHER_ACCESSORIES = 5;
    public const ID_LIGHTING_STUDIO = 6;
    public const ID_VIDEO_CONSUMER = 7;
    public const ID_OPTICS = 9;
    public const ID_VIDEO_PRO = 8;

    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    private int $id;

    #[ORM\Column(name: 'description', type: 'string', length: 255)]
    private string $description;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): WexMaingroup
    {
        $this->id = $id;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): WexMaingroup
    {
        $this->description = $description;
        return $this;
    }
}
