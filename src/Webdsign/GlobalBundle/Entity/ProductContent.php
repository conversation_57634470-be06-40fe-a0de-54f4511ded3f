<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity;

use ContentBundle\Entity\Experiment;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProductContentRepository::class)]
#[ORM\Table(name: 'cameranu.artikelen_content')]
class ProductContent
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;
    #[ORM\ManyToOne(targetEntity: Product::class, inversedBy: 'content')]
    #[ORM\JoinColumn(name: 'artikel_id', referencedColumnName: 'id', nullable: false)]
    private Product $product;
    #[ORM\Column(name: 'pos', type: 'integer')]
    private int $position;
    #[ORM\Column(name: 'tab', type: 'integer')]
    private int $tab;
    #[ORM\Column(name: 'title', type: 'text')]
    private string $title;
    #[ORM\Column(name: 'content', type: 'text')]
    private string $content;
    #[ORM\Column(name: 'ingangsdatum', type: 'date')]
    private DateTime $startDate;
    #[ORM\Column(name: 'vervaldatum', type: 'date')]
    private DateTime $endDate;
    #[ORM\ManyToOne(targetEntity: Experiment::class, inversedBy: 'content')]
    #[ORM\JoinColumn(name: 'experiment', referencedColumnName: 'id', nullable: true)]
    private ?Experiment $experiment;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function setProduct(Product $product): ProductContent
    {
        $this->product = $product;

        return $this;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function setPosition(int $position): ProductContent
    {
        $this->position = $position;

        return $this;
    }

    public function getTab(): int
    {
        return $this->tab;
    }

    public function setTab(int $tab): ProductContent
    {
        $this->tab = $tab;

        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): ProductContent
    {
        $this->title = $title;

        return $this;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function setContent(string $content): ProductContent
    {
        $this->content = $content;

        return $this;
    }

    public function getStartDate(): DateTime
    {
        return $this->startDate;
    }

    public function setStartDate(DateTime $startDate): ProductContent
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): DateTime
    {
        return $this->endDate;
    }

    public function setEndDate(DateTime $endDate): ProductContent
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getExperiment(): ?Experiment
    {
        return $this->experiment;
    }

    public function setExperiment(?Experiment $experiment): void
    {
        $this->experiment = $experiment;
    }
}
