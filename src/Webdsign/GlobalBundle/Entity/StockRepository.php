<?php

namespace Webdsign\GlobalBundle\Entity;

use CatB<PERSON>le\Command\CalculateStockAvailabilityCommand;
use CatB<PERSON>le\Form\Filter\SecondHand\NewStockListFilter;
use CatBundle\Service\Stock\StockHelper;
use DateInterval;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Expr;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use StatisticsBundle\Services\Supplier\PurchaseOverviewHelper;

/**
 * @method Stock|null find($id, $lockMode = null, $lockVersion = null)
 * @method Stock[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method Stock|null findOneBy(array $criteria, array $orderBy = null)
 */
class StockRepository extends WebdsignRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Stock::class);
    }

    /**
     * Voorraad tussen twee datums.
     *
     * @param DateTime $start
     * @param DateTime $stop
     * @return $this
     */
    public function betweenDates(DateTime $start, DateTime $stop): self
    {
        $queryBuilder = $this->getQueryBuilder('st');
        $queryBuilder
            ->andWhere('st.dateIn BETWEEN :dateInStart AND :dateInStop')
            ->setParameter('dateInStart', $start)
            ->setParameter('dateInStop', $stop)
            ->andWhere('st.dateOut IS NULL');

        return $this;
    }

    /**
     * Voorraad tussen twee datums op basis van verkopen.
     *
     * @param DateTime $start
     * @param DateTime $stop
     * @return StockRepository
     */
    public function betweenSalesDates(DateTime $start, DateTime $stop): StockRepository
    {
        $queryBuilder = $this->getQueryBuilder('st');
        $queryBuilder
            ->leftJoin('st.order', 'o')
            ->andWhere('o.dateInvoice BETWEEN :dateInvoiceStart AND :dateInvoiceStop')
            ->setParameter('dateInvoiceStart', $start)
            ->setParameter('dateInvoiceStop', $stop)
            ->andWhere('st.dateOut IS NULL');

        return $this;
    }

    /**
     * Alleen voor een bepaalde leverancier.
     *
     * @param Supplier $supplier
     * @return $this
     */
    public function forSupplier(Supplier $supplier): self
    {
        $queryBuilder = $this->getQueryBuilder('st');
        $queryBuilder
            ->innerJoin('st.supplier', 'su', Expr\Join::WITH, 'su.id = :supplier')
            ->setParameter('supplier', $supplier)
            ->andWhere('st.dateOut IS NULL');

        return $this;
    }

    public function getLastSoldForProduct(Product $product): null|Stock
    {
        $queryBuilder = $this->createQueryBuilder('st');
        $queryBuilder
            ->select('st')
            ->join('st.product', 'p')
            ->join('st.order', 'o')
            ->andWhere('st.product = :product')
            ->andWhere('st.dateOut IS NULL')
            ->setParameter('product', $product)
            ->addOrderBy(
                "CASE WHEN o.dateInvoice = '0000-00-00 00:00:00' THEN 0 ELSE 1 END",
                'ASC'
            )
            ->addOrderBy('o.dateInvoice', 'DESC')
            ->setMaxResults(1);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findProductIdsWithStock(): array
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $zeroOrder = (new OrderInfo())->setId(0);

        $queryBuilder
            ->select('p.id')
            ->join('s.product', 'p')
            ->andWhere('s.dateOut IS NULL')
            ->andWhere('s.order = :zeroOrder')
            ->setParameter('zeroOrder', $zeroOrder)
            ->groupBy('p');

        $result = $queryBuilder->getQuery()->getArrayResult();
        return array_column($result, 'id');
    }

    /**
     * Alleen voor een bepaalde groep leveranciers.
     *
     * @param SupplierGroup $supplierGroup
     * @return $this
     */
    public function forSupplierGroup(SupplierGroup $supplierGroup): self
    {
        $queryBuilder = $this->getQueryBuilder('st');
        $queryBuilder
            ->innerJoin('st.supplier', 'su')
            ->innerJoin('su.group', 'sug', Expr\Join::WITH, 'sug.id = :supplierGroup')
            ->setParameter('supplierGroup', $supplierGroup)
            ->andWhere('st.dateOut IS NULL');

        return $this;
    }

    /**
     * Haal voorraad op
     *
     * @return array|Query
     */
    public function get()
    {
        $queryBuilder = $this->getQueryBuilder('st');

        if ($this->getPaginate() === true) {
            return $queryBuilder->getQuery();
        }

        return $queryBuilder
            ->getQuery()
            ->getResult();
    }

    /**
     * @param Product $product
     * @param bool $onlyUnsold
     * @return float|null
     */
    public function getAveragePurchasePrice(Product $product, bool $onlyUnsold = true): ?float
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder->select([$queryBuilder->expr()->avg('s.purchasePrice')])
            ->where('s.product = :product')
            ->setParameter('product', $product)
            ->andWhere('s.dateOut IS NULL')
            ->setMaxResults(1);

        if ($onlyUnsold === true) {
            $queryBuilder->andWhere('s.ordernumber = 0');
        }

        try {
            $result = $queryBuilder->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $e) {
            $result = null;
        }
        if ($result !== null && !empty($result[1])) {
            return (float)$result[1];
        }
        return null;
    }

    /**
     * @param Product $product
     * @return float|null
     */
    public function getAveragePurchaseInvoicePrice(Product $product): ?float
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder->select([$queryBuilder->expr()->avg('po.invoicePrice')])
            ->where('s.product = :product')
            ->join('s.purchaseOrder', 'po')
            ->setParameter('product', $product)
            ->andWhere('s.ordernumber = 0')
            ->andWhere('s.dateOut IS NULL')
            ->setMaxResults(1);

        try {
            $result = $queryBuilder->getQuery()->getOneOrNullResult();
        } catch (NonUniqueResultException $e) {
            $result = null;
        }
        if ($result !== null && !empty($result[1])) {
            return (float)$result[1];
        }
        return null;
    }

    /**
     * Retouren voor een artikel tussen 2 datums ophalen
     *
     * @param Product $article
     * @param DateTime $start
     * @param DateTime $stop
     * @return array
     */
    public function getArticleReturnsBetweenDates(Product $article, DateTime $start, DateTime $stop): array
    {
        $queryBuilder = $this->createQueryBuilder('t');
        $queryBuilder->select('t.id')
            ->leftJoin('t.articleReturn', 'pr')
            ->andWhere('pr.date BETWEEN :dateStart AND :dateStop')
            ->andWhere('t.product = :article')
            ->setParameter('dateStart', $start)
            ->setParameter('dateStop', $stop)
            ->setParameter('article', $article)
            ->andWhere('t.dateOut IS NULL');

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * @param int $id
     * @return Stock|null
     */
    public function findOneById(int $id): ?Stock
    {
        return $this->findOneBy([
            'id' => $id,
        ]);
    }

    /**
     * @param array $stockIds
     * @return Stock[]
     */
    public function findById(array $stockIds): array
    {
        return $this->findBy([
            'id' => $stockIds,
        ]);
    }

    /**
     * @param Product $product
     * @param StockLocation[] $stockLocations
     * @param string|null $barcode
     * @param StockLocation|null $stockLocation
     * @return Stock[]
     */
    public function findByBarcodeLocationAndProduct(
        Product $product,
        array $stockLocations,
        ?string $barcode = null,
        ?StockLocation $stockLocation = null
    ): array {
        // create fake order because the database field contains 0 instead of NULL
        $zeroOrder = (new OrderInfo())->setId(0);

        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('s, ar, ad')
            ->andWhere('s.location in (:locations)')
            ->andWhere('s.product = :product')
            ->andWhere('s.order = :order')
            ->leftJoin('s.order', 'o')
            ->leftJoin('s.articleReturn', 'ar')
            ->leftJoin('s.articleDeadOnArrival', 'ad')
            ->setParameter('locations', $stockLocations)
            ->setParameter('product', $product)
            ->setParameter('order', $zeroOrder)
            ->andWhere('s.dateOut IS NULL');

        if ($barcode !== null) {
            $queryBuilder
                ->andWhere('s.barcode = :barcode')
                ->setParameter('barcode', $barcode);
        }

        if ($stockLocation instanceof StockLocation) {
            $queryBuilder->orderBy('FIELD(s.location, ' . $stockLocation->getId() . ')', 'DESC');
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @throws NonUniqueResultException
     */
    public function findByBarcodeAndStockLocation(string $barcode, ?StockLocation $stockLocation = null): ?Stock
    {
        $zeroOrder = (new OrderInfo())->setId(0);

        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('s, ar, ad')
            ->andWhere('s.order = :order')
            ->andWhere('s.dateOut is null')
            ->andWhere('s.barcode = :barcode')
            ->leftJoin('s.order', 'o')
            ->leftJoin('s.articleReturn', 'ar')
            ->leftJoin('s.articleDeadOnArrival', 'ad')
            ->setParameter('order', $zeroOrder)
            ->setParameter('barcode', $barcode)
            ->setMaxResults(1);

        if ($stockLocation !== null) {
            $queryBuilder
                ->andWhere('s.location = :location')
                ->setParameter('location', $stockLocation);
        }

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * Alle voorraad teruggeven die geleverd is vanuit een 'Urk'-locatie maar aan een
     * 'niet-Urk' bestelling vastzit en waar nog geen factuur voor bestaat.
     *
     * @param string $origin
     * @param array $externalParents
     * @param int|null $minimalAge
     * @return Stock[]
     * @throws Exception
     */
    public function findExternalInvoiceable(array $parents, ?int $minimalAge = null): array
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('s')
            ->innerJoin('s.order', 'o')
            ->innerJoin('o.origin', 'oo')
            ->innerJoin('s.location', 'sl')
            ->leftJoin('o.internalInvoices', 'ii')
            ->where('sl.parent IN (:parents)')
            ->andWhere('ii.id IS NULL')
            ->andWhere('oo.parent IN (:parents)')
            ->andWhere('sl.parent != oo.parent')
            ->andWhere('o.invoiceNumber != 0')
            ->setParameter('parents', $parents)
            ->andWhere('s.dateOut IS NULL');

        if ($minimalAge !== null) {
            $createdDate = new DateTime();
            $createdDate->setTime(0, 0);
            $createdDate->sub(new DateInterval('P' . $minimalAge . 'D'));
            $queryBuilder
                ->andWhere('o.dateInvoice < :createdDate')
                ->setParameter('createdDate', $createdDate);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param StockLocation[]|ArrayCollection $stockLocations
     * @return string|null
     */
    public function findTotalValue(array $stockLocations): ?string
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('SUM(s.stockValue) AS totalValue')
            ->where('s.location IN (:stockLocations)')
            ->andWhere('s.order = 0')
            ->andWhere('s.dateOut IS NULL')
            ->setParameter('stockLocations', $stockLocations);

        $result = $queryBuilder->getQuery()->getResult(Query::HYDRATE_ARRAY);

        if (count($result)) {
            return $result[0]['totalValue'];
        }

        return null;
    }

    /**
     * @param array $params
     * @param int $amount
     * @return array
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function addProductToStock(array $params, int $amount): array
    {
        $stockIds = [];

        for ($i = 0; $i < $amount; $i++) {
            $query = '
            INSERT INTO cameranu.voorraad
            (
                artikel_id,
                datum_in,
                tstamp_in,
                barcode,
                prijs_inkoop,
                leverancier_id,
                ingescand_door,
                stock_location_id,
                inserted_stock_location_id,
                bestelmemo_id,
                flags
            )
            VALUES
            (
                :artikel_id,
                :datum_in,
                :tstamp_in,
                :barcode,
                :prijs_inkoop,
                :leverancier_id,
                :ingescand_door,
                :stock_location_id,
                :inserted_stock_location_id,
                :bestelmemo_id,
                :flags
            )
        ';

            $stmt = $this->getEntityManager()->getConnection()->prepare($query);
            $stmt->execute([
                'artikel_id' => $params['productId'],
                'datum_in' => date('Y-m-d'),
                'tstamp_in' => date('Y-m-d H:i:s'),
                'barcode' => $params['barcode'],
                'prijs_inkoop' => $params['purchasePrice'],
                'leverancier_id' => $params['supplierId'],
                'ingescand_door' => $params['userId'],
                'inserted_stock_location_id' => $params['stockLocationId'],
                'stock_location_id' => $params['stockLocationId'],
                'bestelmemo_id' => $params['stockMemoId'],
                'flags' => '0'
            ]);

            $stockIds[] = $this->getEntityManager()->getConnection()->lastInsertId();
        }

        return $stockIds;
    }

    /**
     * @param StockLocation $stockLocation
     * @return array
     */
    public function findByStockLocation(StockLocation $stockLocation): array
    {
        $zeroOrder = (new OrderInfo())->setId(0);

        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('s.id, s.productId, p.name, s.purchasePrice, s.barcode')
            ->addSelect('group_concat(l.compartment) as locations')
            ->innerJoin('s.product', 'p')
            ->leftJoin('p.locationCompartmentNode', 'lcn')
            ->leftJoin('lcn.location', 'l')
            ->andWhere('s.location = :location')
            ->andWhere('s.order = :order')
            ->andWhere('s.dateOut IS NULL')
            ->groupBy('s.id')
            ->setParameter('location', $stockLocation)
            ->setParameter('order', $zeroOrder);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * @param StockLocation[] $stockLocations
     * @return array
     */
    public function findByStockLocations(array $stockLocations): array
    {
        $zeroOrder = (new OrderInfo())->setId(0);

        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('s.id, s.productId, p.name, s.purchasePrice')
            ->addSelect('group_concat(l.compartment) as locations')
            ->innerJoin('s.product', 'p')
            ->leftJoin('p.locationCompartmentNode', 'lcn')
            ->leftJoin('lcn.location', 'l')
            ->andWhere('s.location in (:locations)')
            ->andWhere('s.order = :order')
            ->andWhere('s.dateOut IS NULL')
            ->groupBy('s.id')
            ->setParameter('locations', $stockLocations)
            ->setParameter('order', $zeroOrder);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * @param string $barcode
     * @return Stock|null
     */
    public function findOneByBarcode(string $barcode)
    {
        return $this->findOneBy([
            'barcode' => $barcode
        ]);
    }

    /**
     * @param DateTime $from
     * @param DateTime $to
     * @param array $groupIds
     * @param bool $forEuropaSuppliers
     * @param bool $forAllSuppliers
     * @param bool $forSupplier
     * @return array
     */
    public function findForPurchaseOverview(
        DateTime $from,
        DateTime $to,
        array $groupIds = [],
        bool $forEuropaSuppliers = false,
        bool $forAllSuppliers = false,
        bool $forSupplier = false
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');

        $queryBuilder
            ->select('s.id as stockId')
            ->addSelect('so.id as orderId')
            ->addSelect('p.id as productId')
            ->addSelect('p.name as productName')
            ->addSelect('supplier.id as supplierId')
            ->addSelect('supplier.name')
            ->addSelect('supplier.flags')
            ->addSelect('supplierGroup.id as groupId')
            ->addSelect('supplierGroup.name as groupName')
            ->addSelect('IFNULL(tp.price, 0) as purchaseELP')
            ->addSelect('IFNULL(ppal.amount, 0) as lensPremium')
            ->addSelect('IFNULL(pef.spaPremium, 0) as spaPremium')
            ->addSelect('s.purchasePrice')
            ->addSelect('s.dateIn')
            ->addSelect('po.orderDate')
            ->addSelect('so.dateInvoice')
            ->addSelect('(
                SELECT
                    IFNULL(SUM(claimAndFee.value), 0)
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acaf
                JOIN acaf.claimsAndFees as claimAndFee
                JOIN WebdsignGlobalBundle:Product as product WITH acaf.product = product
                JOIN claimAndFee.kind as kind
                LEFT JOIN claimAndFee.suppliers as sell_in_supplier
                LEFT JOIN claimAndFee.supplierGroups as sell_in_supplier_group
                WHERE product = p
                AND (s.dateIn BETWEEN claimAndFee.dateFrom AND claimAndFee.dateTill)
                AND kind.id = :sellIn
                AND (supplier = sell_in_supplier OR supplier.group = sell_in_supplier_group)
            ) as sellIn')
            ->addSelect('(
                SELECT
                    IFNULL(SUM(CASE WHEN margin_fl1.code = :fl1 THEN claimAndFeeFL1.value ELSE 0 END), 0)
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acaf_fl1
                JOIN acaf_fl1.claimsAndFees as claimAndFeeFL1
                JOIN WebdsignGlobalBundle:Product as product_fl1 WITH acaf_fl1.product = product_fl1
                JOIN claimAndFeeFL1.kind as kind_fl1
                JOIN claimAndFeeFL1.flMargin as margin_fl1
                LEFT JOIN claimAndFeeFL1.suppliers as sell_in_fl1_supplier
                LEFT JOIN claimAndFeeFL1.supplierGroups as sell_in_fl1_supplier_group
                WHERE product_fl1 = p
                AND (s.dateIn BETWEEN claimAndFeeFL1.dateFrom AND claimAndFeeFL1.dateTill)
                AND kind_fl1.id = :sellIn
                AND (supplier = sell_in_fl1_supplier OR supplier.group = sell_in_fl1_supplier_group)
            ) as sellInFL1')
            ->addSelect('(
                SELECT
                    IFNULL(SUM(claimAndFeeOut.value), 0)
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acaf_out
                JOIN acaf_out.claimsAndFees as claimAndFeeOut
                JOIN WebdsignGlobalBundle:Product as productOut WITH acaf_out.product = productOut
                JOIN claimAndFeeOut.kind as kindOut
                LEFT JOIN claimAndFeeOut.suppliers as sell_out_supplier
                LEFT JOIN claimAndFeeOut.supplierGroups as sell_out_supplier_group
                WHERE productOut = p
                AND (so.dateInvoice BETWEEN claimAndFeeOut.dateFrom AND claimAndFeeOut.dateTill)
                AND kindOut.id = :sellOut
                AND (supplier = sell_out_supplier OR supplier.group = sell_out_supplier_group)
            ) as sellOut')
            ->addSelect('(
                SELECT
                    IFNULL(SUM(CASE WHEN margin_fl1_out.code = :fl1 THEN claimAndFeeOutFL1.value ELSE 0 END), 0)
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acaf_out_fl1
                JOIN acaf_out_fl1.claimsAndFees as claimAndFeeOutFL1
                JOIN WebdsignGlobalBundle:Product as productOutFL1 WITH acaf_out_fl1.product = productOutFL1
                JOIN claimAndFeeOutFL1.kind as kindOutFL1
                JOIN claimAndFeeOutFL1.flMargin as margin_fl1_out
                LEFT JOIN claimAndFeeOutFL1.suppliers as sell_out_fl1_supplier
                LEFT JOIN claimAndFeeOutFL1.supplierGroups as sell_out_fl1_supplier_group
                WHERE productOutFL1 = p
                AND (so.dateInvoice BETWEEN claimAndFeeOutFL1.dateFrom AND claimAndFeeOutFL1.dateTill)
                AND kindOutFL1.id = :sellOut
                AND (supplier = sell_out_fl1_supplier OR supplier.group = sell_out_fl1_supplier_group)
            ) as sellOutFL1')
            ->leftJoin('s.supplier', 'supplier')
            ->leftJoin('s.order', 'so')
            ->leftJoin('supplier.group', 'supplierGroup')
            ->leftJoin('s.product', 'p')
            ->leftJoin('p.temporaryPrices', 'tp', Expr\Join::WITH, $queryBuilder->expr()->andX(
                $queryBuilder->expr()->eq('tp.type', ':typeELP'),
                $queryBuilder->expr()->lte('tp.from', 's.dateIn'),
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->gte('tp.to', 's.dateIn'),
                    $queryBuilder->expr()->eq('tp.to', ':zeroDate')
                )
            ))
            ->leftJoin('p.productPriceAfterwardsLog', 'ppal', Expr\Join::WITH, $queryBuilder->expr()->andX(
                $queryBuilder->expr()->lte('ppal.startDate', 's.dateIn'),
                $queryBuilder->expr()->eq('BIT_AND(supplier.flags, :afterwardsFlag)', ':afterwardsFlag'),
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->gte('ppal.endDate', 's.dateIn'),
                    $queryBuilder->expr()->isNull('ppal.endDate'),
                    $queryBuilder->expr()->eq('ppal.endDate', ':zeroDate')
                )
            ))
            ->leftJoin('s.purchaseOrder', 'po')
            ->leftJoin('p.productsEuropafoto', 'pef')
            ->andWhere(
                $queryBuilder->expr()->between('s.dateIn', ':from', ':to')
            )
            ->andWhere('s.dateOut IS NULL')
            ->setParameter('typeELP', 'ELP')
            ->setParameter('zeroDate', '0000-00-00 00:00:00')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->setParameter('afterwardsFlag', Supplier::FLAG_CALCULATE_AFTERWARDS)
            ->setParameter('sellIn', ClaimsAndFees::KIND_SELL_IN)
            ->setParameter('sellOut', ClaimsAndFees::KIND_SELL_OUT)
            ->setParameter('fl1', PurchaseOverviewHelper::FL1)
        ;

        if ($forAllSuppliers === false && $forEuropaSuppliers === false && $forSupplier === false) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->in('supplierGroup.id', ':groupIds'))
                ->setParameter('groupIds', $groupIds)
            ;
        }

        if ($forAllSuppliers === false && $forEuropaSuppliers === true && $forSupplier === false) {
            $queryBuilder
                ->andWhere('supplierGroup.email = :email')
                ->setParameter('email', PurchaseOverviewHelper::EUROPA_MAIL)
            ;
        }

        if ($forSupplier === true) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->in('supplier.id', ':groupIds'))
                ->setParameter('groupIds', $groupIds)
            ;
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param DateTime $from
     * @param DateTime $to
     * @param array $origins
     * @param array $groupIds
     * @param bool $forEuropaSuppliers
     * @param bool $forAllSuppliers
     * @param bool $forSupplier
     * @param bool $forAllOrigins
     * @param array $excludeStockIds
     * @return array
     */
    public function findSoldStockItemsForPurchaseOverview(
        DateTime $from,
        DateTime $to,
        array $origins,
        array $groupIds = [],
        bool $forEuropaSuppliers = false,
        bool $forAllSuppliers = false,
        bool $forSupplier = false,
        bool $forAllOrigins = false,
        array $excludeStockIds = []
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('s.id as stockId')
            ->addSelect('(
                SELECT
                    IFNULL(SUM(claimAndFee.value), 0)
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acaf
                JOIN acaf.claimsAndFees as claimAndFee
                JOIN WebdsignGlobalBundle:Product as product WITH acaf.product = product
                JOIN claimAndFee.kind as kind
                LEFT JOIN claimAndFee.suppliers as sell_out_supplier
                LEFT JOIN claimAndFee.supplierGroups as sell_out_supplier_group
                WHERE acaf.product = p
                AND (o.dateInvoice BETWEEN claimAndFee.dateFrom AND claimAndFee.dateTill)
                AND kind.id = :sellOut
                AND (supplier = sell_out_supplier OR supplier.group = sell_out_supplier_group)
            ) as sellOut')
            ->addSelect('(
                SELECT
                    IFNULL(SUM(CASE WHEN margin_fl1_out.code = :fl1 THEN claimAndFeeOutFL1.value ELSE 0 END), 0)
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acaf_out_fl1
                JOIN acaf_out_fl1.claimsAndFees as claimAndFeeOutFL1
                JOIN WebdsignGlobalBundle:Product as productOutFL1 WITH acaf_out_fl1.product = productOutFL1
                JOIN claimAndFeeOutFL1.kind as kindOutFL1
                JOIN claimAndFeeOutFL1.flMargin as margin_fl1_out
                LEFT JOIN claimAndFeeOutFL1.suppliers as sell_out_fl1_supplier
                LEFT JOIN claimAndFeeOutFL1.supplierGroups as sell_out_fl1_supplier_group
                WHERE productOutFL1 = p
                AND (o.dateInvoice BETWEEN claimAndFeeOutFL1.dateFrom AND claimAndFeeOutFL1.dateTill)
                AND kindOutFL1.id = :sellOut
                AND (supplier = sell_out_fl1_supplier OR supplier.group = sell_out_fl1_supplier_group)
            ) as sellOutFL1')
            ->addSelect('supplier.id as supplierId')
            ->addSelect('supplierGroup.id as groupId')
            ->addSelect('supplierGroup.name as groupName')
            ->addSelect('supplier.name')
            ->addSelect('p.id as productId')
            ->addSelect('p.name as productName')
            ->addSelect('s.dateIn')
            ->addSelect('o.dateInvoice')
            ->innerJoin(
                's.order',
                'o',
                Expr\Join::WITH,
                $queryBuilder->expr()->between('o.dateInvoice', ':from', ':to')
            )
            ->leftJoin('s.supplier', 'supplier')
            ->leftJoin('supplier.group', 'supplierGroup')
            ->leftJoin('s.product', 'p')
            ->andWhere('s.dateOut IS NULL')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->setParameter('sellOut', ClaimsAndFees::KIND_SELL_OUT)
            ->setParameter('fl1', PurchaseOverviewHelper::FL1)
        ;

        if ($forAllOrigins === false && count($origins) > 0) {
            $queryBuilder
                ->innerJoin(
                    'o.origin',
                    'oo',
                    Expr\Join::WITH,
                    $queryBuilder->expr()->in('oo.id', ':originIds')
                )
                ->setParameter('originIds', $origins)
            ;
        }

        if ($forAllSuppliers === false && $forEuropaSuppliers === false && $forSupplier === false) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->in('supplierGroup.id', ':groupIds'))
                ->setParameter('groupIds', $groupIds)
            ;
        }

        if ($forAllSuppliers === false && $forEuropaSuppliers === true && $forSupplier === false) {
            $queryBuilder
                ->andWhere('supplierGroup.email = :email')
                ->setParameter('email', PurchaseOverviewHelper::EUROPA_MAIL)
            ;
        }

        if ($forSupplier === true) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->in('supplier.id', ':groupIds'))
                ->setParameter('groupIds', $groupIds)
            ;
        }

        if (count($excludeStockIds) > 0) {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->notIn('s.id', ':excludedIds'))
                ->setParameter('excludedIds', $excludeStockIds)
            ;
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param int $supplierId
     * @param DateTime $from
     * @param DateTime $to
     * @param array $origins
     * @param bool $forAllOrigins
     * @return float
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function findTotalSalesPriceForSuppliers(
        array $supplierIds,
        DateTime $from,
        DateTime $to,
        array $origins,
        bool $forAllOrigins = false
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');

        $queryBuilder
            ->select('SUM(s.salesPrice / ((o.vatPercentage/100)+1)) as salesPrice')
            ->addSelect('supplier.id')
            ->innerJoin(
                's.order',
                'o',
                Expr\Join::WITH,
                $queryBuilder->expr()->between('o.dateInvoice', ':from', ':to')
            )
            ->innerJoin(
                's.supplier',
                'supplier',
                Expr\Join::WITH,
                $queryBuilder->expr()->in('supplier.id', ':supplierIds')
            )
            ->andWhere('s.dateOut IS NULL')
            ->setParameter('from', $from)
            ->setParameter('to', $to)
            ->setParameter('supplierIds', $supplierIds)
            ->addGroupBy('supplier');


        if ($forAllOrigins === false && count($origins) > 0) {
            $queryBuilder
                ->innerJoin(
                    'o.origin',
                    'oo',
                    Expr\Join::WITH,
                    $queryBuilder->expr()->in('oo.id', ':originIds')
                )
                ->setParameter('originIds', $origins)
            ;
        }

        $salesPrices = $queryBuilder->getQuery()->getArrayResult();

        $mappedSalesPrices = [];
        foreach ($salesPrices as $salesPrice) {
            $mappedSalesPrices[$salesPrice['id']] = round((float)$salesPrice['salesPrice'], 2);
        }

        return $mappedSalesPrices;
    }

    /**
     * @param Product $product
     * @return Stock[]
     */
    public function findAvailableForProduct(Product $product): array
    {
        $queryBuilder = $this->createQueryBuilder('s');

        $queryBuilder
            ->where('s.product = :product')
            ->andWhere('s.order = 0')
            ->andWhere('s.dateOut IS NULL')
            ->setParameter('product', $product)
        ;

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param Product $product
     * @param bool $lastStockLog
     * @return array
     */
    public function findUnsoldByProduct(
        Product $product,
        bool $lastStockLog = false,
        ?DateTime $dateFrom = null,
        ?DateTime $dateTill = null,
        ?SupplierGroup $supplierGroup = null,
        ?Supplier $supplier = null
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');

        if ($lastStockLog) {
            $queryBuilder->addSelect('(
                SELECT
                    MAX(sl.timestamp)
                FROM
                    WebdsignGlobalBundle:StockLogN sl
                WHERE sl.stock = s.id
            ) as lastlog');

            $queryBuilder
                ->addOrderBy('lastlog', 'DESC')
                ->addOrderBy('s.dateIn', 'ASC');
        }

        if ($supplier instanceof Supplier) {
            $queryBuilder
                ->andWhere('s.supplier = :supplier')
                ->setParameter('supplier', $supplier);
        } elseif ($supplierGroup instanceof SupplierGroup) {
            $queryBuilder
                ->innerJoin('s.supplier', 'su')
                ->andWhere('su.group = :supplierGroup')
                ->setParameter('supplierGroup', $supplierGroup);
        }

        if ($dateTill instanceof DateTime) {
            $queryBuilder
                ->leftJoin('s.order', 'o')
                ->andWhere(
                    $queryBuilder->expr()->orX(
                        $queryBuilder->expr()->eq('s.order', '0'),
                        $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->neq('o.invoiceNumber', '0'),
                            $queryBuilder->expr()->gte('o.dateInvoice', ':dateTill')
                        )
                    )
                )
                ->setParameter(':dateTill', $dateTill->setTime(23, 59, 59));

            if ($dateFrom instanceof DateTime) {
                $queryBuilder
                    ->andWhere($queryBuilder->expr()->between('s.dateIn', ':dateFrom', ':dateTill'))
                    ->setParameter(':dateFrom', $dateFrom->setTime(0, 0));
            }
        } else {
            $queryBuilder->andWhere('s.order = 0');
        }

        $queryBuilder
            ->andWhere('s.product = :product')
            ->setParameter('product', $product)
            ->andWhere('s.dateOut IS NULL');

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param Product $product
     * @param bool $lastStockLog
     * @param int|null $offset
     * @param int|null $limit
     * @return array
     */
    public function findSoldByProduct(
        Product $product,
        bool $lastStockLog = false,
        ?int $offset = null,
        ?int $limit = null
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');

        if ($lastStockLog) {
            $queryBuilder->addSelect('(
                SELECT
                    MAX(sl.timestamp)
                FROM
                    WebdsignGlobalBundle:StockLogN sl
                WHERE sl.stock = s.id
            ) as lastlog');

            $queryBuilder
                ->addOrderBy('lastlog', 'DESC')
                ->addOrderBy('s.dateIn', 'ASC');
        }

        $queryBuilder
            ->join('s.order', 'o')
            ->where('s.order != 0')
            ->andWhere('s.product = :product')
            ->andWhere('s.dateOut IS NULL')
            ->setParameter('product', $product)
            ->orderBy('o.orderDate', 'DESC');

        if ($limit !== null) {
            $queryBuilder
                ->setFirstResult($offset)
                ->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param DateTime $start
     * @param DateTime $stop
     * @return array
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function findForLogInit(DateTime $start, DateTime $stop): array
    {
        $sql = 'SELECT
            s.id as stockId,
            s.tstamp_in as timestampIn,
            s.ingescand_door as insertedBy,
            s.artikel_id as productId,
            sl.id as locationId,
            sl.is_main_location as isMainLocation,
            sl.shop,
            sl.is_scannable_by_barcode as isScannableByBarcode,
            (
                SELECT
                    logId.id
                FROM
                    `cameranu`.stock_log as logId
                WHERE logId.stock_id = s.id
                AND logId.action_id = 6
                ORDER BY logId.timestamp ASC, logId.id ASC
                LIMIT 1
            ) AS firstLogId,
            (
                SELECT
                    timestamp
                FROM
                    `cameranu`.stock_log as log
                WHERE log.id = firstLogId
            ) AS firstLogTimestamp,
            (
                SELECT
                    extra_info
                FROM
                    `cameranu`.stock_log as logInfo
                WHERE logInfo.id = firstLogId
            ) AS firstLogInfo,
            (
                SELECT
                    logFrom.from_location_id
                FROM
                    `cameranu`.stock_log as logFrom
                WHERE logFrom.id = firstLogId
            ) AS firstLogFromLocation,
            (
                SELECT
                    logTo.to_stock_location_id
                FROM
                    `cameranu`.stock_log as logTo
                WHERE logTo.id = firstLogId
            ) AS firstLogToLocation,
            (
                SELECT
                    fromMainLocation.is_main_location
                FROM
                    `cameranu`.stock_log as logFromMain
                INNER JOIN `cameranu`.`stock_locations` fromMainLocation ON fromMainLocation.id = logFromMain.id
                WHERE logFromMain.id = firstLogId
            ) AS firstLogFromLocationIsMain,
            (
                SELECT
                    toMainLocation.is_main_location
                FROM
                    `cameranu`.stock_log as logToMain
                INNER JOIN `cameranu`.`stock_locations` toMainLocation ON toMainLocation.id = logToMain.id
                WHERE logToMain.id = firstLogId
            ) AS firstLogToLocationIsMain
            FROM `cameranu`.`voorraad` AS s
            INNER JOIN `cameranu`.artikelen p ON p.id = s.artikel_id
            INNER JOIN `cameranu`.subgroepen sg ON p.subgroep_id = sg.id
            INNER JOIN `cameranu`.hoofdgroepen mg ON sg.hoofdgroep_id = mg.id
            INNER JOIN `cameranu`.leveranciers ss ON s.leverancier_id = ss.id
            INNER JOIN `cameranu`.stock_locations sl ON s.stock_location_id = sl.id
            WHERE DATE(s.tstamp_in) BETWEEN :start AND :stop
            AND mg.id NOT IN (:excludedGroups)
            AND (p.flags2 & :flags2) <> :flags2
            AND ss.id NOT IN (:skipSuppliers)';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindValue('start', $start->format('Y-m-d H:i:s'));
        $query->bindValue('stop', $stop->format('Y-m-d H:i:s'));
        $query->bindValue('flags2', Product::FLAG2_IS_AUTO_STOCK_TOP_UP);
        $query->bindValue('excludedGroups', implode(',', StockHelper::SKIP_GROUPS));
        $query->bindValue('skipSuppliers', implode(',', StockHelper::SKIP_SUPPLIERS));
        return $query->executeQuery()->fetchAllAssociative();
    }

    /**
     * @param bool $groupByProduct
     * @return Stock[]
     */
    public function findWithoutStockValue(bool $groupByProduct = false): array
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->andWhere('s.productId != :productId')
            ->andWhere('s.order = :order')
            ->andWhere('s.stockValue = :stockValue')
            ->andWhere('s.purchasePrice > :purchasePrice')
            ->andWhere('s.dateOut IS NULL')
            ->setParameters([
                'productId' => 0,
                'stockValue' => 0,
                'purchasePrice' => 0,
                'order' => (new OrderInfo())->setId(0),
            ])
        ;

        if ($groupByProduct === true) {
            $queryBuilder
                ->groupBy('s.product');
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function countInventoryByProductAndSupplierGroup($productId, $supplierGroupId)
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('COUNT(s.id) as stockCount')
            ->innerJoin('s.supplier', 'sp')
            ->andWhere('sp.group = :supplierGroup')
            ->andWhere('s.product = :product')
            ->andWhere('s.dateOut is null')
            ->setParameters([
                'supplierGroup' => $supplierGroupId,
                'product' => $productId,
            ])
        ;

        return $queryBuilder->getQuery()->getSingleResult();
    }

    public function getProductStockAmountBySupplier(Supplier $supplier): array
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('p.id')
            ->addSelect('COUNT(s.id) as amount')
            ->innerJoin('s.product', 'p')
            ->andWhere('s.supplier = :supplier')
            ->groupBy('p')
            ->setParameter('supplier', $supplier);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function findDeleted(): array
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->select('s')
            ->andWhere('s.dateOut IS NOT NULL');
        return $queryBuilder->getQuery()->getResult();
    }

    public function findDuplicateBarcodeByProductAndBarcode(
        Product $product,
        string $barcode,
        bool $checkOtherProducts = true
    ): array {
        //Check if this product already have stock with this barcode operator is '=' or
        //check if there are other products with stock witch have this barcode operator is '!='
        $productOperator = $checkOtherProducts ? '!=' : '=';

        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->join('s.product', 'p')
            ->where('p ' . $productOperator . ' :product')
            ->andWhere('s.barcode = :barcode')
            ->andWhere('s.order = :order')
            ->andWhere('s.dateOut IS NULL')
            ->setParameters([
                'product' => $product,
                'barcode' => $barcode,
                'order' => (new OrderInfo())->setId(0),
            ]);

        return $queryBuilder
            ->getQuery()
            ->getResult();
    }

    public function findIncomingProductsForDate(DateTime $date): array
    {
        $qb = $this->createQueryBuilder('s');
        $qb
            ->select('prod.id AS product_id')
            ->addSelect('prod.name AS product_name')
            ->addSelect('sup.name AS supplier_name')
            ->addSelect('COUNT(s.id) AS quantity')
            ->addSelect('SUM(IFNULL(memo.price, 0)) AS purchase_price')
            ->addSelect('memo.referenceId AS purchase_reference')
            ->leftJoin('s.product', 'prod')
            ->leftJoin('prod.subgroup', 'sg')
            ->leftJoin('sg.maingroup', 'mg')
            ->leftJoin('mg.rootgroup', 'rg')
            ->leftJoin('s.supplier', 'sup')
            ->leftJoin('s.purchaseOrder', 'memo')
            ->where('s.dateIn = :date')
            ->andWhere('BIT_AND(memo.flags, :memoFinishedFlag) = 0')
            ->andWhere('BIT_AND(prod.flags2, :autoStockTopUpFlag) = 0')
            ->andWhere('mg.id != :occasionRootGroup')
            ->setParameters([
                'date' => $date,
                'memoFinishedFlag' => PurchaseOrder::FLAG_FINISHED,
                'autoStockTopUpFlag' => Product::FLAG2_IS_AUTO_STOCK_TOP_UP,
                'occasionRootGroup' => Rootgroup::OCCASIONS_ID,
            ])
            ->groupBy('product_id')
            ->orderBy('supplier_name, quantity', 'DESC');

        return $qb->getQuery()->getArrayResult();
    }

    public function findProductBetweenDates(Product $product, DateTime $start, DateTime $stop): array
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->leftJoin('s.purchaseOrder', 'memo')
            ->where('s.product = :product')
            ->andWhere('s.dateOut IS NULL')
            ->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isNotNull('memo'),
                        $queryBuilder->expr()->between('memo.orderDate', ':dateStart', ':dateStop')
                    ),
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->isNull('memo'),
                        $queryBuilder->expr()->between('s.dateIn', ':dateStart', ':dateStop')
                    )
                )
            )
            ->setParameters([
                'product' => $product,
                'dateStart' => $start,
                'dateStop' => $stop,
            ]);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findNewSecondHandStockQuery(?NewStockListFilter $stockListFilter = null, bool $onlyAmount = false): Query
    {
        $queryBuilder = $this->createQueryBuilder('s');

        if ($onlyAmount) {
            $queryBuilder->select('COUNT(distinct s)');
        } else {
            $queryBuilder->select('s');
        }

        $queryBuilder
            ->innerJoin('s.location', 'sl')
            ->innerJoin('s.product', 'p')
            ->innerJoin('p.articleGroups', 'pg')
            ->innerJoin('pg.articleGroupType', 'pgt')
            ->andWhere('s.dateOut is null')
            ->andWhere('sl.id in (:location_ids)')
            ->andWhere('BIT_AND(s.flags, ' . Stock::SECOND_HAND_CHECKED_FLAG . ') != ' . Stock::SECOND_HAND_CHECKED_FLAG)
            ->andWhere('pgt.id = :secondHandTypeId')
            ->addOrderBy('s.location', 'ASC')
            ->addOrderBy('s.dateIn', 'DESC')
            ->setParameters([
                'location_ids' => StockLocation::B_LOCATION_IDS,
                'secondHandTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
            ]);

        if ($stockListFilter instanceof NewStockListFilter) {
            if ($stockListFilter->stockLocation !== null) {
                $queryBuilder
                    ->andWhere('sl.parent = :parent')
                    ->setParameter('parent', $stockListFilter->stockLocation->getParent());
            }

            if ($stockListFilter->searchQuery !== null) {
                $queryBuilder
                    ->andWhere($queryBuilder->expr()->orX(
                        $queryBuilder->expr()->like('p.name', ':searchQuery'),
                        $queryBuilder->expr()->like('s.barcode', ':searchQuery'),
                    ))
                    ->setParameter('searchQuery', '%' . $stockListFilter->searchQuery  . '%');
            }
        }

        return $queryBuilder->getQuery();
    }

    public function findRecentSecondHandProductsQuery(): Query
    {
        $dateTime = new DateTime();
        $dateTime->modify('-1 month');

        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->innerJoin('s.location', 'sl')
            ->leftJoin('s.secondHandStockAccessory', 'shsa')
            ->innerJoin('s.product', 'p')
            ->innerJoin('p.articleGroups', 'pg')
            ->innerJoin('pg.articleGroupType', 'pgt')
            ->andWhere('s.dateOut is null')
            ->andWhere('BIT_AND(s.flags, ' . Stock::SECOND_HAND_CHECKED_FLAG . ') = ' . Stock::SECOND_HAND_CHECKED_FLAG)
            ->andWhere('pgt.id = :secondHandTypeId')
            ->andWhere('s.dateIn >= :datetime')
            ->addOrderBy('s.dateIn', 'DESC')
            ->setParameters([
                'secondHandTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'datetime' => $dateTime
            ]);

        return $queryBuilder->getQuery();
    }

    public function setLocationsForStock(array $updateRows): int
    {
        if (empty($updateRows)) {
            return 0;
        }

        $sql = 'INSERT INTO cameranu.voorraad (id, location_compartment_node_id) VALUES ';
        $parameters = [];
        foreach ($updateRows as $i => $row) {
            if ($i !== 0) {
                $sql .= ',';
            }
            [$stockRowId, $locationNodeId] = $row;
            $sql .= " (:stock_row_id$i, :location_compartment_node_id$i) ";
            $parameters['stock_row_id' . $i] = $stockRowId;
            $parameters['location_compartment_node_id' . $i] = $locationNodeId;
        }
        $sql .= ' ON DUPLICATE KEY UPDATE location_compartment_node_id=VALUES(location_compartment_node_id)';

        return (int)$this->getEntityManager()->getConnection()->executeStatement($sql, $parameters);
    }

    /**
     * This is necessary to do efficient mass updating rows if you only have product ids
     */
    public function stockIdsByProduct(array $productIds, int $stockLocationId): array
    {
        $qb = $this->createQueryBuilder('s');
        $query = $qb
            ->select('s.id, s.productId')
            ->where('s.productId IN (:productIds)')
            ->andWhere('s.order = 0')
            ->andWhere('s.dateOut IS NULL')
            ->andWhere('s.location = :stockLocation')
            ->setParameter('productIds', $productIds)
            ->setParameter('stockLocation', $stockLocationId)
            ->getQuery();
        $results = [];
        foreach ($query->getArrayResult() as $row) {
            $results[$row['productId']] ??= [];
            $results[$row['productId']][] = $row['id'];
        }
        return $results;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function findOneByLocationCompartmentNodeAndProductId(LocationCompartmentNode $locationCompartmentNode, StockLocation $stockLocation, int $productId, ?string $barcode): ?Stock
    {
        $queryBuilder = $this->createQueryBuilder('s');
        $queryBuilder
            ->andWhere('s.product = :product')
            ->andWhere('s.locationCompartmentNode = :locationCompartmentNode')
            ->andWhere('s.order = 0')
            ->andWhere('s.dateOut is null')
            ->andWhere('s.location = :location')
            ->setParameter('product', $productId)
            ->setParameter('locationCompartmentNode', $locationCompartmentNode)
            ->setParameter('location', $stockLocation)
            ->setMaxResults(1);

        if ($barcode !== null) {
            $queryBuilder
                ->andWhere('s.barcode = :barcode')
                ->setParameter('barcode', $barcode);
        }

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findMovableForPickList(Product $product, array $stockLocations, string $query): array
    {
        $queryBuilder = $this->createQueryBuilder('s');

        $queryBuilder
            ->select('s.id')
            ->addSelect('lcn.id as locationCompartmentNodeId')
            ->addSelect('lc.compartment as locationCompartment')
            ->addSelect('lcn.subCompartment as locationSubCompartment')
            ->addSelect('COUNT(lcn.id) as amount')
            ->innerJoin('s.locationCompartmentNode', 'lcn')
            ->innerJoin('lcn.location', 'lc')
            ->andWhere('s.product = :product')
            ->andWhere('s.location in (:locations)')
            ->andWhere('s.locationCompartmentNode is not null')
            ->andWhere('s.barcode = :query')
            ->andWhere('s.order = 0')
            ->andWhere('s.dateOut is null')
            ->groupBy('s.locationCompartmentNode')
            ->setParameter('product', $product)
            ->setParameter('locations', $stockLocations)
            ->setParameter('query', $query);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return Stock[]
     */
    public function findForLocationCompartmentNodeProductAndQuery(
        LocationCompartmentNode $locationCompartmentNode,
        Product $product,
        string $query,
        int $amount
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');

        $queryBuilder
            ->select('s')
            ->andWhere('s.locationCompartmentNode = :locationCompartmentNode')
            ->andWhere('s.product = :product')
            ->andWhere('s.barcode = :query')
            ->andWhere('s.order = 0')
            ->andWhere('s.dateOut is null')
            ->setMaxResults($amount)
            ->setParameter('locationCompartmentNode', $locationCompartmentNode)
            ->setParameter('product', $product)
            ->setParameter('query', $query);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param StockLocation[] $stockLocations
     * @param Product[] $products
     */
    public function countByLocationCompartmentNode(
        array $stockLocations,
        array $products
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');

        $queryBuilder
            ->select('p.id as product_id')
            ->addSelect('lcn.id as locationCompartmentNodeId')
            ->addSelect('lc.compartment as compartment')
            ->addSelect('lcn.subCompartment as locationCompartment')
            ->addSelect('SUM(IF(lcn = s.locationCompartmentNode, 1, 0)) as availableStock')
            ->innerJoin('s.product', 'p')
            ->innerJoin('p.locationCompartmentNode', 'lcn')
            ->innerJoin('lcn.location', 'lc')
            ->andWhere('s.product IN (:products)')
            ->andWhere('s.dateOut IS NULL')
            ->andWhere('s.order = 0')
            ->andWhere('s.location IN (:locations)')
            ->andWhere('lc.stockLocation IN (:locations)')
            ->setParameter('products', $products)
            ->setParameter('locations', $stockLocations)
            ->groupBy('lcn.id')
            ->orderBy('p.id')
        ;

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return Stock[]
     */
    public function findForLocationCompartmentAndQuery(
        string $query,
        ?LocationCompartment $locationCompartment = null,
        ?LocationCompartmentNode $locationCompartmentNode = null,
    ): array {
        $queryBuilder = $this->createQueryBuilder('s');

        $queryBuilder
            ->select('s.id, p.id as productid, p.name as productname')
            ->join('s.product', 'p')
            ->andWhere('s.barcode = :query')
            ->andWhere('s.order = 0')
            ->andWhere('s.dateOut is null')
            ->setParameter('query', $query);

        if ($locationCompartmentNode !== null) {
            $queryBuilder
                ->andWhere('s.locationCompartmentNode = :locationCompartmentNode')
                ->setParameter('locationCompartmentNode', $locationCompartmentNode);
        }

        if ($locationCompartment !== null) {
            $queryBuilder
                ->join('s.locationCompartmentNode', 'lcn')
                ->andWhere('lcn.location = :locationCompartment')
                ->setParameter('locationCompartment', $locationCompartment);
        }

        return $queryBuilder->getQuery()->getResult(AbstractQuery::HYDRATE_ARRAY);
    }

    public function getStockForStockAvailability(array $productIds = []): array
    {
        $qb = $this->createQueryBuilder('s')
            ->select('IDENTITY(s.product) AS productId')
            ->addSelect('IF(sl.id = :storeUrkId, :storeUrkVirtualParent, sl.parent) AS parent')
            ->addSelect('COUNT(IDENTITY(s.product)) AS totalStock')
            ->join('s.location', 'sl')
            ->leftJoin('s.order', 'o')
            ->where('(o.id IS NULL OR o.dateInvoice = \'0000-00-00\')')
            ->andWhere('s.dateOut IS NULL')
            ->groupBy('s.product')
            ->addGroupBy('parent')
            ->setParameters([
                'storeUrkId' => StockLocation::WINKEL_URK,
                'storeUrkVirtualParent' => CalculateStockAvailabilityCommand::LOCATION_PARENTS[StockLocation::WINKEL_URK],
            ])
        ;

        if (count($productIds) > 0) {
            $qb
                ->andWhere('IDENTITY(s.product) IN (:productIds)')
                ->setParameter('productIds', $productIds)
            ;
        }

        return $qb->getQuery()->getArrayResult();
    }

    public function getAmountOfOrdersPerStockLocationByProduct(array $stockLocations, Product $product): array
    {
        $query = $this->createQueryBuilder('s');
        $query
            ->select('count(o.id) AS allTime')
            ->addSelect('IDENTITY(s.location) AS location')
            ->leftJoin('s.order', 'o')
            ->where('s.product = :product')
            ->andWhere('BIT_AND(o.flags, 4) = 4')
            ->andWhere('IDENTITY(s.order) > 0')
            ->andWhere('s.location in (:stockLocations)')
            ->setParameter('product', $product->getId())
            ->setParameter('stockLocations', $stockLocations)
            ->groupBy('s.location');

        $results = $query->getQuery()->getArrayResult();

        return $results ?? [];
    }

    public function getOrderDataForPowerReviews(string $domain): array
    {
        $queryBuilder = $this->getQueryBuilder('st');
        $queryBuilder
            ->select('IF(IDENTITY(agp.product) is null, IDENTITY(st.product), IDENTITY(agp.product)) AS page_id')
            ->addSelect('o.id AS order_id')
            ->addSelect('o.firstName AS first_name')
            ->addSelect('CONCAT(TRIM(o.nameInsertion), IF(o.nameInsertion = \'\', \'\', \' \'), o.lastName) AS last_name')
            ->addSelect('o.email')
            ->addSelect('o.dateInvoice AS order_date')
            ->addSelect('IF(IDENTITY(agp.product) is not null, IDENTITY(st.product), NULL) AS variant')
            ->addSelect('o.customerid as user_id')
            ->leftJoin('st.product', 'p')
            ->leftJoin('p.articleGroups', 'ag')
            ->leftJoin(ArticleGroupProduct::class, 'agp', Expr\Join::WITH, 'agp.articleGroup = ag AND agp.type = \'master\'')
            ->andWhere('o.domainOrigin = :domain')
            ->setParameter('domain', $domain);

        return $queryBuilder->getQuery()->getArrayResult();
    }
}
