<?php

namespace Webdsign\GlobalBundle\Entity;

use CatBundle\DTO\PickList\SearchResultLocationDataObject;
use CatB<PERSON>le\Entity\CompetitorData\Competitor;
use CatBundle\Entity\CompetitorData\ProductSellPrice;
use CatB<PERSON>le\Entity\CompetitorData\ProductStock;
use CatBundle\Form\Filter\SecondHand\DiscrepancyListFilter;
use CatBundle\Form\Filter\SecondHand\ProductListFilter;
use CatBundle\Service\Stock\StockHelper;
use DateTime;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query;
use Doctrine\ORM\Query\Expr;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Ggergo\SqlIndexHintBundle\SqlIndexWalker;
use PhpOffice\PhpSpreadsheet\Shared\OLE\PPS\Root;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\DTO\WebsiteConfigurator\ActiveConfigDataObject;
use Webdsign\GlobalBundle\Entity\Purchase\CalculatedPurchasePrice;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandDiscrepantProductSnooze;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandPerformanceTarget;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandState;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandPrice;
use Webdsign\GlobalBundle\Form\Filter\Api\ApiFilterInterface;
use Webdsign\GlobalBundle\Form\Filter\SecondHand\PriceListFilter;
use Webdsign\GlobalBundle\Services\Utility;

/**
 * @method Product findOneBy(array $criteria, array $orderBy = null)
 * @method Product find($id, int $lockMode = null, int $lockVersion = null)
 */
class ProductRepository extends WebdsignRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Product::class);
    }

    /**
     * @param string $ean
     * @return null|array
     * @throws NonUniqueResultException
     */
    public function findByEan(string $ean): ?array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select([
                'p.id',
                'p.name',
            ])
            ->andWhere('p.ean = :ean')
            ->setParameter('ean', $ean)
        ;
        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @return array
     */
    public function findActive(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->andWhere('BIT_AND(p.flags, :product_flag_active) = :product_flag_active')
            ->setParameter('product_flag_active', Product::FLAG_SHOW | Product::FLAG_VISIBLE);
        $query = $queryBuilder->getQuery();
        return $query->getResult();
    }

    /**
     * @param Criteria $criteria
     * @return IterableResult
     * @throws Query\QueryException
     */
    public function findActiveAndNoneCombo(Criteria $criteria): IterableResult
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addCriteria($criteria)
            ->andWhere('BIT_AND(p.flags, :product_flag_show) = :product_flag_show')
            ->setParameter('product_flag_show', Product::FLAG_SHOW)
            ->andWhere('BIT_AND(p.flags, :product_flag_combo) <> :product_flag_combo')
            ->setParameter('product_flag_combo', Product::FLAG_COMBO);

        $query = $queryBuilder->getQuery();
        return $query->iterate(null, Query::HYDRATE_SCALAR);
    }

    public function forMenuItemUpdate(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('partial p.{id}, ptags, partial tags.{tagId}, mit, partial mi.{id}', 'partial miI18n.{id, name}')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->join('p.productTags', 'ptags')
            ->join('ptags.tag', 'tags')
            ->join('tags.menuItemTags', 'mit')
            ->join('mit.menuItem', 'mi', Expr\Join::WITH, 'mi.active = 1')
            ->join('mi.menuI18n', 'miI18n', Expr\Join::WITH, 'miI18n.language = :lang')
            ->andWhere('BIT_AND(p.flags, :p_flags) = :p_flags')
            ->andWhere('BIT_AND(rg.flags, :rg_flags) = :rg_flags')
            ->andWhere('BIT_AND(mg.flags, :mg_flags) = :mg_flags')
            ->andWhere('BIT_AND(sg.flags, :sg_flags) = :sg_flags')
            ->setParameter('p_flags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->setParameter('rg_flags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->setParameter('mg_flags', Maingroup::FLAG_VISIBLE + Maingroup::FLAG_SHOW)
            ->setParameter('sg_flags', Subgroup::FLAG_VISIBLE + Subgroup::FLAG_SHOW)
            ->andWhere('mi.menuId = :menuId')
            ->setparameter('menuId', Menu::LEFT_MENU_ID)
            ->setParameter('lang', 'nl');
        // For some weird reason we store "path" on menu item update, which is some json weirdness
        // For the menu items we require the parent info up to the root
        $menuItemRepo = $this->_em->getRepository(MenuItem::class);
        $maxLevel = $menuItemRepo->maxDepthForMenu(Menu::LEFT_MENU_ID);

        // We join in the parent of menu item, then its parent, then its parent etc...
        // For that we addSelect and join. But empty parent is fine so we do a left join
        // The actual useful data needs another join, the i18n relation
        $start = 'mi';
        for ($level = 1; $level <= $maxLevel; $level++) {
            $joinPrefix = $start . str_repeat('p', $level - 1);
            $parentJoinAlias = $joinPrefix . 'p';
            $parentI18JoinAlias = $parentJoinAlias . 'I18n';
            $queryBuilder
                ->addSelect('partial ' . $parentJoinAlias . '.{id}')
                ->addSelect('partial ' . $parentI18JoinAlias . '.{id, name}')
                // todo: I think the active check should NOT be here?
                ->leftJoin($joinPrefix . '.parent', $parentJoinAlias)
                // This left join is a bit confusing as all items have an i18n entry. But if a parent is empty
                // an inner join here will filter out too much
                ->leftJoin(
                    $parentJoinAlias . '.menuI18n',
                    $parentI18JoinAlias,
                    Expr\Join::WITH,
                    $parentI18JoinAlias . '.language = :lang'
                );
        }

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * @return array|int|string
     */
    public function getUnavailableActiveProducts(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->where('BIT_AND(p.flags, :product_flag_active) = :product_flag_active')
            ->andWhere('BIT_AND(p.flags, :product_flag_visible) = :product_flag_visible')
            ->andWhere('p.expectedDeliveryTime = :product_not_available')
            ->andWhere('BIT_AND(p.flags2, :product_flag2_gone) = :product_flag2_gone')
            ->andWhere('p.inStock = :product_in_stock')
            ->setParameters([
                'product_flag_active' => Product::FLAG_SHOW,
                'product_flag_visible' => Product::FLAG_VISIBLE,
                'product_not_available' => Product::DISCONTINUED,
                'product_flag2_gone' => Product::FLAG2_GONE,
                'product_in_stock' => 0,
            ])
        ;
        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param string $barcode
     * @param CourierList $courierList
     * @return Product|null
     * @throws NonUniqueResultException
     */
    public function findByBarcodeAndCourierList(string $barcode, CourierList $courierList): ?Product
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->innerJoin('p.courierListProducts', 'clp')
            ->innerJoin('clp.courierList', 'cl')
            ->innerJoin('p.stock', 's')
            ->where('cl = :courierList')
            ->andWhere('s.barcode = :barcode')
            ->orderBy('s.dateIn', 'DESC')
            ->setParameter('courierList', $courierList)
            ->setParameter('barcode', $barcode)
            ->setMaxResults(1);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @param string $ean
     * @return Product|null
     * @throws NonUniqueResultException
     */
    public function findOneByEan(string $ean): ?Product
    {
        $queryBuilder = $this->createQueryBuilder('p');
        return $queryBuilder
            ->select('p')
            ->join('p.productCodes', 'pc')
            ->where('pc.code = :ean')
            ->andWhere('pc.type = :type')
            ->setParameters([
                ':ean' => $ean,
                ':type' => 'ean',
            ])
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param string $articleCode
     * @param Subgroup $subGroup
     * @return Product|null
     * @throws NonUniqueResultException
     */
    public function findOneByArticleCodeInSubgroup(string $articleCode, Subgroup $subGroup): ?Product
    {
        $queryBuilder = $this->createQueryBuilder('p');
        return $queryBuilder
            ->select('p')
            ->join('p.productCodes', 'pc')
            ->where('pc.code = :articleCode')
            ->andWhere('pc.type = :type')
            ->andWhere('p.subgroup = :subgroup')
            ->setParameters([
                ':articleCode' => $articleCode,
                ':type' => 'artikelcode',
                ':subgroup' => $subGroup,
            ])
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param string $articleCode
     * @return Product|null
     * @throws NonUniqueResultException
     */
    public function findOneByArticleCode(string $articleCode): ?Product
    {
        $queryBuilder = $this->createQueryBuilder('p');
        return $queryBuilder
            ->select('p')
            ->join('p.productCodes', 'pc')
            ->where('pc.code = :ean')
            ->andWhere('pc.type = :type')
            ->setParameters([
                ':ean' => $articleCode,
                ':type' => 'artikelcode',
            ])
            ->getQuery()
            ->setMaxResults(1)
            ->getOneOrNullResult();
    }

    /**
     * @param array $productIds
     * @return Product[]
     */
    public function findByIds(array $productIds): array
    {
        $queryBuilder = $this->createQueryBuilder('p', 'p.id');
        $queryBuilder
            ->andWhere('p.id IN (:productIds)')
            ->setParameter('productIds', array_values($productIds), Connection::PARAM_INT_ARRAY);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findOneForSupplierCode(string $code): ?Product
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->innerJoin(
                'p.purchasePrice',
                'pp',
                Expr\Join::WITH,
                $queryBuilder->expr()->eq('pp.articleCode', ':code'),
            )->setParameter('code', $code)
            ->setMaxResults(1);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function forCBSCatalogue(bool $getQuery): Query|int
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->join('sg.wexSubgroup', 'wsg')
            ->andWhere('p.categoryId = :catId')
            ->andWhere('BIT_AND(p.flags, :pflags) = :pflags')
            ->andWhere('BIT_AND(sg.flags, :sgflags) = :sgflags')
            ->andWhere('BIT_AND(mg.flags, :mgflags) = :mgflags')
            ->andWhere('BIT_AND(rg.flags, :rgflags) = :rgflags')
            ->andWhere('rg.id not in (:excludedGroups)')
            ->setParameter('catId', ProductCategory::NORMAL)
            ->setParameter('pflags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->setParameter('sgflags', AbstractGroup::FLAG_ACTIVE + AbstractGroup::FLAG_VISIBLE)
            ->setparameter('mgflags', AbstractGroup::FLAG_ACTIVE + AbstractGroup::FLAG_VISIBLE)
            ->setparameter(
                'rgflags',
                AbstractGroup::FLAG_ACTIVE + AbstractGroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU
            )
            ->setParameter('excludedGroups', [Rootgroup::OCCASIONS_ID, Rootgroup::RENTAL_ID]);
        if (!$getQuery) {
            $queryBuilder->select('count(p.id)');
        } else {
            $queryBuilder->select(
                'p.id, p.name, p.cbsAvailable, wsg.id as wsg_id, wsg.description as wsg_description'
            );
        }

        $query = $queryBuilder->getQuery();

        return $getQuery
            ? $query
            : ((int)$query->getSingleScalarResult());
    }

    /**
     * @param int[]|null $ids
     * @param array $config
     * @return array
     */
    public function getProductFeedData(?array $ids = null, array $config = []): array
    {
        if ($ids === null || count(array_filter($ids)) === 0) {
            ini_set('memory_limit', '4096m');
        }

        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('partial p.{
                id,
                name,
                nameShort,
                inStock,
                slug,
                descriptionShort,
                spiderInfo,
                sku,
                flags,
                flags2,
                teaser,
                flagsDelivery,
                wentOnline
            }')
            ->addSelect('sg.name as subgroup_name')
            ->addSelect('mg.name as maingroup_name')
            ->addSelect('rg.name as rootgroup_name')
            ->addSelect('cat.name as cat_name')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->join('p.category', 'cat')
            ->where('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', AbstractGroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', AbstractGroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
        ;

        foreach ($config as $field) {
            switch ($field) {
                case 'page_id':
                    $queryBuilder
                        ->addSelect('IF(IDENTITY(pagp.product) is null, p.id, IDENTITY(pagp.product)) AS page_id')
                        ->leftJoin('p.articleGroups', 'pag')
                        ->leftJoin(ArticleGroupProduct::class, 'pagp', Expr\Join::WITH, 'pagp.articleGroup = pag AND pagp.type = \'master\'');
                    break;
                case 'page_id_variant':
                    $queryBuilder
                        ->addSelect('IF(IDENTITY(vagp.product) is not null, p.id, NULL) AS page_id_variant')
                        ->leftJoin('p.articleGroups', 'vag')
                        ->leftJoin(ArticleGroupProduct::class, 'vagp', Expr\Join::WITH, 'vagp.articleGroup = vag AND vagp.type = \'master\'');
                    break;
                case 'reviewData':
                    $queryBuilder
                        ->addSelect('pr.amount as amountOfReviews')
                        ->addSelect('pr.rating as reviewRating')
                        ->leftJoin(PowerReview::class, 'pr', Expr\Join::WITH, $queryBuilder->expr()->eq('p', 'pr.product'));
                    break;
                case 'sellOutPremium':
                case 'sellInPremium':
                    $queryBuilder
                        ->addSelect('cpp.sellOutAmount')
                        ->addSelect('cpp.sellInAmount')
                        ->leftJoin(CalculatedPurchasePrice::class, 'cpp', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('cpp.product', 'p'),
                            $queryBuilder->expr()->eq('cpp.isPreferred', 1),
                            $queryBuilder->expr()->gt('cpp.updated', ':updatedDate'),
                            $queryBuilder->expr()->eq('cpp.updated', '(
                                    SELECT MAX(cppe.updated) as updated FROM ' . CalculatedPurchasePrice::class . ' cppe WHERE cppe.product = p
                                )'
                            ),
                        ))
                        ->setParameter('updatedDate', new DateTime('-1 day'));
                    break;
                case 'category':
                case 'categorie':
                    $queryBuilder
                        ->addSelect('partial pmi.{id}')
                        ->join('p.productMenuItems', 'pmi');
                    break;
                case 'price':
                    $queryBuilder
                        ->addSelect('prices.price')
                        ->addSelect('prices.priceEx')
                        ->addSelect('prices.priceFromTo')
                        ->leftJoin('p.prices', 'prices', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('prices.country', ':country'),
                            $queryBuilder->expr()->eq('prices.domainId', ':domain')
                        ))
                        ->andWhere(
                            $queryBuilder->expr()->orX(
                                $queryBuilder->expr()->isNotNull('prices.price'),
                                $queryBuilder->expr()->eq('BIT_AND(p.flags, :flagWithoutPrice)', ':flagWithoutPrice')
                            )
                        )
                        ->setParameter('domain', 1)
                        ->setParameter('flagWithoutPrice', product::FLAG_WITHOUT_PRICE_ONLINE)
                        ->setParameter('country', 'NL');
                    break;
                case 'priceEx':
                    $queryBuilder
                        ->addSelect('pricesEx.priceEx')
                        ->leftJoin('p.prices', 'pricesEx', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('pricesEx.country', ':country'),
                            $queryBuilder->expr()->eq('pricesEx.domainId', ':domain')
                        ))
                        ->andWhere(
                            $queryBuilder->expr()->orX(
                                $queryBuilder->expr()->isNotNull('pricesEx.price'),
                                $queryBuilder->expr()->eq('BIT_AND(p.flags, :flagWithoutPrice)', ':flagWithoutPrice')
                            )
                        )
                        ->setParameter('domain', 1)
                        ->setParameter('flagWithoutPrice', product::FLAG_WITHOUT_PRICE_ONLINE)
                        ->setParameter('country', 'NL');
                    break;
                case 'priceBe':
                    $queryBuilder
                        ->addSelect('pricesBe.price as priceBe')
                        ->addSelect('pricesBe.priceFromTo as priceFromToBe')
                        ->leftJoin('p.prices', 'pricesBe', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('pricesBe.country', ':belgium'),
                            $queryBuilder->expr()->eq('pricesBe.domainId', ':domainBe')
                        ))
                        ->setParameter('belgium', 'BE')
                        ->setParameter('domainBe', 1);
                    break;
                case 'image':
                    $queryBuilder
                        ->addSelect('image.image')
                        ->join('p.images', 'image', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('BIT_AND(image.flags, :imageFlags)', ':imageFlags')
                        ))
                        ->setParameter('imageFlags', 1);
                    break;
                case 'margin':
                case 'margin_indication':
                case 'fulfillment_fee':
                    $queryBuilder
                        ->addSelect('partial pm.{id, margin, percentage}')
                        ->leftJoin('p.margin', 'pm');
                    break;
                case 'purchase_price':
                    $queryBuilder
                        ->addSelect('IFNULL(IFNULL(ps.stockValue, ppc.price), 0) as purchase_price')
                        ->leftJoin(
                            'p.stock',
                            'ps',
                            Expr\Join::WITH,
                            $queryBuilder->expr()->andX()->addMultiple([
                                $queryBuilder->expr()->isNull('ps.dateOut'),
                                $queryBuilder->expr()->eq('ps.ordernumber', 0),
                                $queryBuilder->expr()->gte('ps.stockValue', 1)
                            ])
                        )
                        ->leftJoin(
                            'Webdsign\GlobalBundle\Entity\PurchasePriceCalculated',
                            'ppc',
                            Expr\Join::WITH,
                            $queryBuilder->expr()->eq('p', 'ppc.product')
                        );
                    break;
                case 'ean':
                case 'EAN':
                    $queryBuilder
                        ->addSelect('pc.code as ean')
                        ->leftJoin('p.productCodes', 'pc', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('pc.type', ':type'),
                            $queryBuilder->expr()->eq('BIT_AND(pc.flags, :productCodeFlags)', ':productCodeFlags')
                        ))
                        ->setParameter('productCodeFlags', 2)
                        ->setParameter('type', 'ean');

                    $queryBuilder
                        ->addSelect('pco.code as productCodeOccasion')
                        ->leftJoin('p.productCodes', 'pco', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('pco.type', ':productCodeOccasion')
                        ))
                        ->setParameter('productCodeOccasion', ProductCode::TYPE_EAN_OCCASION);
                    break;
                case 'productCode':
                    $queryBuilder
                        ->addSelect('pcp.code as productCode')
                        ->leftJoin('p.productCodes', 'pcp', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('pcp.type', ':productCode')
                        ))
                        ->setParameter('productCode', 'artikelcode');
                    break;
                case 'productCodeOccasion':
                    $queryBuilder
                        ->addSelect('pco.code as productCodeOccasion')
                        ->leftJoin('p.productCodes', 'pco', Expr\Join::WITH, $queryBuilder->expr()->andX(
                            $queryBuilder->expr()->eq('pco.type', ':productCodeOccasion')
                        ))
                        ->setParameter('productCodeOccasion', ProductCode::TYPE_EAN_OCCASION);
                    break;
                case 'spiderInfo':
                    $queryBuilder
                        ->addSelect('sg.spiderInfo as spiderinfo_group');
                    break;
                case 'keywords':
                    $queryBuilder
                        ->addSelect('partial pk.{id, keyword}')
                        ->leftJoin('p.keywords', 'pk');
                    break;
                case 'warranty':
                    $queryBuilder
                        ->addSelect('pw.name as warranty')
                        ->leftJoin('p.warranty', 'pw');
                    break;
                case 'images':
                    $queryBuilder
                        ->addSelect('partial images.{id, image}')
                        ->join('p.images', 'images')
                        ->addOrderBy('images.pos');
                    break;
                case 'sales':
                    $queryBuilder
                        ->addSelect('IFNULL(sales.amount, 0) as salesAmount')
                        ->leftJoin(
                            'p.salesStatistic',
                            'sales',
                            Expr\Join::WITH,
                            $queryBuilder->expr()->andX(
                                $queryBuilder->expr()->eq('sales.date', ':dateYesterday')
                            )
                        )
                        ->setParameter('dateYesterday', date('Y-m-d', strtotime('-1 day')));
                    break;
                case 'hasKickBackPremium':
                    $queryBuilder
                        ->addSelect('IFNULL((
                            SELECT
                                IF(SUM(IFNULL(cf.value, 0)) > 0, 1, 0)
                            FROM ' . ProductClaimsAndFees::class . ' AS pdcf
                            LEFT JOIN pdcf.claimsAndFees AS cf
                            LEFT JOIN pdcf.product as pcf
                            WHERE pcf.id = p.id
                            AND (:today BETWEEN cf.dateFrom AND cf.dateTill)
                            AND IDENTITY(cf.kind) = :sellOut
                            GROUP BY pcf.id
                         ), 0) as hasKickBack')
                        ->setParameter('sellOut', ClaimsAndFeesKind::SELL_OUT)
                        ->setParameter('today', new DateTime());
                    break;
            }
        }

        if (is_array($ids)) {
            $ids = array_filter($ids);

            if (count($ids) !== 0) {
                $queryBuilder->andWhere('p.id in (:ids)')->setParameter('ids', $ids);
            }
        }

        return $queryBuilder->getQuery()
            ->getArrayResult();
    }

    public function getPurchaseOrderAmounts(?int $productId = null)
    {
        $queryBuilder = $this->createQueryBuilder('p');

        $queryBuilder
            ->select('p.id')
            ->addSelect('IFNULL((
                SELECT
                    SUM(po.count)
                FROM ' . PurchaseOrder::class . ' AS po
                WHERE po.product = p
                AND BIT_AND(po.flags, :memo_flags) != :memo_flags
            ), 0) as amount_in_purchase_order')
            ->addSelect('IFNULL((
                SELECT COUNT(pst)
                FROM ' . Stock::class . ' pst
                JOIN pst.purchaseOrder pstpo WITH BIT_AND(pstpo.flags, :memo_flags) != :memo_flags
                WHERE pst.product = p
            ), 0) as scanned_amount')
            ->addSelect('IFNULL((
                SELECT
                    COUNT(ksor) - COUNT(DISTINCT CONCAT(kso.id, \'-\', ksor.orderIndex))
                FROM ' . KitSplitOrder::class . ' kso
                JOIN kso.rows ksor WITH ksor.processed = 1
                JOIN kso.purchaseOrder as ksopo WITH (BIT_AND(ksopo.flags, :memo_flags) != :memo_flags)
                WHERE kso.product = p
                ), 0) AS split_scanned_amount
            ')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->where('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', AbstractGroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', AbstractGroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->setParameter('memo_flags', PurchaseOrder::FLAG_FINISHED);

        if ($productId !== null) {
            $queryBuilder
                ->andWhere('p.id = :id')
                ->setParameter('id', $productId);
        }

        $results = $queryBuilder->getQuery()->getArrayResult();
        $results = array_column($results, null, 'id');
        return $results;
    }

    /**
     * @param int|null $productId
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function getProductSpecs(?int $productId = null): array
    {
        $specs = [];

        $sqlSpecs = '
            SELECT
                sas.articleId,
                sas.specId,
                sas.value,
                ss.tweakwise_name AS name,
                COALESCE(sf.alternativeCheckbox, 0, 1) AS split,
                sp.name as profileName,
                sp.id as profileId
            FROM cameranu.specs_article_specifications AS sas
            INNER JOIN cameranu.artikelen p ON sas.articleId = p.id
            INNER JOIN cameranu.subgroepen sg ON sg.id = p.subgroep_id
            INNER JOIN cameranu.hoofdgroepen mg ON mg.id = sg.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen rg ON rg.id = mg.rootgroep_id
            LEFT JOIN cameranu.specs_specifications ss ON sas.specId = ss.id
            LEFT JOIN cameranu.specs_filters_specifications AS sfs ON sas.specId = sfs.specId
            LEFT JOIN cameranu.specs_filters as sf On sf.id = sfs.filterId
            LEFT JOIN cameranu.specs_profile_specifications sps ON sas.specId = sps.specId
            LEFT JOIN cameranu.specs_profiles sp ON sp.id = sps.profileId
            WHERE ss.status = 1
            AND sas.value != \'\'
            AND (p.flags & :articleFlags) = :articleFlags
            AND (sg.flags & :subGroupFlags) = :subGroupFlags
            AND (mg.flags & :mainGroupFlags) = :mainGroupFlags
            AND (rg.flags & :rootGroupFlags) = :rootGroupFlags
        ';

        if ($productId !== null) {
            $sqlSpecs .= ' AND sas.articleId = :id';
        }

        $sqlSpecs .= ' GROUP BY sas.articleId, sas.specId';

        $query = $this->getEntityManager()->getConnection()->prepare($sqlSpecs);
        $query->bindValue('articleFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $specs[$result['articleId']]['profile'] = $result['profileName'];
            $specs[$result['articleId']]['profileId'] = $result['profileId'];
            unset($result['profileName']);
            unset($result['profileId']);
            if ($result['specId'] === '230') {
                $specs[$result['articleId']]['type'] = $result['value'];
            }

            /**
             * Sommige specs worden soms komma gescheiden ingegeven bijv.
             * Kleur: Zwart, Wit deze opslitsen als aparte spec
             */
            if ((int)$result['split'] === 1) {
                $values = explode(', ', $result['value']);
                foreach ($values as $value) {
                    $result['value'] = ltrim($value, ' ');
                    $specs[$result['articleId']][] = $result;
                }
            } else {
                $specs[$result['articleId']][] = $result;
            }
        }

        return $specs;
    }

    public function getTemporaryPrices(?array $productIds = null, string $country = 'NL'): array
    {
        $temporaryPrices = [];
        $sql = '
            SELECT
                artikel_id AS productId,
                tijdelijke_prijs AS price,
                start_datum AS start,
                eind_datum AS end
            FROM cameranu.prijzen_tijdelijk tp
            WHERE start_datum <= NOW() AND eind_datum > NOW()';

        $sql .= ' AND land = :country';

        if ($productIds !== null) {
            $sql .= sprintf(
                ' AND tp.artikel_id in (%s)',
                implode(
                    ',',
                    array_map(
                        fn($idx) => ':pid' . $idx,
                        array_keys($productIds)
                    )
                )
            );
        }

        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindValue('country', $country);

        if ($productIds !== null) {
            foreach ($productIds as $idx => $productId) {
                $query->bindValue('pid' . $idx, $productId);
            }
        }

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $temporaryPrices[$result['productId']][] = [
                'price' => $result['price'],
                'start' => $result['start'],
                'end' => $result['end'],
            ];
        }

        return $temporaryPrices;
    }

    /**
     * @param int|null $productId
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function getImportantProductSpecs(?int $productId = null): array
    {
        $importantSpecs = [];

        $sqlImportantSpecs = '
            SELECT
                sas.value,
                ss.name AS name,
                sas.articleId
            FROM cameranu.specs_specifications AS ss
            INNER JOIN cameranu.specs_article_specifications sas ON sas.specId = ss.id
            INNER JOIN cameranu.artikelen p ON sas.articleId = p.id
            INNER JOIN cameranu.subgroepen sg ON sg.id = p.subgroep_id
            INNER JOIN cameranu.hoofdgroepen mg ON mg.id = sg.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen rg ON rg.id = mg.rootgroep_id
            WHERE ss.important > 0
            AND (p.flags & :articleFlags) = :articleFlags
            AND (sg.flags & :subGroupFlags) = :subGroupFlags
            AND (mg.flags & :mainGroupFlags) = :mainGroupFlags
            AND (rg.flags & :rootGroupFlags) = :rootGroupFlags
        ';

        if ($productId !== null) {
            $sqlImportantSpecs .= ' AND sas.articleId = :id';
        }

        $sqlImportantSpecs .= ' ORDER BY ss.important';

        $query = $this->getEntityManager()->getConnection()->prepare($sqlImportantSpecs);
        $query->bindValue('articleFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $importantSpecs[$result['articleId']][$result['name']] = $result['value'];
        }

        return $importantSpecs;
    }

    /**
     * @param int|null $productId
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function getSecondhandProducts(?int $productId = null): array
    {
        $secondhandProducts = [];

        $sql = '
            SELECT
                p.naam as productName,
                ao.original_product_id,
                ao.child_product_id,
                prices.prijs as price,
                newPrices.prijs as newPrice
            FROM cameranu.artikelen p
            INNER JOIN cameranu.subgroepen sg ON sg.id = p.subgroep_id
            INNER JOIN cameranu.hoofdgroepen mg ON mg.id = sg.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen rg ON rg.id = mg.rootgroep_id
            INNER JOIN cameranu.article_original ao ON p.id = ao.child_product_id
            INNER JOIN cameranu.prijzen prices ON ao.child_product_id = prices.artikel_id
            INNER JOIN cameranu.prijzen newPrices ON ao.original_product_id = newPrices.artikel_id
            WHERE prices.domain_id = 1
            AND newPrices.domain_id = 1
            AND prices.land = \'NL\'
            AND newPrices.land = \'NL\'
            AND (p.flags & :articleFlags) = :articleFlags
            AND (sg.flags & :subGroupFlags) = :subGroupFlags
            AND (mg.flags & :mainGroupFlags) = :mainGroupFlags
            AND (rg.flags & :rootGroupFlags) = :rootGroupFlags
            AND rg.id != :workShopsRootGroupId
        ';

        if ($productId !== null) {
            $sql .= ' AND ao.original_product_id = :id';
        }

        $sql .= ' ORDER BY price ASC';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindValue('articleFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);
        $query->bindValue('workShopsRootGroupId', Rootgroup::WORKSHOP_ID);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $secondhandProducts[$result['original_product_id']][] = [
                'id' => $result['child_product_id'],
                'name' => $result['productName'],
                'price' => $result['price'],
                'newPrice' => $result['newPrice'],
                'url' => 'https://www.cameranu.nl/p' . $result['child_product_id'] . '/' . Utility::urlPrepareString($result['productName']),
            ];
        }

        return $secondhandProducts;
    }

    /**
     * @param int|null $productId
     * @param bool $catalog
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function getProductsPromotions(?int $productId = null, bool $catalog = true): array
    {
        $productsPromotions = $promotions = [];

        $tagIdsSql = '
            SELECT
                tagId AS id
            FROM cameranu.tags_products
        ';

        if ($productId !== null) {
            $tagIdsSql .= ' WHERE itemId = :id';
        }

        $query = $this->getEntityManager()->getConnection()->prepare($tagIdsSql);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $tagIds = [];

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $tagIds[] = $result['id'];
        }

        $menuItemsSql = '
            SELECT
                menuItemId
            FROM cameranu.artikelen p
            INNER JOIN cameranu.artikelen_menuItems ami ON p.id = ami.productId
        ';

        if ($productId !== null) {
            $menuItemsSql .= ' WHERE p.id = :id';
        }

        $menuItemsSql .= ' GROUP BY menuItemId';

        $query = $this->getEntityManager()->getConnection()->prepare($menuItemsSql);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $menuIds = [];

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $menuIds[] = $result['menuItemId'];
        }

        $sql = '
            SELECT
                ca.id,
                p.id as productId,
                item_id,
                title AS type,
                ca.type AS promoType,
                title_new AS title,
                content,
                description_site AS description,
                ca.description AS descriptionAdmin,
                ca.pos,
                overlay,
                spiderinfo,
                reduction_amount,
                discount_amount,
                cashback_amount,
                promotion_amount,
                color,
                `start`,
                `end`
            FROM cameranu.content_acties ca
            LEFT JOIN cameranu.content_actie_artikel caa ON caa.content_actie_id = ca.id
            LEFT JOIN cameranu.artikelen p ON p.id = caa.item_id
            WHERE (
                (caa.type = \'product\'
            ';

        if ($productId !== null) {
            $sql .= ' AND caa.item_id = :id';
        }

        $tagOr = count($tagIds) ? 'OR (caa.item_id IN(' . implode(', ', $tagIds) . ') AND caa.type = "tag")' : '';
        $menuOr = count($menuIds) ? 'OR (caa.item_id IN(' . implode(', ', $menuIds) . ') AND caa.type = "menuItem")' : '';

        $sql  .= ')
                OR ((p.flags2&128) = 128 AND ca.id = :extendedWarrantyId)
                ' . $tagOr . '
                ' . $menuOr . '
            )
            AND `start` <= :start AND (`end` >= :end OR `end` = 0)
            AND status = \'active\'
            AND ca.type != \'info\'';

        if ($catalog === true) {
            $sql .= ' AND (ca.flags&1) = 1';
        }

        $sql .= ' AND ca.deleted = 0
            ORDER BY ca.pos, ca.type, ca.id
        ';

        $extendedWarrantyId = 9265;
        $time = time();
        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindParam('start', $time);
        $query->bindParam('end', $time);
        $query->bindParam('extendedWarrantyId', $extendedWarrantyId);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $articleId = $result['productId'];
            $promotion = [
                'pos' => $result['pos'],
                'title' => $result['title'],
                'content' => Utility::replaceUploadUrlInContent($result['content']),
                'description' => Utility::replaceUploadUrlInContent($result['description']),
                'description_admin' => $result['descriptionAdmin'],
                'color' => $result['color'],
                'promoType' => $result['promoType'],
                'reduction_amount' => $result['reduction_amount'],
                'discount_amount' => $result['discount_amount'],
                'cashback_amount' => $result['cashback_amount'],
                'promotion_amount' => $result['promotion_amount'],
                'spiderinfo' => $result['spiderinfo'],
                'start_timestamp' => $result['start'],
                'end_timestamp' => $result['end'],
            ];

            $productsPromotions[$articleId][] = $promotion;
            $promotions[$result['item_id']][] = $promotion;
        }

        return [
            'productPromotions' => $productsPromotions,
            'promotions' => $promotions,
        ];
    }

    /**
     * @param int|null $productId
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function getActionTagsByProduct(?int $productId = null): array
    {
        $tagTypes = [
            ProductTag::TYPE_MENU,
            ProductTag::TYPE_DISCOUNT_CODE,
            ProductTag::TYPE_ACTION,
        ];

        $sql = '
            SELECT tp.tagId, tp.itemId
            FROM cameranu.tags
            INNER JOIN cameranu.tags_products tp on tp.tagId = tags.tagId
            WHERE tags.tagTypeId  IN (' . implode(', ', $tagTypes) . ')
        ';

        if ($productId !== null) {
            $sql .= ' AND tp.itemId = :id';
        }

        $query = $this->getEntityManager()->getConnection()->prepare($sql);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $promotions = [];

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $promotions[$result['itemId']][] = $result['tagId'];
        }

        return $promotions;
    }

    /**
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getStockStatusData(
        ActiveConfigDataObject $config,
        ?int $productId = null,
        bool $debug = false,
        string $returnType = 'string'
    ): array {
        // Waarden uit de config halen
        $configShippingCutoffs = $config->shipping->cutoff ?? [];
        $configSamedayCutoffs = $config->shipping->sameDay->cutoff ?? [];
        $configShippingTreshold = $config->shipping->threshold->freeShipping ?? 49.95;
        $configHolidays = $config->holidays ?? [];

        // Workaround om object als array te kunnen gebruiken
        $configShippingCutoffs = (array) $configShippingCutoffs;
        $configSamedayCutoffs = (array) $configSamedayCutoffs;
        $configHolidays = (array) $configHolidays;

        // Waardes converteren zodat het gebruikt kan worden in dit script
        $defaultShippingTime = $configShippingCutoffs['default'] ?? '23:59';
        $shippingTimes = [
            1 => $defaultShippingTime,
            2 => $defaultShippingTime,
            3 => $defaultShippingTime,
            4 => $defaultShippingTime,
            5 => $defaultShippingTime,
            6 => $defaultShippingTime,
            7 => $defaultShippingTime,
        ];

        foreach ($configShippingCutoffs as $dayOfTheWeek => $configShippingCutoff) {
            switch ($dayOfTheWeek) {
                case 'monday':
                    $shippingTimes[1] = $configShippingCutoff;
                    break;
                case 'tuesday':
                    $shippingTimes[2] = $configShippingCutoff;
                    break;
                case 'wednesday':
                    $shippingTimes[3] = $configShippingCutoff;
                    break;
                case 'thursday':
                    $shippingTimes[4] = $configShippingCutoff;
                    break;
                case 'friday':
                    $shippingTimes[5] = $configShippingCutoff;
                    break;
                case 'saturday':
                    $shippingTimes[6] = $configShippingCutoff;
                    break;
                case 'sunday':
                    $shippingTimes[7] = $configShippingCutoff;
                    break;
            }
        }

        $todayString = strtolower(date('l'));
        $cutoff = $configSamedayCutoffs['default'] ?? '11:00';

        if (array_key_exists($todayString, $configSamedayCutoffs)) {
            $cutoff = $configSamedayCutoffs[$todayString];
        }

        $stockStatuses = [];
        $threshold = $configShippingTreshold;
        $sameDay = false;
        $timeStamp = time();
        $currentDay = (int)date('N', $timeStamp);
        $currentDateObject = new DateTime();
        $currentDate = $currentDateObject->format('Y-m-d');
        $tomorrow = clone $currentDateObject;
        $tomorrow->modify('+1 day');

        $endTime = $shippingTimes[$currentDay];
        $seconds = strtotime($currentDate . ' ' . $endTime) - $timeStamp;
        $deliverDate = false;

        $currentDateObject->modify('midnight');

        $checkHoliday = false;

        $blockedDates = $this->getBlockedShippingDates($config);

        foreach ($blockedDates as $blockedDate) {
            $blockedDateFormatted = $blockedDate->format('Y-m-d');
            if (
                $currentDate === $blockedDateFormatted ||
                $tomorrow->format('Y-m-d') === $blockedDateFormatted
            ) {
                $checkHoliday = true;
            }
        }

        if ($checkHoliday) {
            $checkDate = clone $tomorrow;
            for ($i = 0; $i < 10; $i++) {
                $deliverDate = self::canWeShipFreeAfterHoliday($checkDate, $config);
                if ($deliverDate !== false) {
                    break;
                }

                $checkDate = $checkDate->modify('+1 day');
            }
        }

        $ignoreTheseIds = ShipmentMethod::PICK_UP_METHODS;
        $ignoreTheseIds = array_merge($ignoreTheseIds, ShipmentMethod::IGNORE_IN_COURIER_CHECK);
        $ignoreTheseIds = implode(', ', $ignoreTheseIds);

        $shopsStockQuery = '';
        foreach (StockLocation::EXTERNAL_LOCATION_CODES as $shop) {
            $shopQuery = '(
                SELECT
                    SUM(IFNULL(aspl' . ucFirst($shop) . '.stock, 0))
                FROM cameranu.artikelen p
                INNER JOIN cameranu.articles_stock_per_location aspl' . ucfirst($shop) . ' ON aspl' . ucfirst($shop) . '.product_id = p.id
                INNER JOIN cameranu.stock_locations sl' . ucfirst($shop) . '  ON aspl' . ucfirst($shop) . ' .stock_location_id = sl' . ucfirst($shop) . ' .id
                    AND sl' . ucfirst($shop) . '.code = \'' . strtoupper($shop) . '\'
                WHERE p.id = piD
            ) AS stock' . ucfirst($shop) . ',';
            $shopsStockQuery .= $shopQuery;
        }

        $sqlSelect = '
            SELECT
            p.id AS pId,
            p.levertijd,
            p.preorder_expire_days AS preOrderCutoffDays,
            IF ((p.flags2 & :isExcludedFromStockCheckFlag) = :isExcludedFromStockCheckFlag, 1, 0) AS isExcludedFromStockCheck,
            IF ((p.flags & :isSetFlag) = :isSetFlag, 1, 0) AS isSet,
            IF ((p.flags & :isKitFlag) = :isKitFlag, 1, 0) AS isKit,
            pc.naam AS cat,
            p.time_created,
            (
                SELECT
                    sdt.delivery_time AS supplierDeliveryTime
                FROM cameranu.supplier_delivery_times sdt
                INNER JOIN cameranu.leverancier_feeds lf ON sdt.supplier_id = lf.id
                WHERE sdt.product_id = pId
                AND sdt.delivery_time IS NOT NULL
                AND sdt.delivery_time != 0
                AND sdt.updated IS NOT NULL
                AND sdt.updated > NOW() - INTERVAL 1 DAY
                AND use_on_website = 1
                ORDER BY sdt.delivery_time
                LIMIT 1
            ) AS supplierDeliveryTime,
            p.flags_verzend AS pFlagsVerzend,
            rg.id AS rootgroepId,
            (
                SELECT
                    (SUM(IFNULL(aspl.stock, 0)) + SUM(IF(IFNULL(aspl.virtual_stock, 0) > 0, aspl.virtual_stock, 0)))
                FROM cameranu.artikelen p
                INNER JOIN cameranu.articles_stock_per_location aspl ON aspl.product_id = p.id
                INNER JOIN cameranu.stock_locations sl ON aspl.stock_location_id = sl.id AND available_for_dispatch = 1
                WHERE p.id = piD
            ) AS dispatchableStock,
            ' . $shopsStockQuery . '
            IF ((p.flags2 & 16) = 0, 0, 1) AS IsNotOrderAble,
            (
                SELECT
                    COUNT(id)
                FROM cameranu.verzendwijze
                WHERE flags&pFlagsVerzend
                AND domain_id = 1
                AND id != 3
            ) AS countShippingMethods,
            (
                SELECT
                    COUNT(id)
                FROM cameranu.verzendwijze
                WHERE flags&pFlagsVerzend
                AND id = 144
            ) AS hasElectronicShipping,
            (
                SELECT
                    COUNT(id)
                FROM cameranu.verzendwijze
                WHERE flags&pFlagsVerzend
                AND domain_id = 1
                AND id NOT IN (' . $ignoreTheseIds . ')
            ) AS countShippingMethods2,
            (
                SELECT
                    COUNT(id)
                FROM cameranu.verzendwijze
                WHERE flags&pFlagsVerzend
                AND id IN (94, 2)
            ) AS countCourierShipmentMethods,
            prices.prijs AS price,
            (
                SELECT
                    IF (COUNT(id) > 0, 0, 1)
                FROM cameranu.voorraad
                WHERE barcode NOT LIKE \'nalever%\'
                AND artikel_id = pId
                LIMIT 1
            ) AS isPreOrder
        ';

        if ($debug === true) {
            $sqlSelect .= ', tdt.delivery_time_string';
        }

        $sqlFrom = ' FROM  cameranu.artikelen AS p';
        $sqlJoins = ' INNER JOIN cameranu.subgroepen sg ON sg.id = p.subgroep_id
            INNER JOIN cameranu.hoofdgroepen hg ON sg.hoofdgroep_id = hg.id
            INNER JOIN cameranu.rootgroepen rg ON rg.id = hg.rootgroep_id
            LEFT JOIN cameranu.prijzen prices ON prices.artikel_id = p.id AND prices.domain_id = 1 AND land = \'NL\'
            LEFT JOIN cameranu.artikelen_cat pc ON pc.id = p.cat_id
        ';

        if ($debug == true) {
            $sqlJoins .= ' LEFT JOIN cameranu.test_delivery_times tdt ON tdt.product_id = p.id';
        }

        $sqlWhere = ' WHERE (p.flags & :articleFlags) = :articleFlags
            AND (rg.flags & :rootGroupFlags) = :rootGroupFlags
            AND (hg.flags & :mainGroupFlags) = :mainGroupFlags
            AND (sg.flags& :subGroupFlags) = :subGroupFlags
            AND p.cat_id != :rentalId
        ';

        $sql = $sqlSelect . $sqlFrom . $sqlJoins . $sqlWhere;

        if ($productId !== null) {
            $sql .= ' AND p.id = :id';
        }

        $sql .= ' GROUP BY p.id';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindValue('articleFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);
        $query->bindValue('rentalId', ProductCategory::RENTAL_ID);
        $query->bindValue('isExcludedFromStockCheckFlag', Product::FLAG2_CHECK_PREORDER_CUTOFF);
        $query->bindValue('isSetFlag', Product::FLAG_SET_OR_OFFER);
        $query->bindValue('isKitFlag', Product::FLAG_KIT_OR_SET_CHOICE);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $stockStatus = '';
            $stockStatusDate = new DateTime();
            $price = $result['price'];

            $abovePriceThreshold = ($price >= $threshold);
            $underPriceThreshold = ($price < $threshold);
            $inStock = ((int)$result['dispatchableStock'] > 0);
            $onlyHasCourierShipping = ((int)$result['countShippingMethods2'] === 1  && $result['countCourierShipmentMethods'] >= 1);
            $onlyHasElectronicShipping = ((int)$result['countShippingMethods'] === 1 && (bool)$result['hasElectronicShipping']);
            $supplierDeliveryTime = $result['supplierDeliveryTime'];
            $deliveryTime = $result['levertijd'];
            $stockInShop = false;
            $stockInShopDeliveryTime = null;
            $quickOrderString = '';

            if ($endTime !== 'false') {
                if ($currentDay == 6 && $seconds > 0) {
                    $quickOrderString = 'Maandag gratis in huis';
                    $sameDay = true;
                    $stockStatusDate->modify('+2 days');
                } elseif ($currentDay != 6) {
                    $quickOrderString = 'Morgen gratis in huis';
                    $stockStatusDate->modify('+1 days');
                }
            }

            if (
                $currentDay === 7 ||
                (
                    $currentDay === 6 &&
                    isset($shippingTimes[6]) &&
                    date('H:i') >= date('H:i', strtotime($shippingTimes[6]))
                )
            ) {
                $quickOrderString = 'Maandag gratis in huis';
                $sameDay = true;
            }

            if (
                $underPriceThreshold ||
                (
                    $sameDay === true &&
                    $config->shipping->sameDay->free->active !== true
                )
            ) {
                $quickOrderString = str_replace(' gratis', '', $quickOrderString);
            }

            if ($checkHoliday && $deliverDate) {
                /**
                 * @todo als dit op de schop gaat deze string (en de andere bestelstrings) netjes vertalen
                 */
                $dayString = strtolower($deliverDate->format('l'));
                switch ($dayString) {
                    case 'monday':
                        $dayString = 'maandag';
                        break;
                    case 'tuesday':
                        $dayString = 'dinsdag';
                        break;
                    case 'wednesday':
                        $dayString = 'woensdag';
                        break;
                    case 'thursday':
                        $dayString = 'donderdag';
                        break;
                    case 'friday':
                        $dayString = 'vrijdag';
                        break;
                    case 'saturday':
                        $dayString = 'zaterdag';
                        break;
                    case 'sunday':
                        $dayString = 'zondag';
                        break;
                }

                $stockStatusDate = $deliverDate;
                $quickOrderString = sprintf('%s gratis in huis', ucfirst($dayString));
            }

            $isSupplierPreferred = match ($supplierDeliveryTime) {
                '1', '1-2', '1-3' => true,
                default => false,
            };

            if ($isSupplierPreferred === false || $supplierDeliveryTime === null) {
                $dispachableStock = $result['dispatchableStock'];

                if (
                    $result['stock' . StockLocation::CODE_AMS_STORE] > 0 ||
                    $result['stock' . StockLocation::CODE_APL_STORE] > 0 ||
                    $result['stock' . StockLocation::CODE_GRO_STORE] > 0 ||
                    $result['stock' . StockLocation::CODE_EHW_STORE] > 0 ||
                    $result['stock' . StockLocation::CODE_ROT_STORE] > 0 ||
                    $result['stock' . StockLocation::CODE_UTR_STORE] > 0
                ) {
                    $dispachableStock += max($result['stock' . StockLocation::CODE_APL_STORE], 0);
                    $dispachableStock += max($result['stock' . StockLocation::CODE_AMS_STORE], 0);
                    $dispachableStock += max($result['stock' . StockLocation::CODE_GRO_STORE], 0);
                    $dispachableStock += max($result['stock' . StockLocation::CODE_EHW_STORE], 0);
                    $dispachableStock += max($result['stock' . StockLocation::CODE_ROT_STORE], 0);
                    $dispachableStock += max($result['stock' . StockLocation::CODE_UTR_STORE], 0);

                    if ($dispachableStock > 0 && $isSupplierPreferred === false) {
                        $stockInShop = true;
                        $stockInShopDeliveryTime = '1-3';
                    }
                }

                if ($stockInShopDeliveryTime === null && $result['stock' . StockLocation::CODE_ANT_STORE] > 0) {
                    $dispachableStock += max($result['stock' . StockLocation::CODE_ANT_STORE], 0);

                    $isSupplierPreferred = match ($supplierDeliveryTime) {
                        '1', '1-2', '1-3', '2', '3' => true,
                        default => false,
                    };

                    if ($dispachableStock > 0 && $isSupplierPreferred === false) {
                        $stockInShop = true;
                        $stockInShopDeliveryTime = '3-5';
                    }
                }
            }

            // Als het een kit betreft die niet compleet bij leverancier of winkel vandaan kan komen
            // Dan gaan we de children checken

            if (
                !$inStock &&
                ($supplierDeliveryTime === null || $supplierDeliveryTime === '0') &&
                $stockInShop === false &&
                (int)$result['isKit'] === 1
            ) {
                $supplierDeliveryTime = $this->getSupplierDeliveryDateByChildren($result);
            }

            if ((int)$result['IsNotOrderAble'] === 1) {
                $stockStatus = 'Nog niet bestelbaar';
                $stockStatusDate = null;
            } elseif (
                $inStock &&
                $sameDay &&
                !$onlyHasElectronicShipping &&
                !$onlyHasCourierShipping &&
                !in_array($currentDay, [6, 7], true) &&
                $timeStamp <= strtotime(date('Y-m-d' . $cutoff))
            ) {
                $stockStatus = 'Vandaag in huis';
                $stockStatusDate = new DateTime();
            } elseif (
                $inStock &&
                $abovePriceThreshold &&
                !$onlyHasElectronicShipping &&
                !$onlyHasCourierShipping &&
                $quickOrderString !== ''
            ) {
                $stockStatus = $quickOrderString;
            } elseif (
                $inStock &&
                $underPriceThreshold &&
                !$onlyHasElectronicShipping &&
                !$onlyHasCourierShipping &&
                $quickOrderString !== ''
            ) {
                $stockStatus = str_replace(' gratis', '', $quickOrderString);
            } elseif ($inStock && $onlyHasCourierShipping) {
                $stockStatus = 'Op voorraad';
                $stockStatusDate->modify('+1 days');
            } elseif ($inStock && $onlyHasElectronicShipping) {
                $stockStatus = 'Direct leverbaar';
            } elseif ($supplierDeliveryTime === '1-2') {
                $stockStatus = '1 tot 2 werkdagen';
                $stockStatusDate->modify('+2 days');
            } elseif ($supplierDeliveryTime === '3-5' || (!$inStock && $stockInShopDeliveryTime === '3-5')) {
                $stockStatus = '3 tot 5 werkdagen';
                $stockStatusDate->modify('+5 days');
            } elseif ($supplierDeliveryTime === '1-3' || $supplierDeliveryTime === '3' || (!$inStock && $stockInShopDeliveryTime === '1-3')) {
                $stockStatus = '1 tot 3 werkdagen';
                $stockStatusDate->modify('+3 days');
            } elseif (!$inStock && $supplierDeliveryTime !== null && $supplierDeliveryTime !== '0') {
                $stockStatus = str_replace('-', ' tot ', $supplierDeliveryTime) . ' werkdagen';
                $stockStatusDate->modify('+' . $supplierDeliveryTime . ' days');
            } elseif ($deliveryTime !== 'Op bestelling leverbaar' && $this->isPreOrder($result)) {
                $stockStatusDate = null;
                if ($deliveryTime !== '') {
                    $stockStatus = $deliveryTime;
                } else {
                    $stockStatus = 'Nieuw product! Reserveer nu!';
                }
            } elseif (!empty($deliveryTime) && stripos(strtolower($deliveryTime), 'onbekend') !== false) {
                $stockStatus = 'Levertijd onbekend';
                $stockStatusDate = null;
            } elseif ((int)$result['IsNotOrderAble'] === 0 && !$inStock) {
                $stockStatus = 'backorder';
                $stockStatusDate = new DateTime();
                if ($supplierDeliveryTime !== null) {
                    $stockStatusDate->modify('+' . $supplierDeliveryTime . ' days');
                } else {
                    $stockStatusDate->modify('+ 5 weeks');
                }
            } else {
                $stockStatus = 'Tijdelijk uitverkocht';
                $stockStatusDate = null;
            }

            if ($debug === true && $result['delivery_time_string'] !== null) {
                if ($result['delivery_time_string'] !== $stockStatus) {
                    $message = 'product: ' . $result['pId'] . ' heeft stockStatus: ' . $stockStatus .
                        ' maar op website heeft hij: ' . $result['delivery_time_string'] . PHP_EOL
                    ;

                    print_r($message);
                }
            }

            switch ($returnType) {
                case 'datetime':
                    $stockStatuses[$result['pId']] = $stockStatusDate;
                    break;
                case 'string':
                default:
                    $stockStatuses[$result['pId']] = $stockStatus;
            }
        }

        return $stockStatuses;
    }

    /**
     * @param $product
     * @return bool
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function isPreOrder($product): bool
    {
        $preOrder = false;

        // check if product is a kit
        $kit = (($product['cat'] !== 'artikel' && $product['isSet']) || $product['cat'] === 'kit');

        if ($kit) {
            $query = $this->createQueryBuilder('p');

            $query
                ->select('p')
                ->join(ProductOfferSet::class, 'aa', 'WITH', 'aa.product = p')
                ->andWhere('p = aa.product')
                ->andWhere('aa.parentProduct = :product')
                ->setParameter('product', $product['pId'])
                ->orderBy('aa.position');

            $children = $query->getQuery()->getResult();

            foreach ($children as $child) {
                $infoQuery = '
                    SELECT
                        p.id AS pId,
                        p.preorder_expire_days AS preOrderCutoffDays,
                        IF ((p.flags2 & :isExcludedFromStockCheckFlag) = :isExcludedFromStockCheckFlag, 1, 0) AS isExcludedFromStockCheck,
                        IF ((p.flags & :isSetFlag) = :isSetFlag, 1, 0) AS isSet,
                        pc.naam AS cat,
                        p.time_created,
                        (
                            SELECT
                                (SUM(IFNULL(aspl.stock, 0)) + SUM(IF(IFNULL(aspl.virtual_stock, 0) > 0, aspl.virtual_stock, 0)))
                            FROM cameranu.artikelen p
                            INNER JOIN cameranu.articles_stock_per_location aspl ON aspl.product_id = p.id
                            INNER JOIN cameranu.stock_locations sl ON aspl.stock_location_id = sl.id AND available_for_dispatch = 1
                            WHERE p.id = piD
                        ) AS dispatchableStock,
                        (
                            SELECT
                                IF (COUNT(id) > 0, 0, 1)
                            FROM cameranu.voorraad
                            WHERE barcode NOT LIKE \'nalever%\'
                            AND artikel_id = pId
                            LIMIT 1
                        ) AS isPreOrder
                    FROM  cameranu.artikelen AS p
                    LEFT JOIN cameranu.artikelen_cat pc ON pc.id = p.cat_id
                    WHERE
                        p.id = :id';

                $info = $this->getEntityManager()->getConnection()->prepare($infoQuery);
                $info->bindValue('isExcludedFromStockCheckFlag', Product::FLAG2_CHECK_PREORDER_CUTOFF);
                $info->bindValue('isSetFlag', Product::FLAG_SET_OR_OFFER);
                $info->bindValue('id', $child->getId());

                $result = $info->execute()->fetchAssociative();

                $childPreOrder = $this->isPreOrder($result);
                if ($childPreOrder) {
                    $preOrder = true;
                    break;
                }
            }
        } else {
            if ((bool)$product['isExcludedFromStockCheck'] === true) {
                $preOrderCutoffDays = (int)$product['preOrderCutoffDays'];
            } else {
                $preOrderCutoffDays = 90;
            }

            $created = strtotime($product['time_created']);
            $now = time();
            $diff = ceil(($now - $created) / 60 / 60 / 24);

            if (
                (int)$diff <= $preOrderCutoffDays &&
                ((bool)$product['isExcludedFromStockCheck'] === true && $product['dispatchableStock'] <= 0)
            ) {
                $preOrder = true;
            } elseif ((bool)$product['isExcludedFromStockCheck'] === false) {
                $preOrder = (bool)$product['isPreOrder'];
            }
        }
        return $preOrder;
    }

    /**
     * Returns a list of dates where we can or do not ship orders. This is a
     * combination of holidays and cutoff times.
     *
     * @return DateTimeImmutable[]
     */
    private function getBlockedShippingDates(ActiveConfigDataObject $config): array
    {
        $blockedDates = [];

        $holidays = $config->holidays ?? [];
        $shipping = $config->shipping ?? [];

        $blockStartDate = new DateTimeImmutable();

        for ($i = 0; $i < 7; $i++) {
            $blockCheckDate = $blockStartDate->modify('+' . $i . ' days');
            $blockCheckDay = strtolower($blockCheckDate->format('l'));

            $cutoffTime = $shipping->cutoff->{$blockCheckDay} ?? false;
            if (!$cutoffTime) {
                $blockedDates[] = $blockCheckDate;
            }
        }

        foreach ($holidays as $holiday) {
            $holidayDate = is_string($holiday->date) ? new DateTimeImmutable($holiday->date) : null;
            if ($holidayDate instanceof DateTimeImmutable) {
                $blockedDates[] = $holidayDate;
            }
        }

        $blockedDates = array_map(
            static fn (DateTimeImmutable $date) => $date->setTime(0, 0),
            $blockedDates
        );

        return $blockedDates;
    }

    /**
     * @param Datetime $currentDate
     * @param ActiveConfigDataObject $config
     * @return bool|Datetime
     * @throws \Exception
     */
    private function canWeShipFreeAfterHoliday(DateTime $currentDate, ActiveConfigDataObject $config)
    {
        $blockedDates = $this->getBlockedShippingDates($config);

        /**
         * Get today's shipping time cutoff
         */
        $currentDay = strtolower($currentDate->format('l'));
        $shippingTimeCutoff = $config->shipping->cutoff->{$currentDay} ?? false;

        /**
         * Are we still shipping today
         */
        $stillShipping = !($shippingTimeCutoff !== false && utility::secondsDifferenceWithTime($shippingTimeCutoff) < 0);

        $yesterdayDate = clone $currentDate;
        $yesterdayDate->modify('-1 day');
        $yesterdayDate->setTime(0, 0);

        foreach ($blockedDates as $blockedDate) {
            $currentDate->setTime(0, 0);

            if (
                $currentDate->getTimestamp() === $blockedDate->getTimeStamp() ||
                (
                    $stillShipping === false &&
                    $yesterdayDate->getTimestamp() === $blockedDate->getTimeStamp()
                )
            ) {
                return false;
            }
        }

        /**
         * Is het vandaag(als in letterlijk huidige dag) niet zaterdag of zondag?
         * En is sameDay levering gratis
         */
        if (
            !in_array(date('w'), [6, 0]) &&
            $config->shipping->sameDay->free->active === true
        ) {
            return $currentDate;
        }

        /**
         * Is het vandaag vrijdag en de leverdatum zaterdag
         */
        if (date('N') == 5 && $currentDate->format('N') == 6) {
            return false;
        }

        /**
         * Is leverdatum zondag
         */
        if ($currentDate->format('N') == 7) {
            return false;
        }

        return $currentDate;
    }

    /**
     * @return array
     */
    public function getNDAProducts(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        return $queryBuilder
            ->andWhere('BIT_AND(p.flags2, :NDAFlag) = :NDAFlag')
            ->setParameter('NDAFlag', Product::FLAG2_IS_NDA)
            ->getQuery()
            ->getResult();
    }

    public function getBrandData(
        array $productIds,
        int $productFlags = Product::FLAG_SHOW + Product::FLAG_VISIBLE,
        bool $indexById = false
    ): array {
        $productIds = array_filter($productIds);

        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('sas.value AS brand')
            ->join('p.specsArticleSpecification', 'sas')
            ->join('sas.specSpecification', 'ss')
            ->join('ss.specsFiltersSpecification', 'sfs')
            ->andWhere('sfs.filterId = :filterId')
            ->setParameter('filterId', 34)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', $productFlags);

        if ($productIds !== []) {
            $queryBuilder
                ->andWhere('p.id in (:ids)')
                ->setParameter('ids', $productIds);
        }

        $results = $queryBuilder->getQuery()->getArrayResult();

        if ($indexById) {
            $rows = $results;
            $results = [];
            foreach ($rows as $row) {
                $results[$row['id']] = $row['brand'];
            }
        }

        return $results;
    }

    /**
     * @return void
     */
    public function clear(): void
    {
        $this->getEntityManager()->clear(Product::class);
    }


    /**
     * @param int $productId
     * @return false|array
     * @throws \Doctrine\DBAL\Exception
     */
    public function getActionLabel(int $productId)
    {
        $productPromotions = [];
        $promotions = $this->getProductsPromotions($productId);

        if (
            array_key_exists('productPromotions', $promotions) &&
            is_array($promotions['productPromotions']) &&
            array_key_exists($productId, $promotions['productPromotions'])
        ) {
            $productPromotions = $promotions['productPromotions'][$productId];
        }

        $tagPromotions = [];
        $actionTagProducts = $this->getActionTagsByProduct($productId);
        $actionTags = array_key_exists($productId, $actionTagProducts) ? $actionTagProducts[$productId] : [];


        foreach ($actionTags as $tagId) {
            if (
                array_key_exists('promotions', $promotions) &&
                is_array($promotions['promotions']) &&
                array_key_exists($tagId, $promotions['promotions'])
            ) {
                $tagPromotions[] = $promotions['promotions'][$tagId];
            }
        }

        if (count($tagPromotions) > 0) {
            foreach ($tagPromotions as $promotion) {
                $promotion = current($promotion);
                //samenvoegen voor naam op label bepaling
                array_push($productPromotions, $promotion);
            }
        }

        //Automatische korting? verschil tussen van/voor en huidig prijs wordt ingevuld.
        foreach ($productPromotions as $key => $promotion) {
            if (
                str_contains($promotion['title'], '%korting%') ||
                str_contains($promotion['description'], '%korting%')
            ) {
                $product = $this->findOneBy(['id' => $productId]);
                $prices = $product->getPrices();
                $correctPrice = null;
                foreach ($prices as $price) {
                    if ($price->getDomainId() === 1 && $price->getCountry() === 'NL') {
                        if ($correctPrice instanceof ProductPrice) {
                            if ($correctPrice->getUpdatedTime() > $price->getUpdatedTime()) {
                                continue;
                            }
                        }
                        $correctPrice = $price;
                    }
                }

                if ($correctPrice->getPriceFromTo() !== 0) {
                    $discount = $correctPrice->getPriceFromTo() - $correctPrice->getPrice();

                    if (intval($discount) !== $discount) {
                        $promotion['title'] = str_replace(',-', '', $promotion['title']);
                    }

                    $title = str_replace('%korting%', Utility::formatPrice($discount), $promotion['title']);
                    $description = str_replace('%korting%', Utility::formatPrice($discount), $promotion['description']);

                    $productPromotions[$key]['pos'] = -10;
                    $productPromotions[$key]['title'] = '<span class="black-friday-label">' . $title . '</span>';
                    $productPromotions[$key]['description'] = $description;
                } else {
                    unset($productPromotions[$key]);
                }
            }
        }

        usort($productPromotions, function ($promo1, $promo2) {
            return $promo1['pos'] <=> $promo2['pos'];
        });

        return current($productPromotions);
    }

    /**
     * @param int|null $productId
     * @return array
     * @throws \Doctrine\DBAL\Exception
     */
    public function getStockPerLocation(?int $productId = null, bool $includeInvisible = false): array
    {
        $stockSql = '
            SELECT
                p.id as piD,
                (
                    SELECT (SUM(IFNULL(aspl.stock, 0)) + SUM(IF(IFNULL(aspl.virtual_stock, 0) > 0, aspl.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location aspl ON aspl.product_id = p.id
                    INNER JOIN cameranu.stock_locations sl ON aspl.stock_location_id = sl.id AND available_for_dispatch = 1
                    WHERE p.id = piD
                ) AS dispatchableStock,
                (
                    SELECT (SUM(IFNULL(asplAms.stock, 0)) + SUM(IF(IFNULL(asplAms.virtual_stock, 0) > 0, asplAms.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location asplAms ON asplAms.product_id = p.id
                    INNER JOIN cameranu.stock_locations slAms ON asplAms.stock_location_id = slAms.id AND slAms.parent = \'cameranu_amsterdam\'
                    WHERE p.id = piD
                ) AS stockAms,
                (
                    SELECT (SUM(IFNULL(asplApl.stock, 0)) + SUM(IF(IFNULL(asplApl.virtual_stock, 0) > 0, asplApl.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location asplApl ON asplApl.product_id = p.id
                    INNER JOIN cameranu.stock_locations slApl ON asplApl.stock_location_id = slApl.id AND slApl.parent = \'cameranu_apeldoorn\'
                    WHERE p.id = piD
                ) AS stockApl,
                (
                    SELECT (SUM(IFNULL(asplGro.stock, 0)) + SUM(IF(IFNULL(asplGro.virtual_stock, 0) > 0, asplGro.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location asplGro ON asplGro.product_id = p.id
                    INNER JOIN cameranu.stock_locations slGro ON asplGro.stock_location_id = slGro.id AND slGro.parent = \'cameranu_groningen\'
                    WHERE p.id = piD
                ) AS stockGro,
                (
                    SELECT (SUM(IFNULL(asplEhw.stock, 0)) + SUM(IF(IFNULL(asplEhw.virtual_stock, 0) > 0, asplEhw.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location asplEhw ON asplEhw.product_id = p.id
                    INNER JOIN cameranu.stock_locations slEhw ON asplEhw.stock_location_id = slEhw.id AND slEhw.parent = \'cameranu_eindhoven\'
                    WHERE p.id = piD
                ) AS stockEhw,
                (
                    SELECT (SUM(IFNULL(asplRot.stock, 0)) + SUM(IF(IFNULL(asplRot.virtual_stock, 0) > 0, asplRot.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location asplRot ON asplRot.product_id = p.id
                    INNER JOIN cameranu.stock_locations slRot ON asplRot.stock_location_id = slRot.id AND slRot.parent = \'cameranu_rotterdam\'
                    WHERE p.id = piD
                ) AS stockRot,
                (
                    SELECT (SUM(IFNULL(asplUtr.stock, 0)) + SUM(IF(IFNULL(asplUtr.virtual_stock, 0) > 0, asplUtr.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location asplUtr ON asplUtr.product_id = p.id
                    INNER JOIN cameranu.stock_locations slUtr ON asplUtr.stock_location_id = slUtr.id AND slUtr.parent = \'cameranu_utrecht\'
                    WHERE p.id = piD
                ) AS stockUtr,
                (
                    SELECT (SUM(IFNULL(asplAnt.stock, 0)) + SUM(IF(IFNULL(asplAnt.virtual_stock, 0) > 0, asplAnt.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location asplAnt ON asplAnt.product_id = p.id
                    INNER JOIN cameranu.stock_locations slAnt ON asplAnt.stock_location_id = slAnt.id AND slAnt.parent = \'cameranu_antwerpen\'
                    WHERE p.id = piD
                ) AS stock092W
            FROM  cameranu.artikelen AS p
            INNER JOIN cameranu.subgroepen sg ON sg.id = p.subgroep_id
            INNER JOIN cameranu.hoofdgroepen hg ON sg.hoofdgroep_id = hg.id
            INNER JOIN cameranu.rootgroepen rg ON rg.id = hg.rootgroep_id
            WHERE (p.flags & :articleFlags) = :articleFlags
            AND (rg.flags & :rootGroupFlags) = :rootGroupFlags
            AND (hg.flags & :mainGroupFlags) = :mainGroupFlags
            AND (sg.flags& :subGroupFlags) = :subGroupFlags
            AND p.cat_id != :rentalId
        ';

        if ($productId !== null) {
            $stockSql .= ' AND p.id = :id';
        }

        $productFlags = Product::FLAG_SHOW;
        if ($includeInvisible === false) {
            $productFlags += Product::FLAG_VISIBLE;
        }

        $query = $this->getEntityManager()->getConnection()->prepare($stockSql);
        $query->bindValue('articleFlags', $productFlags);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);
        $query->bindValue('rentalId', ProductCategory::RENTAL_ID);

        if ($productId !== null) {
            $query->bindValue('id', $productId);
        }

        $stockPerLocation = [];

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $result) {
            $dispatchable = $result['dispatchableStock'] ?? 0;
            $ams = $result['stockAms'] ?? 0;
            $apl = $result['stockApl'] ?? 0;
            $gro = $result['stockGro'] ?? 0;
            $ehw = $result['stockEhw'] ?? 0;
            $rot = $result['stockRot'] ?? 0;
            $utr = $result['stockUtr'] ?? 0;
            $ant = $result['stock092W'] ?? 0;
            $withoutUrk = $ams + $apl + $gro + $ehw + $rot + $ant + $utr;
            $status = $dispatchable <= 0 ? 'nee' : 'ja';

            $stockPerLocation[$result['piD']] = [
                'urk' => $dispatchable,
                'amsterdam' => $ams,
                'apeldoorn' => $apl,
                'groningen' => $gro,
                'eindhoven' => $ehw,
                'rotterdam' => $rot,
                'utrecht' => $utr,
                'antwerpen' => $ant,
                'zonder_urk' => $withoutUrk,
                'status' => $status,
            ];
        }

        return $stockPerLocation;
    }

    public function getDispatchableStockForProductId(int $productId): int
    {
        $stock = $this->getDispatchableStockForProductIds([$productId]);
        return $stock[$productId] ?? 0;
    }

    /**
     * @param int[] $productIds
     * @return array<int, int>
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    public function getDispatchableStockForProductIds(array $productIds, array $excludedGroups = null): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id, (SUM(IFNULL(sil.stock, 0)) + SUM(IF(IFNULL(sil.virtualStock, 0) > 0, sil.virtualStock, 0))) AS dispatchable')
            ->join('p.stockInLocations', 'sil')
            ->join('sil.stockLocation', 'sl')
            ->where('p.id IN (:ids)')
            ->andWhere('sl.availableForDispatch = 1')
            ->setParameter('ids', $productIds)
            ->groupBy('p.id');

        if ($excludedGroups !== null) {
            $queryBuilder
                ->join('p.subgroup', 'sg')
                ->join('sg.maingroup', 'mg')
                ->join('mg.rootgroup', 'rg')
                ->andWhere('rg.id not in (:excludedGroups)')
                ->setParameter('excludedGroups', $excludedGroups);
        }

        $result = [];
        foreach ($queryBuilder->getQuery()->getArrayResult() as $row) {
            $result[(int)$row['id']] = (int)$row['dispatchable'];
        }

        return $result;
    }

    /**
     * @return array
     */
    public function getCategoryPath(): array
    {
        $queryBuilder = $this->getQueryBuilder('p');
        $queryBuilder
            ->select('p.id as productId')
            ->addSelect('pmi.path')
            ->join('p.productMenuItems', 'pmi')
            ->join('pmi.menuItem', 'mi')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->andWhere('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->andWhere('mi.active = :active')
            ->setParameter('active', 1)
            ->andWhere('mi.menuId = :menuId')
            ->setParameter('menuId', MenuItem::LEFT_MENU)
            ->andWhere('mi.type IN (:types)')
            ->setParameter('types', ['regular', ''])
            ->andWhere('pmi.isEndPoint = :end')
            ->setParameter('end', 1)
            ->orderBy('mi.sort, p.id', 'DESC')
        ;

        $results = $queryBuilder->getQuery()->getArrayResult();
        $paths = [];

        $queryBuilder = new QueryBuilder($this->_em);
        $queryBuilder
            ->select('partial mi.{id, url, path}')
            ->from('WebdsignGlobalBundle:MenuItem', 'mi', 'mi.id')
            ->andWhere('mi.active = :active')
            ->setParameter('active', 1)
            ->andWhere('mi.menuId = :menuId')
            ->setParameter('menuId', MenuItem::LEFT_MENU)
            ->andWhere('mi.type IN (:types)')
            ->setParameter('types', ['regular', ''])
        ;

        $urlResults = $queryBuilder->getQuery()->getArrayResult();
        $urls = [];

        foreach ($urlResults as $urlResult) {
            $url = $urlResult['url'] !== '' ? $urlResult['url'] : 'c' . $urlResult['id'] . '/' . $urlResult['path'];
            $urls[$urlResult['id']] = strpos($url, '/nl/') !== false ? $url : '/nl/' . $url;
        }

        foreach ($results as $result) {
            $parts = json_decode($result['path']);

            $id = array_key_exists(0, $parts) ? $parts[0]->id : 0;
            $path['rootgroup_website'] = [
                'id' => $id,
                'name' => array_key_exists(0, $parts) ? $parts[0]->name : '',
                'url' =>  array_key_exists($id, $urls) ? $urls[$id] : '',
            ];

            $id = array_key_exists(1, $parts) ? $parts[1]->id : 0;
            $path['maingroup_website'] = [
                'id' => $id,
                'name' => array_key_exists(1, $parts) ? $parts[1]->name : '',
                'url' =>  array_key_exists($id, $urls) ? $urls[$id] : '',
            ];

            $id = array_key_exists(2, $parts) ? $parts[2]->id : 0;
            $path['subgroup_website'] = [
                'id' => $id,
                'name' => array_key_exists(2, $parts) ? $parts[2]->name : '',
                'url' =>  array_key_exists($id, $urls) ? $urls[$id] : '',
            ];

            $id = array_key_exists(3, $parts) ? $parts[3]->id : 0;
            $path['subgroupsub_website'] = [
                'id' => $id,
                'name' => array_key_exists(3, $parts) ? $parts[3]->name : '',
                'url' =>  array_key_exists($id, $urls) ? $urls[$id] : '',
            ];

            $paths[$result['productId']] = $path;
        }

        return $paths;
    }

    /**
     * @param int|null $productId
     * @param bool $useIds
     * @return array
     */
    public function getProductTags(?int $productId = null, bool $useIds = false): array
    {
        $queryBuilder = new QueryBuilder($this->_em);

        $queryBuilder
            ->select('partial p.{id}')
            ->addSelect('partial pt.{id}')
            ->addSelect('partial t.{tagId, value}')
            ->from('WebdsignGlobalBundle:ProductTag', 'pt', 'pt.id')
            ->join('pt.product', 'p')
            ->join('pt.tag', 't')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->andWhere('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
        ;

        if ($productId !== null) {
            $queryBuilder
                ->andWhere('p.id = :id')
                ->setParameter('id', $productId)
            ;
        }

        $result = $queryBuilder->getQuery()->getArrayResult();
        $tags = [];

        foreach ($result as $tagInfo) {
            if (
                array_key_exists('product', $tagInfo) &&
                array_key_exists('id', $tagInfo['product']) &&
                array_key_exists('tag', $tagInfo) &&
                array_key_exists('value', $tagInfo['tag'])
            ) {
                if ($useIds === true) {
                    $tags[$tagInfo['product']['id']][] = $tagInfo['tag']['tagId'];
                } else {
                    $tags[$tagInfo['product']['id']][] = $tagInfo['tag']['value'];
                }
            }
        }

        return $tags;
    }

    /**
     * @param array $product
     * @return string|null
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getSupplierDeliveryDateByChildren(array $product): ?string
    {
        $sql = '
            SELECT
                art_id as childId,
                (
                    SELECT
                        (SUM(IFNULL(aspl.stock, 0)) + SUM(IF(IFNULL(aspl.virtual_stock, 0) > 0, aspl.virtual_stock, 0)))
                    FROM cameranu.artikelen p
                    INNER JOIN cameranu.articles_stock_per_location aspl ON aspl.product_id = p.id
                    INNER JOIN cameranu.stock_locations sl ON aspl.stock_location_id = sl.id AND available_for_dispatch = 1
                    WHERE p.id = childId
                ) AS dispatchableStock
            FROM cameranu.artikelen_aanbiedingen AS aa
            INNER JOIN cameranu.artikelen child ON child.id = aa.art_id
            WHERE aa.parent_art_id = :id
        ';

        switch ($product['cat']) {
            case product::CAT_COMBO_1:
            case product::CAT_SALE:
                $query = $this->getEntityManager()->getConnection()->prepare($sql);
                $query->bindValue('id', $product['pId']);
                break;
            case product::CAT_KIT:
                if ($product['dispatchableStock'] <= 0) {
                    $query = $this->getEntityManager()->getConnection()->prepare($sql);
                    $query->bindValue('id', $product['pId']);
                }
                break;
        }

        $ids = [];
        if (isset($query)) {
            $result = $query->executeQuery();
            foreach ($result->fetchAllAssociative() as $result) {
                if ($result['dispatchableStock'] >= 1) {
                    continue;
                }

                $ids[] = $result['childId'];
            }
        }

        $ids = array_unique($ids);

        if (count($ids) > 0) {
            // Als de delivery_time geen nullen of 0en bevat returned hij de goede delivery_time
            $sql = '
                SELECT
                    product_id as childId,
                    MIN(delivery_time) as deliveryTime
                FROM cameranu.supplier_delivery_times sdt
                INNER JOIN cameranu.leverancier_feeds lf ON sdt.supplier_id = lf.id
                WHERE product_id IN (' . implode(', ', $ids) . ')
                AND lf.use_on_website = 1
                AND sdt.updated IS NOT NULL
                AND sdt.updated > NOW() - INTERVAL 1 DAY
                GROUP BY sdt.product_id, sdt.supplier_id
                HAVING deliveryTime != 0 AND deliveryTime IS NOT NULL
            ';

            $query = $this->getEntityManager()->getConnection()->prepare($sql);
            $result = $query->executeQuery();

            $childDeliveryTimes = $validDeliveryTimes = [];

            foreach ($result->fetchAllAssociative() as $deliveryTime) {
                $childDeliveryTimes[$deliveryTime['childId']][] = $deliveryTime['deliveryTime'];
            }

            //per child de snelste geldige levertijd pakken
            foreach ($childDeliveryTimes as $childDeliveryTime) {
                $validDeliveryTimes[] = min($childDeliveryTime);
            }

            if (is_array($validDeliveryTimes) && count($validDeliveryTimes) === count($ids)) {
                // Alle children hebben een geldige levertijd we pakken de hoogste
                $deliveryTime['delivery_time'] = max($validDeliveryTimes);
            } else {
                return null;
            }

            if (!empty($deliveryTime['delivery_time'])) {
                return $deliveryTime['delivery_time'];
            }
        }

        return null;
    }

    /**
     * @param array|null $tagIds
     * @return array
     */
    public function findForCourierListByTags(?array $tagIds = null): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('p.name')
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW)
        ;

        if ($tagIds !== null && count($tagIds) >= 0) {
            $queryBuilder
                ->innerJoin('p.productTags', 'pt')
                ->innerJoin('pt.tag', 'tag')
                ->andWhere($queryBuilder->expr()->in('tag.tagId', ':tagIds'))
                ->setParameter('tagIds', $tagIds)
            ;
        } else {
            return [];
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param array|null $groups
     * @return array
     */
    public function findForCourierListByGroup(?array $groups = null): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('p.name')
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW)
        ;

        $group = null;
        $groupType = null;
        if ($groups !== null) {
            foreach ($groups as $type => $id) {
                if ($id === '') {
                    continue;
                }

                $group = (int)$id;
                $groupType = $type;
            }
        } else {
            return [];
        }

        if ($group !== null) {
            switch ($groupType) {
                case 'root':
                    $queryBuilder
                        ->leftJoin('p.subgroup', 'sg')
                        ->leftJoin('sg.maingroup', 'mg')
                        ->leftJoin('mg.rootgroup', 'rg')
                        ->andWhere('rg.id = :rgId')
                        ->setParameter('rgId', $group);
                    break;
                case 'main':
                    $queryBuilder
                        ->leftJoin('p.subgroup', 'sg')
                        ->leftJoin('sg.maingroup', 'mg')
                        ->andWhere('mg.id = :mgId')
                        ->setParameter('mgId', $group);
                    break;
                case 'sub':
                    $queryBuilder
                        ->leftJoin('p.subgroup', 'sg')
                        ->andWhere('sg.id = :sgId')
                        ->setParameter('sgId', $group);
                    break;
            }

            return $queryBuilder->getQuery()->getResult();
        }

        return [];
    }

    /**
     * @param \Webdsign\GlobalBundle\Entity\Event\Product[] $eventProducts
     * @return Product[]
     */
    public function findForEventProducts(array $eventProducts): array
    {
        $queryBuilder = $this->createQueryBuilder('p');

        $queryBuilder
            ->select('ep.id,p,prices')
            ->join(\Webdsign\GlobalBundle\Entity\Event\Product::class, 'ep', 'WITH', 'ep.product = p.id')
            ->leftJoin('p.prices', 'prices', Expr\Join::WITH, $queryBuilder->expr()->andX(
                $queryBuilder->expr()->eq('prices.country', ':country'),
                $queryBuilder->expr()->eq('prices.domainId', ':domain')
            ))
            ->andWhere('ep.id IN (:ids)')
            ->setParameter('ids', $eventProducts)
            ->setParameter(':country', 'NL')
            ->setParameter(':domain', Domain::CAMERANU);

        $products = $queryBuilder
            ->getQuery()
            ->setHint(Query::HINT_FORCE_PARTIAL_LOAD, true)
            ->getResult();

        $result = [];

        foreach ($products as $product) {
            $result[$product['id']] = $product[0];
        }

        return $result;
    }

    /**
     * Vind producten waar min_voorraad afwijkt van de minimal_stock in StockInLocations
     *
     * @param StockLocation $stockLocation
     * @return array
     */
    public function getWrongOrMissingMinStockForLocation(StockLocation $stockLocation): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $minStockField = 'minStock';
        if ($stockLocation->getId() === StockLocation::WINKEL_URK) {
            $minStockField .= 'W';
        }

        $queryBuilder
            ->select('p')
            ->leftJoin('p.stockInLocations', 'sil')
            ->andWhere('sil.stockLocation = :stockLocation')
            ->andWhere($queryBuilder->expr()->isNotNull('p.' . $minStockField))
            ->andWhere('p.' . $minStockField . ' != 0')
            ->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->neq('sil.minimalStock', ' p.' . $minStockField),
                $queryBuilder->expr()->isNull('sil.minimalStock')
            ))
            ->setParameter('stockLocation', $stockLocation)
        ;

        return $queryBuilder->getQuery()->getResult();
    }

    public function findOneByEanWithLeadingZeros(string $ean, bool $noImportLoads = true): ?int
    {
        $products = $this->findByEanWithLeadingZeros([$ean], $noImportLoads);
        return $products[0]['id'] ?? null;
    }

    /**
     * @param string[] $eans
     * @return int[]
     */
    public function findByEanWithLeadingZeros(array $eans, bool $noImportLoads = true, bool $forStockInfoImport = false): array
    {
        $eans = array_map(static fn (string $ean) => ltrim($ean, '0'), $eans);
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select($forStockInfoImport ? 'p.id, pc.codeTrimmed' : 'p.id')
            ->join('p.productCodes', 'pc')
            ->andWhere('pc.codeTrimmed IN (:eans)')
            ->andWhere('pc.type = :type')
            ->setParameter('type', 'ean')
            ->setParameter('eans', $eans);
        if ($forStockInfoImport) {
            $queryBuilder
                ->join('p.subgroup', 'sg')
                ->join('sg.maingroup', 'mg')
                ->andWhere('mg.id != :mgId')
                ->andWhere('BIT_AND(p.flags, :flags) = :flags')
                ->setParameter('mgId', Maingroup::MAINGROUP_ID_OCCASIONS_AND_DEMO)
                ->setParameter('flags', Product::FLAG_SHOW);
        }

        if ($noImportLoads) {
            $queryBuilder
                ->join('p.subgroup', 's')
                ->join('s.maingroup', 'm')
                ->andWhere('m.id not in (:ignoreIds)')
                ->setParameter('ignoreIds', StockHelper::SKIP_GROUPS);
        }

        $query = $queryBuilder
            ->getQuery()
            ->setHint(Query::HINT_CUSTOM_OUTPUT_WALKER, SqlIndexWalker::class)
            ->setHint(SqlIndexWalker::HINT_INDEX, [
                'pc' => 'use index (artikelen_ean_naam_IDX)',
            ]);
        $result = $query->getArrayResult();

        if ($forStockInfoImport) {
            $rows = $result;
            $result = [];
            foreach ($rows as $row) {
                $result[$row['codeTrimmed']] = $row['id'];
            }
        }

        return $result;
    }

    public function getProductsWithAccessories(): array
    {
        $sql = '
            SELECT
                product_id,
                setArticles.id as accessory_id,
                rootgroep.id as rootgroup
            FROM
                cameranu.artikelen
            INNER JOIN cameranu.subgroepen subgroep ON subgroep.id = artikelen.subgroep_id
            INNER JOIN cameranu.hoofdgroepen hoofdgroep ON hoofdgroep.id = subgroep.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen rootgroep ON rootgroep.id = hoofdgroep.rootgroep_id
            INNER JOIN cameranu.product_accessoryset ON artikelen.id = product_accessoryset.product_id
            INNER JOIN cameranu.accessoryset_position ON product_accessoryset.accessoryset_id = accessoryset_position.tag_id
            INNER JOIN cameranu.tags_products ON accessoryset_position.tags_products_id = tags_products.tags_products_id
            INNER JOIN cameranu.artikelen AS setArticles ON tags_products.itemId = setArticles.id
            INNER JOIN cameranu.subgroepen ac_subgroep ON ac_subgroep.id = setArticles.subgroep_id
            INNER JOIN cameranu.hoofdgroepen ac_hoofdgroep ON ac_hoofdgroep.id = ac_subgroep.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen ac_rootgroep ON ac_rootgroep.id = ac_hoofdgroep.rootgroep_id
            WHERE (setArticles.flags & :productFlags) = :productFlags
            AND (ac_subgroep.flags & :subGroupFlags) = :subGroupFlags
            AND (ac_hoofdgroep.flags & :mainGroupFLags) = :mainGroupFLags
            AND (ac_rootgroep.flags & :rootGroupFlags) = :rootGroupFlags
            AND (artikelen.flags & :productFlags) = :productFlags
            AND (subgroep.flags & :subGroupFlags) = :subGroupFlags
            AND (hoofdgroep.flags & :mainGroupFLags) = :mainGroupFLags
            AND (rootgroep.flags & :rootGroupFlags) = :rootGroupFlags
        ';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindValue('productFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFLags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);

        $result = [];

        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $product) {
            $result[$product['product_id']][$product['accessory_id']] = $product['accessory_id'];
            $result[$product['product_id']]['group'] = $product['rootgroup'];
        }

        $idArray = array_keys($result);

        $sql = '
            WITH filtered_shopcart AS (
                SELECT
                    artikel_id,
                    bestelling_id,
                    prijs
                FROM
                    cameranu.shopcart
                WHERE
                    id >= (
                        SELECT MIN(id)
                        FROM cameranu.shopcart
                        WHERE tstamp >= \'2022-09-14\'
                          AND tstamp < \'2024-10-15\'
                    )
                    AND bestelling_id > 0
            )

             SELECT
                ar_sh.artikel_id AS product_id,
                ass_sh.artikel_id as accessory_id,
                rootgroep.id as rootgroup
            FROM
                cameranu.extra_opties
            INNER JOIN filtered_shopcart AS ar_sh
                ON ar_sh.artikel_id = extra_opties.parent_artikel_id
            INNER JOIN filtered_shopcart AS ass_sh
                ON ass_sh.artikel_id = extra_opties.eo_artikel_id
                AND ass_sh.bestelling_id = ar_sh.bestelling_id
            INNER JOIN cameranu.artikelen as ar on ar_sh.artikel_id = ar.id
            INNER JOIN cameranu.artikelen as ass on ass_sh.artikel_id = ass.id
            INNER JOIN cameranu.subgroepen subgroep ON subgroep.id = ar.subgroep_id
            INNER JOIN cameranu.hoofdgroepen hoofdgroep ON hoofdgroep.id = subgroep.hoofdgroep_id
            INNER JOIN cameranu.rootgroepen rootgroep ON rootgroep.id = hoofdgroep.rootgroep_id
            WHERE
                ass_sh.prijs between 10 and 300
                AND (ass.flags & :productFlags) = :productFlags
                AND (ar.flags & :productFlags) = :productFlags
                AND (subgroep.flags & :subGroupFlags) = :subGroupFlags
                AND (hoofdgroep.flags & :mainGroupFLags) = :mainGroupFLags
                AND (rootgroep.flags & :rootGroupFlags) = :rootGroupFlags
            GROUP BY product_id, accessory_id
            ';

        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindValue('productFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        $query->bindValue('subGroupFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE);
        $query->bindValue('mainGroupFLags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE);
        $query->bindValue('rootGroupFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU);
        $queryResult = $query->executeQuery();

        foreach ($queryResult->fetchAllAssociative() as $product) {
            $result[$product['product_id']][$product['accessory_id']] = $product['accessory_id'];
            $result[$product['product_id']]['group'] = $product['rootgroup'];
        }

        $this->getEntityManager()->clear();

        return $result;
    }

    /**
     * @return Product[]
     */
    public function findByApiFilter(ApiFilterInterface $filter, int $defaultLimit): array
    {
        $limit = $filter->limit ?? $defaultLimit;
        $offset = $filter->page ? $limit * ($filter->page - 1) : 0;

        $queryBuilder = $this->createQueryBuilder('p');

        $queryBuilder
            ->join('p.subgroup', 's')
            ->join('s.maingroup', 'm')
            ->join('m.rootgroup', 'r')
            ->select('p, s, m, r')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        // Standaard alleen producten laten zien uit actieve groepen. Als dit in de toekomst anders moet graag regelen via ProductFilter
        $queryBuilder
            ->andWhere('BIT_AND(s.flags, :cameranuSubgroupFlag) = :cameranuSubgroupFlag')
            ->setParameter('cameranuSubgroupFlag', AbstractGroup::FLAG_ACTIVE | AbstractGroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(m.flags, :cameranuMaingroupFlag) = :cameranuMaingroupFlag')
            ->setParameter('cameranuMaingroupFlag', AbstractGroup::FLAG_ACTIVE | AbstractGroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(r.flags, :cameranuRootgroupFlag) = :cameranuRootgroupFlag')
            ->setParameter('cameranuRootgroupFlag', AbstractGroup::FLAG_ACTIVE | AbstractGroup::FLAG_VISIBLE | Rootgroup::FLAG_CAMERANU);

        if ($filter->isActive !== null) {
            $comparison = $filter->isActive === true ? '=' : '!=';

            $queryBuilder
                ->andWhere('BIT_AND(p.flags, :productFlagVisible) ' . $comparison . ' :productFlagVisible')
                ->setParameter('productFlagVisible', Product::FLAG_VISIBLE);
        }

        if ($filter->isVisible !== null) {
            $comparison = $filter->isVisible === true ? '=' : '!=';

            $queryBuilder
                ->andWhere('BIT_AND(p.flags, :productFlagShow) ' . $comparison . ' :productFlagShow')
                ->setParameter('productFlagShow', Product::FLAG_SHOW);
        }

        if ($filter->subgroup !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->eq('s.id', ':subgroupId')
            );

            $queryBuilder->setParameter('subgroupId', $filter->subgroup);
        }

        if ($filter->maingroup !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->eq('m.id', ':maingroupId')
            );

            $queryBuilder->setParameter('maingroupId', $filter->maingroup);
        }

        if ($filter->rootgroup !== null) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->eq('r.id', ':rootgroupId')
            );

            $queryBuilder->setParameter('rootgroupId', $filter->rootgroup);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function findSecondHandProductByParentAndState(Product $parent, SecondHandState $state): ?product
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->innerJoin('p.secondHandProductState', 'ps')
            ->innerjoin('p.articleGroups', 'ag')
            ->innerjoin('ag.articleGroupType', 'agt')
            ->leftJoin('ag.articleGroupProduct', 'agp')
            ->where('ps.secondHandState = :state')
            ->andWhere('agt.id = :groupTypeId')
            ->andWhere('agp.type = :productType')
            ->andWhere('agp.product = :parent')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'productType' => ArticleGroupProduct::TYPE_MASTER,
                'parent' => $parent,
                'state' => $state
            ])->setMaxResults(1);

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    /**
     * @return Query|Product[]
     */
    public function findMainSecondHandProducts(
        ?PriceListFilter $filter = null,
        ?DateTime $fromDate = null,
        bool $onlyQuery = false,
        ?array $orderBy = null,
        ?array $ids = null,
        ?ProductListFilter $productListFilter = null,
        bool $onlyWithoutPrice = false
    ) {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :productType')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->innerjoin('ag.articleGroupType', 'agt')
            ->where('agt.id = :groupTypeId')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'productType' => ArticleGroupProduct::TYPE_MASTER,
            ]);

        // Brand filter
        if ($filter instanceof PriceListFilter && !empty($filter->getBrand())) {
            $queryBuilder
                ->leftJoin('p.specsArticleSpecification', 'sasBrand')
                ->leftJoin(
                    SpecsFiltersSpecification::class,
                    'sfs',
                    'WITH',
                    'sfs.specId = sasBrand.specSpecification AND sfs.filterId = :filterId'
                )
                ->andWhere('sasBrand.value LIKE :brand')
                ->setParameter('filterId', SpecsFiltersSpecification::BRAND)
                ->setParameter('brand', '%' . $filter->getBrand() . '%');
        }

        // Product type filter
        if ($filter instanceof PriceListFilter && !empty($filter->getProductType())) {
            $queryBuilder
                ->join('p.specsArticleSpecification', 'sasProductType', 'WITH', 'sasProductType.value LIKE :sasProductType')
                ->setParameter('sasProductType', '%' . $filter->getProductType() . '%');
        }

        if ($productListFilter instanceof ProductListFilter) {
            if ($productListFilter->getSortOption() !== null) {
                [$column, $direction] = explode('-', $productListFilter->getSortOption());

                $orderBy = [
                    'column' => $column,
                    'direction' => $direction
                ];
            }

            if ($productListFilter->getBrand() instanceof SpecsArticleSpecification && !empty($productListFilter->getBrand()->getValue())) {
                $queryBuilder
                    ->join('p.specsArticleSpecification', 'sasBrand')
                    ->join(
                        SpecsFiltersSpecification::class,
                        'sfs',
                        'WITH',
                        'sfs.specId = sasBrand.specSpecification AND sfs.filterId = :filterId'
                    )
                    ->andWhere('sasBrand.value = :brand')
                    ->setParameter('filterId', SpecsFiltersSpecification::BRAND)
                    ->setParameter('brand', $productListFilter->getBrand()->getValue());
            }

            if ($productListFilter->getProductGroup()?->getSubgroup() !== null) {
                $queryBuilder
                    ->join('p.subgroup', 'sg')
                    ->andWhere('sg = :subgroup')
                    ->setParameter('subgroup', $productListFilter->getProductGroup()->getSubgroup())
                ;
            } elseif ($productListFilter->getProductGroup()?->getMaingroup() !== null) {
                $queryBuilder
                    ->join('p.subgroup', 'sg')
                    ->join('sg.maingroup', 'mg')
                    ->andWhere('mg = :mainGroup')
                    ->setParameter('mainGroup', $productListFilter->getProductGroup()->getMaingroup());
            }

            if ($productListFilter->getIntakeable() !== null) {
                switch ($productListFilter->getIntakeable()) {
                    case 'intakeable':
                        $queryBuilder
                            ->andWhere('p.intakeableAsSecondHand = 1');
                        break;
                    case 'not-intakeable':
                        $queryBuilder
                            ->andWhere('p.intakeableAsSecondHand = 0');
                        break;
                    case 'archived':
                        $queryBuilder
                            ->andWhere('BIT_AND(p.flags, :product_flag_active) != :product_flag_active')
                            ->setParameter('product_flag_active', Product::FLAG_SHOW | Product::FLAG_VISIBLE);
                        break;
                }
            }
        }

        if ($fromDate instanceof DateTime) {
            $queryBuilder
                ->andWhere('p.timeCreated >= :datetime')
                ->setParameter('datetime', $fromDate);
        }

        if ($ids !== null) {
            $queryBuilder
                ->andWhere('p.id IN (:productIds)')
                ->setParameter('productIds', $ids);
        }

        if ($onlyWithoutPrice) {
            $queryBuilder->leftJoin(SecondHandPrice::class, 'shp', 'WITH', 'shp.product = p AND shp.isBasePrice = :isBasePrice');
            $queryBuilder->andWhere('shp.intakePrice = 0');
            $queryBuilder->setParameter('isBasePrice', true);
        }

        if ($orderBy !== null) {
            if ($orderBy['column'] === 'rank') {
                $queryBuilder->leftJoin('p.secondHandProductStockFactor', 'sf');
                $queryBuilder->orderBy('sf.rank', $orderBy['direction']);
            } else {
                $queryBuilder->orderBy('p.' . $orderBy['column'], $orderBy['direction']);
            }
        }

        $query = $queryBuilder->getQuery();

        if ($onlyQuery) {
            return $query;
        }

        return $query->getResult();
    }

    public function findMainSecondHandProductByName(string $name): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :productType')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->innerjoin('ag.articleGroupType', 'agt')
            ->where('agt.id = :groupTypeId')
            ->andWhere('p.name IN (:names)')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'productType' => ArticleGroupProduct::TYPE_MASTER,
                'names' => [
                    $name,
                    $name . ' - Tweedehands'
                ]
            ]);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findMainSecondhandProductByOriginalProductId(int $productId): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->innerJoin(
                OriginalProduct::class,
                'op',
                'WITH',
                'IDENTITY(op.product) = :productId AND op.child = p.id AND op.type = :originalType'
            )
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = op.child AND agp.type = :productType')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->innerjoin('ag.articleGroupType', 'agt')
            ->where('agt.id = :groupTypeId')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'productType' => ArticleGroupProduct::TYPE_MASTER,
                'originalType' => OriginalProduct::TYPE_OCCASION,
                'productId' => $productId,
            ]);

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @return Product[]
     */
    public function findChildSecondHandProducts(Product $parent, bool $onlyIntakeable = false): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('cp')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = :parent AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->where('p = :parent')
            ->andWhere('agt.id = :groupTypeId')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'parent' => $parent,
            ]);

        if ($onlyIntakeable === true) {
            $queryBuilder
                ->andWhere('cp.intakeableAsSecondHand = 1');
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function findMainSecondHandProductIds(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->innerJoin('ag.articleGroupType', 'agt')
            ->andWhere('agt.id = :groupTypeId')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
            ]);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function getSecondhandExportInfoByParentIds(array $ids): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('cp.id as productId')
            ->addSelect('p.id as parentProductId')
            ->addSelect('cp.name')
            ->addSelect('css.quality as state')
            ->addSelect('mg.name as hoofdgroep')
            ->addSelect('sg.name as subgroep')
            ->addSelect('(
                SELECT sas.value FROM ' . SpecsArticleSpecification::class . ' as sas
                INNER JOIN ' . SpecsFiltersSpecification::class . ' as sfs WITH sas.specId = sfs.specId AND sfs.filterId = :filterId
                WHERE sas.product = cp
            ) as brand')
            ->addSelect('shp.intakePrice')
            ->addSelect('shp.salesPrice')
            ->addSelect('IF(cp.intakeableAsSecondHand = 1, \'1\', \'0\') as intakeable')
            ->addSelect('IFNULL(opp.id, \'\') as originalProductId')
            ->addSelect('IFNULL(opp.name, \'\') as originalProductName')
            ->addSelect('IFNULL(originalPrices.price, \'\') as originalSalesPrice')
            ->addSelect('IFNULL(sf.rank, \'9999\') as rankedNr')
            ->addSelect('IFNULL(sf.stock, \'0\') as stock')
            ->addSelect('IFNULL(sf.averageSalesRate, \'0\') as ASR')
            ->addSelect('IFNULL(sf.daysSalesInInventoryQuarter, \'0\') as DSI_Quarter')
            ->addSelect('IFNULL(sf.daysSalesInInventoryYear, \'0\') as DSI_Year')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->innerJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->innerJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->innerJoin('cp.secondHandProductState', 'cps')
            ->innerJoin('cps.secondHandState', 'css')
            ->join('cp.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->leftJoin(SecondHandPrice::class, 'shp', 'WITH', 'shp.product = cp')
            ->leftJoin(
                OriginalProduct::class,
                'op',
                'WITH',
                'op.child = cp.id AND op.type = :originalType'
            )
            ->leftJoin('op.product', 'opp')
            ->leftJoin('opp.prices', 'originalPrices', Expr\Join::WITH, $queryBuilder->expr()->andX(
                $queryBuilder->expr()->eq('originalPrices.country', ':country'),
                $queryBuilder->expr()->eq('originalPrices.domainId', ':domain'),
                $queryBuilder->expr()->isNotNull('originalPrices.price')
            ))
            ->leftJoin('p.secondHandProductStockFactor', 'sf')
            ->where('p.id In (:parentIds)')
            ->setParameters([
                'parentIds' => $ids,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'originalType' => OriginalProduct::TYPE_OCCASION,
                'domain' => Domain::CAMERANU,
                'country' => 'NL',
                'filterId' => SpecsFiltersSpecification::BRAND,
            ])
            ->addOrderBy('p.id', 'ASC')
            ->addOrderBy('css.quality', 'DESC');

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function getIdsForActiveProducts(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->andWhere('BIT_AND(p.flags, :product_flag_active) = :product_flag_active')
            ->setParameter('product_flag_active', Product::FLAG_SHOW | Product::FLAG_VISIBLE);

        return array_column($queryBuilder->getQuery()->getArrayResult(), 'id');
    }

    public function getForActiveProductsWithPurchasePricesAsArray(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('partial p.{id}')
            ->addSelect('partial pp.{id, price, description, isPreferredPrice, supplierId}')
            ->addSelect('partial s.{id, isPreferred}')
            ->leftJoin('p.purchasePrice', 'pp')
            ->leftJoin('pp.supplier', 's')
            ->andWhere('BIT_AND(p.flags, :product_flag_active) = :product_flag_active')
            ->setParameter('product_flag_active', Product::FLAG_SHOW | Product::FLAG_VISIBLE);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function getActiveProductsAsArray(bool $includeGroupVisibility = false): array
    {

        $queryBuilder = $this->createQueryBuilder('p', 'p.id');
        $queryBuilder
            ->select('p.id, p.name')
            ->andWhere('bit_and(p.flags, :product_flag_active) = :product_flag_active')
            ->setParameter('product_flag_active', Product::FLAG_SHOW | Product::FLAG_VISIBLE);

        if ($includeGroupVisibility === true) {
            $queryBuilder
                ->join('p.subgroup', 'sg')
                ->join('sg.maingroup', 'mg')
                ->join('mg.rootgroup', 'rg')
                ->andWhere('bit_and(rg.flags, :rootgroup_flag_active) = :rootgroup_flag_active')
                ->andWhere('bit_and(sg.flags, :subgroup_flag_active) = :subgroup_flag_active')
                ->andWhere('bit_and(mg.flags, :maingroup_flag_active) = :maingroup_flag_active')
                ->setParameter('rootgroup_flag_active', AbstractGroup::FLAG_ACTIVE | AbstractGroup::FLAG_VISIBLE | Rootgroup::FLAG_CAMERANU)
                ->setParameter('maingroup_flag_active', AbstractGroup::FLAG_ACTIVE | AbstractGroup::FLAG_VISIBLE)
                ->setParameter('subgroup_flag_active', AbstractGroup::FLAG_ACTIVE | AbstractGroup::FLAG_VISIBLE)
            ;
        }

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function getLocationCompartmentCodesForProduct(
        Product $product,
        array $forLocations = [StockLocation::MAGAZIJN_URK]
    ): string {
        $sql = '
            SELECT
                GROUP_CONCAT(
                    CONCAT(
                        locatieVakken.vak,
                        IF(
                            locatieVakkenNodes.subvak != \'\',
                            CONCAT(
                                \'-\',
                                locatieVakkenNodes.subvak
                            ),
                            \'\'
                        )
                    )
                ) as vak
            FROM cameranu.locatieVakkenNodes
            LEFT JOIN cameranu.locatieVakken ON (
                 locatieVakkenNodes.locatie_id = locatieVakken.id AND
                 locatieVakken.stock_location_id IN (:stockLocations)
            )
            WHERE locatieVakkenNodes.artikel_id = :productId';

        $locationIdString = implode($forLocations);
        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        $query->bindValue('stockLocations', $locationIdString);
        $query->bindValue('productId', $product->getId());
        $result = current(($query->executeQuery()->fetchAllAssociative()));

        $locationString = is_array($result) && array_key_exists('vak', $result) ? $result['vak'] : '';
        return $locationString ?? '';
    }

    /**
     * @param ShipmentMethod[] $shippingMethods
     * @throws Exception
     */
    public function findByShippingMethods(array $shippingMethods, int $limit, int $offset): array
    {
        if (empty($shippingMethods)) {
            return [];
        }

        $statement = '
            SELECT p.id,
                   p.flags_verzend AS flagsDelivery
              FROM cameranu.artikelen AS p
             WHERE p.flags_verzend IS NOT NULL
               AND p.flags_verzend != 0
               AND (';

        foreach ($shippingMethods as $index => $shippingMethod) {
            if ($shippingMethod->getFlags() === 0) {
                continue;
            }

            $statement .= $index !== 0 ? ' OR ' : '';
            $statement .= '(p.flags_verzend & :shippingMethodFlag' . $shippingMethod->getId() . ') = :shippingMethodFlag' . $shippingMethod->getId();
        }

        $statement .= ') LIMIT ' . $limit . ' OFFSET ' . $offset;

        $query = $this->getEntityManager()->getConnection()->prepare($statement);

        foreach ($shippingMethods as $shippingMethod) {
            if ($shippingMethod->getFlags() === 0) {
                continue;
            }

            $query->bindValue('shippingMethodFlag' . $shippingMethod->getId(), ($shippingMethod->getFlags() & ~ShipmentMethod::FLAG_ACTIVE));
        }

        return $query->executeQuery()->fetchAllAssociative();
    }

    private function qbForCBSFindBy(): QueryBuilder
    {
        return $this->createQueryBuilder('p')
            ->select('p.id, p.name, p.cbsAvailable')
            ->andWhere('BIT_AND(p.flags, :flag) = :flag')
            ->setParameter('flag', Product::FLAG_SHOW)
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->andWhere('rg.id not in (:excludedGroups)')
            ->setParameter('excludedGroups', [Rootgroup::OCCASIONS_ID, Rootgroup::RENTAL_ID]);
    }

    public function findBySpecsProfile(SpecsProfile $profile): array
    {
        $qb = $this->qbForCBSFindBy()
            ->join('p.specsProfiles', 'profiles')
            ->andWhere('profiles.id = :prof')
            ->setParameter('prof', $profile);
        return $qb->getQuery()->getArrayResult();
    }

    public function findBySpecValue(SpecsProfileSpecifications $specification, string $value): array
    {
        $qb = $this->qbForCBSFindBy()
            ->join('p.specsArticleSpecification', 'sas')
            ->andWhere('sas.specId = :specId')
            ->andWhere('sas.value = :value')
            ->setParameter('specId', $specification->getSpecId())
            ->setParameter('value', $value);
        return $qb->getQuery()->getArrayResult();
    }

    public function setCbsAvailable(array $ids, bool $available): int
    {
        $query = $this->_em->createQueryBuilder()
            ->update(Product::class, 'p')
            ->where('p.id IN (:ids)')
            ->set('p.cbsAvailable', ':available')
            ->setParameter('ids', $ids)
            ->setParameter('available', $available)
            ->getQuery();
        return $query->execute();
    }

    public function findVisibleOccasionAndDemoProducts(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->where('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', AbstractGroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', AbstractGroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->andWhere('rg.id IN (:occasionAndDemo)')
            ->setParameter('occasionAndDemo', Rootgroup::OCCASION_AND_DEMO_IDS);

        return $queryBuilder->getQuery()->getResult();
    }

    public function getPurchasePriceCalculationData(array $productIds = []): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('pp.id as purchasePriceId')
            ->addSelect('pp.price')
            ->addSelect('pp.isPreferredPrice as isPreferred')
            ->addSelect('s.id as supplierId')
            ->addSelect('supplierGroup.usesCBS as usesCbs')
            ->addSelect('IFNULL(tp.price, 0) as elpPrice')
            ->addSelect('(
                SELECT
                    IFNULL (
                        SUM(
                            CASE
                                WHEN valueType.id = :valueTypeEuroId THEN claimAndFee.value
                                WHEN valueType.id = :valueTypePercentageId THEN ((pp.price / 100) * claimAndFee.value)
                                ELSE 0
                            END
                        ),
                        0
                    )
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acaf
                JOIN acaf.claimsAndFees as claimAndFee
                JOIN claimAndFee.valueType as valueType
                JOIN WebdsignGlobalBundle:Product as product WITH acaf.product = product
                JOIN claimAndFee.kind as kind
                LEFT JOIN claimAndFee.suppliers as sell_in_supplier
                LEFT JOIN claimAndFee.supplierGroups as sell_in_supplier_group
                WHERE product = p
                AND (NOW() BETWEEN claimAndFee.dateFrom AND claimAndFee.dateTill)
                AND kind.id = :sellInKindId
                AND (s = sell_in_supplier OR s.group = sell_in_supplier_group)
            ) as sellIn')
            ->addSelect('(
                SELECT
                    IFNULL (
                        SUM(
                            CASE
                                WHEN valueTypeOut.id = :valueTypeEuroId THEN claimAndFeeOut.value
                                WHEN valueTypeOut.id = :valueTypePercentageId THEN ((prices.price / 100) * claimAndFeeOut.value)
                                ELSE 0
                            END
                        ),
                        0
                    )
                FROM
                    WebdsignGlobalBundle:ProductClaimsAndFees as acafOut
                JOIN acafOut.claimsAndFees as claimAndFeeOut
                JOIN claimAndFeeOut.valueType as valueTypeOut
                JOIN WebdsignGlobalBundle:Product as productOut WITH acafOut.product = productOut
                JOIN claimAndFeeOut.kind as kindOut
                LEFT JOIN claimAndFeeOut.suppliers as sell_out_supplier
                LEFT JOIN claimAndFeeOut.supplierGroups as sell_out_supplier_group
                WHERE productOut = p
                AND (NOW() BETWEEN claimAndFeeOut.dateFrom AND claimAndFeeOut.dateTill)
                AND kindOut.id = :sellOutKindId
                AND (s = sell_out_supplier OR s.group = sell_out_supplier_group)
            ) as sellOut')
            ->join('p.purchasePrice', 'pp')
            ->join('pp.supplier', 's')
            ->join('s.group', 'supplierGroup')
            ->leftJoin('p.prices', 'prices', Expr\Join::WITH, $queryBuilder->expr()->andX(
                $queryBuilder->expr()->eq('prices.country', ':country'),
                $queryBuilder->expr()->eq('prices.domainId', ':domain'),
                $queryBuilder->expr()->isNotNull('prices.price')
            ))
            ->leftJoin('p.temporaryPrices', 'tp', Expr\Join::WITH, $queryBuilder->expr()->andX(
                $queryBuilder->expr()->eq('tp.type', ':typeELP'),
                $queryBuilder->expr()->lte('tp.from', 'NOW()'),
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->gte('tp.to', 'NOW()'),
                    $queryBuilder->expr()->eq('tp.to', ':zeroDate')
                )
            ))
            ->addSelect('sg.name as subgroup_name')
            ->addSelect('mg.name as maingroup_name')
            ->addSelect('rg.name as rootgroup_name')
            ->addSelect('cat.name as cat_name')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->join('p.category', 'cat')
            ->where('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', AbstractGroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', AbstractGroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->setParameter('domain', Domain::CAMERANU)
            ->setParameter('country', 'NL')
            ->setParameter('sellInKindId', ClaimsAndFeesKind::SELL_IN)
            ->setParameter('sellOutKindId', ClaimsAndFeesKind::SELL_OUT)
            ->setParameter('valueTypeEuroId', ClaimsAndFeesValueType::EURO)
            ->setParameter('valueTypePercentageId', ClaimsAndFeesValueType::PERCENTAGE)
            ->setParameter('typeELP', 'ELP')
            ->setParameter('zeroDate', '0000-00-00 00:00:00');

        if ($productIds !== []) {
            $queryBuilder
                ->andWhere('p.id in (:ids)')
                ->setParameter('ids', $productIds);
        }

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function hasToBeDividedByTeamSales(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->where('BIT_AND(p.flags2, :flags) = :flags')
            ->setParameter('flags', Product::FLAG2_DIVIDE_BY_SALES_TEAM);

        return $queryBuilder->getQuery()->getResult();
    }

    public function getContent(?int $productId = null): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->addSelect('p.id')
            ->addSelect('p.name')
            ->addSelect('pc.content')
            ->leftJoin('p.content', 'pc')
            ->andWhere('pc.tab = 2')
            ->andWhere('pc.position = 10')
            ->andWhere('pc.startDate <= :now')
            ->andWhere('pc.endDate >= :now OR pc.endDate = :zeroDate')
            ->setParameter('now', new DateTime())
            ->setParameter('zeroDate', new DateTime('0000-00-00'));

        if ($productId !== null) {
            $queryBuilder
                ->andWhere('p.id = :id')
                ->setParameter('id', $productId);
        }

        $contents = [];
        $results = $queryBuilder->getQuery()->getArrayResult();
        foreach ($results as $result) {
            $content = str_replace('::artikelnaam::', $result['name'], $result['content']);
            $contents[$result['id']][] = Utility::replaceUploadUrlInContent($content);
        }

        return $contents;
    }

    public function getInfoForDiscountCodePreview(array $productIds): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('p.name')
            ->addSelect('image.image')
            ->join('p.images', 'image', Expr\Join::WITH, $queryBuilder->expr()->andX(
                $queryBuilder->expr()->eq('BIT_AND(image.flags, :imageFlags)', ':imageFlags')
            ))
            ->where('p.id in (:ids)')
            ->setParameter('ids', $productIds)
            ->setParameter('imageFlags', 1);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * Return products based on filtering in PowerBI. Availability is saved to later calculate percentage.
     */
    public function findForStockAvailability(): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->andWhere('rg.id NOT IN (:skipRootgroupIds)')
            ->andWhere('mg.id NOT IN (:skipMaingroupIds)')
            ->andWhere('sg.id NOT IN (:skipSubgroupIds)')
            ->setParameters([
                'pFlags' => Product::FLAG_SHOW,
                'skipRootgroupIds' => [
                    // Filtered in PowerBI
                    Rootgroup::WORKSHOP_ID,
                    Rootgroup::RENTAL_ID,
                    Rootgroup::COMBODEALS_ID,
                    Rootgroup::COMBODEALS_NEW_ID,

                    // Filtered in cacheStockCalculation.php legacy script; means there will be no articles_stock_per_location rows
                    Rootgroup::WARRANTY_EXTENSIONS_ID,
                    Rootgroup::OPT_ACCESSORY_SETS_ID,
                    Rootgroup::FIND_YOUR_ACCESSORIES,
                    Rootgroup::LAST_MINUTES_ID,
                ],
                'skipMaingroupIds' => [
                    Maingroup::ADMINISTRATION_ID,
                    Maingroup::GIFT_CARD_ID,
                ],
                'skipSubgroupIds' => [
                    Subgroup::COMPANY_CLOTHES_ID,
                    Subgroup::PASSPORT_PHOTO_ID,
                    Subgroup::PLASTIC_BAG_ID,
                    Subgroup::PRINT_SHOP_ID,
                ],
            ]);
        ;

        $result = $queryBuilder->getQuery()->getArrayResult();
        $ids = array_column($result, 'id');
        return $ids;
    }

    public function findforSubgroupIds(array $subgroupIds): ArrayCollection
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->join('p.subgroup', 'sg')
            ->where('sg.id IN (:subgroupIds)')
            ->setParameter('subgroupIds', $subgroupIds);

        return new ArrayCollection($queryBuilder->getQuery()->getResult());
    }

    public function findForPickList(PickList $pickList): array
    {
        $queryBuilder = $this->createQueryBuilder('p', 'p.id');
        $queryBuilder
            ->select('p.id, p.name')
            ->join(PickListEntry::class, 'ple', Expr\Join::WITH, 'ple.product = p')
            ->join('ple.pickListLink', 'pll')
            ->andWhere('pll.pickList = :pickList')
            ->setParameter('pickList', $pickList)
        ;

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * Find total turnover by secondHandParentIds this is the sum of the childStock sold in the previous 12 months
     */
    public function findSecondhandTurnoverForParentIds(array $parentProductIds): array
    {
        $zeroOrder = (new OrderInfo())->setId(0);
        $fromDate = (new DateTime())->modify('-1 year');

        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('SUM(s.salesPrice) as turnover')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->leftJoin(
                Stock::class,
                's',
                Join::WITH,
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('s.product', 'cp'),
                    $queryBuilder->expr()->neq('s.order', ':zeroOrder'),
                    $queryBuilder->expr()->isNull('s.dateOut')
                )
            )->leftJoin(
                OrderInfo::class,
                'so',
                Join::WITH,
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('s.order', 'so'),
                    $queryBuilder->expr()->gte('so.dateInvoice', ':fromDate')
                )
            )
            ->where('p.id IN (:parentIds)')
            ->andWhere('agt.id = :groupTypeId')
            ->groupBy('p.id')
            ->orderBy('turnover', 'DESC')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'parentIds' => $parentProductIds,
                'zeroOrder' => $zeroOrder,
                'fromDate' => $fromDate,
            ]);

        $result = $queryBuilder->getQuery()->getArrayResult();
        return array_combine(
            array_column($result, 'id'),
            array_column($result, 'turnover')
        );
    }

    public function findSecondHandPricesforParentIdsByQuality(array $parentProductIds, int $quality): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('cpp.salesPrice')
            ->addSelect('cpp.intakePrice')
            ->addSelect('((cpp.salesPrice - cpp.intakePrice) / cpp.salesPrice) * 100 as margin')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->leftJoin('cp.secondHandProductState', 'cpsps')
            ->leftJoin('cpsps.secondHandState', 'cpss')
            ->leftJoin('cp.secondHandPrice', 'cpp')
            ->where('p.id IN (:parentIds)')
            ->andWhere('agt.id = :groupTypeId')
            ->andWhere('cpss.quality = :quality')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'parentIds' => $parentProductIds,
                'quality' => $quality,
            ]);

        $result = $queryBuilder->getQuery()->getArrayResult();
        return array_column(
            array_map(function ($row) {
                return [
                    'id' => $row['id'],
                    'data' => [
                        'salesPrice' => $row['salesPrice'],
                        'intakePrice' => $row['intakePrice'],
                        'margin' => round($row['margin'], 2),
                    ],
                ];
            }, $result),
            'data',
            'id'
        );
    }

    /**
     * Find total inStock by secondHandParentIds this is the sum of the childStocks
     */
    public function findCurrentSecondhandStockForParentIds(array $parentProductIds): array
    {
        $zeroOrder = (new OrderInfo())->setId(0);

        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('COUNT(s.id) as currentStock')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->leftJoin(
                Stock::class,
                's',
                Join::WITH,
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('s.product', 'cp'),
                    $queryBuilder->expr()->eq('s.order', ':zeroOrder'),
                    $queryBuilder->expr()->isNull('s.dateOut')
                )
            )
            ->where('p.id IN (:parentIds)')
            ->andWhere('agt.id = :groupTypeId')
            ->groupBy('p.id')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'parentIds' => $parentProductIds,
                'zeroOrder' => $zeroOrder,
            ]);

        $result = $queryBuilder->getQuery()->getArrayResult();
        return array_combine(
            array_column($result, 'id'),
            array_column($result, 'currentStock')
        );
    }

    public function findLastDateInForSecondhandParentIds(array $productIds): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('MAX(s.dateIn) as dateIn')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->leftJoin(
                Stock::class,
                's',
                Join::WITH,
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('s.product', 'cp'),
                    $queryBuilder->expr()->isNull('s.dateOut')
                )
            )
            ->where('p.id IN (:parentIds)')
            ->andWhere('agt.id = :groupTypeId')
            ->orderBy('s.dateIn', 'DESC')
            ->groupBy('p.id')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'parentIds' => $productIds,
            ]);

        $result = $queryBuilder->getQuery()->getArrayResult();
        return array_combine(
            array_column($result, 'id'),
            array_column($result, 'dateIn')
        );
    }

    public function findLastDateOutForSecondhandParentIds(array $productIds): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('MAX(o.dateInvoice) as dateOut')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->leftJoin(
                Stock::class,
                's',
                Join::WITH,
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('s.product', 'cp'),
                    $queryBuilder->expr()->isNull('s.dateOut')
                )
            )
            ->innerJoin('s.order', 'o')
            ->where('p.id IN (:parentIds)')
            ->andWhere('agt.id = :groupTypeId')
            ->orderBy('s.dateIn', 'DESC')
            ->groupBy('p.id')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'parentIds' => $productIds,
            ]);

        $result = $queryBuilder->getQuery()->getArrayResult();
        return array_combine(
            array_column($result, 'id'),
            array_column($result, 'dateOut')
        );
    }

    public function getAverageDaysToSellSecondhandProducts(array $productIds): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id')
            ->addSelect('(
                SELECT AVG(DATE_DIFF(o.dateInvoice, s.dateIn))
                FROM
                   WebdsignGlobalBundle:Stock as s
                LEFT JOIN s.order o
                WHERE s.productId IN (
                    SELECT cp.id
                    FROM WebdsignGlobalBundle:Product mp
                    INNER JOIN WebdsignGlobalBundle:ArticleGroupProduct as agp WITH (agp.productId = mp.id AND agp.type = :master)
                    INNER JOIN WebdsignGlobalBundle:ArticleGroup as ag WITH (ag = agp.articleGroup)
                    LEFT JOIN WebdsignGlobalBundle:ArticleGroupProduct as agpc WITH (ag = agpc.articleGroup AND agpc.type = :slave)
                    LEFT JOIN WebdsignGlobalBundle:Product as cp WITH (agpc.product = cp)
                    LEFT JOIN WebdsignGlobalBundle:ArticleGroupType as agt WITH (ag.articleGroupType = agt AND agt.id = :groupTypeId)
                    WHERE mp.id = p.id
                ) AND s.dateOut IS NULL
            ) as avgDaysToSell')
            ->where('p.id IN (:parentIds)')
            ->setParameters([
                'parentIds' => $productIds,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
            ]);

        $result = $queryBuilder->getQuery()->getArrayResult();
        return array_combine(
            array_column($result, 'id'),
            array_column($result, 'avgDaysToSell')
        );
    }

    /**
     * DSI (Days Sales in Inventory)
     */
    public function getCalculatedDSIForSecondHandProducts(array $productIds, string $period = '1 year'): array
    {
        $endDate = new DateTime();
        $startDate = (new DateTime())->modify('-' . $period);
        $days = $startDate->diff($endDate)->days;

        $zeroOrder = (new OrderInfo())->setId(0);

        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id as masterProductId')
            // Inventory at start of period
            ->addSelect('SUM(
                CASE
                    WHEN s.order = :zeroOrder AND s.dateIn < :startDate THEN s.purchasePrice
                    WHEN s.order != :zeroOrder AND s.dateIn < :startDate AND o.dateInvoice > :startDate THEN s.purchasePrice
                    ELSE 0
                END
            ) AS startInventory')
            ->addSelect('SUM(
                CASE
                    WHEN s.dateIn BETWEEN :startDate AND :endDate THEN s.purchasePrice
                    ELSE 0
                END
            ) AS totalPurchases')
            ->addSelect('SUM(
                CASE
                    WHEN s.order = :zeroOrder THEN s.purchasePrice
                    ELSE 0
                END
            ) AS currentInventory')
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = p AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->leftJoin('cp.stock', 's')
            ->leftJoin('s.order', 'o')
            ->where('p.id IN (:parentIds)')
            ->andWhere('s.dateOut IS NULL')
            ->andWhere('agt.id = :groupTypeId')
            ->groupBy('p.id')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'parentIds' => $productIds,
                'startDate' => $startDate->format('Y-m-d'),
                'endDate' => $endDate->format('Y-m-d'),
                'zeroOrder' => $zeroOrder,
            ]);

        $inventoryValues = $queryBuilder->getQuery()->getArrayResult();

        $results = [];
        foreach ($inventoryValues as $result) {
            $startInventory = $result['startInventory'] ?? 0;
            $totalPurchases = $result['totalPurchases'] ?? 0;
            $currentInventory = $result['currentInventory'] ?? 0;

            // COGS (Cost of goods sold)
            $cogs = $startInventory + $totalPurchases - $currentInventory;

            // AI (Average Inventory)
            $ai = ($startInventory + $currentInventory) / 2;

            // DSI (Days Sales in Inventory)
            $dsi = $cogs > 0 ? round(($ai / $cogs) * $days, 2) : 0;
            $results[$result['masterProductId']] = $dsi;
        }

        return $results;
    }

    public function findSecondhandParentIdsByChildIds(array $skus): array
    {
        $qb = $this->createQueryBuilder('p'); // p = child product

        $qb
            ->select('DISTINCT IDENTITY(agpMaster.product) AS parentId')
            ->innerJoin(ArticleGroupProduct::class, 'agpChild', 'WITH', 'agpChild.product = p AND agpChild.type = :slave')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agpChild.articleGroup = ag')
            ->innerJoin(ArticleGroupProduct::class, 'agpMaster', 'WITH', 'agpMaster.articleGroup = ag AND agpMaster.type = :master')
            ->innerJoin('ag.articleGroupType', 'agt')
            ->where('p.id IN (:skus)')
            ->andWhere('agt.id = :groupTypeId')
            ->setParameters([
                'skus' => $skus,
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'master' => ArticleGroupProduct::TYPE_MASTER,
            ]);

        return array_column($qb->getQuery()->getArrayResult(), 'parentId');
    }

    public function findGroupedSecondHandChildIdsByParentIds(array $parentIds): array
    {
        $qb = $this->createQueryBuilder('p'); // p = child

        $qb
            ->select('IDENTITY(agpMaster.product) AS parentId, p.id, ss.quality AS quality')
            ->innerJoin(ArticleGroupProduct::class, 'agpChild', 'WITH', 'agpChild.product = p AND agpChild.type = :slave')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agpChild.articleGroup = ag')
            ->innerJoin(ArticleGroupProduct::class, 'agpMaster', 'WITH', 'agpMaster.articleGroup = ag AND agpMaster.type = :master')
            ->innerJoin('ag.articleGroupType', 'agt')
            ->leftJoin('p.secondHandProductState', 'ps')
            ->leftJoin('ps.secondHandState', 'ss')
            ->where('agpMaster.product IN (:parentIds)')
            ->andWhere('agt.id = :groupTypeId')
            ->setParameters([
                'parentIds' => $parentIds,
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'master' => ArticleGroupProduct::TYPE_MASTER,
            ]);

        $results = $qb->getQuery()->getArrayResult();

        $grouped = [];
        foreach ($results as $row) {
            $parentId = (int)$row['parentId'];
            $sku = $row['id'];
            $quality = (int)$row['quality'];
            $grouped[$parentId][$sku] = $quality;
        }

        return $grouped;
    }

    public function findDiscrepantSecondhandProducts(
        ?array $orderBy = [],
        ?DiscrepancyListFilter $filter = null,
        ?int $page = 1,
        ?int $limit = 12,
    ) {
        $selects = [];
        $joins = [];
        $parameters = [];
        $conditions = [];
        $discrepancyQueries = [];

        $selects[] = 'p.id AS productId';
        $selects[] = 'REPLACE(p.naam, \' - Tweedehands\', \'\') AS name';
        $selects[] = 'IFNULL(sf.rank, 99999) AS rank';
        $selects[] = 'IFNULL(sf.stock, 0) AS stock';
        $selects[] = 'IFNULL(sf.average_sales_rate, 0) AS asr';
        $selects[] = 'IFNULL(sf.days_sales_in_inventory_quarter, 0) AS dsiq';
        $selects[] = 'IFNULL(sf.days_sales_in_inventory_year, 0) AS dsiy';
        $selects[] = 'IFNULL(sf.conversion_rate, 0) AS cr';
        $selects[] = 'sf.last_out AS lastOut';
        $selects[] = 'sf.last_in AS lastIn';
        $selects[] = 'IFNULL(sf.margin_percentage, 0) AS marginPercentage';
        $selects[] = 'IFNULL(sf.sales_price, 0) AS salesPrice';
        $selects[] = 'IFNULL(sf.intake_price, 0) AS intakePrice';
        $selects[] = 'dps.snoozed_until';

        $from = ' FROM cameranu.artikelen p ';

        $joins[] = 'INNER JOIN cameranu.artikelgroepen_artikelen aga ON aga.artikelId = p.id AND aga.type = :productType';
        $joins[] = 'INNER JOIN cameranu.artikelgroepen ag ON aga.artikelgroepId = ag.id AND ag.article_group_type_id = :groupTypeId';
        $joins[] = 'LEFT JOIN cameranu.second_hand_product_stock_factors sf ON p.id = sf.product_id';
        $joins[] = 'LEFT JOIN cameranu.snoozed_discrepant_second_hand_products dps ON p.id = dps.product_id';

        $conditions[] = '(dps.snoozed_until < NOW() OR snoozed_until IS NULL)';

        $parameters['groupTypeId'] = ArticleGroupType::GROUP_TYPE_SECOND_HAND;
        $parameters['productType'] = ArticleGroupProduct::TYPE_MASTER;

        $subQueries = [];
        foreach (SecondHandPerformanceTarget::VALID_KEYS as $targetKey) {
            $subquery = "(
                SELECT
                  CASE
                    WHEN product_targets.value IS NOT NULL THEN product_targets.value
                    ELSE main_group_targets.value
                  END AS $targetKey
                FROM cameranu.artikelen a
                LEFT JOIN (
                  SELECT pt.product_id, pt.value
                  FROM cameranu.second_hand_product_performance_targets pt
                  JOIN cameranu.second_hand_performance_targets ptk ON pt.performance_target_id = ptk.id
                  WHERE ptk.key = '$targetKey'
                ) AS product_targets ON product_targets.product_id = a.id
                LEFT JOIN cameranu.subgroepen sg ON a.subgroep_id = sg.id
                LEFT JOIN cameranu.hoofdgroepen hg ON sg.hoofdgroep_id = hg.id
                LEFT JOIN (
                  SELECT mgt.main_group_id, mgt.value
                  FROM cameranu.second_hand_main_group_performance_targets mgt
                  JOIN cameranu.second_hand_performance_targets mgtk ON mgt.performance_target_id = mgtk.id
                  WHERE mgtk.key = '$targetKey'
                ) AS main_group_targets ON main_group_targets.main_group_id = hg.id
                WHERE p.id = a.id
            )";

            $subQueries[$targetKey] = $subquery . ' as ' . $targetKey;
            $discrepancyQueries[$targetKey] = $subquery;
        }

        foreach (Competitor::VALID_STOCK_FACTOR_KEYS as $competitorKey) {
            $intakeStmt = 'IFNULL(sf.intake_price_' . $competitorKey . ', 0)';
            $salesStmt = 'IFNULL(sf.sales_price_' . $competitorKey . ', 0)';

            $selects[] = $intakeStmt . ' AS ' . 'intake_price_'. $competitorKey;
            $selects[] = $salesStmt. ' AS ' . 'sales_price_'. $competitorKey;

            $discrepancyQueries['intake_price_'. $competitorKey] = $intakeStmt;
            $discrepancyQueries['sales_price_'. $competitorKey] = $salesStmt;
        }

        if ($filter->getSortOption() !== null) {
            [$column, $direction] = explode('-', $filter->getSortOption());

            $orderBy = [
                'column' => $column,
                'direction' => $direction
            ];
        }

        if ($filter->getBrand() instanceof SpecsArticleSpecification && !empty($filter->getBrand()->getValue())) {
            $joins[] = 'INNER JOIN cameranu.specs_article_specifications sas ON sas.articleId = p.id AND sas.value = :brand';
            $joins[] = 'INNER JOIN cameranu.specs_filters_specifications sfs ON sfs.specId = sas.specId AND sfs.filterId = :filterId';
            $parameters['brand'] = $filter->getBrand()->getValue();
            $parameters['filterId'] = SpecsFiltersSpecification::BRAND;
        }

        if ($filter->getProductGroup()?->getSubgroup() !== null) {
            $joins[] = 'INNER JOIN cameranu.subgroepen sg ON sg.id = p.subgroep_id AND sg.id = :subgroupId';
            $parameters['subgroupId'] = $filter->getProductGroup()->getSubgroup()->getId();
        } elseif ($filter->getProductGroup()?->getMaingroup() !== null) {
            $joins[] = 'INNER JOIN cameranu.subgroepen sg ON sg.id = p.subgroep_id';
            $joins[] = 'INNER JOIN cameranu.hoofdgroepen mg ON mg.id = sg.hoofdgroep_id AND mg.id = :maingroupId';
            $parameters['maingroupId'] = $filter->getProductGroup()->getMaingroup()->getId();
        }

        if ($filter->getStock() !== null) {
            switch ($filter->getStock()) {
                case 0:
                    $conditions[] = 'IFNULL(sf.stock, 0) = 0';
                    break;
                case 1:
                    $conditions[] = 'IFNULL(sf.stock, 0) > 0';
                    break;
            }
        }

        $discrepancyCondition = [];
        foreach (SecondHandPerformanceTarget::VALID_KEYS as $targetKey) {
            $targetCondition = match ($targetKey) {
                SecondHandPerformanceTarget::KEY_STOCK_AMOUNT => 'IFNULL(sf.stock, 0) > ' . $discrepancyQueries[$targetKey],
                SecondHandPerformanceTarget::KEY_ASR => 'IFNULL(sf.average_sales_rate, 0) > ' . $discrepancyQueries[$targetKey],
//                SecondHandPerformanceTarget::KEY_DSI_Q => 'IFNULL(sf.days_sales_in_inventory_quarter, 0) > ' . $discrepancyQueries[$targetKey],//Uitgezet CAM-6871
//                SecondHandPerformanceTarget::KEY_DSI_Y => 'IFNULL(sf.days_sales_in_inventory_year, 0) > ' . $discrepancyQueries[$targetKey],//Uitgezet CAM-6871
//                SecondHandPerformanceTarget::KEY_CONVERSION_RATE => 'IFNULL(sf.conversion_rate, 0) < ' . $discrepancyQueries[$targetKey],//TODO tijdelijk uit
                SecondHandPerformanceTarget::KEY_MINIMAL_MARGIN => 'IFNULL(sf.margin_percentage, 0) < ' . $discrepancyQueries[$targetKey],
//                SecondHandPerformanceTarget::KEY_LAST_DATE_IN => 'DATE_DIFF(NOW(), IFNULL(sf.last_in, NOW())) > ' . $discrepancyQueries[$targetKey],//Uitgezet CAM-6871
//                SecondHandPerformanceTarget::KEY_LAST_DATE_OUT => 'DATE_DIFF(NOW(), IFNULL(sf.last_out, NOW())) > ' . $discrepancyQueries[$targetKey],//Uitgezet CAM-6871
                default => null,
            };

            if ($targetCondition !== null) {
                $discrepancyConditions[] = $targetCondition;
            }
        }

        foreach (Competitor::VALID_STOCK_FACTOR_KEYS as $competitorKey) {
            $intakeCondition = '(' . $discrepancyQueries['intake_price_'. $competitorKey] . ' != 0 AND IFNULL(sf.intake_price, 0) < '. $discrepancyQueries['intake_price_'. $competitorKey] . ')';

            $discrepancyConditions[] = $intakeCondition;
        }

        $sql = $from . implode(' ' . PHP_EOL, $joins) . ' WHERE 1=1 ';

        if (!empty($conditions)) {
            $sql .= ' AND ' . implode(' AND ', $conditions);
        }

        if (!empty($discrepancyConditions)) {
            $sql .= ' AND (' . implode(' OR ', $discrepancyConditions) . ')';
        }

        $countSql = 'SELECT COUNT(DISTINCT p.id) as count ' . $sql;
        $countQuery = $this->getEntityManager()->getConnection()->prepare($countSql);
        foreach ($parameters as $key => $value) {
            $countQuery->bindValue($key, $value);
        }

        $totalCount = current($countQuery->executeQuery()->fetchFirstColumn());

        $sql = 'SELECT ' . implode(',' . PHP_EOL, $selects) . ',' . implode(',', $subQueries) . $sql;

        if ($orderBy !== null) {
            $sql .= ' ORDER BY ' . $orderBy['column'] . ' ' . $orderBy['direction'];
        }

        $sql .= ' LIMIT ' . $limit . ' OFFSET ' . ($page - 1) * $limit;
        $query = $this->getEntityManager()->getConnection()->prepare($sql);
        foreach ($parameters as $key => $value) {
            $query->bindValue($key, $value);
        }

        $results = $query->executeQuery()->fetchAllAssociative();

        return [
            'results' => $results,
            'totalCount' => $totalCount,
        ];
    }

    public function findLatestSalespriceForProductIdsAndCompetitorQuery(
        array $products,
        Competitor $competitor,
        int $quality,
        ?DateTimeImmutable $maxAge = null,
    ): array {
        $qb = $this->createQueryBuilder('product');
        $qb
            ->select([
                'product.id as product_id',
                'psp.price AS price',
            ])
            ->innerJoin(ArticleGroupProduct::class, 'agp', 'WITH', 'agp.product = product AND agp.type = :master')
            ->innerJoin(ArticleGroup::class, 'ag', 'WITH', 'agp.articleGroup = ag')
            ->leftJoin(ArticleGroupProduct::class, 'agpc', 'WITH', 'agpc.articleGroup = ag AND agpc.type = :slave')
            ->leftJoin(Product::class, 'cp', 'WITH', 'cp = agpc.product')
            ->leftJoin('ag.articleGroupType', 'agt')
            ->leftJoin('cp.secondHandProductState', 'cpsps')
            ->leftJoin('cpsps.secondHandState', 'cpss')
            ->leftJoin(ProductSellPrice::class, 'psp', 'WITH', 'cp = psp.product')
            ->innerJoin(ProductStock::class, 'sp', 'WITH', 'sp.competitorMonitorProductId = psp.competitorMonitorProductId')
            ->where('product.id IN (:products)')
            ->andWhere('agt.id = :groupTypeId')
            ->andWhere('cpss.quality = :quality')
            ->andWhere('psp.competitor = :competitor')
            ->andWhere('sp.amount > 0')
            ->orderBy('product.id')
            ->addOrderBy('psp.indexedAt', 'DESC')
            ->addOrderBy('psp.price', 'DESC')
            ->setParameters([
                'groupTypeId' => ArticleGroupType::GROUP_TYPE_SECOND_HAND,
                'master' => ArticleGroupProduct::TYPE_MASTER,
                'slave' => ArticleGroupProduct::TYPE_SLAVE,
                'quality' => $quality,
            ]);

        if ($maxAge) {
            $qb->andWhere('psp.indexedAt >= :maxAge');
            $qb->setParameter('maxAge', $maxAge);
        }

        $qb->setParameter('products', $products);
        $qb->setParameter('competitor', $competitor->getKey());

        $results = $qb->getQuery()->getArrayResult();

        $latest = [];
        foreach ($results as $row) {
            $productId = $row['product_id'];
            if (!isset($latest[$productId])) {
                $latest[$productId] = $row['price'];
            }
        }

        return $latest;
    }

    public function productEverHadStock(Product $product, Datetime $fromDate = null): bool
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('COUNT(s)')
            ->join('p.stock', 's')
            ->where('p = :product')
            ->setParameter('product', $product);

        if ($fromDate !== null) {
            $queryBuilder
                ->andWhere('p.timeCreated >= :fromDate')
                ->setParameter('fromDate', $fromDate);
        }

        return $queryBuilder->getQuery()->getSingleScalarResult() > 0;
    }

    public function findPriceExportData(?string $country = 'NL'): array
    {
        $qb = $this->createQueryBuilder('p');
        $qb
            ->select('p.id')
            ->addSelect('p.name')
            ->addSelect('p.descriptionShort')
            ->addSelect('p.inStock')
            ->addSelect('sg.name as subgroup')
            ->addSelect('prices.price')
            ->addSelect('pc.code as ean')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->leftJoin('p.prices', 'prices', Expr\Join::WITH, $qb->expr()->andX(
                $qb->expr()->eq('prices.country', ':country'),
                $qb->expr()->eq('prices.domainId', ':domain'),
                $qb->expr()->isNotNull('prices.price'),
            ))
            ->leftJoin('p.productCodes', 'pc', Expr\Join::WITH, $qb->expr()->andX(
                $qb->expr()->eq('pc.type', ':type'),
                $qb->expr()->eq('BIT_AND(pc.flags, :productCodeFlags)', ':productCodeFlags')
            ))
            ->andWhere('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->setParameter('sgFlags', AbstractGroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->setParameter('mgFlags', AbstractGroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->setParameter('rgFlags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->setParameter('productCodeFlags', ProductCode::FLAG_MAIN_CODE)
            ->setParameter('type', ProductCode::TYPE_EAN)
            ->setParameter('domain', Domain::CAMERANU)
            ->setParameter('country', $country)
            ->orderBy('p.id');

        if (strtolower($country) !== 'nl') {
            $qb
                ->addSelect('pricesNL.price as priceNL')
                ->leftJoin('p.prices', 'pricesNL', Expr\Join::WITH, $qb->expr()->andX(
                    $qb->expr()->eq('pricesNL.country', '\'NL\''),
                    $qb->expr()->eq('pricesNL.domainId', ':domain'),
                    $qb->expr()->isNotNull('pricesNL.price'),
            ));
        }

        $results = $qb->getQuery()->getArrayResult();

        return $results;
    }

    public function findMatchingLenses(Product $product, ?int $rootGroupId = null): array
    {
        $queryBuilder = $this->createQueryBuilder('p');

        $queryBuilder->select('p.id');

        $lensMountSpecs = current($product->getSpecs(specIds: SpecsSpecification::LENS_MOUNT_SPEC_IDS));
        $lensMountValue = is_array($lensMountSpecs) ? current($lensMountSpecs) : null;
        if ($lensMountValue === null || empty($lensMountValue['value'])) {
            return []; // geen lensMount ingesteld dus niks past.
        }

        //Voor videocamera's pakken we alleen passende Cinema objectieven
        if ($product->getSpecProfileId() === SpecsProfile::VIDEO_CAMERA_ID) {
            $queryBuilder
                ->innerJoin('p.specsArticleSpecification', 'sas')
                ->andWhere('sas.specId = :lensTypeSpecId')
                ->andWhere('sas2.value = :lensTypeSpecValue')
                ->setParameter('lensTypeSpecId', SpecsSpecification::LENS_TYPE_SPEC_ID)
                ->setParameter('lensTypeSpecValue', SpecsSpecification::SPEC_VALUE_CINEMA);
        }

        $queryBuilder
            ->innerJoin('p.specsArticleSpecification', 'sas2')
            ->andWhere('sas2.specId IN (:lensMountSpecIds)')
            ->andWhere('sas2.value = :lensMountValue')
            ->innerJoin('p.specsProfiles', 'sp')
            ->andWhere('sp.id = :lensProfileId')
            ->innerJoin('p.subgroup', 'sg')
            ->innerJoin('sg.maingroup', 'mg')
            ->innerJoin('mg.rootgroup', 'rg')
            ->andWhere('rg.id = :rootGroupId')
            ->andWhere('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', AbstractGroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', AbstractGroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :product_flag_active) = :product_flag_active')
            ->leftJoin(
                ProductPurchasedAdditionally::class,
                'ppa',
                'WITH',
                'ppa.product = :product AND ppa.additionalProduct = p'
            )
            ->addOrderBy('ppa.amountSold', 'DESC')
            ->setParameter('product', $product)
            ->innerJoin('p.popularity', 'pp')
            ->addOrderBy('pp.popularity', 'DESC')
            ->setParameter('product_flag_active', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->setParameter('lensMountSpecIds', SpecsSpecification::LENS_MOUNT_SPEC_IDS)
            ->setParameter('lensMountValue', $lensMountValue['value'])
            ->setParameter('lensProfileId', SpecsProfile::LENSES_ID)
            ->setParameter('rootGroupId', $rootGroupId ?? Rootgroup::LENSES_ID)
            ->groupBy('p.id');

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function getAllKits(string $country = 'NL'): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p.id', 'p.name', 'pr.price', 'pr.country', 'sum(pc.price) as childPrice')
            ->leftJoin('p.prices', 'pr', 'WITH', 'pr.country = :country')
            ->leftJoin('p.offerSetAsParent', 'aa')
            ->leftJoin('aa.product', 'ap')
            ->leftJoin('ap.prices', 'pc', 'WITH', 'pc.country = :country')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->join('p.category', 'cat')
            ->where('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_CAMERANU + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_SHOW)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', AbstractGroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', AbstractGroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->andWhere('p.categoryId = :catId')
            ->setParameter('catId', ProductCategory::KIT)
            ->setParameter('country', $country)
            ->groupBy('p.id');

        return $queryBuilder->getQuery()->getResult();
    }

    public function findByMenuItem(int $menuItemId, int $limit = 0): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->join('p.productMenuItems', 'pmi')
            ->join('pmi.menuItem', 'mi')
            ->join('p.subgroup', 'sg')
            ->join('sg.maingroup', 'mg')
            ->join('mg.rootgroup', 'rg')
            ->andWhere('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
            ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE)
            ->andWhere('mi.active = :active')
            ->setParameter('active', 1)
            ->andWhere('mi.menuId = :menuId')
            ->setParameter('menuId', MenuItem::LEFT_MENU)
            ->andWhere('mi.type IN (:types)')
            ->setParameter('types', ['regular', ''])
            ->andWhere('mi.id = :menuItemId')
            ->setParameter('menuItemId', $menuItemId)
        ;

        if ($limit > 0) {
            $queryBuilder
                ->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function findBySpecProfile(SpecsProfile $profile, bool $active, bool $activeGroups = false, int $limit = 0): array
    {
        $queryBuilder = $this->createQueryBuilder('p');
        $queryBuilder
            ->select('p')
            ->join('p.specsProfiles', 'profiles')
            ->andWhere('profiles.id = :prof')
            ->setParameter('prof', $profile);

        if ($active) {
            $queryBuilder
                ->andWhere('BIT_AND(p.flags, :pFlags) = :pFlags')
                ->setParameter('pFlags', Product::FLAG_SHOW + Product::FLAG_VISIBLE);
        }

        if ($activeGroups) {
            $queryBuilder
                ->join('p.subgroup', 'sg')
                ->join('sg.maingroup', 'mg')
                ->join('mg.rootgroup', 'rg')
                ->andWhere('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
                ->setParameter('rgFlags', Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU)
                ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
                ->setParameter('mgFlags', Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE)
                ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
                ->setParameter('sgFlags', Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE)
                ->andWhere('rg.id not in (:excludedGroups)')
                ->setParameter('excludedGroups', [Rootgroup::OCCASIONS_ID, Rootgroup::RENTAL_ID]);
        }

        if ($limit > 0) {
            $queryBuilder->setMaxResults($limit);
        }

        return $queryBuilder->getQuery()->getArrayResult();
    }
}
