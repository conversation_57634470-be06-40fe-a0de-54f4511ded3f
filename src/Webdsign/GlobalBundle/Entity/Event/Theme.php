<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity\Event;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Webdsign\GlobalBundle\Entity\Tag;

#[ORM\Entity(repositoryClass: ThemeRepository::class)]
#[ORM\Table(name: 'cameranu.event_themes')]
class Theme
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private int $id;
    #[Assert\NotBlank]
    #[ORM\Column(name: 'name', type: 'string', length: 255)]
    private string $name;

    #[ORM\ManyToOne(targetEntity: Tag::class)]
    #[ORM\JoinColumn(name: 'tag_id', referencedColumnName: 'tagId', nullable: true)]
    private ?Tag $tag = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getTag(): ?Tag
    {
        return $this->tag;
    }

    public function setTag(?Tag $tag): Theme
    {
        $this->tag = $tag;
        return $this;
    }

    public function __toString()
    {
        return $this->getName();
    }
}
