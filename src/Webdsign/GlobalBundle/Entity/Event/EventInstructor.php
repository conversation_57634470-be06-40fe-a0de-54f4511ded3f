<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity\Event;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: EventInstructorRepository::class)]
#[ORM\Table(name: 'cameranu.events_instructors')]
class EventInstructor
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private int $id;

    #[ORM\ManyToOne(targetEntity: Event::class)]
    #[ORM\JoinColumn(name: 'event_id', referencedColumnName: 'id', nullable: false)]
    private Event $event;

    #[ORM\ManyToOne(targetEntity: Instructor::class)]
    #[ORM\JoinColumn(name: 'instructor_id', referencedColumnName: 'id', nullable: false)]
    private Instructor $instructor;

    public function getId(): int
    {
        return $this->id;
    }

    public function getEvent(): Event
    {
        return $this->event;
    }

    public function setEvent(Event $event): void
    {
        $this->event = $event;
    }

    public function getInstructor(): Instructor
    {
        return $this->instructor;
    }

    public function setInstructor(Instructor $instructor): void
    {
        $this->instructor = $instructor;
    }
}
