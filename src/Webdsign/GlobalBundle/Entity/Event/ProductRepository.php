<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity\Event;

use CatBundle\DTO\Event\OverviewFilter;
use DateInterval;
use DateTimeImmutable;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Webdsign\GlobalBundle\Entity\Maingroup;
use Webdsign\GlobalBundle\Entity\Product as NormalProduct;
use Webdsign\GlobalBundle\Entity\Rootgroup;
use Webdsign\GlobalBundle\Entity\Subgroup;

/**
 * @method Product|null find($id, $lockMode = null, $lockVersion = null)
 * @method Product|null findOneBy(array $criteria, array $orderBy = null)
 * @method Product[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method Product[] findAll()
 */
class ProductRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Product::class);
    }

    public function getUpcomingQuery(DateInterval|string $interval = '1 month'): QueryBuilder
    {
        if (is_string($interval)) {
            $datetime = DateInterval::createFromDateString($interval);
        } else {
            $datetime = $interval;
        }

        return $this->createQueryBuilder('p')
            ->andWhere('p.startDateAndTime >= :startDate')
            ->setParameter('startDate', new DateTimeImmutable(), Types::DATETIME_IMMUTABLE)
            ->andWhere('p.endDateAndTime <= :endDate')
            ->setParameter('endDate', (new DateTimeImmutable())->add($datetime), Types::DATETIME_IMMUTABLE)
            ->orderBy('p.startDateAndTime', 'ASC');
    }

    /**
     * @return Product[]
     */
    public function getUpcoming(DateInterval|string $datetime = '1 month', ?int $maxResults = 8): array
    {
        $upcomingEventsQuery = $this->getUpcomingQuery($datetime);

        if (is_int($maxResults)) {
            $upcomingEventsQuery->setMaxResults($maxResults);
        }

        return $upcomingEventsQuery->getQuery()->getResult();
    }

    /**
     * @return Product[]
     */
    public function getOnDate(DateTimeImmutable $date, ?bool $needsPayment = null): array
    {
        $queryBuilder = $this->createQueryBuilder('p')
            ->andWhere('date(p.startDateAndTime) = :startDate')
            ->setParameter('startDate', $date->format('Y-m-d'));

        if ($needsPayment !== null) {
            $queryBuilder
                ->andWhere('p.needsPayment = :needsPayment')
                ->setParameter('needsPayment', $needsPayment);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function getEventsForProductFeed(DateInterval|string $interval = '1 month'): array
    {
        if (is_string($interval)) {
            $datetime = DateInterval::createFromDateString($interval);
        } else {
            $datetime = $interval;
        }

        return $this->createQueryBuilder('p')
            ->innerJoin('p.event', 'e')
            ->innerJoin('p.product', 'pp')
            ->innerJoin('pp.subgroup', 'sg')
            ->innerJoin('sg.maingroup', 'mg')
            ->innerJoin('mg.rootgroup', 'rg')
            ->where('BIT_AND(rg.flags, :rgFlags) = :rgFlags')
            ->setParameter('rgFlags', Rootgroup::FLAG_SHOW + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU)
            ->andWhere('BIT_AND(mg.flags, :mgFlags) = :mgFlags')
            ->setParameter('mgFlags', Maingroup::FLAG_SHOW + Maingroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(sg.flags, :sgFlags) = :sgFlags')
            ->setParameter('sgFlags', Subgroup::FLAG_SHOW + Subgroup::FLAG_VISIBLE)
            ->andWhere('BIT_AND(pp.flags, :ppFlags) = :ppFlags')
            ->setParameter('ppFlags', NormalProduct::FLAG_SHOW + NormalProduct::FLAG_VISIBLE)
            ->andWhere('e.visible = true')
            ->andWhere('p.endDateAndTime >= :currentDate')
            ->setParameter('currentDate', new DateTimeImmutable(), Types::DATETIME_IMMUTABLE)
            ->andWhere('p.endDateAndTime <= :endDate')
            ->setParameter('endDate', (new DateTimeImmutable())->add($datetime), Types::DATETIME_IMMUTABLE)
            ->orderBy('p.startDateAndTime', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Product[]
     */
    public function getForOverview(OverviewFilter $filter): array
    {
        $qb = $this->createQueryBuilder('p');

        $qb
            ->select('p,e,i,l,r')
            ->join('p.event', 'e')
            ->join('e.instructor', 'i')
            ->join('p.location', 'l')
            ->leftJoin('p.registrations', 'r');

        if ($filter->instructor !== null) {
            $qb
                ->andWhere('i = :instructor')
                ->setParameter('instructor', $filter->instructor);
        }
        if ($filter->location !== null) {
            $qb->andwhere('p.location = :location')
                ->setParameter('location', $filter->location);
        }
        if ($filter->category !== null) {
            $qb->andWhere('e.category = :category')
                ->setParameter('category', $filter->category);
        }
        if ($filter->visible !== null) {
            $qb->andWhere('e.visible = :visible')
                ->setParameter('visible', $filter->visible);
        }
        if ($filter->expired !== null) {
            $now = new DateTimeImmutable();
            $qb->andwhere($filter->expired ? 'p.endDateAndTime < :expired' : 'p.endDateAndTime >= :expired')
                ->setParameter('expired', $now);
        }
        if ($filter->province !== null) {
            $qb
                ->andWhere('l.province = :province')
                ->setParameter('province', $filter->province);
        }

        if ($filter->softDelete) {
            $qb->andWhere('p.softdelete = :softDelete')
                ->setParameter('softDelete', $filter->softDelete);
        } else {
            $qb->andWhere($qb->expr()->orX(
                $qb->expr()->eq('p.softdelete', 0),
                $qb->expr()->isNull('p.softdelete')
            ));
        }

        return $qb->getQuery()->getResult();
    }
}
