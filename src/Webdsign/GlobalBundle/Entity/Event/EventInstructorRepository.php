<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity\Event;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method EventInstructor|null find($id, $lockMode = null, $lockVersion = null)
 * @method EventInstructor|null findOneBy(array $criteria, array $orderBy = null)
 * @method EventInstructor[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method EventInstructor[] findAll()
 */
class EventInstructorRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EventInstructor::class);
    }

    /**
     * @return Instructor[]
     */
    public function findInstructorsByEvent(Event $event): array
    {
        return $this->createQueryBuilder('ei')
            ->select('i')
            ->join('ei.instructor', 'i')
            ->where('ei.event = :event')
            ->setParameter('event', $event)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Event[]
     */
    public function findEventsByInstructor(Instructor $instructor): array
    {
        return $this->createQueryBuilder('ei')
            ->select('e')
            ->join('ei.event', 'e')
            ->where('ei.instructor = :instructor')
            ->setParameter('instructor', $instructor)
            ->getQuery()
            ->getResult();
    }

    /**
     * Returns a comma-separated string of instructor names for the given event
     */
    public function getInstructorNamesForEvent(Event $event): string
    {
        $instructors = $this->createQueryBuilder('ei')
            ->select('i.firstName', 'i.prefix', 'i.lastName')
            ->join('ei.instructor', 'i')
            ->where('ei.event = :event')
            ->setParameter('event', $event)
            ->orderBy('i.firstName', 'ASC')
            ->addOrderBy('i.lastName', 'ASC')
            ->getQuery()
            ->getResult();

        $names = [];
        foreach ($instructors as $instructor) {
            $names[] = implode(' ', array_filter([
                $instructor['firstName'],
                $instructor['prefix'],
                $instructor['lastName'],
            ]));
        }

        return implode(', ', $names);
    }
}
