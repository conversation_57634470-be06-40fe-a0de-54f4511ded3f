<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity\Event;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\User;

#[ORM\Entity(repositoryClass: EventRepository::class)]
#[ORM\Table(name: 'cameranu.events')]
class Event
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private int $id;
    #[ORM\Column(name: 'visible', type: 'boolean', nullable: true)]
    private ?bool $visible;
    #[ORM\Column(name: 'canGoOffline', type: 'boolean', nullable: true)]
    private ?bool $canGoOffline;
    #[ORM\Column(name: 'title', type: 'string', length: 255)]
    private string $title;
    #[ORM\Column(name: 'description', type: 'text')]
    private string $description;
    #[ORM\Column(name: 'meta_title', type: 'string', length: 255, nullable: true)]
    private ?string $metaTitle;
    #[ORM\Column(name: 'meta_description', type: 'text', nullable: true)]
    private ?string $metaDescription;
    #[ORM\Column(name: 'program_description', type: 'text', nullable: true)]
    private ?string $programDescription;
    #[ORM\Column(name: 'program_list', type: 'text', nullable: true)]
    private ?string $programList;
    #[ORM\Column(name: 'practical_information', type: 'text', nullable: true)]
    private ?string $practicalInformation;
    #[ORM\Column(name: 'included_list', type: 'text', nullable: true)]
    private ?string $includedList;
    #[ORM\Column(name: 'hero_image', type: 'string', length: 255, nullable: true)]
    private ?string $heroImage;
    #[ORM\Column(name: 'thumbnail_image', type: 'string', length: 255, nullable: true)]
    private ?string $thumbnailImage = null;
    #[ORM\Column(name: 'impression_images', type: 'json', nullable: true)]
    private ?array $impressionImages;
    #[ORM\Column(name: 'type', type: 'event_type_enum')]
    private string $type;
    #[ORM\Column(name: 'level', type: 'event_level_enum')]
    private string $level;
    #[ORM\Column(name: 'created', type: 'datetime_immutable')]
    private DateTimeImmutable $created;
    #[ORM\Column(name: 'updated', type: 'datetime_immutable')]
    private DateTimeImmutable $updated;
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'created_by', referencedColumnName: 'id')]
    private User $creator;
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'updated_by', referencedColumnName: 'id')]
    private User $updater;
    #[ORM\ManyToOne(targetEntity: StockLocation::class)]
    #[ORM\JoinColumn(name: 'stock_location_id', referencedColumnName: 'id')]
    private StockLocation $stockLocation;
    #[ORM\ManyToOne(targetEntity: Category::class)]
    #[ORM\JoinColumn(name: 'category_id', referencedColumnName: 'id')]
    private Category $category;
    /**
     * @var EventInstructor[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'event', targetEntity: EventInstructor::class, cascade: ['persist', 'remove'])]
    private Collection $eventInstructors;
    /**
     * @var Product[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'event', targetEntity: Product::class)]
    private Collection $products;
    /**
     * @var ChecklistItem[]|ArrayCollection
     */
    #[ORM\ManyToMany(targetEntity: ChecklistItem::class)]
    #[ORM\JoinTable(name: 'cameranu.events_checklist_items', joinColumns: [new ORM\JoinColumn(name: 'event_id', referencedColumnName: 'id')], inverseJoinColumns: [new ORM\JoinColumn(name: 'checklist_item_id', referencedColumnName: 'id')])]
    private Collection $checklistItems;
    /**
     * @var Theme[]|ArrayCollection
     */
    #[ORM\ManyToMany(targetEntity: Theme::class)]
    #[ORM\JoinTable(name: 'cameranu.events_themes', joinColumns: [new ORM\JoinColumn(name: 'event_id', referencedColumnName: 'id')], inverseJoinColumns: [new ORM\JoinColumn(name: 'theme_id', referencedColumnName: 'id')])]
    private Collection $themes;

    public function __construct()
    {
        $this->products = new ArrayCollection();
        $this->checklistItems = new ArrayCollection();
        $this->themes = new ArrayCollection();
        $this->eventInstructors = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getVisible(): ?bool
    {
        return $this->visible;
    }

    public function setVisible(?bool $visible): void
    {
        $this->visible = $visible;
    }

    public function getCanGoOffline(): ?bool
    {
        return $this->canGoOffline;
    }

    public function setCanGoOffline(?bool $canGoOffline): void
    {
        $this->canGoOffline = $canGoOffline;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): void
    {
        $this->title = $title;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    public function setMetaTitle(?string $metaTitle): void
    {
        $this->metaTitle = $metaTitle;
    }

    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    public function setMetaDescription(?string $metaDescription): void
    {
        $this->metaDescription = $metaDescription;
    }

    public function getProgramDescription(): ?string
    {
        return $this->programDescription;
    }

    public function setProgramDescription(?string $programDescription): void
    {
        $this->programDescription = $programDescription;
    }

    public function getProgramList(): ?string
    {
        return $this->programList;
    }

    public function setProgramList(?string $programList): void
    {
        $this->programList = $programList;
    }

    public function getPracticalInformation(): ?string
    {
        return $this->practicalInformation;
    }

    public function setPracticalInformation(?string $practicalInformation): void
    {
        $this->practicalInformation = $practicalInformation;
    }

    public function getIncludedList(): ?string
    {
        return $this->includedList;
    }

    public function setIncludedList(?string $includedList): void
    {
        $this->includedList = $includedList;
    }

    public function getHeroImage(): ?string
    {
        return $this->heroImage;
    }

    public function setHeroImage(?string $heroImage): void
    {
        $this->heroImage = $heroImage;
    }

    public function getThumbnailImage(): ?string
    {
        return $this->thumbnailImage;
    }

    public function setThumbnailImage(?string $thumbnailImage): void
    {
        $this->thumbnailImage = $thumbnailImage;
    }

    public function getImpressionImages(): ?array
    {
        return $this->impressionImages;
    }

    public function setImpressionImages(?array $impressionImages): void
    {
        $this->impressionImages = $impressionImages;
    }

    /**
     * @deprecated Use getCategory()->getName() instead.
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @deprecated Use setCategory() instead.
     */
    public function setType(string $type): void
    {
        $this->type = $type;
    }

    public function getLevel(): string
    {
        return $this->level;
    }

    public function setLevel(string $level): void
    {
        $this->level = $level;
    }

    public function getCreated(): DateTimeImmutable
    {
        return $this->created;
    }

    public function setCreated(DateTimeImmutable $created): void
    {
        $this->created = $created;
    }

    public function getUpdated(): DateTimeImmutable
    {
        return $this->updated;
    }

    public function setUpdated(DateTimeImmutable $updated): void
    {
        $this->updated = $updated;
    }

    public function getCreator(): User
    {
        return $this->creator;
    }

    public function setCreator(User $creator): void
    {
        $this->creator = $creator;
    }

    public function getUpdater(): User
    {
        return $this->updater;
    }

    public function setUpdater(User $updater): void
    {
        $this->updater = $updater;
    }

    public function getStockLocation(): StockLocation
    {
        return $this->stockLocation;
    }

    public function setStockLocation(StockLocation $stockLocation): void
    {
        $this->stockLocation = $stockLocation;
    }

    public function getCategory(): Category
    {
        return $this->category;
    }

    public function setCategory(Category $category): void
    {
        $this->category = $category;
    }

    public function getEventInstructors(): Collection
    {
        return $this->eventInstructors;
    }

    public function setEventInstructors(Collection $eventInstructors): void
    {
        $this->eventInstructors = $eventInstructors;
    }

    public function addEventInstructor(EventInstructor $eventInstructor): void
    {
        if (!$this->eventInstructors->contains($eventInstructor)) {
            $this->eventInstructors->add($eventInstructor);
            $eventInstructor->setEvent($this);
        }
    }

    public function removeEventInstructor(EventInstructor $eventInstructor): void
    {
        $this->eventInstructors->removeElement($eventInstructor);
    }

    /**
     * @return Instructor[]
     */
    public function getInstructors(): array
    {
        return $this->eventInstructors->map(function (EventInstructor $eventInstructor) {
            return $eventInstructor->getInstructor();
        })->toArray();
    }

    public function addInstructor(Instructor $instructor): void
    {
        // Check if this instructor is already associated with this event
        foreach ($this->eventInstructors as $eventInstructor) {
            if ($eventInstructor->getInstructor() === $instructor) {
                return; // Already exists, don't add duplicate
            }
        }

        $eventInstructor = new EventInstructor();
        $eventInstructor->setEvent($this);
        $eventInstructor->setInstructor($instructor);
        $this->addEventInstructor($eventInstructor);
    }

    public function removeInstructor(Instructor $instructor): void
    {
        foreach ($this->eventInstructors as $eventInstructor) {
            if ($eventInstructor->getInstructor() === $instructor) {
                $this->removeEventInstructor($eventInstructor);
                break;
            }
        }
    }

    public function getProducts(): Collection
    {
        return $this->products;
    }

    public function setProducts(ArrayCollection $products): void
    {
        $this->products = $products;
    }

    public function getChecklistItems(): Collection
    {
        return $this->checklistItems;
    }

    public function setChecklistItems(Collection $checklistItems): void
    {
        $this->checklistItems = $checklistItems;
    }

    public function getThemes(): Collection
    {
        return $this->themes;
    }

    public function setThemes(Collection $themes): void
    {
        $this->themes = $themes;
    }

    public function __toString()
    {
        return $this->getTitle();
    }
}
