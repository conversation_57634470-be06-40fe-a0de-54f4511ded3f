<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Persistence\ManagerRegistry;

/**
 * @method ParkingShipmentMethod[] findAll()
 * @method ParkingShipmentMethod[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method ParkingShipmentMethod|null findOneBy(array $criteria, array $orderBy = null)
 * @method ParkingShipmentMethod|null find($id, int $lockMode = null, int $lockVersion = null)
 */
class ParkingShipmentMethodRepository extends WebdsignRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ParkingShipmentMethod::class);
    }
}
