<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Persistence\ManagerRegistry;
use Webdsign\GlobalBundle\Entity\Tag;

class TagRepository extends WebdsignRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Tag::class);
    }

    /**
     * @param array $values
     * @param int|null $typeId
     * @return Tag[]
     */
    public function findByValues(array $values, ?int $typeId = null): array
    {
        $queryBuilder = $this->createQueryBuilder('t');
        $queryBuilder
            ->andWhere('t.value IN (:values)')
            ->setParameter('values', $values);

        if ($typeId !== null) {
            $queryBuilder
                ->andWhere('t.tagTypeId = :typeId')
                ->setParameter('typeId', $typeId);
        }

        return $queryBuilder->getQuery()->getResult();
    }
}
