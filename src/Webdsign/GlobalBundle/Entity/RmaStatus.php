<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;


/**
 * Rma
 */
#[ORM\Entity(repositoryClass: RmaStatusRepository::class)]
#[ORM\Table(name: 'service_rmaStatus')]
class RmaStatus
{
    public const STATE_DONE = 85;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'title', type: 'string', unique: true)]
    private $title;

    /**
     * @var int
     */
    #[ORM\Column(name: 'sortOrder', type: 'integer')]
    private $sortOrder;

    /**
     * @var ServiceTag[]|ArrayCollection
     */
    #[ORM\ManyToMany(targetEntity: ServiceTag::class)]
    #[ORM\JoinTable(name: 'service_rmaStatusTags', joinColumns: [new ORM\JoinColumn(name: 'statusId', referencedColumnName: 'id')], inverseJoinColumns: [new ORM\JoinColumn(name: 'tagId', referencedColumnName: 'id')])]
    private $tags;

    /**
     * @var RmaWorkflow[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'status', targetEntity: RmaWorkflow::class)]
    private $workflow;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return RmaStatus
     */
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return RmaStatus
     */
    public function setTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * @return int
     */
    public function getSortOrder()
    {
        return $this->sortOrder;
    }

    /**
     * @param int $sortOrder
     * @return RmaStatus
     */
    public function setSortOrder($sortOrder)
    {
        $this->sortOrder = $sortOrder;

        return $this;
    }

    /**
     * @return ArrayCollection|ServiceTag[]
     */
    public function getTags()
    {
        return $this->tags;
    }

    /**
     * @param ArrayCollection|ServiceTag[] $tags
     * @return RmaStatus
     */
    public function setTags($tags)
    {
        $this->tags = $tags;

        return $this;
    }

    /**
     * @return ArrayCollection|RmaWorkflow[]
     */
    public function getWorkflow()
    {
        return $this->workflow;
    }

    /**
     * @param ArrayCollection|RmaWorkflow[] $workflow
     * @return RmaStatus
     */
    public function setWorkflow($workflow)
    {
        $this->workflow = $workflow;
        return $this;
    }

    /**
     * @param ServiceTag $tag
     * @return ArrayCollection|RmaWorkflow[]
     */
    public function getWorkflowForTag(ServiceTag $tag)
    {
        $workflows = new ArrayCollection();
        foreach($this->getWorkflow() as $workflow) {
            if ($workflow->getTag() === $tag) {
                $workflows[] = $workflow;
            }
        }
        return $workflows;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return $this->getTitle();
    }
}
