<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Webdsign\GlobalBundle\Entity\PurchaseReference;

/**
 * @method PurchaseReference|null find($id, $lockMode = null, $lockVersion = null)
 * @method PurchaseReference|null findOneBy(array $criteria, array $orderBy = null)
 * @method PurchaseReference[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method PurchaseReference[] findAll()
 */
class PurchaseReferenceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PurchaseReference::class);
    }

}
