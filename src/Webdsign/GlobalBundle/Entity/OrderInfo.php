<?php

/** @noinspection NotOptimalIfConditionsInspection */

namespace Webdsign\GlobalBundle\Entity;

use CatBundle\Form\Filter\AdyenOrderCheckFilter;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;
use Exception;
use Symfony\Component\Security\Core\User\UserInterface;
use Throwable;
use Webdsign\GlobalBundle\Entity\Payment\PaymentPeriod;

/**
 * OrderInfo
 */
#[ORM\Entity(repositoryClass: OrderInfoRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: 'cameranu.bestelling_naw')]
class OrderInfo implements LoggerInterface
{
    public const SEX_MAN = 0;
    public const SEX_WOMAN = 1;

    public const FLAG_WAITING_FOR_PAYMENT = 2 ** 0; // Wachten op betaling / ophalen als verzendwijze=3
    public const FLAG_PAYMENT_COMPLETE = 2 ** 1; // Bestelling binnen
    public const FLAG_ORDER_COMPLETE = 2 ** 2; // Bestelling afgerond
    public const FLAG_PAYMENT_OKAY = 2 ** 3; // iDEAL (of credit card / of PayPal) betaling ==> OK!!
    public const FLAG_SEND_SMS = 2 ** 9; // SMS versturen bij updates van bestelling
    public const FLAG_GOEDHART_READY = 2 ** 6;
    public const FLAG_GOEDHART_OK = 2 ** 7;
    public const FLAG_GOEDHART_ERROR = 2 ** 8;
    public const FLAG_NO_VAT = 2 ** 13; // BTW 0 factuur
    public const FLAG_PARTIAL_DELIVERY = 2 ** 17; // In delen verzenden?
    public const FLAG_IN_PAYMENT_SCREEN = 2 ** 22; // Bestelling zit in betaalscherm
    public const FLAG_SETTLEMENT_HANDLED = 2 ** 25; // Vendiro settlement verwerkt
    public const FLAG_SEND_LATER_URK_DELIVERY_MAIL_SENT = 2 ** 28; // 'Later sturen Urk' Levertijd mails verstuurd
    public const SHIPPING_METHOD_CANCELLED = 6; // Verzendwijze geannuleerd
    public const START_NEW_INVOICE_NUMBERS_ID = 4677973; //Id vanaf waar we het nieuwe format factuurnummer gebruiken
    /**
     * @var float|null
     */
    protected ?float $discount_inc = null;
    /**
     * @var float|null
     */
    protected ?float $discount_ex = null;

    /** @var EntityManagerInterface */
    private $entityManager;

    /**
     * @var CartItem[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: CartItem::class)]
    protected $cartitems;
    /**
     * @var Stock[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: Stock::class)]
    protected $stockItems;
    /**
     * @var OrderPayments[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: OrderPayments::class)]
    protected $orderPayments;
    /**
     * @var Customer|null
     */
    #[ORM\ManyToOne(targetEntity: Customer::class, inversedBy: 'orders')]
    #[ORM\JoinColumn(name: 'klantnummer', referencedColumnName: 'id')]
    protected $customer;
    /**
     * @var Coupon[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: Coupon::class)]
    protected $coupons;
    /**
     * @var LedgerField[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: LedgerField::class)]
    protected $ledgerFields;
    /**
     * @var LegacyRma[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: LegacyRma::class)]
    protected $legacyRmas;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'ordernummer', type: 'integer')]
    private $ordernr;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'factuurnummer', type: 'integer')]
    private $invoiceNumber;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'titel', type: 'integer')]
    private $sex;
    /**
     * @var string
     */
    #[ORM\Column(name: 'naam', type: 'string', length: 255)]
    private $name;
    /**
     * @var string
     */
    #[ORM\Column(name: 'voornaam', type: 'string', length: 60)]
    private $firstName;
    /**
     * @var string
     */
    #[ORM\Column(name: 'tussenvoegsel', type: 'string', length: 16)]
    private $nameInsertion;
    /**
     * @var string
     */
    #[ORM\Column(name: 'achternaam', type: 'string', length: 60)]
    private $lastName;
    /**
     * @var string
     */
    #[ORM\Column(name: 'adres', type: 'string', length: 255)]
    private $address;
    /**
     * @var string
     */
    #[ORM\Column(name: 'adres2', type: 'string', length: 255)]
    private $address2;
    /**
     * @var string
     */
    #[ORM\Column(name: 'huisnr', type: 'string', length: 255)]
    private $housenr;
    /**
     * @var string
     */
    #[ORM\Column(name: 'huisnrext', type: 'string', length: 255)]
    private $housenrext;
    /**
     * @var string
     */
    #[ORM\Column(name: 'postcode', type: 'string', length: 255)]
    private $zipcode;
    /**
     * @var string
     */
    #[ORM\Column(name: 'postcode2', type: 'string', length: 255)]
    private $zipcode2;
    /**
     * @var string
     */
    #[ORM\Column(name: 'woonplaats', type: 'string', length: 255)]
    private $city;
    /**
     * @var string
     */
    #[ORM\Column(name: 'land', type: 'string', length: 255)]
    private $country;
    /**
     * @var string
     */
    #[ORM\Column(name: 'telefoon', type: 'string', length: 255)]
    private $phonenr;
    /**
     * @var string
     */
    #[ORM\Column(name: 'mobiel', type: 'string', length: 255)]
    private $mobile;
    /**
     * @var string
     */
    #[ORM\Column(name: 'email', type: 'string', length: 255)]
    private $email;
    /**
     * @var string
     */
    #[ORM\Column(name: 'besteldatum', type: 'string', length: 255)]
    private $orderDate;

    /**
     * @var string
     */
    #[ORM\Column(name: 'besteltijd', type: 'string', length: 255)]
    private $orderTime;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bedrijfsnaam', type: 'string', length: 255)]
    private $company;

    /**
     * @var string
     */
    #[ORM\Column(name: 'company_email', type: 'string', length: 100)]
    private $companyEmail;

    /**
     * @var string
     */
    #[ORM\Column(name: 'btwnummer', type: 'string', length: 255)]
    private $taxNumber;
    /**
     * @var string
     */
    #[ORM\Column(name: 'btw', type: 'decimal', precision: 3, scale: 1)]
    private $vatPercentage;
    /**
     * @var string
     */
    #[ORM\Column(name: 'latlong', type: 'string', length: 255)]
    private $latlong;
    /**
     * @var string
     */
    #[ORM\Column(name: 'opmerkingen', type: 'text')]
    private $customerComment;
    /**
     * @var string
     */
    #[ORM\Column(name: 'opmerkingen2', type: 'text')]
    private $ourComment;
    /**
     * @var UserInterface
     */
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id')]
    private $handledBy;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'flags', type: 'integer', length: 11)]
    private $flags;

    /**
     * @var ShipmentMethod
     */
    #[ORM\ManyToOne(targetEntity: ShipmentMethod::class)]
    #[ORM\JoinColumn(name: 'verzendwijze', referencedColumnName: 'id')]
    private $shippingMethod;

    /**
     * @var ShipmentMethod
     */
    #[ORM\ManyToOne(targetEntity: ShipmentMethod::class)]
    #[ORM\JoinColumn(name: 'previousShippingMethod', referencedColumnName: 'id')]
    private $previousShippingMethod;

    /**
     * @var PaymentMethod
     */
    #[ORM\ManyToOne(targetEntity: PaymentMethod::class)]
    #[ORM\JoinColumn(name: 'betaalwijze_id', referencedColumnName: 'id')]
    private $paymentMethod;

    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'datum_factuur', type: 'date')]
    private $dateInvoice;
    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'datum_afgeh', type: 'date')]
    private $dateHandled;
    /**
     * @var DateTime $courierTime
     */
    #[ORM\Column(name: 'route_tijd', type: 'time')]
    private $courierTime;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'klantnummer', type: 'integer', length: 11)]
    private $customerid;

    /**
     * @var string
     */
    #[ORM\Column(name: 'taal', type: 'string', length: 2)]
    private $language;

    /**
     * @var Origin
     */
    #[ORM\OneToOne(targetEntity: Origin::class)]
    #[ORM\JoinColumn(name: 'herkomst', referencedColumnName: 'id')]
    protected $origin;

    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_naam', type: 'string', length: 136)]
    private $deliveryName;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_voornaam', type: 'string', length: 60)]
    private $deliveryFirstName;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_tussenvoegsel', type: 'string', length: 16)]
    private $deliveryNameInsertion;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_achternaam', type: 'string', length: 60)]
    private $deliveryLastName;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_adres', type: 'string', length: 50)]
    private $deliveryAddress;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_adres2', type: 'string', length: 50)]
    private $deliveryAddress2;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_huisnr', type: 'string', length: 5)]
    private $deliveryHousenr;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_huisnrext', type: 'string', length: 15)]
    private $deliveryHousenrext;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_postcode', type: 'string', length: 20)]
    private $deliveryZipcode;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_postcode2', type: 'string', length: 2)]
    private $deliveryZipcode2;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_woonplaats', type: 'string', length: 50)]
    private $deliveryCity;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_land', type: 'string', length: 40)]
    private $deliveryCountry;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_telefoon', type: 'string', length: 30)]
    private $deliveryPhonenr;
    /**
     * @var string
     */
    #[ORM\Column(name: 'bezorg_bedrijfsnaam', type: 'string', length: 50)]
    private $deliveryCompany;

    /**
     * @var string
     */
    #[ORM\Column(name: 'referentie', type: 'string', length: 50)]
    private $reference;

    /**
     * @var int
     */
    #[ORM\Column(name: 'domain_id', type: 'integer')]
    private $domainId;
    /**
     * @var string
     */
    #[ORM\Column(name: 'ipnr', type: 'string', length: 15)]
    private $ipNumber;

    /**
     * @var Parking|null
     */
    #[ORM\ManyToOne(targetEntity: Parking::class)]
    #[ORM\JoinColumn(name: 'parkeren_id', referencedColumnName: 'id')]
    private $parking;

    /**
     * @var OrderParking
     */
    #[ORM\OneToOne(mappedBy: 'order', targetEntity: OrderParking::class, fetch: 'EAGER')]
    private $orderParking;

    /**
     * @var BackOrder[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'parentOrder', targetEntity: BackOrder::class)]
    private $backOrders;

    /**
     * @var BackOrder[]|ArrayCollection
     */
    #[ORM\OneToMany(mappedBy: 'newOrder', targetEntity: BackOrder::class)]
    private $backOrderParentOrders;

    /**
     * @var OrderWait
     */
    #[ORM\OneToOne(mappedBy: 'order', targetEntity: OrderWait::class, fetch: 'EAGER')]
    private $orderWait;

    /**
     * @var float
     */
    #[ORM\Column(name: 'verzendkosten', type: 'float')]
    private $shippingCosts;

    /**
     * @var float
     */
    #[ORM\Column(name: 'verzendkosten_ex', type: 'float')]
    private $shippingCostsEx;

    /**
     * @var float
     */
    #[ORM\Column(name: 'betaalkosten', type: 'float')]
    private $paymentCosts;

    /**
     * @var float
     */
    #[ORM\Column(name: 'betaalkosten_ex', type: 'float')]
    private $paymentCostsEx;

    /**
     * @var float
     */
    #[ORM\Column(name: 'aanbieding_korting', type: 'float')]
    private $discountCosts;
    #[ORM\Column(name: 'aanbieding_korting_ex', type: 'float')]
    private float $discountCostsEx;
    /**
     * @var InternalInvoice[]|Collection $invoiceLines
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: InternalInvoice::class, fetch: 'LAZY')]
    private $internalInvoices;

    /**
     * @var ArticleReturns[]|Collection $articlesReturn
     */
    #[ORM\OneToMany(mappedBy: 'creditOrder', targetEntity: ArticleReturns::class)]
    private $articlesReturn;

    /**
     * @var ArticleDeadOnArrival[]|Collection $articlesDeadOnArrival
     */
    #[ORM\OneToMany(mappedBy: 'creditOrder', targetEntity: ArticleDeadOnArrival::class)]
    private $articlesDeadOnArrival;

    /**
     * @var DateTime|null
     */
    #[ORM\Column(name: 'imported_in_analytics', type: 'datetime', nullable: true)]
    private $importedInAnalytics;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'checked', type: 'boolean', options: ['default' => 0])]
    private $checked;

    /**
     * @var AdyenBatchReport[]|Collection $adyenBatchReports
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: AdyenBatchReport::class)]
    private $adyenBatchReports;

    /**
     * @var AdyenLog[]|Collection $adyenLogs
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: AdyenLog::class)]
    private $adyenLogs;

    #[ORM\OneToMany(mappedBy: 'order', targetEntity: \Webdsign\GlobalBundle\Entity\PSP\Log::class)]
    private Collection $pspLogs;

    /**
     * @var OrderBox[]|Collection $orderBoxes
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: OrderBox::class, fetch: 'LAZY')]
    protected $orderBoxes;

    /**
     * @var CancellationLog[]|Collection $cancellationLogs
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: CancellationLog::class, fetch: 'LAZY')]
    protected $cancellationLogs;

    /**
     * @var ReferrerMail
     */
    #[ORM\OneToOne(mappedBy: 'order', targetEntity: ReferrerMail::class)]
    private $referrerMail;

    /**
     * @var string
     */
    #[ORM\Column(name: 'ga_id', type: 'string', nullable: true)]
    private $gaClientId;

    /**
     * @var null|OrderReference
     */
    #[ORM\OneToOne(mappedBy: 'order', targetEntity: OrderReference::class)]
    private $orderReference;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'prevent_automatic_cancel', type: 'boolean', nullable: false, options: ['default' => false])]
    private $preventAutomaticCancel;

    /**
     * @var string
     */
    #[ORM\Column(name: 'issuerID', type: 'string')]
    private string $issuerId;

    #[ORM\OneToMany(mappedBy: 'order', targetEntity: Postnl::class)]
    private Collection $postnls;

    /**
     * @var string $extraInvoiceInfo
     */
    #[ORM\Column(name: 'vrij4', type: 'string', options: ['default' => ''])]
    private string $extraInvoiceInfo;

    #[ORM\OneToMany(mappedBy: 'userTeam', targetEntity: OrderHistory::class)]
    private Collection $orderHistories;

    #[ORM\ManyToOne(targetEntity: PaymentPeriod::class)]
    #[ORM\JoinColumn(name: 'payment_period_id', referencedColumnName: 'id')]
    private ?PaymentPeriod $paymentPeriod;


    #[ORM\OneToMany(mappedBy: 'order', targetEntity: OrderAnalyticsInfo::class)]
    private Collection $orderAnalyticsInfo;

    #[ORM\Column(name: 'psp_reference', type: 'string')]
    private ?string $pspReference;

    /**
     * @var Collection
     */
    #[ORM\OneToMany(mappedBy: 'order', targetEntity: OrderTickets::class)]
    private Collection $orderTickets;

    #[ORM\Column(name: 'domain_origin', type: 'string', nullable: true)]
    private ?string $domainOrigin;

    #[ORM\Column(name: 'rfm_segment', type: 'string', nullable: true)]
    private ?string $rfmSegment;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $this->domainId = 1;
        $this->invoiceNumber = 0;
        $this->customerComment = '';
        $this->ourComment = '';
        $this->extraInvoiceInfo = '';
        $this->paymentMethod = 0;
        $this->shippingCosts = 0;
        $this->shippingCostsEx = 0;
        $this->paymentCosts = 0;
        $this->paymentCostsEx = 0;
        $this->discountCosts = 0;
        $this->discountCostsEx = 0.;

        $this->cartitems = new ArrayCollection();
        $this->stockItems = new ArrayCollection();
        $this->orderPayments = new ArrayCollection();
        $this->coupons = new ArrayCollection();
        $this->ledgerFields = new ArrayCollection();
        $this->legacyRmas = new ArrayCollection();
        $this->backOrderParentOrders = new ArrayCollection();
        $this->internalInvoices = new ArrayCollection();
        $this->orderBoxes = new ArrayCollection();
        $this->cancellationLogs = new ArrayCollection();
        $this->postnls = new ArrayCollection();
        $this->articlesDeadOnArrival = new ArrayCollection();
        $this->orderHistories = new ArrayCollection();
        $this->orderAnalyticsInfo = new ArrayCollection();
        $this->pspLogs = new ArrayCollection();
        $this->createOrdernr();
    }

    /**
     * @param LifecycleEventArgs $args
     * @return void
     */
    #[ORM\PostLoad]
    public function postLoad(LifecycleEventArgs $args): void
    {
        $this->entityManager = $args->getEntityManager();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return OrderInfo
     */
    public function setId(int $id): OrderInfo
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get ordernr
     *
     * @return integer
     */
    public function getOrdernr()
    {
        return $this->ordernr;
    }

    /**
     * Set ordernr
     *
     * @param integer $ordernr
     * @return OrderInfo
     */
    public function setOrdernr($ordernr): OrderInfo
    {
        $this->ordernr = $ordernr;

        return $this;
    }

    /**
     * @return OrderInfo
     * @throws Exception
     */
    public function createOrdernr(): OrderInfo
    {
        $this->setOrdernr(date('U' . random_int(10, 99)));

        return $this;
    }

    /**
     * @return int
     */
    public function getInvoiceNumber()
    {
        return $this->invoiceNumber;
    }

    /**
     * @param int $invoiceNumber
     * @return void
     */
    public function setInvoiceNumber($invoiceNumber): void
    {
        $this->invoiceNumber = $invoiceNumber;
    }

    public function getSex()
    {
        return $this->sex;
    }

    public function setSex(string $sex): OrderInfo
    {
        $this->sex = 0;
        if (strtolower($sex) !== 'dhr' || (int) $sex === 1) {
            $this->sex = 1;
        }

        return $this;
    }

    /**
     * Get naam
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set naam
     *
     * @param string $name
     * @return OrderInfo
     */
    public function setName($name): OrderInfo
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Get adres
     *
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Set adres
     *
     * @param string $address
     * @return OrderInfo
     */
    public function setAddress($address): OrderInfo
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get adres2
     *
     * @return string
     */
    public function getAddress2()
    {
        return $this->address2;
    }

    /**
     * Set adres2
     *
     * @param string $address2
     * @return OrderInfo
     */
    public function setAddress2($address2): OrderInfo
    {
        $this->address2 = $address2;

        return $this;
    }

    /**
     * Get huisnr
     *
     * @return string
     */
    public function getHousenr()
    {
        return $this->housenr;
    }

    /**
     * Set huisnr
     *
     * @param string $housenr
     * @return OrderInfo
     */
    public function setHousenr($housenr): OrderInfo
    {
        $this->housenr = $housenr;

        return $this;
    }

    /**
     * Get huisnrext
     *
     * @return string
     */
    public function getHousenrext()
    {
        return $this->housenrext;
    }

    /**
     * Set huisnrext
     *
     * @param string $housenrext
     * @return OrderInfo
     */
    public function setHousenrext($housenrext): OrderInfo
    {
        $this->housenrext = $housenrext;

        return $this;
    }

    /**
     * Get postcode
     *
     * @return string
     */
    public function getZipcode()
    {
        return $this->zipcode;
    }

    /**
     * Set postcode
     *
     * @param string $zipcode
     * @return OrderInfo
     */
    public function setZipcode($zipcode): OrderInfo
    {
        $this->zipcode = $zipcode;

        return $this;
    }

    /**
     * Get postcode2
     *
     * @return string
     */
    public function getZipcode2()
    {
        return $this->zipcode2;
    }

    /**
     * Set postcode2
     *
     * @param string $zipcode2
     * @return OrderInfo
     */
    public function setZipcode2($zipcode2): OrderInfo
    {
        $this->zipcode2 = $zipcode2;

        return $this;
    }

    /**
     * Get woonplaats
     *
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * Set woonplaats
     *
     * @param string $city
     * @return OrderInfo
     */
    public function setCity($city): OrderInfo
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get land
     *
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * Set land
     *
     * @param string $country
     * @return OrderInfo
     */
    public function setCountry($country): OrderInfo
    {
        $this->country = $country;

        return $this;
    }

    /**
     * Get telefoon
     *
     * @return string
     */
    public function getPhonenr()
    {
        return $this->phonenr;
    }

    /**
     * Set telefoon
     *
     * @param string $phonenr
     * @return OrderInfo
     */
    public function setPhonenr($phonenr): OrderInfo
    {
        $this->phonenr = str_replace(' ', '', $phonenr);

        return $this;
    }

    /**
     * Get mobiel
     *
     * @return string
     */
    public function getMobile()
    {
        return $this->mobile;
    }

    /**
     * Set mobiel
     *
     * @param string $mobile
     * @return OrderInfo
     */
    public function setMobile($mobile): OrderInfo
    {
        $this->mobile = str_replace(' ', '', $mobile);

        return $this;
    }

    /**
     * Get email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Set email
     *
     * @param string $email
     * @return OrderInfo
     */
    public function setEmail($email): OrderInfo
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Get orderdatum
     *
     * @return string
     */
    public function getOrderDate()
    {
        return $this->orderDate;
    }

    /**
     * Set orderdatum
     *
     * @param string $orderDate
     * @return OrderInfo
     */
    public function setOrderDate($orderDate): OrderInfo
    {
        $this->orderDate = $orderDate;

        return $this;
    }

    /**
     * @return string
     */
    public function getOrderTime()
    {
        return $this->orderTime;
    }

    /**
     * @param string $orderTime
     * @return OrderInfo
     */
    public function setOrderTime($orderTime): OrderInfo
    {
        $this->orderTime = $orderTime;

        return $this;
    }

    /**
     * @return DateTime
     * @throws Exception
     */
    public function getOrderDateTime(): DateTime
    {
        return new DateTime($this->getOrderDate() . ' ' . $this->getOrderTime());
    }

    /**
     * Get bedrijfsnaam
     *
     * @return string
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * Set bedrijfsnaam
     *
     * @param string $company
     * @return OrderInfo
     */
    public function setCompany($company): OrderInfo
    {
        $this->company = $company;

        return $this;
    }

    public function getCompanyEmail()
    {
        return $this->companyEmail;
    }

    public function setCompanyEmail(?string $companyEmail): OrderInfo
    {
        $this->companyEmail = $companyEmail;

        return $this;
    }

    /**
     * @return string
     */
    public function getTaxNumber()
    {
        return $this->taxNumber;
    }

    /**
     * @param string $taxNumber
     * @return OrderInfo
     */
    public function setTaxNumber($taxNumber): OrderInfo
    {
        $this->taxNumber = $taxNumber;

        return $this;
    }

    /**
     * @return string
     */
    public function getVatPercentage()
    {
        return $this->vatPercentage;
    }

    /**
     * @param string $vatPercentage
     * @return OrderInfo
     */
    public function setVatPercentage(string $vatPercentage): OrderInfo
    {
        $this->vatPercentage = $vatPercentage;

        return $this;
    }

    /**
     * @return string
     */
    public function getCustomerComment()
    {
        return $this->customerComment;
    }

    /**
     * @param string $customerComment
     * @return OrderInfo
     */
    public function setCustomerComment($customerComment): OrderInfo
    {
        $this->customerComment = $customerComment;

        return $this;
    }

    /**
     * @return string
     */
    public function getOurComment()
    {
        return $this->ourComment;
    }

    /**
     * @param string $ourComment
     * @return OrderInfo
     */
    public function setOurComment($ourComment): OrderInfo
    {
        $this->ourComment = $ourComment;

        return $this;
    }

    public function getHandledBy(): ?User
    {
        if ($this->handledBy instanceof User && $this->handledBy->getId() === 0) {
            return null;
        }

        return $this->handledBy;
    }

    /**
     * @param UserInterface $handledBy
     * @return OrderInfo
     */
    public function setHandledBy(UserInterface $handledBy): OrderInfo
    {
        $this->handledBy = $handledBy;

        return $this;
    }

    /**
     * Get flags
     *
     * @return integer
     */
    public function getFlags()
    {
        return $this->flags;
    }

    /**
     * Set flags
     *
     * @param integer $flags
     * @return OrderInfo
     */
    public function setFlags($flags): OrderInfo
    {
        $this->flags = $flags;

        return $this;
    }

    /**
     * @return ShipmentMethod|null
     */
    public function getShippingMethod(): ?ShipmentMethod
    {
        return $this->shippingMethod instanceof ShipmentMethod ? $this->shippingMethod : null;
    }

    /**
     * @param ShipmentMethod $shippingMethod
     * @param bool $savePrevious
     * @return OrderInfo
     */
    public function setShippingMethod(ShipmentMethod $shippingMethod, bool $savePrevious = false): OrderInfo
    {
        // Set or empty the previous shipping method
        $this->setPreviousShippingMethod($savePrevious === true ? $this->getShippingMethod() : null);

        $this->shippingMethod = $shippingMethod;

        return $this;
    }

    /**
     * @return ShipmentMethod|null
     */
    public function getPreviousShippingMethod(): ?ShipmentMethod
    {
        return $this->previousShippingMethod;
    }

    /**
     * @param ShipmentMethod|null $shippingMethod
     * @return OrderInfo
     */
    public function setPreviousShippingMethod(?ShipmentMethod $shippingMethod): OrderInfo
    {
        $this->previousShippingMethod = $shippingMethod;
        return $this;
    }

    /**
     * @return PaymentMethod
     */
    public function getPaymentMethod(): ?PaymentMethod
    {
        return ($this->paymentMethod && $this->paymentMethod->getId() !== 0) ? $this->paymentMethod : null;
    }

    /**
     * @param PaymentMethod $paymentMethod
     * @return OrderInfo
     */
    public function setPaymentMethod(PaymentMethod $paymentMethod): OrderInfo
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    /**
     * @return string
     */
    public function getLatlong(): string
    {
        return $this->latlong;
    }

    /**
     * @param string $latlong
     * @return OrderInfo
     */
    public function setLatlong($latlong): OrderInfo
    {
        $this->latlong = $latlong;

        return $this;
    }

    /**
     * Get $dateInvoice
     *
     * @return DateTime
     */
    public function getDateInvoice(): DateTime
    {
        return $this->dateInvoice;
    }

    /**
     * Set dateInvoice
     *
     * @param DateTime $dateInvoice
     * @return OrderInfo
     */
    public function setDateInvoice($dateInvoice): OrderInfo
    {
        $this->dateInvoice = $dateInvoice;

        return $this;
    }

    /**
     * @return DateTime
     */
    public function getDateHandled(): DateTime
    {
        return $this->dateHandled;
    }

    /**
     * @param DateTime $dateHandled
     * @return OrderInfo
     */
    public function setDateHandled(DateTime $dateHandled): OrderInfo
    {
        $this->dateHandled = $dateHandled;

        return $this;
    }

    /**
     * @return DateTime
     */
    public function getCourierTime(): DateTime
    {
        return $this->courierTime;
    }

    /**
     * @param DateTime $courierTime
     * @return OrderInfo
     */
    public function setCourierTime(DateTime $courierTime): OrderInfo
    {
        $this->courierTime = $courierTime;

        return $this;
    }

    /**
     * Kijk of het een service order is
     *
     * @return bool
     */
    public function isServiceOrder(): bool
    {
        return ($this->flags & 2097152) === 2097152;
    }

    /**
     * Kijk of het een afgeronde order is
     *
     * @return bool
     */
    public function isFinishedOrder(): bool
    {
        return ($this->flags & self::FLAG_ORDER_COMPLETE) === self::FLAG_ORDER_COMPLETE;
    }

    public function setIsFinishedOrder(): OrderInfo
    {
        $this->setFlags($this->getFlags() | self::FLAG_ORDER_COMPLETE);

        return $this;
    }

    /**
     * Get $customerid
     *
     * @return string
     */
    public function getCustomerid()
    {
        return $this->customerid;
    }

    /**
     * Set customerid
     *
     * @param integer $customerid
     * @return OrderInfo
     */
    public function setCustomerid($customerid): OrderInfo
    {
        $this->customerid = $customerid;

        return $this;
    }

    /**
     * @return Customer|null
     */
    public function getCustomer(): ?Customer
    {
        // blijkbaar kan "customer->id" 0 zijn...
        if ($this->customer instanceof Customer && $this->customer->getId() === 0) {
            return null;
        }
        return $this->customer;
    }

    /**
     * Set customer
     *
     * @param Customer $customer
     * @return OrderInfo
     */
    public function setCustomer($customer): OrderInfo
    {
        $this->customer = $customer;

        return $this;
    }

    /**
     * @return ArrayCollection|CartItem[]
     */
    public function getCartitems()
    {
        return $this->cartitems;
    }

    /**
     * @return ArrayCollection|Stock[]
     */
    public function getStockItems()
    {
        return $this->stockItems;
    }

    /**
     * @param ArrayCollection|Stock[] $stockItems
     * @return OrderInfo
     */
    public function setStockItems($stockItems): OrderInfo
    {
        $this->stockItems = $stockItems;
        return $this;
    }

    /**
     * @return ArrayCollection|OrderPayments[]
     */
    public function getOrderPayments()
    {
        return $this->orderPayments;
    }

    /**
     * @param ArrayCollection|OrderPayments[] $orderPayments
     * @return void
     */
    public function setOrderPayments($orderPayments): void
    {
        $this->orderPayments = $orderPayments;
    }

    /**
     * @return string
     */
    public function getLanguage()
    {
        return $this->language;
    }

    /**
     * @param string $language
     * @return OrderInfo
     */
    public function setLanguage($language): OrderInfo
    {
        $this->language = $language;

        return $this;
    }

    /**
     * @return Origin
     */
    public function getOrigin(): Origin
    {
        return $this->origin;
    }

    /**
     * @param Origin $origin
     * @return OrderInfo
     */
    public function setOrigin($origin): OrderInfo
    {
        $this->origin = $origin;

        return $this;
    }

    /**
     * @return string
     */
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * @param string $firstName
     * @return OrderInfo
     */
    public function setFirstName($firstName): OrderInfo
    {
        $this->firstName = $firstName;
        return $this;
    }

    /**
     * @return string
     */
    public function getNameInsertion()
    {
        return $this->nameInsertion;
    }

    /**
     * @param string $nameInsertion
     * @return OrderInfo
     */
    public function setNameInsertion($nameInsertion): OrderInfo
    {
        $this->nameInsertion = $nameInsertion;
        return $this;
    }

    /**
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * @param string $lastName
     * @return OrderInfo
     */
    public function setLastName($lastName): OrderInfo
    {
        $this->lastName = $lastName;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryName()
    {
        return $this->deliveryName;
    }

    /**
     * @param string $deliveryName
     * @return OrderInfo
     */
    public function setDeliveryName($deliveryName): OrderInfo
    {
        $this->deliveryName = $deliveryName;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryFirstName()
    {
        return $this->deliveryFirstName;
    }

    /**
     * @param string $deliveryFirstName
     * @return OrderInfo
     */
    public function setDeliveryFirstName($deliveryFirstName): OrderInfo
    {
        $this->deliveryFirstName = $deliveryFirstName;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryNameInsertion()
    {
        return $this->deliveryNameInsertion;
    }

    /**
     * @param string $deliveryNameInsertion
     * @return OrderInfo
     */
    public function setDeliveryNameInsertion($deliveryNameInsertion): OrderInfo
    {
        $this->deliveryNameInsertion = $deliveryNameInsertion;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryLastName()
    {
        return $this->deliveryLastName;
    }

    /**
     * @param string $deliveryLastName
     * @return OrderInfo
     */
    public function setDeliveryLastName($deliveryLastName): OrderInfo
    {
        $this->deliveryLastName = $deliveryLastName;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryAddress()
    {
        return $this->deliveryAddress;
    }

    /**
     * @param string $deliveryAddress
     * @return OrderInfo
     */
    public function setDeliveryAddress($deliveryAddress): OrderInfo
    {
        $this->deliveryAddress = $deliveryAddress;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryAddress2()
    {
        return $this->deliveryAddress2;
    }

    /**
     * @param string $deliveryAddress2
     * @return OrderInfo
     */
    public function setDeliveryAddress2($deliveryAddress2): OrderInfo
    {
        $this->deliveryAddress2 = $deliveryAddress2;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryHousenr()
    {
        return $this->deliveryHousenr;
    }

    /**
     * @param string $deliveryHousenr
     * @return OrderInfo
     */
    public function setDeliveryHousenr($deliveryHousenr): OrderInfo
    {
        $this->deliveryHousenr = $deliveryHousenr;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryHousenrext()
    {
        return $this->deliveryHousenrext;
    }

    /**
     * @param string $deliveryHousenrext
     * @return OrderInfo
     */
    public function setDeliveryHousenrext($deliveryHousenrext): OrderInfo
    {
        $this->deliveryHousenrext = $deliveryHousenrext;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryZipcode()
    {
        return $this->deliveryZipcode;
    }

    /**
     * @param string $deliveryZipcode
     * @return OrderInfo
     */
    public function setDeliveryZipcode($deliveryZipcode): OrderInfo
    {
        $this->deliveryZipcode = $deliveryZipcode;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryZipcode2()
    {
        return $this->deliveryZipcode2;
    }

    /**
     * @param string $deliveryZipcode2
     * @return OrderInfo
     */
    public function setDeliveryZipcode2($deliveryZipcode2): OrderInfo
    {
        $this->deliveryZipcode2 = $deliveryZipcode2;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryCity()
    {
        return $this->deliveryCity;
    }

    /**
     * @param string $deliveryCity
     * @return OrderInfo
     */
    public function setDeliveryCity($deliveryCity): OrderInfo
    {
        $this->deliveryCity = $deliveryCity;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryCountry()
    {
        return $this->deliveryCountry;
    }

    /**
     * @param string $deliveryCountry
     * @return OrderInfo
     */
    public function setDeliveryCountry($deliveryCountry): OrderInfo
    {
        $this->deliveryCountry = $deliveryCountry;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryPhonenr()
    {
        return $this->deliveryPhonenr;
    }

    /**
     * @param string $deliveryPhonenr
     * @return OrderInfo
     */
    public function setDeliveryPhonenr($deliveryPhonenr): OrderInfo
    {
        $this->deliveryPhonenr = $deliveryPhonenr;
        return $this;
    }

    /**
     * @return string
     */
    public function getDeliveryCompany()
    {
        return $this->deliveryCompany;
    }

    /**
     * @param string $deliveryCompany
     * @return OrderInfo
     */
    public function setDeliveryCompany($deliveryCompany): OrderInfo
    {
        $this->deliveryCompany = $deliveryCompany;
        return $this;
    }


    /**
     * @return ArrayCollection|Coupon[]
     */
    public function getCoupons()
    {
        return $this->coupons;
    }

    /**
     * @param ArrayCollection|Stock[] $coupons
     * @return OrderInfo
     */
    public function setCoupons($coupons): OrderInfo
    {
        $this->coupons = $coupons;

        return $this;
    }

    /**
     * @return LedgerField[]|ArrayCollection
     */
    public function getLedgerFields()
    {
        return $this->ledgerFields;
    }

    /**
     * @param LedgerField[]|ArrayCollection $ledgerFields
     * @return OrderInfo
     */
    public function setLedgerFields($ledgerFields): OrderInfo
    {
        $this->ledgerFields = $ledgerFields;
        return $this;
    }

    /**
     * @return ArrayCollection|LegacyRma[]
     */
    public function getLegacyRmas()
    {
        return $this->legacyRmas;
    }

    /**
     * @param ArrayCollection|LegacyRma[] $legacyRmas
     * @return OrderInfo
     */
    public function setLegacyRmas($legacyRmas): OrderInfo
    {
        $this->legacyRmas = $legacyRmas;
        return $this;
    }

    /**
     * Bereken de totale waarde van deze order.
     * Komt uit de website
     *
     * @param boolean $includeOtherCosts
     * @param boolean $excludePaymentCosts
     * @param boolean $excludeCoupons
     * @param boolean $excludeShippingCosts
     * @param bool $incVat
     * @param bool $includeDiscountCosts
     * @param bool $includeReturnsValue
     * @param bool $checkVatFree
     * @param bool $calculateDiscount
     * @return float
     */
    public function getTotalValue(
        $includeOtherCosts = true,
        $excludePaymentCosts = false,
        $excludeCoupons = false,
        $excludeShippingCosts = false,
        $incVat = true,
        $includeDiscountCosts = false,
        $includeReturnsValue = false,
        $checkVatFree = false,
        bool $calculateDiscount = false,
        bool $includeComboParents = true
    ): float {
        $totalValue = 0;
        $cartItems = $this->getCartitems();
        $cartItemRepository = $this->entityManager->getRepository(CartItem::class);

        // Gaan we uit van gezette BTW flag of meegegeven parameter incVat?
        $vatCheck = $checkVatFree === true && $this->isVatFree();
        $incVat = $vatCheck === false ? $incVat : false;
        $taxFree = !$incVat;

        // Get all valid cart item parents
        $validParents = [];
        foreach ($cartItems as $cartItem) {
            if($cartItem->getParentId() === 0 || in_array($cartItem->getParentId(), $validParents)) {
                continue;
            }

            if (count($cartItemRepository->findBy(['id' => $cartItem->getParentId()])) !== 0) {
                $validParents[] = $cartItem->getParentId();
            }
        }

        // Alle producten in deze bestelling.
        foreach ($cartItems as $cartItem) {
            try {
                if ($cartItem->getStatus() & CartItem::FLAG_IS_PARENT_WITHOUT_OWN_STOCK && !in_array($cartItem->getId(), $validParents) && !$includeComboParents) {
                    continue;
                }

                // Because entity lazy loading is a beautiful thing
                if ($cartItem->getParentId() === 0 || !in_array($cartItem->getParentId(), $validParents)) {
                    $totalValue += $cartItem->getTotal($taxFree, true);
                }
            } catch (EntityNotFoundException) {
                // Entity of type 'Webdsign\GlobalBundle\Entity\Product' for IDs id(xxxx) was not found slikken we
                // met opzet, in productie zou dit niet voor moeten komen
            }
        }

        if ($includeOtherCosts === false) {
            return $totalValue;
        }

        // Grootboekvelden meenemen.
        $fields = $this->getLedgerFields();

        if ($fields) {
            foreach ($fields as $field) {
                $totalValue += $incVat === true ? $field->getAmount() : $field->getAmountEx();
            }
        }

        // En de verzendkosten.
        if ($excludeShippingCosts === false) {
            $totalValue += $incVat === true ? $this->shippingCosts : $this->shippingCostsEx;
        }

        // Minus cadeaubonnen.
        if ($excludeCoupons === false) {
            $coupons = $this->getCoupons();
            foreach ($coupons as $coupon) {
                $totalValue -= $coupon->getAmount();
            }
        }

        // Extra betaalkosten uitrekenen.
        if ($excludePaymentCosts === false) {
            $totalValue += $incVat === true ? $this->paymentCosts : $this->paymentCostsEx;
        }

        // Aanbiedings korting erafhalen bedragen zijn - dus + gebruiken.
        if ($includeDiscountCosts !== false) {
            if ($calculateDiscount === true) {
                $totalValue += $this->getDiscount($taxFree);
            } else {
                $totalValue += $this->getDiscountCosts();
            }
        }

        //retourtjes erafhalen
        if ($includeReturnsValue !== false) {
            $returns = $this->getArticlesReturn();
            /** @var ArticleReturns $return */
            foreach ($returns as $return) {
                $totalValue -= $incVat === true ? $return->getPrice() : $return->getPriceEx();
            }
        }

        if (abs($totalValue) < 0.001) {
            $totalValue = 0;
        }

        return $totalValue;
    }

    /**
     * @return bool
     */
    public function isVatFree(): bool
    {
        return ($this->flags & self::FLAG_NO_VAT) === self::FLAG_NO_VAT;
    }

    /**
     * @return void
     */
    public function setVatFree(): void
    {
        $this->setFlags($this->getFlags() | self::FLAG_NO_VAT);
    }

    /**
     * @return float
     */
    public function getTotalPurchaseValue(): float
    {
        $totalValue = 0;
        $stockItems = $this->getStockItems();
        if ($stockItems) {
            foreach ($stockItems as $stockItem) {
                $totalValue += $stockItem->getPurchasePrice();
            }
        }

        if ($totalValue < 0.001) {
            $totalValue = 0;
        }

        return $totalValue;
    }

    /**
     * Is dit een geannuleerde order?
     *
     * @return bool
     */
    public function isCancelledOrder(): bool
    {
        $shippingMethod = $this->getShippingMethod();
        return $shippingMethod instanceof ShipmentMethod && $shippingMethod->getId() === ShipmentMethod::CANCELLED;
    }

    /**
     * Is dit een retour order?
     *
     * @return bool
     */
    public function isReturnedOrder()
    {
        $articleReturns = $this->getArticlesReturn();

        if (is_object($articleReturns) && count($articleReturns) > 0) {
            return true;
        }

        return false;
    }

    /**
     * @return bool
     */
    public function isQuotation(): bool
    {
        $shippingMethod = $this->getShippingMethod();
        return $shippingMethod && $shippingMethod->isQuotation();
    }

    /**
     * @return Parking|null
     */
    public function getParking(): ?Parking
    {
        $parking = $this->parking;

        // Set parking naar null omdat 0 een niet bestaande parking entity is en een error veroorzaakt.
        if ($parking === 0) {
            return null;
        }

        return $this->parking;
    }

    /**
     * @param Parking|null $parking
     * @return OrderInfo
     */
    public function setParking(?Parking $parking): OrderInfo
    {
        $this->parking = $parking;
        return $this;
    }

    /**
     * @return int
     */
    public function getDomainId()
    {
        return $this->domainId;
    }

    /**
     * @return string
     */
    public function getIpNumber()
    {
        return $this->ipNumber;
    }

    /**
     * @param string $ipNumber
     * @return OrderInfo
     */
    public function setIpNumber(string $ipNumber): OrderInfo
    {
        $this->ipNumber = $ipNumber;

        return $this;
    }

    /**
     * @return null|OrderParking
     */
    public function getOrderParking(): ?OrderParking
    {
        return $this->orderParking;
    }

    /**
     * @param OrderParking $orderParking
     * @return OrderInfo
     */
    public function setOrderParking($orderParking): OrderInfo
    {
        $this->orderParking = $orderParking;

        return $this;
    }

    /**
     * @return ArrayCollection|BackOrder[]
     */
    public function getBackOrders()
    {
        return $this->backOrders;
    }

    /**
     * @param ArrayCollection|BackOrder[] $backOrders
     * @return OrderInfo
     */
    public function setBackOrders($backOrders): OrderInfo
    {
        $this->backOrders = $backOrders;
        return $this;
    }

    /**
     * @return ArrayCollection|BackOrder[]
     */
    public function getBackOrderParentOrders()
    {
        return $this->backOrderParentOrders;
    }

    /**
     * @param ArrayCollection|BackOrder[] $backOrderParentOrders
     * @return OrderInfo
     */
    public function setBackOrderParentOrders($backOrderParentOrders): ?OrderInfo
    {
        $this->backOrderParentOrders = $backOrderParentOrders;
        return $this;
    }

    /**
     * @return OrderWait
     */
    public function getOrderWait()
    {
        return $this->orderWait;
    }

    /**
     * @param OrderWait $orderWait
     * @return $this
     */
    public function setOrderWait(OrderWait $orderWait): OrderInfo
    {
        $this->orderWait = $orderWait;

        return $this;
    }

    /**
     * @return float
     */
    public function getShippingCosts(): float
    {
        return $this->shippingCosts;
    }

    /**
     * @param float $shippingCosts
     * @return OrderInfo
     */
    public function setShippingCosts(float $shippingCosts): OrderInfo
    {
        $this->shippingCosts = $shippingCosts;
        return $this;
    }

    /**
     * @return float
     */
    public function getShippingCostsEx(): float
    {
        return $this->shippingCostsEx;
    }

    /**
     * @param float $shippingCostsEx
     * @return OrderInfo
     */
    public function setShippingCostsEx(float $shippingCostsEx): OrderInfo
    {
        $this->shippingCostsEx = $shippingCostsEx;

        return $this;
    }

    /**
     * @return float
     */
    public function getPaymentCosts(): float
    {
        return $this->paymentCosts;
    }

    /**
     * @param float $paymentCosts
     * @return OrderInfo
     */
    public function setPaymentCosts(float $paymentCosts): OrderInfo
    {
        $this->paymentCosts = $paymentCosts;
        return $this;
    }

    /**
     * @return float
     */
    public function getPaymentCostsEx(): float
    {
        return $this->paymentCostsEx;
    }

    /**
     * @param float $paymentCostsEx
     * @return OrderInfo
     */
    public function setPaymentCostsEx(float $paymentCostsEx): OrderInfo
    {
        $this->paymentCostsEx = $paymentCostsEx;

        return $this;
    }

    /**
     * @deprecated use orderInfo::getDiscount instead
     * @return float
     */
    public function getDiscountCosts(): float
    {
        return $this->discountCosts;
    }

    /**
     * @param float $discountCosts
     * @return OrderInfo
     */
    public function setDiscountCosts(float $discountCosts): OrderInfo
    {
        $this->discountCosts = $discountCosts;
        return $this;
    }

    public function getDiscountCostsEx(): float
    {
        return $this->discountCostsEx;
    }

    public function setDiscountCostsEx(float $discountCostsEx): OrderInfo
    {
        $this->discountCostsEx = $discountCostsEx;
        return $this;
    }

    /**
     * @return bool
     */
    public function hasBackOrders(): bool
    {
        return $this->getBackOrders()->count() > 0;
    }

    /**
     * @return bool
     */
    public function isBackOrder(): bool
    {
        return $this->getBackOrderParentOrders()->count() > 0;
    }

    /**
     * @return OrderInfo[]
     */
    public function getBackOrderOrders(): array
    {
        $backOrderOrders = [];
        foreach ($this->getBackOrders() as $backOrder) {
            $backOrderOrders[] = $backOrder->getNewOrder();
        }

        return $backOrderOrders;
    }

    /**
     * @return OrderInfo[]
     */
    public function getBackOrderParentOrderOrders(): array
    {
        $backOrderOrders = [];
        foreach ($this->getBackOrders() as $backOrder) {
            $backOrderOrders[] = $backOrder->getNewOrder();
        }

        return $backOrderOrders;
    }

    /**
     * @param PreUpdateEventArgs|LifecycleEventArgs $eventArgs
     * @return void
     */
    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function fixName($eventArgs): void
    {
        $fixName = false;
        $fixDeliveryName = false;
        if ($eventArgs instanceof PreUpdateEventArgs) {
            if (
                $eventArgs->hasChangedField('firstName')
                || $eventArgs->hasChangedField('lastName')
                || $eventArgs->hasChangedField('nameInsertion')
            ) {
                $fixName = true;
            }
            if (
                $eventArgs->hasChangedField('deliveryFirstName')
                || $eventArgs->hasChangedField('deliveryLastName')
                || $eventArgs->hasChangedField('deliveryNameInsertion')
            ) {
                $fixDeliveryName = true;
            }
        } else {
            $fixName = true;
            $fixDeliveryName = true;
        }

        if ($fixName) {
            $name = trim(implode(' ', [
                !empty($this->getFirstName()) ? $this->getFirstName() : '',
                !empty($this->getNameInsertion()) ? $this->getNameInsertion() : '',
                !empty($this->getLastName()) ? $this->getLastName() : '',
            ]));
            if (!empty($name)) {
                $this->setName($name);
            }
        }

        if ($fixDeliveryName) {
            $name = trim(implode(' ', [
                !empty($this->getDeliveryFirstName()) ? $this->getDeliveryFirstName() : '',
                !empty($this->getDeliveryNameInsertion()) ? $this->getDeliveryNameInsertion() : '',
                !empty($this->getDeliveryLastName()) ? $this->getDeliveryLastName() : '',
            ]));
            if (!empty($name)) {
                $this->setDeliveryName($name);
            }
        }
    }

    /**
     * Probeer zo goed mogelijk een naam van de klant te "verzinnen",
     * blijkbaar is het niet verplicht dit goed in te vullen...
     *
     * @return string[]
     */
    public function tryRetrievingCustomerName(): array
    {
        // eerst zoals het hoort, gewoon de voor en achternaam achterhalen adhv de order
        $firstName = $this->getFirstName();
        $lastNamePrefix = $this->getNameInsertion();
        $lastName = $this->getLastName();

        // niet via de order? Dan via de klant van de order proberen
        $customer = $this->getCustomer();
        if (empty($firstName) && empty($lastName) && $customer instanceof Customer) {
            $firstName = $customer->getFirstName();
            $lastNamePrefix = $customer->getLastNamePrefix();
            $lastName = $customer->getLastName();
        }

        // anders dan via het "oude" naam-veld van de order
        if (empty($firstName) && empty($lastName)) {
            $firstName = '';
            $lastNamePrefix = '';
            $lastName = $this->getName();
        }

        // of via het "oude" naam-veld van de klant
        if (empty($firstName) && empty($lastName) && $customer instanceof Customer) {
            $firstName = '';
            $lastNamePrefix = '';
            $lastName = $customer->getName();
        }

        // nog niet? kijk of de klant wellicht een of meerdere extra adressen heeft met een naam...
        if (empty($firstName) && empty($lastName) && $customer instanceof Customer) {
            foreach ($customer->getAddresses() as $customerAddress) {
                if (empty($firstName) && empty($lastName)) {
                    $firstName = $customerAddress->getFirstName();
                    $lastNamePrefix = $customerAddress->getLastNamePrefix();
                    $lastName = $customerAddress->getLastName();
                }

                // ook hierbij checken via het "oude" naam-veld
                if (empty($firstName) && empty($lastName)) {
                    $firstName = '';
                    $lastNamePrefix = '';
                    $lastName = $customerAddress->getName();
                }
            }
        }

        // anders het afleveradres van de order?
        if (empty($firstName) && empty($lastName)) {
            $firstName = $this->getDeliveryFirstName();
            $lastNamePrefix = $this->getDeliveryNameInsertion();
            $lastName = $this->getDeliveryLastName();
        }

        // en anders ook van het afleveradres via het "oude" naam-veld proberen
        if (empty($firstName) && empty($lastName)) {
            $firstName = '';
            $lastNamePrefix = '';
            $lastName = $this->getDeliveryName();
        }

        // nog altijd geen naam? probeer dan een bedijfsnaam...
        if (empty($firstName) && empty($lastName)) {
            $firstName = '';
            $lastNamePrefix = '';
            $lastName = $this->getCompany();
        }

        // wellicht heeft de klant een bedrijfsnaam?
        if (empty($firstName) && empty($lastName) && $customer instanceof Customer) {
            $firstName = '';
            $lastNamePrefix = '';
            $lastName = $this->getCompany();
        }

        // als laatste optie dan maar het user-gedeelte van het e-mailadres gebruiken...
        if (empty($firstName) && empty($lastName)) {
            $email = $this->getEmail();

            // of het e-mailadres van de klant...
            if (empty($email) && $customer instanceof Customer) {
                $email = $customer->getEmail();

                // of eventuele extra e-mailadressen dan?
                if (empty($email)) {
                    foreach ($customer->getContacts() as $customerContact) {
                        if (empty($email) && $customerContact->getType()->getIcon() === 'email') {
                            $email = $customerContact->getValue();
                        }
                    }
                }
            }
            if (!empty($email)) {
                $firstName = '';
                $lastNamePrefix = '';
                $lastName = ucwords(strtolower(str_replace('.', ' ', explode('@', $email, 2)[0])));
            }
        }

        // en nu heb ik geen opties meer: Hallo meneer/mevrouw Onbekend!
        if (empty($firstName) && empty($lastName)) {
            $firstName = '';
            $lastNamePrefix = '';
            $lastName = 'Onbekend';
        }

        // Blijft de situatie over dat er alleen een voornaam bekend is,
        // in dat geval wordt de voornaam de achternaam (voornaam is nu weer leeg!)
        if (empty($lastName)) {
            $lastName = $firstName;
            $firstName = '';
        }

        // En als de voornaam (nu) leeg is, dan gaan we er eentje voor deze klant bedenken...
        if (empty($firstName)) {
            $splitPoint = strpos($lastName, ' '); // splitsen op de eerste spatie
            if ($splitPoint !== false) {
                $firstName = substr($lastName, 0, $splitPoint);
                $lastName = substr($lastName, $splitPoint); // en de "voornaam" van de achternaam af halen
            } else {
                // geen spatie? Dan maar de eerste letter...
                $firstName = $lastName[0];
            }
        }

        return [
            'firstName' => $firstName,
            'lastNamePrefix' => $lastNamePrefix,
            'lastName' => $lastName,
        ];
    }

    /**
     * Returns the total revenue of an order based on payments received
     *
     * @return float
     */
    public function getOrderRevenue(): float
    {
        $orderRevenue = 0.00;
        $orderPayments = $this->getOrderPayments();
        foreach ($orderPayments as $orderPayment) {
            $orderRevenue += $orderPayment->getAmount();
        }
        return $orderRevenue;
    }

    /**
     * @return Collection|InternalInvoice[]
     */
    public function getInternalInvoices()
    {
        return $this->internalInvoices;
    }

    /**
     * @param Collection|InternalInvoice[] $internalInvoices
     * @return OrderInfo
     */
    public function setInternalInvoices($internalInvoices): OrderInfo
    {
        $this->internalInvoices = $internalInvoices;

        return $this;
    }

    public function __toString()
    {
        return (string) $this->getId();
    }

    /**
     * @return mixed
     */
    public function getArticlesReturn()
    {
        return $this->articlesReturn;
    }

    /**
     * @param mixed $articlesReturn
     * @return OrderInfo
     */
    public function setArticlesReturn($articlesReturn): OrderInfo
    {
        $this->articlesReturn = $articlesReturn;
        return $this;
    }

    public function hasDeadOnArrivals(): bool
    {
        return $this->getArticlesDeadOnArrival()->count() > 0;
    }

    public function getArticlesDeadOnArrival()
    {
        return $this->articlesDeadOnArrival;
    }

    /**
     * @param mixed $articlesDeadOnArrival
     */
    public function setArticlesDeadOnArrival($articlesDeadOnArrival): OrderInfo
    {
        $this->articlesDeadOnArrival = $articlesDeadOnArrival;
        return $this;
    }

    /**
     * @return string
     */
    public function getReference()
    {
        return $this->reference;
    }

    /**
     * @param string $reference
     * @return OrderInfo
     */
    public function setReference(string $reference): OrderInfo
    {
        $this->reference = $reference;
        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getImportedInAnalytics(): ?DateTime
    {
        return $this->importedInAnalytics;
    }

    /**
     * @param DateTime|null $importedInAnalytics
     * @return OrderInfo
     */
    public function setImportedInAnalytics(?DateTime $importedInAnalytics): OrderInfo
    {
        $this->importedInAnalytics = $importedInAnalytics;

        return $this;
    }

    /**
     * @return bool
     */
    public function isChecked(): bool
    {
        return $this->checked;
    }

    /**
     * @return OrderInfo
     */
    public function toggleCheck(): OrderInfo
    {
        $this->checked = !$this->checked;
        return $this;
    }

    /**
     * @param bool $checked
     *
     * @return OrderInfo
     */
    public function setChecked($checked = false): OrderInfo
    {
        $this->checked = $checked;
        return $this;
    }

    /**
     * @return bool
     */
    public function isPayedWithAdyen(): bool
    {
        foreach ($this->orderPayments as $payment) {
            if ($payment->getPaymentType()->getId() === AdyenOrderCheckFilter::ADYEN_PAYMENT_TYPE) {
                return true;
            }
        }

        return false;
    }

    /**
     * @return bool
     */
    public function isGoedhartOk(): bool
    {
        return ($this->flags & self::FLAG_GOEDHART_OK) === self::FLAG_GOEDHART_OK;
    }

    public function setGoedhartOk(): void
    {
        $this->setFlags($this->getFlags() | self::FLAG_GOEDHART_OK);
    }

    /**
     * @return Collection|AdyenBatchReport[]
     */
    public function getAdyenBatchReports()
    {
        return $this->adyenBatchReports;
    }

    /**
     * @return float
     */
    public function getTotalInAdyenBatchReports(): float
    {
        $total = 0.;

        foreach ($this->getAdyenBatchReports() as $batchReport) {
            $total += $batchReport->getGrossCredit() - $batchReport->getGrossDebit() + $batchReport->getCommission();
        }

        return round($total, 2);
    }

    /**
     * @return void
     */
    public function setPayed(): void
    {
        $this->setFlags($this->getFlags() | self::FLAG_PAYMENT_COMPLETE + self::FLAG_PAYMENT_OKAY);
    }

    /**
     * @return void
     */
    public function setNotInPaymentScreen(): void
    {
        $this->setFlags($this->getFlags() & ~self::FLAG_IN_PAYMENT_SCREEN);
    }

    /**
     * @return void
     */
    public function setInPaymentScreen(): void
    {
        $this->setFlags($this->getFlags() | self::FLAG_IN_PAYMENT_SCREEN);
    }

    /**
     * @return bool
     */
    public function isInPaymentScreen(): bool
    {
        return ($this->getFlags() & self::FLAG_IN_PAYMENT_SCREEN) === self::FLAG_IN_PAYMENT_SCREEN;
    }

    /**
     * @return Collection|AdyenLog[]
     */
    public function getAdyenLogs()
    {
        return $this->adyenLogs;
    }

    /**
     * @return PSP\Log[]|ArrayCollection
     */
    public function getPspLogs(): Collection
    {
        return $this->pspLogs;
    }

    /**
     * @return OrderBox[]|Collection
     */
    public function getOrderBoxes()
    {
        return $this->orderBoxes;
    }

    /**
     * @return CancellationLog[]|Collection
     */
    public function getCancellationLogs()
    {
        return $this->cancellationLogs;
    }


    public function getVerifyLog()
    {
        $criteria = Criteria::create()
            ->where(Criteria::expr()->eq('type', AdyenLog::TYPE_VERIFY))
            ->orderBy(['id' => Criteria::DESC]);

        return $this->getAdyenLogs()->matching($criteria)->current();
    }

    /**
     * @return false|AdyenLog
     */
    public function getPaymentLog()
    {
        $criteria = Criteria::create()
            ->where(Criteria::expr()->eq('type', AdyenLog::TYPE_PAYMENT))
            ->orderBy(['id' => Criteria::DESC]);

        return $this->getAdyenLogs()->matching($criteria)->current();
    }

    /**
     * @return bool
     */
    public function hasReturns(): bool
    {
        return $this->getArticlesReturn()->count() > 0;
    }

    /**
     * @return ReferrerMail|null
     */
    public function getReferrerMail(): ?ReferrerMail
    {
        return $this->referrerMail;
    }

    /**
     * @param ReferrerMail $referrerMail
     * @return OrderInfo
     */
    public function setReferrerMail(ReferrerMail $referrerMail): OrderInfo
    {
        $this->referrerMail = $referrerMail;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getGaClientId(): ?string
    {
        return $this->gaClientId;
    }

    /**
     * @param string $gaClientId
     * @return OrderInfo
     */
    public function setGaClientId(string $gaClientId): OrderInfo
    {
        $this->gaClientId = $gaClientId;
        return $this;
    }

    /**
     * @return bool
     */
    public function hasPartialShipping(): bool
    {
        return ($this->getFlags() & self::FLAG_PARTIAL_DELIVERY) === self::FLAG_PARTIAL_DELIVERY;
    }

    /**
     * @param bool $includeDiscountCosts
     * @param bool $includeReturns
     * @param bool $includeStillToReceive
     * @param array $excludePaymentMethods
     * @return bool
     */
    public function isPaid(
        bool $includeDiscountCosts = false,
        bool $includeReturns = false,
        bool $includeStillToReceive = true,
        array $excludePaymentMethods = [],
        bool $calculateDiscountCosts = false,
    ): bool {
        // Get total value for order
        $orderTotalValue = $this->getTotalValue(
            true,
            false,
            false,
            false,
            true,
            $includeDiscountCosts,
            $includeReturns,
            calculateDiscount: $calculateDiscountCosts
        );

        // Get total paid
        $orderPayments = $this->getOrderPayments();
        $totalPaid = $this->getTotalPaid($includeStillToReceive, $orderPayments, $excludePaymentMethods);

        // return true if order has 1 or more payment rows and the paid check box is green
        return (count($orderPayments) > 0 && ((float)abs((float)$totalPaid - (float)$orderTotalValue) <= 0.01));
    }

    /**
     * @param bool $includeStillToReceive
     * @param null|array|PersistentCollection|ArrayCollection $orderPayments
     * @param array $excludePaymentMethods
     * @return float
     */
    public function getTotalPaid(
        bool $includeStillToReceive = true,
        $orderPayments = null,
        array $excludePaymentMethods = []
    ): float {
        if (null === $orderPayments) {
            $orderPayments = $this->getOrderPayments();
        }

        $totalPaid = 0.;

        /** @var ExternalPaymentRepository $externalPaymentRepository */
        $externalPaymentRepository = $this->entityManager->getRepository(ExternalPayment::class);

        foreach ($orderPayments as $orderPayment) {
            $orderPaymentType = $orderPayment->getPaymentType() ?? null;
            $skipPayment = false;

            // Check if we need to exclude payment methods
            if ($orderPaymentType instanceof PaymentType && !empty($excludePaymentMethods)) {
                // Check payment type
                if (in_array(strtolower($orderPaymentType->getDescription()), $excludePaymentMethods)) {
                    $skipPayment = true;

                    // Check Adyen payment type
                } elseif (
                    $orderPaymentType->getId() === PaymentType::TYPE_ADYEN &&
                    in_array(strtolower($orderPayment->getAdyenType()), $excludePaymentMethods)
                ) {
                    // Check if afterpay payment status is success
                    switch (strtolower($orderPayment->getAdyenType())) {
                        case 'afterpay_default':
                            // check if psp reference is set, otherwise skip this payment
                            if (empty($orderPayment->getAdyenPspReference())) {
                                $skipPayment = true;
                                break;
                            }

                            // Try to get the external payment information
                            $externalPayment = $externalPaymentRepository->findOneByOrderAndToken(
                                $this,
                                $orderPayment->getAdyenPspReference()
                            );

                            // If external payment is not found or status is not success, skip this payment
                            if (
                                !$externalPayment instanceof ExternalPayment ||
                                $externalPayment->getStatus() !== ExternalPayment::STATUS_SUCCESS
                            ) {
                                $skipPayment = true;
                                break;
                            }

                            break;
                    }
                } elseif ($orderPayment->getPaymentType() === PaymentType::SPRAYPAY_PAYMENT_ID) {
                    $externalPayment = $externalPaymentRepository->findOneByOrderAndToken(
                        $this,
                        $orderPayment->getAdyenPspReference()
                    );

                    if (
                        !$externalPayment instanceof ExternalPayment ||
                        $externalPayment->getStatus() !== ExternalPayment::STATUS_SUCCESS
                    ) {
                        $skipPayment = true;
                    }
                }
            }

            // Skip payment if we need to
            if ($skipPayment) {
                continue;
            }

            // Add payment tot totals
            $totalPaid += $orderPayment->getAmount();
        }

        return $totalPaid;
    }

    public function getUnpaidAmount(
        ?float $totalValue = null,
        bool $includeDiscountCosts = false,
        bool $includeReturns = false
    ): float {
        if (null === $totalValue) {
            $totalValue = $this->getTotalValue(
                true,
                false,
                false,
                false,
                true,
                $includeDiscountCosts,
                $includeReturns
            );
        }

        $paid = self::getTotalPaid();

        return $totalValue - $paid;
    }

    /**
     * @return OrderReference|null
     */
    public function getOrderReference(): ?OrderReference
    {
        return $this->orderReference;
    }

    /**
     * @param OrderReference $orderReference
     * @return OrderInfo
     */
    public function setOrderReference(OrderReference $orderReference): OrderInfo
    {
        $this->orderReference = $orderReference;
        return $this;
    }

    /**
     * @return bool
     */
    public function hasReference(): bool
    {
        return (bool) ($this->getOrderReference() instanceof OrderReference);
    }

    /**
     * @return bool
     */
    public function getPreventAutomaticCancel(): bool
    {
        if ($this->preventAutomaticCancel === null) {
            $this->setPreventAutomaticCancel(false);
        }

        return $this->preventAutomaticCancel;
    }

    /**
     * @param bool $preventAutomaticCancel
     * @return OrderInfo
     */
    public function setPreventAutomaticCancel(bool $preventAutomaticCancel): OrderInfo
    {
        $this->preventAutomaticCancel = $preventAutomaticCancel;
        return $this;
    }

    /**
     * @return string
     */
    public function getIssuerId(): string
    {
        return $this->issuerId;
    }

    /**
     * @param string $issuerId
     * @return OrderInfo
     */
    public function setIssuerId(string $issuerId): OrderInfo
    {
        $this->issuerId = $issuerId;
        return $this;
    }

    /**
     * Bereken aanbiedingskorting
     * vroeger was dit orderInfo::getDiscountCost()
     *
     * @param bool $excludeVat
     * @return float|int
     */
    public function getDiscount(bool $excludeVat = false): float
    {
        if ($this->discount_inc !== null && $excludeVat === false) {
            return $this->discount_inc;
        }

        if ($this->discount_ex !== null && $excludeVat === true) {
            return $this->discount_ex;
        }

        $discount = 0;

        /**
         * @var cartItem $cartItem
         * Producten met korting niet meenemen
         */
        foreach ($this->getCartitems() as $cartItem) {
            $check = false;

            /**
             * Door gepruts met kits in de admin heb je soms children waarvan de parent niet meer bestaat,
             * dit levert rare verschillen op dus even checken of parent er nog is
             * anders skippen we het zaakkien
             */
            if ($cartItem->getParentId() !== 0) {
                try {
                    /**
                     * Je krijgt een niet geïnitialiseerd object dus even een random getter aanroepen
                     * om een EntityNotFoundException te triggeren die we kunnen afvangen
                     */
                    $check = (bool) $cartItem->getParent()->getOrderId();
                } catch (EntityNotFoundException $e) {
                }
            } else {
                $check = true;
            }

            if ($check === true) {
                try {
                    // This try catch block is a work-around.
                    // A cart item might refer to a deleted product (orphaned entities).
                    $product = $cartItem->getProduct();
                    $validProduct = !$product->isWeekOffer() || $product->isKitOrSetChoice();
                } catch (Throwable $exception) {
                    $validProduct = false;
                }
            } else {
                // Dit gebeurt alleen bij shopcartregels zonder geldige parent
                // In multivers worden deze gewoon geëxporteerd dus dat doen we hier ook maar
                $validProduct = true;
            }

            if ($validProduct || $cartItem->isFree()) {
                if ($excludeVat === true) {
                    $discountPrice = (($cartItem->getPriceDiscount() / (100 + $cartItem->getTax())) * 100);
                    $discount += $discountPrice * $cartItem->getAmount();
                    continue;
                }

                $discount += $cartItem->getPriceDiscount() * $cartItem->getAmount();
            }
        }

        if ($excludeVat === false) {
            $this->discount_inc = $discount;
            return $this->discount_inc;
        }

        $this->discount_ex = $discount;
        return $this->discount_ex;
    }

    /**
     * @return bool
     */
    public function isWebOrder(): bool
    {
        return in_array($this->getOrigin()->getId(), Origin::WEB_ORIGINS);
    }

    /**
     * @return Postnl[]
     */
    public function getPostnls(): Collection
    {
        return $this->postnls;
    }

    /**
     * @return bool
     */
    public function isEligibleForCompletion(): bool
    {
        return self::getInvoiceNumber() !== null &&
            self::getDateInvoice() !== null &&
            self::isPaid();
    }

    /**
     * @return void
     */
    public function setOrderComplete(): void
    {
        $this->setFlags($this->getFlags() | self::FLAG_ORDER_COMPLETE);
    }

    public function getExtraInvoiceInfo(): string
    {
        return $this->extraInvoiceInfo;
    }

    public function setExtraInvoiceInfo(string $extraInvoiceInfo): OrderInfo
    {
        $this->extraInvoiceInfo = $extraInvoiceInfo;
        return $this;
    }

    public function isVendiroOrder(): bool
    {
        return in_array($this->getOrigin()->getParent(), Origin::VENDIRO_PARENTS);
    }

    public function getOrderHistories(): Collection
    {
        return $this->orderHistories;
    }

    public function getTrackingRecord()
    {
        // extract transporter from shipping method
        $shippingMethod = $this->getShippingMethod();
        if (!$shippingMethod instanceof ShipmentMethod) {
            return null;
        }

        $transporter = $shippingMethod->getTransporter();
        if ($transporter->getId() === 0) {
            return null;
        }

        $objName = ucfirst($transporter->getShort());
        $trackingClassName = '\Webdsign\GlobalBundle\Entity\\' . $objName;

        if (!class_exists($trackingClassName)) {
            return null;
        }

        $trackingRepository = $this->entityManager->getRepository($trackingClassName);
        // find tracking record
        $trackingRecord = $trackingRepository->findByOrder($this, false);


        if (is_a($trackingRecord, $trackingClassName)) {
            return $trackingRecord;
        }

        return null;
    }

    public function getTrackingZipcode(): string
    {
        $zip = $this->getDeliveryZipcode() !== '' ? $this->getDeliveryZipcode() : $this->getZipcode();

        if (in_array($this->getDeliveryName(), ['Pakje Gemak', 'PakjeGemak'])) {
            $zip = $this->getZipcode();
        }

        return trim(str_replace(' ', '', $zip));
    }

    public function getPaymentPeriod(): ?PaymentPeriod
    {
        return $this->paymentPeriod;
    }

    public function setPaymentPeriod(?PaymentPeriod $paymentPeriod): OrderInfo
    {
        $this->paymentPeriod = $paymentPeriod;
        return $this;
    }

    public function isPickup(): bool
    {
        $shippingMethod = $this->getShippingMethod();

        return $shippingMethod === null || in_array(
            $shippingMethod->getId(),
            [...ShipmentMethod::PICK_UP_METHODS, ShipmentMethod::CASH],
            true
        );
    }

    /**
     * @return OrderAnalyticsInfo[]|Collection
     */
    public function getOrderAnalyticsInfo(): Collection
    {
        return $this->orderAnalyticsInfo;
    }

    public function setOrderAnalyticsInfo(Collection $orderAnalyticsInfo): OrderInfo
    {
        $this->orderAnalyticsInfo = $orderAnalyticsInfo;
        return $this;
    }

    public function getPspReference(): ?string
    {
        return $this->pspReference;
    }

    public function setPspReference(?string $pspReference): OrderInfo
    {
        $this->pspReference = $pspReference;
        return $this;
    }

    public function isEventOrder(): bool
    {
        if (count($this->cartitems) === 0) {
            return false;
        }

        /** @var CartItem $cartItem */
        $cartItem = $this->cartitems->first();
        if (!$cartItem instanceof CartItem) {
            return false;
        }

        return $cartItem->getProduct()->getEventProduct() instanceof Event\Product;
    }

    public function getDomainOrigin(): ?string
    {
        return $this->domainOrigin;
    }

    public function getRfmSegment(): ?string
    {
        return $this->rfmSegment;
    }

    public function setRfmSegment(?string $rfmSegment): OrderInfo
    {
        $this->rfmSegment = $rfmSegment;
        return $this;
    }

    public function setSendLaterDeliveryMailSent(): void
    {
        $this->setFlags($this->getFlags() | self::FLAG_SEND_LATER_URK_DELIVERY_MAIL_SENT);
    }

    public function hasSendLaterDeliveryMailSent(): bool
    {
        return ($this->getFlags() & self::FLAG_SEND_LATER_URK_DELIVERY_MAIL_SENT) === self::FLAG_SEND_LATER_URK_DELIVERY_MAIL_SENT;
    }
}
