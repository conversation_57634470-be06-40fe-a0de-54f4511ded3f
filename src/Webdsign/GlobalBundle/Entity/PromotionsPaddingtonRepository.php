<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Persistence\ManagerRegistry;

/**
 * @method PromotionsPaddington findOneBy(array $criteria, array $orderBy = null)
 * @method PromotionsPaddington find($id, int $lockMode = null, int $lockVersion = null)
 */
class PromotionsPaddingtonRepository extends WebdsignRepository {
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PromotionsPaddington::class);
    }
}
