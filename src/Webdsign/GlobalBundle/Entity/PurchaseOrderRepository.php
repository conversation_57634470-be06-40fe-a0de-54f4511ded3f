<?php

namespace Webdsign\GlobalBundle\Entity;

use CatBundle\Form\DataObject\GroupDataObject;
use CatBundle\Form\Filter\PurchaseOrder\ProductListFilter;
use DateTime;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method PurchaseOrder findOneBy(array $criteria, array $orderBy = null)
 * @method PurchaseOrder find($id, int $lockMode = null, int $lockVersion = null)
 */
class PurchaseOrderRepository extends WebdsignRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PurchaseOrder::class);
    }


    /**
     * @param Product $product
     * @param bool $useInvoicePrice
     * @return float|null
     */
    public function getAveragePurchasePriceFromCurrentOrders(Product $product, bool $useInvoicePrice = false) :? float
    {
        $dql = /** @lang DQL */"
            SELECT
                SUM(po.count * po.price) / SUM(po.count),
                SUM(po.count * po.invoicePrice) / SUM(po.count)
            FROM
                " . PurchaseOrder::class . " AS po
            WHERE
                po.product = :product
                AND
                BIT_AND(po.flags, :finishedFlag) != :finishedFlag
        ";

        $query = $this->getEntityManager()->createQuery($dql);
        $query->setMaxResults(1);
        $query->setParameters([
            'product' => $product,
            'finishedFlag' => PurchaseOrder::FLAG_FINISHED
        ]);

        try {
            $result = $query->getOneOrNullResult();
        } catch (NonUniqueResultException $e) {
            $result = null;
        }

        $field = 1;
        if ($useInvoicePrice) {
            $field = 2;
        }

        if ($result !== null && !empty($result[$field])) {
            return (float)$result[$field];
        }
        return null;
    }

    /**
     * @param Product $product
     * @param string $orderByDirection
     * @param int|null $limit
     * @return PurchaseOrder[]
     */
    public function getForProduct(
        Product $product,
        string $orderByDirection = 'ASC',
        ?int $limit = null,
        ?DateTime $dateFrom = null,
        ?DateTime $dateTill = null
    ): array {
        $queryBuilder = $this->createQueryBuilder('po');
        $queryBuilder
            ->select('po')
            ->where('po.product = :product')
            ->andWhere('BIT_AND(po.flags, :finishedFlag) != :finishedFlag')
            ->andWhere('BIT_AND(po.flags, :hideNotFinishedFlag) != :hideNotFinishedFlag')
            ->orderBy('po.deliveryDate', $orderByDirection)
            ->setParameter('product', $product)
            ->setParameter('finishedFlag', PurchaseOrder::FLAG_FINISHED)
            ->setParameter('hideNotFinishedFlag', PurchaseOrder::FLAG_HIDE_NOT_FINISHED);

        if ($limit !== null) {
            $queryBuilder->setMaxResults($limit);
        }

        if ($dateFrom instanceof DateTime) {
            $queryBuilder
                ->andWhere('po.orderDate >= :dateFrom')
                ->setParameter('dateFrom', $dateFrom);
        }

        if ($dateTill instanceof DateTime) {
            $queryBuilder
                ->andWhere('po.orderDate <= :dateTill')
                ->setParameter('dateTill', $dateTill);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    /**
     * @param Supplier $supplier
     * @return array
     */
    public function findForSupplier(Supplier $supplier): array
    {
        $queryBuilder = $this->createQueryBuilder('po');
        $queryBuilder
            ->select('po.productId, po.count, po.price, p.name')
            ->addSelect('group_concat(l.compartment) as locations')
            ->innerJoin('po.product', 'p')
            ->leftJoin('p.locationCompartmentNode', 'lcn')
            ->leftJoin('lcn.location', 'l')
            ->andWhere('po.supplier = :supplier')
            ->groupBy('po.productId')
            ->setParameter('supplier', $supplier);

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * @param array $suppliers
     * @return array
     */
    public function findForSuppliers(array $suppliers, ?int $userId = null): array
    {
        $queryBuilder = $this->createQueryBuilder('po');
        $queryBuilder
            ->select('po.productId, po.count, po.price, p.name')
            ->addSelect('group_concat(l.compartment) as locations')
            ->innerJoin('po.product', 'p')
            ->leftJoin('p.locationCompartmentNode', 'lcn')
            ->leftJoin('lcn.location', 'l')
            ->andWhere('po.supplier in (:suppliers)')
            ->groupBy('po.productId')
            ->setParameter('suppliers', $suppliers);

        if ($userId !== null) {
            $queryBuilder
                ->andWhere('po.user = :user')
                ->setParameter('user', $userId);
        }

        return $queryBuilder->getQuery()->getArrayResult();
    }

    /**
     * @return PurchaseOrder[]
     */
    public function findOrderableForSupplier(SupplierGroup $supplierGroup): array
    {
        $queryBuilder = $this->createQueryBuilder('po');

        $queryBuilder
            ->join('po.supplier', 's')
            ->andWhere('s.group = :supplierGroup')
            ->andWhere('po.count > 0')
            ->andWhere('BIT_AND(po.flags, :flags) = :minimalStock')
            ->setParameter('supplierGroup', $supplierGroup)
            ->setParameter('flags', PurchaseOrder::FLAG_FINISHED | PurchaseOrder::FLAG_FROM_MINIMAL_STOCK | PurchaseOrder::FLAG_HIDE_NOT_FINISHED)
            ->setParameter('minimalStock', PurchaseOrder::FLAG_FROM_MINIMAL_STOCK);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findByProductAndReference(Product $product, string $reference, bool $onlyOpen = false): ?array
    {
        $queryBuilder = $this->createQueryBuilder('po');
        $queryBuilder
            ->andWhere('po.product = :product')
            ->andWhere('po.referenceId = :reference')
            ->setParameter(':product', $product)
            ->setParameter(':reference', $reference);

        if ($onlyOpen === true) {
            $queryBuilder
                ->andWhere('BIT_AND(po.flags, :finishedFlag) != :finishedFlag')
                ->andWhere('BIT_AND(po.flags, :hideNotFinishedFlag) != :hideNotFinishedFlag')
                ->setParameter('finishedFlag', PurchaseOrder::FLAG_FINISHED)
                ->setParameter('hideNotFinishedFlag', PurchaseOrder::FLAG_HIDE_NOT_FINISHED);
        }

        return $queryBuilder->getQuery()->getResult();
    }

    public function forSupplierBackordersOverview(
        ?array $ids = null,
        int $interval = 0,
        SupplierGroup $supplierGroup = null,
        string $sortOption = null,
        string $deliveryDate = null,
        WexMaingroup $eigCategory = null,
    ): ?array {
        $queryBuilder = $this->createQueryBuilder('po');
        $queryBuilder
            ->select([
                'po.id',
                'po.productId',
                'po.orderDate',
                'po.deliveryDate',
                'po.description',
                'po.count',
                'po.flags',
                'po.price',
                'po.reference',
                'po.referenceId',
                'po.checkDate',
            ])
            ->addSelect('(
            		SELECT
                        COUNT(o.id) AS amount
                    FROM ' . OrderInfo::class . ' o
                    INNER JOIN ' . CartItem::class . ' ci WITH ci.orderId = o.id
                    INNER JOIN ' . ShipmentMethod::class . ' sh WITH sh.id = IDENTITY(o.shippingMethod) AND BIT_AND(sh.flags, :canceledFlag) != :canceledFlag
                    WHERE BIT_AND(o.flags, :orderFinishedFlag) = 0
                    AND ci.productId = po.productId
            ) AS amountInOrders')
            ->addSelect('(
                SELECT
                    COUNT(st) AS scanned
                FROM ' . Stock::class . ' st
                WHERE st.orderNoteid = po AND st.productId = po.productId
            ) AS scannedAmount')
            ->addSelect('
                IF (
                    BIT_AND(p.flags, :kitFlag) = :kitFlag
                    AND BIT_AND(s.flags, :supplierKitFlag) = :supplierKitFlag,
                    (
                        SELECT
                            ROUND(COUNT(kso) - COUNT(DISTINCT ksor.orderIndex))
                        FROM ' . KitSplitOrder::class . ' kso
                        INNER JOIN ' . KitSplitOrderRow::class . ' ksor WITH ksor.kitSplitOrder = kso AND ksor.processed = 1
                        WHERE kso.purchaseOrder = po AND kso.product = po.product
                    ),
                    0
                ) AS splitScannedAmount
            ')
            ->addSelect([
                'p.name',
                'p.flags as flagsArtikel',
                'sg.id as leverancierId',
                'sg.name as leverancier',
                'u.name as user',
                'pc.code as ean',
                'pc2.code as ean2',
                'wexMain.description as EIGCategory'
            ])
            ->join('po.supplier', 's')
            ->join('s.group', 'sg')
            ->leftJoin('po.product', 'p')
            ->leftJoin('p.subgroup', 'sub')
            ->leftJoin('sub.maingroup', 'main')
            ->leftJoin('sub.wexSubgroup', 'wexSub')
            ->leftJoin('wexSub.wexMaingroup', 'wexMain')
            ->leftJoin('po.user', 'u')
            ->leftJoin(ProductCode::class, 'pc', 'WITH', 'pc.product = p and pc.type = \'ean\' and BIT_AND(pc.flags, :flags) = :flags')
            ->leftJoin(ProductCode::class, 'pc2', 'WITH', 'pc2.product = p and pc2.type = \'artikelcode\' and BIT_AND(pc2.flags, :flags) = :flags')
            ->where('BIT_AND(po.flags, :finishedFlag) = 0')
            ->andWhere('s.domainId = 1')
            ->andWhere('p.name != \'\'')
            ->andWhere('sg.name != \'\'')
            ->andWhere('BIT_AND(sub.flags, :flagShow) = :flagShow')
            ->andWhere('BIT_AND(main.flags, :mainFlagShow) = :mainFlagShow')
            ->setParameter('flags', ProductCode::FLAG_MAIN_CODE)
            ->setParameter('finishedFlag', PurchaseOrder::FLAG_FINISHED)
            ->setParameter('flagShow', Subgroup::FLAG_SHOW)
            ->setParameter('mainFlagShow', Maingroup::FLAG_SHOW)
            ->setParameter('kitFlag', Product::FLAG_KIT_OR_SET_CHOICE)
            ->setParameter('supplierKitFlag', Supplier::FLAG_KITS_POSSIBLE_TO_SPLIT)
            ->setParameter('canceledFlag', ShipmentMethod::FLAG_CANCELLED)
            ->setParameter('orderFinishedFlag', OrderInfo::FLAG_ORDER_COMPLETE);

        if ($interval !== 0) {
            if ($interval === -1) {
                $queryBuilder
                    ->andWhere('po.deliveryDate < :deliveryDate')
                    ->andWhere('po.deliveryDate != \'00-00-0000\'')
                    ->setParameter('deliveryDate', new DateTime());
            } else {
                $date = new DateTime(sprintf('-%d weeks', $interval));

                $queryBuilder
                    ->andWhere('po.orderDate < :orderDate')
                    ->setParameter('orderDate', $date);
            }
        }

        if ($supplierGroup !== null) {
            $queryBuilder
                ->andWhere('sg = :supplierGroup')
                ->setParameter('supplierGroup', $supplierGroup);
        }

        if ($deliveryDate !== null) {
            $queryBuilder
                ->andWhere('po.deliveryDate LIKE :deliveryDate')
                ->setParameter('deliveryDate', '%' . $deliveryDate . '%');
        }

        if ($eigCategory !== null) {
            $queryBuilder
                ->andWhere('wexMain = :eigCategory')
                ->setParameter('eigCategory', $eigCategory);
        }

        if ($ids !== null) {
            $queryBuilder
                ->andWhere('po.id in (:ids)')
                ->setParameter('ids', $ids);
        }

        $sort = 'sg.name, po.product, po.orderDate';
        $order = 'ASC';
        if ($sortOption !== null) {
            switch ($sortOption) {
                case 'amountInOrders':
                    $sort = 'amountInOrders';
                    $order = 'DESC';
                    break;
                case 'suppliers':
                    $sort = 'sg.name';
                    break;
                case 'orderDate':
                    $sort = 'po.orderDate';
                    break;
                case 'amount':
                    $sort = 'po.count';
                    break;
                case 'price':
                    $sort = 'po.price';
                    $order = 'DESC';
                    break;
                case 'eig':
                    $sort = 'wexMain.description';
                    break;
            }
        }

        $queryBuilder
            ->orderBy($sort, $order);

        return $queryBuilder->getQuery()->getResult();
    }

    public function findProductWithMissingPurchaseOrder(?ProductListFilter $productListFilter = null): array
    {
        $queryBuilder = $this->getEntityManager()->createQueryBuilder();

        $subQueryBuilder = $this->getEntityManager()->createQueryBuilder();
        $subQuery = $subQueryBuilder
            ->select('1')
            ->from(Supplier::class, 'l2')
            ->innerJoin(PurchasePrice::class, 'ipl2', 'WITH', 'ipl2.supplier = l2')
            ->innerJoin(SupplierFeed::class, 'lf2', 'WITH', 'lf2.supplierGroup = l2.group')
            ->where('ipl2.product = a')
            ->andWhere('lf2.supplierGroup = lf.supplierGroup')
            ->getDQL();

        $queryBuilder
            ->select('a.id as productId')
            ->addSelect('a.name as productName')
            ->addSelect('lv.id as supplierStockId')
            ->addSelect('lv.priceEx')
            ->addSelect('IDENTITY(lv.supplier) as supplierStockSupplierId')
            ->addSelect('lf.id as supplierFeedId')
            ->addSelect('IDENTITY(lf.supplierGroup) as feedCollectionId')
            ->addSelect('IDENTITY(l.group) as supplierCollectionId')
            ->addSelect('l.id as supplierId')
            ->addSelect('lf.name as supplierName')
            ->addSelect('ipl.id as purchasePriceListId')
            ->from(Product::class, 'a')
            ->innerJoin(SupplierStock::class, 'lv', 'WITH', 'lv.product = a')
            ->leftJoin(SupplierFeed::class, 'lf', 'WITH', 'IDENTITY(lv.supplier) = lf.id AND lf.show = 1')
            ->leftJoin(Supplier::class, 'l', 'WITH', 'lf.supplierGroup = l.group')
            ->leftJoin(SupplierGroup::class, 'lg', 'WITH', 'lg.id = l.group')
            ->leftJoin(PurchasePrice::class, 'ipl', 'WITH', 'ipl.product = a AND ipl.supplier = l');

        $queryBuilder
            ->andWhere('lf.id IS NOT NULL')
            ->andWhere("NOT EXISTS ($subQuery)")
            ->groupBy('a.id')
            ->addGroupBy('l.group');

        if ($productListFilter instanceof ProductListFilter) {
            if ($productListFilter->getProductGroup()?->getSubgroup() !== null) {
                $queryBuilder
                    ->join('a.subgroup', 'sg')
                    ->andWhere('sg = :subgroup')
                    ->setParameter('subgroup', $productListFilter->getProductGroup()->getSubgroup());
            } elseif ($productListFilter->getProductGroup()?->getMaingroup() !== null) {
                $queryBuilder
                    ->join('a.subgroup', 'sg')
                    ->join('sg.maingroup', 'mg')
                    ->andWhere('mg = :mainGroup')
                    ->setParameter('mainGroup', $productListFilter->getProductGroup()->getMaingroup());
            } elseif ($productListFilter->getProductGroup()?->getRootgroup() !== null) {
                $queryBuilder
                    ->join('a.subgroup', 'sg')
                    ->join('sg.maingroup', 'mg')
                    ->join('mg.rootgroup', 'rg')
                    ->andWhere('rg = :rootGroup')
                    ->setParameter('rootGroup', $productListFilter->getProductGroup()->getRootgroup());
            }

            if (
                $productListFilter->getBrand() instanceof SpecsArticleSpecification &&
                !empty($productListFilter->getBrand()->getValue())
            ) {
                $queryBuilder
                    ->join('a.specsArticleSpecification', 'sasBrand')
                    ->join(
                        SpecsFiltersSpecification::class,
                        'sfs',
                        'WITH',
                        'sfs.specId = sasBrand.specSpecification AND sfs.filterId = :filterId'
                    )
                    ->andWhere('sasBrand.value = :brand')
                    ->setParameter('brand', $productListFilter->getBrand()->getValue())
                    ->setParameter('filterId', SpecsFiltersSpecification::BRAND);
            }

            if (
                $productListFilter->getSupplierFeed() instanceof SupplierFeed &&
                !empty($productListFilter->getSupplierFeed()->getId())
            ) {
                $queryBuilder
                    ->andWhere('lf.id = :supplierFeedId')
                    ->setParameter('supplierFeedId', $productListFilter->getSupplierFeed()->getId());
            }
        }

        return $queryBuilder->getQuery()->getArrayResult();
    }

    public function findProductsForBulkPurchasePriceUpdate(
        ?string $brandName = null,
        ?int $supplierFeedId = null,
        ?int $rootGroupId = null,
        ?int $mainGroupId = null,
        ?int $subGroupId = null
    ): array {
        $entityManager = $this->getEntityManager();
        $productListFilter = new ProductListFilter();

        if ($rootGroupId !== null || $mainGroupId !== null || $subGroupId !== null) {
            $productGroup = new GroupDataObject();

            if ($rootGroupId !== null) {
                $rootGroup = $entityManager->getRepository(Rootgroup::class)->find($rootGroupId);
                $productGroup->setRootgroup($rootGroup);
            }

            if ($mainGroupId !== null) {
                $mainGroup = $entityManager->getRepository(Maingroup::class)->find($mainGroupId);
                $productGroup->setMaingroup($mainGroup);
            }

            if ($subGroupId !== null) {
                $subGroup = $entityManager->getRepository(Subgroup::class)->find($subGroupId);
                $productGroup->setSubgroup($subGroup);
            }

            $productListFilter->setProductGroup($productGroup);
        }

        if ($brandName !== null) {
            $brandSpec = $entityManager->getRepository(SpecsArticleSpecification::class)
                ->findOneBy(['value' => $brandName]);

            if ($brandSpec !== null) {
                $productListFilter->setBrand($brandSpec);
            }
        }

        if ($supplierFeedId !== null) {
            $supplierFeed = $entityManager->getRepository(SupplierFeed::class)->find($supplierFeedId);

            if ($supplierFeed !== null) {
                $productListFilter->setSupplierFeed($supplierFeed);
            }
        }

        return $this->findProductWithMissingPurchaseOrder($productListFilter);
    }
}
