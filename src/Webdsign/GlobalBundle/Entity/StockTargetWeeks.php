<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: StockTargetWeeksRepository::class)]
#[ORM\Table('cameranu.stock_target_weeks')]
#[ORM\UniqueConstraint(name: 'unique_wex_maingroup_parent', columns: ['wex_maingroup_id', 'parent'])]
class StockTargetWeeks
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: WexMaingroup::class)]
    #[ORM\JoinColumn(name: 'wex_maingroup_id', referencedColumnName: 'id', nullable: false)]
    private WexMaingroup $wexMaingroup;

    #[ORM\Column(name: 'parent', type: 'string', length: 255)]
    private string $parent;

    #[ORM\Column(name: 'target_weeks', type: 'integer')]
    private int $targetWeeks;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getWexMaingroup(): WexMaingroup
    {
        return $this->wexMaingroup;
    }

    public function setWexMaingroup(WexMaingroup $wexMaingroup): self
    {
        $this->wexMaingroup = $wexMaingroup;
        return $this;
    }

    public function getParent(): string
    {
        return $this->parent;
    }

    public function setParent(string $parent): self
    {
        $this->parent = $parent;
        return $this;
    }

    public function getTargetWeeks(): int
    {
        return $this->targetWeeks;
    }

    public function setTargetWeeks(int $targetWeeks): self
    {
        $this->targetWeeks = $targetWeeks;
        return $this;
    }
}
