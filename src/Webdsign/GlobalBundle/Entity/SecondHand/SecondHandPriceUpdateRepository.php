<?php

namespace Webdsign\GlobalBundle\Entity\SecondHand;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SecondHandPriceUpdate>
 *
 * @method SecondHandPriceUpdate|null find($id, $lockMode = null, $lockVersion = null)
 * @method SecondHandPriceUpdate|null findOneBy(array $criteria, array $orderBy = null)
 * @method SecondHandPriceUpdate[]    findAll()
 * @method SecondHandPriceUpdate[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SecondHandPriceUpdateRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SecondHandPriceUpdate::class);
    }
}
