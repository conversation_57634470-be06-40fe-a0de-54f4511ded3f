<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\PersistentCollection;

/**
 * @Gedmo\Mapping\Annotation\Tree(type="nested")
 */
#[ORM\Entity(repositoryClass: MenuItemRepository::class)]
#[ORM\Table('cameranu.menuItems')]
class MenuItem
{
    public const int LEFT_MENU = 8;
    public const int MIRRORLESS_CAMERA_CATEGORY_ID = 46895;
    public const int LENSE_ID = 22;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'menuItemId', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var MenuItem|null
     * @Gedmo\Mapping\Annotation\TreeParent
     */
    #[ORM\ManyToOne(targetEntity: MenuItem::class, inversedBy: 'children')]
    #[ORM\JoinColumn(name: 'parentMenuItemId', referencedColumnName: 'menuItemId')]
    private ?MenuItem $parent;

    /**
     * @Gedmo\Mapping\Annotation\TreeLeft
     */
    #[ORM\Column(name: 'lft', type: 'integer')]
    private int $lft;

    /**
     * @Gedmo\Mapping\Annotation\TreeRight
     */
    #[ORM\Column(name: 'rgt', type: 'integer')]
    private int $rgt;

    /**
     * @var PersistentCollection|null
     */
    #[ORM\OneToMany(mappedBy: 'parent', targetEntity: MenuItem::class)]
    private $children;

    /**
     * @Gedmo\Mapping\Annotation\TreeLevel
     */
    #[ORM\Column(name: 'depth', type: 'integer')]
    private int $depth;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'active', type: 'boolean')]
    private $active;

    /**
     * @var string
     */
    #[ORM\Column(name: 'type', type: 'string')]
    private $type;

    #[ORM\ManyToOne(targetEntity: Menu::class, inversedBy: 'menuItems')]
    #[ORM\JoinColumn(name: 'menuId', referencedColumnName: 'menuId')]
    private Menu $menu;

    #[ORM\Column(name: 'menuId', type: 'integer')]
    private int $menuId;

    /**
     * @var PersistentCollection|null
     */
    #[ORM\OneToMany(mappedBy: 'menuItem', targetEntity: MenuI18n::class)]
    private $menuI18n;

    /**
     * @var PersistentCollection|null
     */
    #[ORM\OneToMany(mappedBy: 'menuItem', targetEntity: ProductMenuItem::class)]
    private $productMenuItem;

    /**
     * @var string
     */
    #[ORM\Column(name: 'url', type: 'string')]
    private $url;

    /**
     * @var Page|null
     */
    #[ORM\ManyToOne(targetEntity: Page::class)]
    #[ORM\JoinColumn(name: 'contentpageId', referencedColumnName: 'id')]
    private ?Page $page;

    #[ORM\ManyToOne(targetEntity: MenuItem::class)]
    #[ORM\JoinColumn(name: 'referenceMenuId', referencedColumnName: 'menuItemId')]
    private ?MenuItem $referenceMenu;

    #[ORM\Column(name: 'redirectInactive', type: 'string', length: 255, nullable: true)]
    private ?string $redirectInactive;

    #[ORM\Column(name: 'redirectInactiveType', type: 'integer', nullable: true)]
    private ?int $redirectInactiveType;

    #[ORM\Column(name: 'defaultSort', type: 'string')]
    private string $defaultSort;

     #[ORM\Column(name: 'canonical', type: 'integer', nullable: true)]
    private ?int $canonical;

    #[ORM\Column(name: 'add_to_title', type: 'integer')]
    private int $addToTitle;

    #[ORM\Column(name: 'flags', type: 'integer', nullable: true)]
    private ?int $flags;

    #[ORM\Column(name: 'searchWeight', type: 'integer', nullable: true)]
    private ?int $searchWeight;

    #[ORM\Column(name: 'menu_page_match', type: 'string', length: 255, nullable: true)]
    private ?string $menuPageMatch;

    #[ORM\Column(name: 'open_amount', type: 'integer')]
    private int $openAmount;

    #[ORM\Column(name: 'experimentContentpageId', type: 'integer', nullable: true)]
    private ?int $experimentContentpageId;

    #[ORM\Column(name: 'experimentId', type: 'integer', nullable: true)]
    private ?int $experimentId;

    #[ORM\Column(name: 'brand_image_url', type: 'string', length: 255, nullable: true)]
    private ?string $brandImageUrl;

    #[ORM\Column(name: 'image_url', type: 'string', length: 255)]
    private ?string $imageUrl;

    #[ORM\Column(name: 'path', type: 'string')]
    private string $path;

    /**
     * @var integer
     */
    #[ORM\Column(name: 'sort', type: 'integer')]
    private int $sort;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return MenuItem
     */
    public function setId(int $id): MenuItem
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return MenuItem|null
     */
    public function getParent(): ?MenuItem
    {
        return $this->parent;
    }

    /**
     * @param MenuItem $parent
     * @return MenuItem
     */
    public function setParent(?MenuItem $parent): MenuItem
    {
        $this->parent = $parent;
        return $this;
    }

    /**
     * @return PersistentCollection|null
     */
    public function getChildren(): ?PersistentCollection
    {
        return $this->children;
    }

    /**
     * @return int
     */
    public function getDepth(): int
    {
        return $this->depth;
    }

    /**
     * @param int $depth
     * @return MenuItem
     */
    public function setDepth(int $depth): MenuItem
    {
        $this->depth = $depth;
        return $this;
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->active;
    }

    /**
     * @param bool $active
     * @return MenuItem
     */
    public function setActive(bool $active): MenuItem
    {
        $this->active = $active;
        return $this;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param string $type
     * @return MenuItem
     */
    public function setType(string $type): MenuItem
    {
        $this->type = $type;
        return $this;
    }

    /**
     * @return PersistentCollection|null
     */
    public function getMenuI18n(): ?PersistentCollection
    {
        return $this->menuI18n;
    }

    /**
     * @param MenuI18n $menuI18n
     * @return MenuItem
     */
    public function setMenuI18n(MenuI18n $menuI18n): MenuItem
    {
        $this->menuI18n = $menuI18n;
        return $this;
    }

    /**
     * @return PersistentCollection|null
     */
    public function getProductMenuItem(): ?PersistentCollection
    {
        return $this->productMenuItem;
    }

    /**
     * @param ProductMenuItem $productMenuItem
     * @return MenuItem
     */
    public function setProductMenuItem(?ProductMenuItem $productMenuItem): MenuItem
    {
        $this->productMenuItem = $productMenuItem;
        return $this;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * @param string $url
     * @return MenuItem
     */
    public function setUrl(string $url): MenuItem
    {
        $this->url = $url;
        return $this;
    }

    /**
     * @return string
     */
    public function getPath(): string
    {
        return $this->path;
    }

    /**
     * @param string $path
     * @return MenuItem
     */
    public function setPath(string $path): MenuItem
    {
        $this->path = $path;
        return $this;
    }

    /**
     * @return int
     */
    public function getSort(): int
    {
        return $this->sort;
    }

    /**
     * @param int $sort
     * @return MenuItem
     */
    public function setSort(int $sort): MenuItem
    {
        $this->sort = $sort;
        return $this;
    }

    public function getName(string $language = MenuI18n::LANG_NL): ?MenuI18n
    {
        $i18n = $this->getMenuI18n()->filter(function (MenuI18n $i18n) use ($language) {
            return $i18n->getLanguage() === $language;
        })->first();
        return $i18n !== false ? $i18n : null;
    }

    public function getRgt(): int
    {
        return $this->rgt;
    }

    public function setRgt(int $rgt): self
    {
        $this->rgt = $rgt;

        return $this;
    }

    public function getLft(): int
    {
        return $this->lft;
    }

    public function setLft(int $lft): self
    {
        $this->lft = $lft;

        return $this;
    }

    public function getMenu(): Menu
    {
        return $this->menu;
    }

    public function setMenu(Menu $menu): self
    {
        $this->menu = $menu;

        return $this;
    }

    public function getPage(): ?Page
    {
        return $this->page;
    }

    public function setPage(?Page $page): self
    {
        $this->page = $page;

        return $this;
    }

    public function getReferenceMenu(): ?MenuItem
    {
        return $this->referenceMenu;
    }

    public function setReferenceMenu(?MenuItem $referenceMenu): self
    {
        $this->referenceMenu = $referenceMenu;

        return $this;
    }

    public function getRedirectInactive(): ?string
    {
        return $this->redirectInactive;
    }

    public function setRedirectInactive(?string $redirectInactive): self
    {
        $this->redirectInactive = $redirectInactive;

        return $this;
    }

    public function getRedirectInactiveType(): ?int
    {
        return $this->redirectInactiveType;
    }

    public function setRedirectInactiveType(?int $redirectInactiveType): self
    {
        $this->redirectInactiveType = $redirectInactiveType;

        return $this;
    }

    public function getDefaultSort(): string
    {
        return $this->defaultSort;
    }

    public function setDefaultSort(string $defaultSort): self
    {
        $this->defaultSort = $defaultSort;

        return $this;
    }

    public function getCanonical(): ?int
    {
        return $this->canonical;
    }

    public function setCanonical(?int $canonical): self
    {
        $this->canonical = $canonical;

        return $this;
    }

    public function getAddToTitle(): int
    {
        return $this->addToTitle;
    }

    public function setAddToTitle(int $addToTitle): self
    {
        $this->addToTitle = $addToTitle;

        return $this;
    }

    public function getFlags(): ?int
    {
        return $this->flags;
    }

    public function setFlags(?int $flags): self
    {
        $this->flags = $flags;

        return $this;
    }

    public function getSearchWeight(): ?int
    {
        return $this->searchWeight;
    }

    public function setSearchWeight(?int $searchWeight): self
    {
        $this->searchWeight = $searchWeight;

        return $this;
    }

    public function getMenuPageMatch(): ?string
    {
        return $this->menuPageMatch;
    }

    public function setMenuPageMatch(?string $menuPageMatch): self
    {
        $this->menuPageMatch = $menuPageMatch;

        return $this;
    }

    public function getOpenAmount(): int
    {
        return $this->openAmount;
    }

    public function setOpenAmount(int $openAmount): self
    {
        $this->openAmount = $openAmount;

        return $this;
    }

    public function getExperimentContentpageId(): ?int
    {
        return $this->experimentContentpageId;
    }

    public function setExperimentContentpageId(?int $experimentContentpageId): self
    {
        $this->experimentContentpageId = $experimentContentpageId;

        return $this;
    }

    public function getExperimentId(): ?int
    {
        return $this->experimentId;
    }

    public function setExperimentId(?int $experimentId): self
    {
        $this->experimentId = $experimentId;

        return $this;
    }

    public function getBrandImageUrl(): ?string
    {
        return $this->brandImageUrl;
    }

    public function setBrandImageUrl(?string $brandImageUrl): self
    {
        $this->brandImageUrl = $brandImageUrl;

        return $this;
    }

    public function getImageUrl(): ?string
    {
        return $this->imageUrl;
    }

    public function setImageUrl(?string $imageUrl): self
    {
        $this->imageUrl = $imageUrl;

        return $this;
    }
}
