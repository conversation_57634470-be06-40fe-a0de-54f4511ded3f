<?php

namespace Webdsign\GlobalBundle\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cameranu.purchase_price_calculated')]
class PurchasePriceCalculated
{
    /**
     * @var Product
     */
    #[ORM\OneToOne(targetEntity: Product::class)]
    #[ORM\JoinColumn(name: 'product_id', referencedColumnName: 'id')]
    #[ORM\Id]
    private $product;

    /**
     * @var User|null
     */
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: true)]
    private $user;

    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'last_updated', type: 'datetime')]
    private $lastUpdated;

    /**
     * @var float
     */
    #[ORM\Column(name: 'price', type: 'decimal', precision: 8, scale: 2)]
    private $price;

    /**
     * @param Product $product
     * @param float $price
     * @param User|null $user
     */
    public function __construct(Product $product, float $price, ?User $user = null)
    {
        $this->product = $product;
        $this->price = $price;
        $this->user = $user;
        try {
            $this->lastUpdated = new DateTime();
        } catch (\Exception $e) {
        }
    }


    /**
     * @return Product
     */
    public function getProduct(): Product
    {
        return $this->product;
    }

    /**
     * @return User|null
     */
    public function getUser(): ?User
    {
        return $this->user;
    }

    /**
     * @param User|null $user
     * @return PurchasePriceCalculated
     */
    public function setUser(?User $user): PurchasePriceCalculated
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @return DateTime
     */
    public function getLastUpdated(): DateTime
    {
        return $this->lastUpdated;
    }

    /**
     * @return float
     */
    public function getPrice(): float
    {
        return $this->price;
    }

    /**
     * @param float $price
     * @return PurchasePriceCalculated
     */
    public function setPrice(float $price): PurchasePriceCalculated
    {
        $this->price = $price;
        try {
            $this->lastUpdated = new DateTime();
        } catch (\Exception $e) {
        }
        return $this;
    }
}
