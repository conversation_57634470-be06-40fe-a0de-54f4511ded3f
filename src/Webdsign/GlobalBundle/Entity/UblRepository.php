<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Persistence\ManagerRegistry;
use Webdsign\GlobalBundle\Entity\Ubl;

class UblRepository extends WebdsignRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Ubl::class);
    }

    public function getBySupplier($id, $one = false)
    {
        $query = $this
            ->createQueryBuilder('u')
            ->innerJoin('u.suppliers', 's')
            ->where("s.id = :id")
            ->setParameter('id', $id);

        if ($one) {
            $query->setMaxResults(1);
        }

        return $query->getQuery();
    }

    public function getBySuppliers($suppliers)
    {
        $id_array = [];

        foreach ($suppliers as $supplier) {
            $id_array[] = $supplier->getId();
        }

        return $this
            ->createQueryBuilder('u')
            ->innerJoin('u.suppliers', 's')
            ->where("s.id IN (:id)")
            ->setParameter('id', array_values($id_array))
            ->getQuery()
            ->getResult();
    }
}
