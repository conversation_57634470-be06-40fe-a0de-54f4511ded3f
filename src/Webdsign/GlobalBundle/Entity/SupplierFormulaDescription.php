<?php

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'cameranu.leveranciersformules_omschrijving')]
class SupplierFormulaDescription
{
    /**
     * @var integer
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'description', type: 'text')]
    private $title;

    /**
     * @var ArrayCollection|SupplierFormula[]
     */
    #[ORM\OneToMany(mappedBy: 'description', targetEntity: SupplierFormula::class)]
    private $formulas;

    /**
     * SupplierFormulaDescription constructor.
     */
    public function __construct()
    {
        $this->formulas = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param int $id
     * @return SupplierFormulaDescription
     */
    public function setId(int $id): SupplierFormulaDescription
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * @param string $title
     * @return SupplierFormulaDescription
     */
    public function setTitle(string $title): SupplierFormulaDescription
    {
        $this->title = $title;
        return $this;
    }

    /**
     * @return ArrayCollection|SupplierFormula[]
     */
    public function getFormulas()
    {
        return $this->formulas;
    }

    /**
     * @param ArrayCollection|SupplierFormula[] $formulas
     * @return SupplierFormulaDescription
     */
    public function setFormulas($formulas): SupplierFormulaDescription
    {
        $this->formulas = $formulas;
        return $this;
    }

}
