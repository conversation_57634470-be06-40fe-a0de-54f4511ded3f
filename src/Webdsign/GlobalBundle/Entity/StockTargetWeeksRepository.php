<?php

declare(strict_types=1);

namespace Webdsign\GlobalBundle\Entity;

use Doctrine\Persistence\ManagerRegistry;

/**
 * @method StockTargetWeeks|null find($id, $lockMode = null, $lockVersion = null)
 * @method StockTargetWeeks|null findOneBy(array $criteria, array $orderBy = null)
 * @method StockTargetWeeks[] findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method StockTargetWeeks[] findAll()
 */
class StockTargetWeeksRepository extends WebdsignRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, StockTargetWeeks::class);
    }

    public function findByWexMaingroupAndParent(WexMaingroup $wexMaingroup, string $parent): ?StockTargetWeeks
    {
        return $this->findOneBy([
            'wexMaingroup' => $wexMaingroup,
            'parent' => $parent,
        ]);
    }
}
