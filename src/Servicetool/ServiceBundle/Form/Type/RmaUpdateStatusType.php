<?php

namespace Servicetool\ServiceBundle\Form\Type;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Webdsign\GlobalBundle\Entity\Rma;
use Webdsign\GlobalBundle\Entity\RmaStatus;
use Webdsign\GlobalBundle\Entity\RmaWorkflow;


class RmaUpdateStatusType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('id', HiddenType::class)
            ->add('return', CheckboxType::class, [
                'mapped'   => false,
                'required' => false,
                'data'     => false,
                'label'    => 'service.rma.scan.openRma',
            ])
            ->add('submit', SubmitType::class, [
                'label'    => 'core.save',
                'attr'     => [ 'class' => 'btn btn-primary']
            ])
            ->add('back', SubmitType::class, [
                'label'    => 'core.actionbar.back',
                'attr'     => [ 'class' => 'btn btn-danger']
            ]);

        $builder->addEventListener(FormEvents::PRE_SET_DATA, function(FormEvent $event) {
            $rma = $event->getData();
            $form = $event->getForm();

            if ($rma instanceof Rma) {
                $workflows = $rma->getStatus()->getWorkflowForTag($rma->getRmaTag());
                $choices = [];
                foreach($workflows as $workflow) {
                    $choices[$workflow->getId()] = $workflow->getNextStatus();
                }

                $form->add('status', EntityType::class, [
                    'required'          => true,
                    'label'             => 'service.rma.status',
                    'class'             => RmaStatus::class,
                    'expanded'          => false,
                    'multiple'          => false,
                    'choices'           => $choices,
                    // 'choices_as_values' => true,
                    'choice_label'      => function(RmaStatus $status)
                    {
                        return $status->getTitle();
                    },
                    'choice_attr'       => function(RmaStatus $status) use ($workflows)
                    {
                        /** @var RmaWorkflow $currentWorkflow */
                        $currentWorkflow = null;
                        foreach($workflows as $workflow) {
                            if ($workflow->getNextStatus() === $status) {
                                $currentWorkflow = $workflow;
                                break;
                            }
                        }

                        return [
                            'data-workflow' => $currentWorkflow->getId(),
                            'data-workflow-key' => $currentWorkflow->getKey()?:'',
                        ];
                    },
                ]);
            }
        });

    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults([
            'data_class' => Rma::class,
            'csrf_protection' => false,
        ]);
    }

    /**
     * @return string
     */
    public function getBlockPrefix()
    {
        return 'rmaupdatestatus';
    }
}
