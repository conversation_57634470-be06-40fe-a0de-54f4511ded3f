<?php

namespace Servicetool\ServiceBundle\Services\Rma;


use Symfony\Component\Security\Core\User\UserInterface;
use Webdsign\GlobalBundle\Entity\Rma;
use Webdsign\GlobalBundle\Entity\RmaWorkflow;

interface ScanActionInterface
{
    /**
     * @param Rma $rma
     * @param RmaWorkflow $workflow
     * @param UserInterface $user
     * @return void
     */
    public function execute(Rma $rma, RmaWorkflow $workflow, UserInterface $user);
}