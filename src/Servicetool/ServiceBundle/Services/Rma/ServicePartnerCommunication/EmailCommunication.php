<?php

namespace Servicetool\ServiceBundle\Services\Rma\ServicePartnerCommunication;

use Symfony\Component\Security\Core\User\UserInterface;
use Webdsign\GlobalBundle\Entity\Rma;

class EmailCommunication extends AbstractCommunication
{
    /**
     * @param Rma $rma
     * @param UserInterface $user
     * @return bool
     */
    public function sendRmaRequest(Rma $rma, UserInterface $user)
    {
        $to = $rma->getTicket()->getServicePartner()->getEmail();

        return $this->getRmaConfirmation()->sendMail(
            $rma,
            $to,
            $this->getRequestRmaTemplateId(),
            'nl',
            [],
            false,
            $user,
            '',
            true
        );
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return 'email';
    }
}