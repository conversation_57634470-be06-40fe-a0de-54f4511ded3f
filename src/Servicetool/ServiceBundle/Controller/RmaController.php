<?php

namespace Servicetool\ServiceBundle\Controller;

use CatBundle\Service\PackageLabelGenerator;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\ManagerRegistry;
use Exception;
use Knp\Component\Pager\PaginatorInterface;
use Servicetool\ServiceBundle\ServicetoolServiceBundle;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Routing\Annotation\Route;
use Servicetool\ServiceBundle\Form\Type\RmaSearchType;
use Servicetool\ServiceBundle\Form\Type\RmaUpdateStatusType;
use Servicetool\ServiceBundle\Resources\PDF\Letter;
use Servicetool\ServiceBundle\Resources\PDF\RmaCustomerLetter;
use Servicetool\ServiceBundle\Services\Rma\ScanActionInterface;
use Servicetool\ServiceBundle\Services\TicketList;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;
use Webdsign\GlobalBundle\Controller\AbstractWebdsignController;
use Webdsign\GlobalBundle\Entity\Attachment;
use Webdsign\GlobalBundle\Entity\CustomerAddress;
use Webdsign\GlobalBundle\Entity\Mailbox;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\PostnlPackageLabel;
use Webdsign\GlobalBundle\Entity\PostnlProductcode;
use Webdsign\GlobalBundle\Entity\RmaType;
use Webdsign\GlobalBundle\Entity\RmaWorkflow;
use Webdsign\GlobalBundle\Exception\OrderInfoCustomerNotFoundException;
use Webdsign\GlobalBundle\Exception\InvalidPaymentMethodException;
use Webdsign\GlobalBundle\Exception\NoServicePartnerForRmaException;
use Webdsign\GlobalBundle\Exception\PrinterException;
use Webdsign\GlobalBundle\Exception\RmaNotFoundException;
use Webdsign\GlobalBundle\Logger;
use Webdsign\GlobalBundle\Services\RMA\HandleNewRma;
use Webdsign\GlobalBundle\Services\RMA\HandleRmaReply;
use Webdsign\GlobalBundle\Services\ClangWebHook;
use Webdsign\GlobalBundle\Services\Uploader;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\OrderTickets;
use Webdsign\GlobalBundle\Entity\Stock;
use Webdsign\GlobalBundle\Entity\Country;
use Webdsign\GlobalBundle\Entity\ExternalContact;
use Webdsign\GlobalBundle\Entity\PostnlLabel;
use Webdsign\GlobalBundle\Entity\PostnlParcelAnnounce;
use Webdsign\GlobalBundle\Entity\Rma;
use Webdsign\GlobalBundle\Entity\RmaProduct;
use Webdsign\GlobalBundle\Entity\RmaStatus;
use Webdsign\GlobalBundle\Entity\Ticket;
use Webdsign\GlobalBundle\Entity\TicketReply;
use Webdsign\GlobalBundle\Form\Type\CreatePostnlLabelType;
use Webdsign\GlobalBundle\Form\Type\RmaType as RmaTypeForm;
use Webdsign\GlobalBundle\Form\Type\RmaLetterType;
use Webdsign\GlobalBundle\Form\Type\TicketFilterType;
use Webdsign\GlobalBundle\Services\RmaConfirmation;
use Webdsign\GlobalBundle\Entity\ServiceTag;
use Webdsign\GlobalBundle\Entity\ServiceTagRepository;
use Webdsign\GlobalBundle\Entity\Postnl;

/**
 * Beheren van tickets.
 *
 * @Route("/rma")
 */
class RmaController extends AbstractWebdsignController
{
    public function __construct(
        HttpKernelInterface $httpKernel,
        TranslatorInterface $t,
        Logger $l,
        TokenStorageInterface $tokenStorage,
        AuthorizationCheckerInterface $authorizationChecker,
        CsrfTokenManagerInterface $csrfTokenManager,
        ManagerRegistry $registry,
        PaginatorInterface $paginator,
        Environment $twig,
        RouterInterface $router,
        RequestStack $requestStack,
        FormFactoryInterface $formFactory,
        SerializerInterface $serializer,
        private readonly HandleNewRma $rmaHelper,
        private readonly PackageLabelGenerator $packageLabelGenerator
    ) {
        parent::__construct(
            $httpKernel,
            $t,
            $l,
            $tokenStorage,
            $authorizationChecker,
            $csrfTokenManager,
            $registry,
            $paginator,
            $twig,
            $router,
            $requestStack,
            $formFactory,
            $serializer,
        );
    }

    /**
     * @Route("/create/{ticketId}", name="rmaCreate", defaults={"rmaId": null, "customerId": null, "orderId": null, "tracecode": null, "delivery": null, "forceStockItems": false})
     * @Route("/edit/{rmaId}", name="rmaEdit", defaults={"ticketId": null, "customerId": null, "orderId": null, "tracecode": null, "delivery": null, "forceStockItems": false})
     * @Route("/edit/{rmaId}/force", name="rmaEditForceStock", defaults={"ticketId": null, "customerId": null, "orderId": null, "tracecode": null, "delivery": null, "forceStockItems": true})
     * @Route("/createForCustomer/{customerId}", name="rmaCreateForCustomer", defaults={"rmaId": null, "ticketId": null, "orderId":null, "delivery": null, "tracecode": null, "forceStockItems": false})
     * @Route("/createForOrder/{customerId}/{orderId}", name="rmaCreateForOrder", defaults={"rmaId": null, "ticketId": null, "delivery": null, "tracecode": null, "forceStockItems": false})
     * @Route("/createForDelivery/{customerId}/{orderId}/{delivery}/{tracecode}", name="rmaCreateForPostNL", defaults={"rmaId": null, "ticketId": null, "forceStockItems": false}, requirements={"delivery":"(postnl|ups)"})
     * @Route("/createForNewUser", name="rmaCreateCustomerAndRma")
     * @param Request $request
     * @param int $ticketId
     * @param int $rmaId
     * @param int $customerId
     * @param int $orderId
     * @param string $tracecode
     * @param string $delivery
     * @param bool $forceStockItems
     * @return Response
     */
    public function editAction(
        Request $request,
        ?int $ticketId = null,
        ?int $rmaId = null,
        ?int $customerId = null,
        ?int $orderId = null,
        ?string $tracecode = null,
        ?string $delivery = null,
        bool $forceStockItems = false
    ) {
        $objectManager = $this->getDoctrine()->getManager();

        $rmaRepository = $this->getDoctrine()->getRepository(Rma::class);
        $rmaTypeRepository = $this->getDoctrine()->getRepository(RmaType::class);
        $rmaStatusRepository = $this->getDoctrine()->getRepository(RmaStatus::class);
        $ticketRepository = $this->getDoctrine()->getRepository(Ticket::class);
        $orderRepository = $this->getDoctrine()->getRepository(OrderInfo::class);
        $createNewUser = false;
        /** @var Customer $customer */
        $customer = null;
        /** @var Ticket $ticket */
        $ticket = null;

        $alwaysUpdateTicketTitle = false;

        if (null !== $ticketId) { // route: rmaCreate
            $ticket = $ticketRepository->find($ticketId);
            if (!$ticket) {
                throw new NotFoundHttpException("Ticket ".$ticketId." not found!");
            }
            $rma = new Rma();
            $rma->setTicket($ticket);
            $rma->addRmaProduct(new RmaProduct());

            $rmaStatus = $rmaStatusRepository->findOneBy(['title'=>'Nog niet binnen']);
            if ($rmaStatus instanceof  RmaStatus) {
                $rma->setStatus($rmaStatus);
            }
        } elseif (null !== $rmaId) { // route: edit
            /** @var Rma $rma */
            $rma = $rmaRepository->find($rmaId);
            if (!$rma) {
                throw new NotFoundHttpException("RMA ".$rmaId." not found!");
            }
            $ticket = $rma->getTicket();
        } elseif (null !== $customerId) { // route: createFor*
            $alwaysUpdateTicketTitle = true;

            $customerRepository = $this->getDoctrine()->getRepository(Customer::class);
            $customer = $customerRepository->find($customerId);
            if (!$customer) {
                throw new NotFoundHttpException("Customer not found");
            }

            $rma = new Rma();
            $rmaProduct = new RmaProduct();
            if ($delivery !== null) { // route: createForDelivery
                $rmaProduct->setStockItem(null);
                $rmaProduct->setTitle($delivery);
                $rmaProduct->setBarcode($tracecode);
            }
            $rma->addRmaProduct($rmaProduct);

            $rmaStatus = $rmaStatusRepository->findOneBy(['title'=>'Nog niet binnen']);
            if ($rmaStatus instanceof  RmaStatus) {
                $rma->setStatus($rmaStatus);
            }

            $rmaType = $rmaTypeRepository->findOneBy(['id' => 1]);
            if ($rmaType instanceof  RmaType) {
                $rma->setType($rmaType);
            }
        } else { // route: createForNewUser
            $createNewUser = true;
            $rma = new Rma();
            $customer = new Customer();
            $customer
                ->setFirstName('')
                ->setLastNamePrefix('')
                ->setLastName('')
                ->setEmail('')
                ->setFlags(Customer::FLAG_SERVICE)
                ->setIdentifyHash('')
                ->setPassword('')
                ->setCompanyName('')
                ->setVatIdentificationNumber('')
            ;
            $rmaProduct = new RmaProduct();
            $rma->addRmaProduct($rmaProduct);
            $rmaStatus = $rmaStatusRepository->findOneBy(['title' => 'Nog niet binnen']);

            if ($rmaStatus instanceof  RmaStatus) {
                $rma->setStatus($rmaStatus);
            }

            $rmaType = $rmaTypeRepository->findOneBy(['id' => 1]);
            if ($rmaType instanceof  RmaType) {
                $rma->setType($rmaType);
            }

            $objectManager->persist($customer);
        }

        $orderIds = array();
        $stockIds = array();

        if ($forceStockItems || null !== $ticket && ($rma->hasRmaProductWithStockItem() || $ticketId !== null)) {
            $orders = $ticket->getOrdersForTicket();
            foreach ($orders as $orderInfo) {
                $orderIds[] = $orderInfo->getOrdernr();
                $items = $orderInfo->getStockItems();
                foreach ($items as $stock) {
                    $stockIds[] = $stock->getId();
                }
            }
        } elseif (null !== $orderId && null === $delivery) {
            /** @var OrderInfo $orderInfo */
            $orderInfo = $orderRepository->find($orderId);
            $orderIds[] = $orderInfo->getOrdernr();
            $items = $orderInfo->getStockItems();
            foreach ($items as $stock) {
                $stockIds[] = $stock->getId();
            }
        }

        $originalProducts = new ArrayCollection();
        // check of het product in de RMA nog wel aanwezig is in de lijst met orderproducten, zo niet, dan reset naar null, productnaam+serienummer blijven wel bestaan
        foreach ($rma->getRmaProducts() as $rmaProduct) {
            $originalProducts->add($rmaProduct);
            if ($rmaProduct->getStockItem() && !in_array($rmaProduct->getStockItem()->getId(), $stockIds)) {
                $rmaProduct->setStockItem(null);
            }
        }

        $user = $this->getUser();
        $originRepository = $objectManager->getRepository(Origin::class);
        $defaultDropoffLocationId = Origin::URK;

        foreach ($user->getRoleEntities() as $role) {
            switch ($role->getRole()) {
                case 'ROLE_WINKEL-AMSTERDAM':
                    $defaultDropoffLocationId = Origin::AMSTERDAM;
                    break 2;
                case 'ROLE_WINKEL-APELDOORN':
                    $defaultDropoffLocationId = Origin::APELDOORN;
                    break 2;
                case 'ROLE_WINKEL-GRONINGEN':
                    $defaultDropoffLocationId = Origin::GRONINGEN;
                    break 2;
                case 'ROLE_WINKEL-EINDHOVEN':
                    $defaultDropoffLocationId = Origin::EINDHOVEN;
                    break 2;
                case 'ROLE_WINKEL-ROTTERDAM':
                    $defaultDropoffLocationId = Origin::ROTTERDAM;
                    break 2;
                case 'ROLE_WINKEL-ANTWERPEN':
                    $defaultDropoffLocationId = Origin::ANTWERPEN;
                    break 2;
                case 'ROLE_WINKEL-UTRECHT':
                    $defaultDropoffLocationId = Origin::UTRECHT;
                    break 2;
            }
        }

        $defaultDropoffLocation = $originRepository->find($defaultDropoffLocationId);

        $repairTagId = 181;
        $defaultTag = $createNewUser ? $objectManager
            ->getRepository(ServiceTag::class)
            ->findBy(['id' => $repairTagId]) : null
        ;

        //Get postalcode url and pass to form
        $postalcodeUrl = $this->generateUrl(
            'postalcode'
        );

        $rmaForm = $this->createForm(RmaTypeForm::class, $rma, [
            'orderIds' => $orderIds,
            'customer' => $customer,
            'objectManager' => $this->getDoctrine()->getManager(),
            'defaultDropoffLocation' => (null === $rma->getId() || $rma->getDropoffLocation() === null) ? $defaultDropoffLocation : $rma->getDropoffLocation(),
            'defaultTag' => $defaultTag,
            'tagIds' => $createNewUser ? [$repairTagId] : [],
            'newCustomer' => $createNewUser,
            'postalcodeUrl' => $postalcodeUrl,
        ]);

        $rmaForm->handleRequest($request);

        if($rmaForm->isSubmitted() && $rmaForm->isValid()) {
            foreach($rma->getRmaProducts() as $rmaProduct) {
                $rmaProduct->setRma($rma);
                $stockItem = $rmaProduct->getStockItem();
                if ($stockItem instanceof Stock) {
                    $rmaProduct->updateTitleFromStock();
                    // check op dubbele...
                    foreach($rma->getRmaProducts() as $rmaProductDuplicate) {
                        if ($rmaProductDuplicate !== $rmaProduct && $rmaProductDuplicate->getStockItem() === $stockItem) {
                            $rma->removeRmaProduct($rmaProduct);
                            $objectManager->remove($rmaProduct);
                        }
                    }
                } else {
                    $rmaProduct->setStockItem(null);
                }
            }

            foreach($originalProducts as $rmaProduct) {
                if (false === $rma->getRmaProducts()->contains($rmaProduct)) {
                    $objectManager->remove($rmaProduct);
                }
            }

            if (null !== $customer) { // route: createFor*
                // eerst ticket maken
                $ticket = new Ticket();
                $ticket->setHash($this->container->get('webdsignservice.tickethash')->getHash());
                $ticket->setState('open');
                $ticket->setCustomer($customer);
                //oorsprong bepalen
                $ticketOrigin = $customer->getOrigin();
                if ($orderId !== null){
                    $orderRepository = $this->getDoctrine()->getRepository(OrderInfo::class);
                    $order = $orderRepository->find($orderId);
                    if ($order instanceof OrderInfo) {
                        $ticketOrigin = $order->getOrigin();
                    }
                }
                $ticket->setOrigin($ticketOrigin);

                foreach($rmaForm->get('tags')->getData() as $tag) {
                    if ($tag instanceof ServiceTag) {
                        $ticket->addTag($tag);
                    }
                }
                $ticket->setSubject("");
                $ticket->setTitle('rma'); // wordt straks overschreven, maar voor het aanmaken van het ticket is deze verplicht
                $ticket->setFromAddress($customer->getEmail());

                $servicePartner = $rmaForm->get('servicePartner')->getData();
                if ($servicePartner instanceof ExternalContact) {
                    $ticket->setServicePartner($servicePartner);
                }

                // Adres aanmaken als het een nieuwe klant is
                if ($createNewUser === true) {
                    $customer->setFirstName($rmaForm->get('customerName')->getData());
                    $customer->setName($rmaForm->get('customerName')->getData());
                    $customer->setEmail($rmaForm->get('customerEmail')->getData());
                    $customer->setPhonenr($rmaForm->get('customerPhonenr')->getData());
                    $objectManager->persist($customer);

                    $this->rmaHelper->createAddressForRmaCustomer($rma, $customer);
                } elseif ($customer->getAddresses()->count() === 0) {
                    $this->rmaHelper->createAddressForRmaCustomer($rma, $customer);
                }

                $rma->setTicket($ticket);
                $objectManager->persist($ticket);
                $objectManager->persist($rma);
                $objectManager->flush();

                if (null !== $orderId) {
                    /** @var OrderInfo $orderInfo */
                    $orderInfo = $orderRepository->find($orderId);
                    if ($orderInfo instanceof OrderInfo) {
                        $orderTicket = new OrderTickets();
                        $orderTicket->setOrder($order);
                        $orderTicket->setTicket($ticket);
                        $objectManager->persist($orderTicket);
                    }
                }
            }

            // indien nog niet aanwezig, kijken of we meteen de servicepartner kunnen koppelen
            if (!$ticket->getServicePartner() instanceof ExternalContact) {
                try {
                    $externalContactRepository = $this->getDoctrine()->getRepository(ExternalContact::class);
                    $servicePartner = $externalContactRepository->findForRma($rma, true);
                    $ticket->setServicePartner($servicePartner);
                    $objectManager->persist($ticket);
                } catch (NoServicePartnerForRmaException $exception) {
                    $this->addFlash('warning', $this->t->trans('service.rma.servicepartner.error') . ":<br>\n" . $this->t->trans($exception->getMessage()));
                }
            }

            $objectManager->persist($rma);
            $objectManager->flush();

            if ($request->get('updateTicketTitle')) {
                $ticket->setTitle($rma->generateNewTicketTitle());
                $objectManager->persist($ticket);
                $objectManager->flush();
            }

            if ($rmaForm->get('print')->isClicked()) {
                return $this->redirectToRoute('rmaCustomerConfirmationPrint', array('rmaId' => $rma->getId()));
            }
            return $this->redirectToRoute('ticketShow', array('id' => $rma->getTicket()->getId()));
        }

        $pageTitle = $rmaId === null ? $this->t->trans('service.ticket.rma.create') : $this->t->trans('service.ticket.rma.title') . ' ' . $rma->getId();

        if ($createNewUser === true) {
            $pageTitle = $this->t->trans('service.ticket.rma.create.new.user');
        }

        /**
         * Update naar Clang sturen
         *
         * @var ClangWebHook $clangWebHook
         */
        $clangWebHook = $this->get('webdsign.clang_web_hook');
        if ($customer instanceof Customer && $customer->getId() !== null) {
            $clangWebHook->handleData(['customerId' => $customer->getId()], 'customer');
        }

        return $this->render(
            '@ServicetoolService/Rma/create.html.twig',
            array(
                "page_title" => $pageTitle,
                'multipleProducts' => null === $ticket || $ticket->hasTagWithName('retour'),
                "createRmaForm" => $rmaForm->createView(),
                "stockItemWarning" => $forceStockItems || ( $rma->getId() && count($orderIds) > 0 && !$rma->hasRmaProductWithStockItem()),
                "alwaysUpdateTicketTitle" => $alwaysUpdateTicketTitle,
            )
        );
    }

    /**
     * @Route("/tickets", name="ticketRmaList")
     * @param Request $request
     * @return Response
     */
    public function listTicketsAction(Request $request): Response
    {
        // doorsturen naar TicketController (is in principe hetzelfde, maar voor de duidelijkheid gaat de aanroep via RMA controller)
        return $this->forward(
            'Servicetool\ServiceBundle\Controller\TicketController::listAction', [
                'taskId' => false,
                'logout' => false,
                'rma' => true,
                '_route' => 'ticketRmaList'
            ],
            $request->query->all()
        );
    }

    /**
     * @Route("/createForVendiroOrder/{customerId}/{orderId}", name="rmaCreateForVendiroOrder")
     * @param Request $request
     * @param int $customerId
     * @param int $orderId
     * @return RedirectResponse|Response
     */
    public function createVendiroAction(Request $request, int $customerId, int $orderId)
    {
        $objectManager = $this->getDoctrine()->getManager();
        $alwaysUpdateTicketTitle = true;
        $ticket = null;

        $customerRepository = $this->getDoctrine()->getRepository(Customer::class);
        $rmaStatusRepository = $this->getDoctrine()->getRepository(RmaStatus::class);
        $orderRepository = $this->getDoctrine()->getRepository(OrderInfo::class);

        $customer = $customerRepository->find($customerId);
        if (!$customer instanceof Customer) {
            throw new NotFoundHttpException('Customer not found');
        }

        $pageTitle =  $this->t->trans('service.ticket.rma.create');

        $rma = new Rma();
        $rmaProduct = new RmaProduct();
        $rma->addRmaProduct($rmaProduct);

        $rmaStatus = $rmaStatusRepository->findOneBy(['title' => 'Nog niet binnen']);
        if ($rmaStatus instanceof  RmaStatus) {
            $rma->setStatus($rmaStatus);
        }

        /**
         * @var OrderInfo $orderInfo
         */
        $orderInfo = $orderRepository->find($orderId);
        $orderIds[] = $orderInfo->getOrdernr();
        $stockIds = [];
        $items = $orderInfo->getStockItems();
        foreach ($items as $stock) {
            $stockIds[] = $stock->getId();
        }

        $originalProducts = new ArrayCollection();
        // check of het product in de RMA nog wel aanwezig is in de lijst met orderproducten,
        // zo niet, dan reset naar null, productnaam+serienummer blijven wel bestaan
        foreach ($rma->getRmaProducts() as $rmaProduct) {
            $originalProducts->add($rmaProduct);
            if ($rmaProduct->getStockItem() && !in_array($rmaProduct->getStockItem()->getId(), $stockIds)) {
                $rmaProduct->setStockItem(null);
            }
        }

        $defaultDropoffLocation = $objectManager
            ->getRepository(Origin::class)
            ->findOneBy(['source' => 'winkel', 'physicalLocation' => 1])
        ;

        $tagIds = [
            577, //Vendiro DOA
            582, //Vendiro Retour
            587, // Vendiro Reparatie
        ];

        $rmaForm = $this->createForm(RmaTypeForm::class, $rma, [
            'orderIds' => $orderIds,
            'customer' => $customer,
            'objectManager' => $objectManager,
            'tagIds' => $tagIds,
            'defaultDropoffLocation' => $defaultDropoffLocation,
        ]);

        $rmaForm->handleRequest($request);

        if ($rmaForm->isSubmitted() && $rmaForm->isValid()) {
            foreach ($rma->getRmaProducts() as $rmaProduct) {
                $rmaProduct->setRma($rma);
                $stockItem = $rmaProduct->getStockItem();
                if ($stockItem instanceof Stock) {
                    $rmaProduct->updateTitleFromStock();
                    // check op dubbelen
                    foreach ($rma->getRmaProducts() as $rmaProductDuplicate) {
                        if (
                            $rmaProductDuplicate !== $rmaProduct &&
                            $rmaProductDuplicate->getStockItem() === $stockItem
                        ) {
                            $rma->removeRmaProduct($rmaProduct);
                            $objectManager->remove($rmaProduct);
                        }
                    }
                } else {
                    $rmaProduct->setStockItem(null);
                }
            }

            foreach ($originalProducts as $rmaProduct) {
                if ($rma->getRmaProducts()->contains($rmaProduct) === false) {
                    $objectManager->remove($rmaProduct);
                }
            }

            // eerst ticket
            $ticket = new Ticket();
            $ticket->setHash($this->container->get('webdsignservice.tickethash')->getHash());
            $ticket->setState('open');
            $ticket->setCustomer($customer);

            //oorsprong bepalen
            $ticketOrigin = $customer->getOrigin();
            if ($orderInfo instanceof OrderInfo) {
                $ticketOrigin = $orderInfo->getOrigin();
            }

            $ticket->setOrigin($ticketOrigin);

            foreach ($rmaForm->get('tags')->getData() as $tag) {
                if ($tag instanceof ServiceTag) {
                    $ticket->addTag($tag);
                }
            }

            $ticket->setSubject('');
            $ticket->setTitle('rma');
            $ticket->setFromAddress($customer->getEmail());

            $servicePartner = $rmaForm->get('servicePartner')->getData();
            if ($servicePartner instanceof ExternalContact) {
                $ticket->setServicePartner($servicePartner);
            }

            $rma->setTicket($ticket);
            $objectManager->persist($ticket);
            $objectManager->persist($rma);
            $objectManager->flush();

            if ($orderInfo instanceof OrderInfo) {
                $orderTicket = new OrderTickets();
                $orderTicket->setOrder($orderInfo);
                $orderTicket->setTicket($ticket);
                $objectManager->persist($orderTicket);
            }

            // indien nog niet aanwezig, kijken of we een servicepartner kunnen koppelen
            if (!$ticket->getServicePartner() instanceof ExternalContact) {
                try {
                    $externalContactRepository = $this->getDoctrine()->getRepository(ExternalContact::class);
                    $servicePartner = $externalContactRepository->findForRma($rma, false);
                    $ticket->setServicePartner($servicePartner);
                    $objectManager->persist($ticket);
                } catch (NoServicePartnerForRmaException $exception) {
                    $this->addFlash('warning', $this->t->trans('service.rma.servicepartner.error') . ":<br>\n" . $this->t->trans($exception->getMessage()));
                }
            }

            $objectManager->persist($rma);
            $objectManager->flush();

            $ticket->setTitle($rma->generateNewTicketTitle());
            $objectManager->persist($ticket);
            $objectManager->flush();

            if ($rmaForm->get('print')->isClicked()) {
                return $this->redirectToRoute('rmaCustomerConfirmationPrint', ['rmaId' => $rma->getId()]);
            }

            return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
        }

        return $this->render(
            '@ServicetoolService/Rma/create.html.twig',
            [
                'page_title' => $pageTitle,
                'multipleProducts' => false,
                'createRmaForm' => $rmaForm->createView(),
                'stockItemWarning' => ($rma->getId() && count($orderIds) > 0 && !$rma->hasRmaProductWithStockItem()),
                'alwaysUpdateTicketTitle' => $alwaysUpdateTicketTitle,
                'is_vendiro' => true
            ]
        );
    }

    /**
     * @Route("/createLetter/{rmaId}", name="rmaCreateLetter", requirements={"rmaId":"[0-9]+"})
     * @param Request $request
     * @param int $rmaId
     * @return Response
     */
    public function createLetterAction(Request $request, $rmaId)
    {
        /** @var EntityRepository $rmaRepository */
        $rmaRepository = $this->getDoctrine()->getRepository(Rma::class);

        /** @var Rma $rma */
        $rma = $rmaRepository->find($rmaId);
        if (!$rma) {
            throw new NotFoundHttpException("RMA ".$rmaId." not found!");
        }

        $servicePartnerCommunication = $this->get("rma.servicepartner.emailcommunication");
        $rmaLetter = $servicePartnerCommunication->createLetterEntity($rma, $this->getUser());

        $form = $this->createForm(RmaLetterType::class, $rmaLetter);
        if ($request->isMethod(Request::METHOD_POST)) {
            $form->handleRequest($request);

            if ($form->isValid()) {

                $ticketReply = $servicePartnerCommunication->addLetterPdfToRma($rma, $rmaLetter, $this->getUser());

                /** @var Attachment $attachment */
                $attachment = $ticketReply->getAttachments()->first();

                if ($form->get('print')->isClicked()) {
                    $printer = $this->get("webdsign.printer");
                    try {
                        $printer->printFile($this->getUser(), $attachment);
                    } catch (PrinterException $exception) {
                        $this->addFlash('danger', 'Printer: ' . $exception->getMessage());
                    }
                }

                return $this->redirectToRoute('ticketShow', array('id' => $rma->getTicket()->getId()));
            }
        }

        return $this->render(
            '@ServicetoolService/Rma/letter.html.twig',
            array(
                'letterBarcode' => $rmaId,
                'rma' => $rma,
                'body' => $rmaLetter->getBody(true),
                'rmaLetterForm' => $form->createView(),
            )
        );
    }

    /**
     * @Route("/customerConfirmation/{rmaId}", name="rmaCustomerConfirmation", defaults={"print": false})
     * @Route("/customerConfirmation/{rmaId}/print", name="rmaCustomerConfirmationPrint", defaults={"print": true})
     *
     * @param int $rmaId
     * @param bool $print
     * @return Response
     */
    public function customerConfirmationAction($rmaId, $print = false)
    {
        /** @var EntityRepository $rmaRepository */
        $rmaRepository = $this->getDoctrine()->getRepository(Rma::class);
        /** @var Rma $rma */
        $rma = $rmaRepository->find($rmaId);
        if ($rma instanceof Rma) {

            $errorsResult = $this->checkForErrors($rma);
            if ($errorsResult) {
                $this->addFlash('danger', $errorsResult['message']);
                return $this->redirectToRoute('ticketShow', array('id' => $rma->getTicket()->getId()));
            }

            $pdf = new RmaCustomerLetter($rma);
            $pdf->setAddressLines(
                $rma->getCustomerName().PHP_EOL.
                $rma->getCustomerStreet().' '.$rma->getCustomerHouseNr().' '.$rma->getCustomerHouseNrExt().PHP_EOL.
                $rma->getCustomerPostalcode().' '.$rma->getCustomerCity().PHP_EOL.
                ($rma->getCustomerCountry()->getCode() != 'NL' ? $rma->getCustomerCountry()->getTitle().PHP_EOL : '').
                PHP_EOL.
                $rma->getTicket()->getCustomer()->getEmail().PHP_EOL.
                $rma->getTicket()->getCustomer()->getPhonenr()
            );
            $pdf->generateLetter();

            $attachmentName = "rma-confirmation-".$rmaId."-".date("Ymd").".pdf";
            /** @var Uploader $uploader */
            $uploader = $this->container->get('webdsign.uploader');
            $attachment = $uploader->uploadNewFile($attachmentName, $pdf->Output("S"));

            $ticketReply = new TicketReply();
            $ticketReply->setTicket($rma->getTicket());
            $ticketReply->setContent($this->t->trans('service.rma.customerletter.message'));
            $ticketReply->setReplyType('internal');
            $ticketReply->setCreated(new \DateTime());
            $ticketReply->setNotified(true);

            $ticketReply->setCreatedBy($this->get('security.token_storage')->getToken()->getUser());
            $ticketReply->addAttachment($attachment);

            $em = $this->getDoctrine()->getManager();
            $em->persist($ticketReply);
            $em->flush();

            if ($print) {
                $printer = $this->get("webdsign.printer");
                try {
                    $printer->printFile($this->getUser(), $attachment);
                } catch (PrinterException) {

                }
            }

            return $this->redirectToRoute('ticketShow', array('id' => $rma->getTicket()->getId()));
        }
        throw new NotFoundHttpException('RMA ' . $rmaId .' not found');
    }

    /**
     * @Route("/customerConfirmation/mail/{rmaId}/{lang}", name="rmaCustomerConfirmationMail")
     * @param int $rmaId
     * @param string $lang
     * @return Response
     */
    public function confirmationEmailAction($rmaId, $lang)
    {
        /** @var EntityRepository $rmaRepository */
        $rmaRepository = $this->getDoctrine()->getRepository(Rma::class);
        /** @var Rma $rma */
        $rma = $rmaRepository->find($rmaId);
        if ($rma instanceof Rma) {

            $errorsResult = $this->checkForErrors($rma);

            if (!$errorsResult) {
                /** @var RmaConfirmation $rmaConfirmation */
                $rmaConfirmation = $this->get('webdsignservice.rmaconfirmation');
                $content = nl2br($rmaConfirmation->getMailContent($rma, null, $lang));

                return new JsonResponse(
                    array(
                        'content' => $content,
                    )
                );
            } else {
                return new JsonResponse(
                    array(
                        'message' => $errorsResult['message'],
                        'errors' => $errorsResult['fields'],
                    ),
                    Response::HTTP_NOT_FOUND
                );
            }
        }
        throw new NotFoundHttpException('RMA ' . $rmaId .' not found');
    }

    protected function checkForErrors(Rma $rma): array|bool
    {
        $errors = [];

        if (empty($rma->getCustomerName())) {
            $errors[] = 'name';
        }

        if (empty($rma->getCustomerStreet())) {
            $errors[] = 'address';
        }

        if (empty($rma->getCustomerHouseNr())) {
            $errors[] = 'housenr';
        }

        if (empty($rma->getCustomerCity())) {
            $errors[] = 'city';
        }

        if (!($rma->getCustomerCountry() instanceof Country)) {
            $errors[] = 'country';
        }

        if (count($errors)) {
            $message = $this->t->trans(
                'service.rma.error.confirmationInfoMissing',
                [
                    '%fields%' => implode(
                        ", ",
                        array_map(
                            function ($field) {
                                return $this->t->trans('customer.address.label.'.$field);
                            },
                            $errors
                        )
                    )
                ]
            );

            return ['message' => $message, 'fields' => $errors];
        }

        return false;
    }

    /**
     * @Route("/postnl/{rma}", name="rmaCreatePostnlLabel")
     *
     * @param Request $request
     * @param Rma $rma
     * @return Response
     * @throws Exception
     * @throws RmaNotFoundException
     */
    public function postNlLabelAction(Request $request, Rma $rma): Response
    {
        if (!$rma instanceof Rma) {
            throw new RmaNotFoundException('RMA niet gevonden');
        }

        $mailboxRepository = $this->getDoctrine()->getRepository(Mailbox::class);
        $mailbox = $mailboxRepository->find(Mailbox::ID_SERVICE_AT_CAMERANU_DOT_NL);

        $postnlLabelData = new PostnlLabel();

        $postnlLabelData->setRmaStatus($rma->getStatus());
        $unknownServicePartnerName = $this->getParameter('webdsign.service_partner_unknown_name');
        $labelOptions = [
            'rma' => $rma,
            'user' => $this->getUser(),
            'unknownServicePartnerName' => $unknownServicePartnerName,
        ];

        $form = $this->createForm(CreatePostnlLabelType::class, $postnlLabelData, $labelOptions);

        if ($request->getMethod() === Request::METHOD_POST) {
            $form->handleRequest($request);
            if ($form->isSubmitted() && $form->isValid()) {
                $formData = $form->getData();

                if ($postnlLabelData->getRmaStatus() !== $rma->getStatus()) {
                    $rma->setStatus($postnlLabelData->getRmaStatus());
                }

                if ($postnlLabelData->getTarget() === PostnlParcelAnnounce::TYPE_RMA_TO_CUSTOMER) {
                    $customer = $rma->getTicket()->getCustomer();
                    $customerEmail = $customer instanceof Customer ? $customer->getEmail() : '';
                    $fromAddress = $rma->getTicket()->getFromAddress();
                    $mailAddress = empty($fromAddress) ? $customerEmail : $fromAddress;

                    if (filter_var($mailAddress, FILTER_VALIDATE_EMAIL) === false) {
                        $this->addFlash(
                            'danger',
                            $mailAddress . ' is niet geldig controleer of er misschien een spatie voor of achter staat'
                        );

                        return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
                    }
                }

                if ($postnlLabelData->getTarget() === PostnlParcelAnnounce::TYPE_RMA_TO_SERVICE_PARTNER) {
                    $servicePartner = $rma->getTicket()->getServicePartner();
                    $deliveryAddress = [
                        'company' => $servicePartner->getName(),
                        'name' => $servicePartner->getAttention(),
                        'street' => $servicePartner->getAddress(),
                        'houseNr' => $servicePartner->getHousenumber(),
                        'houseNrExt' => $servicePartner->getHousenumberExt(),
                        'postalcode' => $servicePartner->getZipcode(),
                        'city' => $servicePartner->getCity(),
                        'countryCode' => $servicePartner->getCountry(),
                    ];
                } else {
                    $deliveryAddress = [
                        'company' => '',
                        'name' => $rma->getCustomerName(),
                        'street' => $rma->getCustomerStreet(),
                        'houseNr' => $rma->getCustomerHouseNr(),
                        'houseNrExt' => $rma->getCustomerHouseNrExt(),
                        'postalcode' => $rma->getCustomerPostalcode(),
                        'city' => $rma->getCustomerCity(),
                        'countryCode' => $rma->getCustomerCountry()->getCode(),
                    ];
                }

                $this->packageLabelGenerator->setLogOptions([
                    'user' => $this->getUser(),
                    'rma' => $rma,
                    'order' => null,
                ]);

                $postnlPackageLabel = new PostnlPackageLabel();
                $postnlPackageLabel->setNetworkPartnerActive($this->getParameter('postnl.network_partner_active'));
                $postnlPackageLabel->setAuthToken($this->getParameter('postnl_token'));
                $postnlPackageLabel->setTransporterParams(
                    $this->container->getParameter('postnl')[$formData->getSender()]
                );

                $postnlPackageLabel->setDeliveryAddress($deliveryAddress);
                $postnlPackageLabel->setSender($formData->getSender());

                $productcodeRepository = $this->getDoctrine()->getRepository(PostnlProductcode::class);
                $productcode = $productcodeRepository->findByProductcode(PostnlPackageLabel::PRODUCT_CODE_DEFAULT_NL); // aangetekend, niet bij buren bezorgen

                $postnlPackageLabel->setProductcode($productcode);
                $params = $this->packageLabelGenerator->getTransporterParams('postnl');
                $message = $postnlPackageLabel->createTransporterPrefetchBarcodeMessage($rma);

                // send message
                $response = $this->packageLabelGenerator->sendMessage(
                    $params['barcode_url'],
                    $message,
                    $params['barcode_expected_http'],
                    'GET',
                    true,
                    false
                );

                // check barcode
                if (!isset($response['Barcode'])) {
                    $this->addFlash(
                        'danger',
                        'Kon geen Track&Trace ophalen bij PostNL'
                    );

                    return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
                }

                $mainBarcode = $postnlPackageLabel->extractTrackTraceCode($response);

                // new tracking record
                $postnlTrackingRecord = new Postnl();
                $postnlTrackingRecord->setRma($rma);
                $postnlTrackingRecord->setTrackingcode($mainBarcode);
                $this->getDoctrine()->getManager()->persist($postnlTrackingRecord);
                try {
                    $this->getDoctrine()->getManager()->flush();
                } catch (OptimisticLockException $e) {
                }

                // get a number of barcodes based on the number of requested parcels
                if ($formData->getNumberOfPackages() > 1) {
                    for ($i = 1; $i <= $formData->getNumberOfPackages(); $i++) {
                        // get barcode
                        if (!isset($postnlTrackingRecord->getChildren()[$i])) {
                            $message = $postnlPackageLabel->createTransporterPrefetchBarcodeMessage($rma);
                            $response = $this->packageLabelGenerator->sendMessage(
                                $params['barcode_url'],
                                $message,
                                $params['barcode_expected_http'],
                                'GET',
                                true,
                                false
                            );
                            $barcode = $postnlPackageLabel->extractTrackTraceCode($response);

                            $child = new Postnl();
                            $child->setRma($rma);
                            $child->setTrackingcode($barcode);
                            $child->setCreated(new DateTime('now'));
                            $postnlTrackingRecord->addChild($child);

                            $this->getDoctrine()->getManager()->persist($child);
                        }
                    }
                    try {
                        $this->getDoctrine()->getManager()->flush();
                    } catch (OptimisticLockException $e) {
                    }
                }

                // number of parcels not equal to number of existing child records, remove those records
                $trackingRecordChildren = count($postnlTrackingRecord->getChildren());
                if ($trackingRecordChildren !== $formData->getNumberOfPackages() - 1) {
                    for ($i = $formData->getNumberOfPackages() - 1; $i < $trackingRecordChildren; $i++) {
                        // remove excessive records
                        $this->getDoctrine()->getManager()->remove($postnlTrackingRecord->getChildren()[$i]);
                        $postnlTrackingRecord->removeChild($postnlTrackingRecord->getChildren()[$i]);
                    }
                    try {
                        $this->getDoctrine()->getManager()->persist($postnlTrackingRecord);
                        $this->getDoctrine()->getManager()->flush();
                    } catch (OptimisticLockException $e) {
                    }
                }

                $postnlPackageLabel->setPostnl($postnlTrackingRecord);
                $message = $postnlPackageLabel->createTransporterMessage($rma, $formData->getNumberOfPackages());

                // send message
                $response = $this->packageLabelGenerator->sendMessage(
                    $params['label_url'],
                    $message,
                    $params['label_expected_http'],
                    'POST',
                    true,
                    false
                );

                $ticketReply = new TicketReply();
                $ticketReply->setTicket($rma->getTicket());
                $ticketReply->setReplyType('internal');
                $ticketReply->setCreated(new DateTime());
                $ticketReply->setCreatedBy($this->get('security.token_storage')->getToken()->getUser());

                $em = $this->getDoctrine()->getManager();

                if (array_key_exists('Fault', $response)) {
                    $content = [];
                    foreach ($response['Fault'] as $fault) {
                        $content[] = $fault['Errors']['Omschrijving'] . ' (' . $fault['Errors']['Code'] . ')';
                    }

                    $ticketReply->setContent(
                        'PostNL meldt fouten bij het opmaken van het verzendlabel:' .
                        PHP_EOL .
                        implode(PHP_EOL, $content)
                    );
                } elseif (array_key_exists('fault', $response)) {
                    $fault = $response['fault'];
                    $content[] = $fault['faultstring'] . ' (' . $fault['detail']['errorcode'] . ')';
                    $contentString = 'PostNL meldt fouten bij het opmaken van het verzendlabel:' . PHP_EOL;
                    $contentString .= implode(PHP_EOL, $content);
                    $ticketReply->setContent($contentString);
                } elseif (array_key_exists('ResponseShipments', $response)) {
                    $labelFilenames = [];

                    foreach ($response['ResponseShipments'] as $shipment) {
                        foreach ($shipment['Labels'] as $label) {
                            $labelFilenames[] = $postnlPackageLabel->handleLabel(base64_decode($label['Content']));
                        }
                    }

                    $printer = null;
                    if ($postnlLabelData->getPrinting()) {
                        if ($postnlLabelData->getLabeltype() === PostnlLabel::LABEL_TYPE_A4) {
                            $printer = $this->getUser()->getPrinter();
                        } else {
                            $printer = $this->getUser()->getLabelPrinter();
                        }
                    }

                    foreach ($labelFilenames as $labelFilename) {
                        /** @var Uploader $uploader */
                        $uploader = $this->container->get('webdsign.uploader');
                        $attachment = $uploader->uploadNewFile(
                            str_replace('/tmp/', '', $labelFilename),
                            file_get_contents($labelFilename)
                        );
                        $ticketReply->addAttachment($attachment);
                    }

                    $content = $trackAndTraceCodes[] = $mainBarcode;
                    foreach ($postnlTrackingRecord->getChildren() as $trackingRecord) {
                        /** @var Postnl $trackingRecord */
                        $content .= '<br />' . PHP_EOL . $trackingRecord->getTrackingcode();
                        $trackAndTraceCodes[] = $trackingRecord->getTrackingcode();
                    }

                    $ticketReply->setContent(
                        $this->t->trans('service.rma.postnl.message', [
                            '%trackcode%' => $content,
                            '%to%' => (!empty($deliveryAddress['company']) ? $deliveryAddress['company'] . ' ' : '') . $deliveryAddress['name'],
                        ]) .
                        ($printer !== null ?
                            PHP_EOL . $this->t->trans('service.rma.postnl.message.print', [
                                '%printer%' => $printer,
                            ])
                            :
                            ''
                        )
                    );

                    // Make ticket reply to customer with the track and trace code
                    $closeTicket = false;
                    if ($postnlLabelData->getTarget() === PostnlParcelAnnounce::TYPE_RMA_TO_CUSTOMER) {
                        $handleRmaReply = $this->container->get('handle_rma_reply');

                        if ($handleRmaReply instanceof HandleRmaReply) {
                            $handleRmaReply->trackAndTraceToCustomer(
                                $rma,
                                $mailbox,
                                $trackAndTraceCodes,
                                true
                            );
                        }

                        $closeTicket = true;
                    }

                    // Set the ticket status to closed of the option for rma state change to done was selected
                    if (
                        $closeTicket === true || (
                            $rma->getStatus() !== null &&
                            $rma->getStatus()->getId() === RmaStatus::STATE_DONE
                        )
                    ) {
                        $rma->getTicket()->setState(Ticket::STATE_CLOSED);
                        $em->flush();
                    }

                    // Print the label
                    if ($printer !== null && isset($attachment)) {
                        usleep(1000000);
                        if ($postnlLabelData->getLabeltype() === PostnlLabel::LABEL_TYPE_A4) {
                            $this->get('webdsign.printer')->printFile($this->getUser(), $attachment);
                        } else {
                            $this->get('webdsign.printer')->printLabel($this->getUser(), $attachment);
                        }
                    }
                } else {
                    $contentString = 'PostNL meldt fouten bij het opmaken van het verzendlabel:' . PHP_EOL;
                    $contentString .= 'Onbekende error (controleer adres van ontvanger bijv. is de postcode geldig?)';
                    $ticketReply->setContent($contentString);
                }

                $em->persist($ticketReply);
                $em->flush();

                return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
            }
        }

        return $this->render(
            '@ServicetoolService/Rma/postnl.html.twig',
            [
                'rma' => $rma,
                'rmaPostnlForm' => $form->createView(),
            ]
        );
    }

    /**
     * Genereer het RMA submenu
     *
     * @return Response
     */
    public function renderMenuAction()
    {
        /** @var ServiceTagRepository $tagRepository */
        $tagRepository = $this->getDoctrine()->getRepository(ServiceTag::class);
        $rmaTags = $tagRepository->getActiveFromCategory('rma');

        return $this->render(
            '@ServicetoolService/Rma/menu.html.twig',
            array(
                'tags' => $rmaTags,
            )
        );
    }

    /**
     * @Route("/statusForTag/{tagIds}", name="rmaStatusListForTag")
     * @param int|string $tagIds
     * @return Response
     */
    public function statusForTagAction($tagIds)
    {
        /** @var \Doctrine\ORM\EntityRepository $statusRepository */
        $statusRepository = $this->getDoctrine()->getRepository(RmaStatus::class);

        $tagIdList = explode(',', $tagIds);
        $queryBuilder = $statusRepository->createQueryBuilder('s');
        $queryBuilder->join('s.tags', 't')
            ->where('t.id IN (:tagIds)')
            ->andWhere('t.category = :tagCategory')
            ->orderBy('s.sortOrder', 'ASC')
            ->setParameter('tagCategory', 'rma')
            ->setParameter('tagIds', $tagIdList);
        /** @var RmaStatus[] $result */
        $result = $queryBuilder->getQuery()->getResult();
        $statusList = array();
        foreach($result as $rmaStatus) {
            $statusList[] = array('id' => $rmaStatus->getId(), 'text' => $rmaStatus->getTitle());
        }

        return new JsonResponse($statusList);
    }

    /**
     * @Route("/export", name="exportRmaTickets")
     * @param Request $request
     * @return Response
     */
    public function exportTicketListAction(Request $request)
    {
        /** @var TicketList $ticketList */
        $ticketList = $this->get('servicetool.ticketlist');

        $form = $this->createForm(TicketFilterType::class, $ticketList->getTicketFilter(), array(
            'doctrine' => $this->getDoctrine(),
            'cache' => $this->get('memcached'),
            'translate' => $this->t,
            'isRmaView' => true,
        ));

        $ticketList->handleRequest($request, $form);
        $query = $ticketList->getTicketsQuery();

        $paginator = $this->get('knp_paginator');

        $direction = ($request->get('direction')) ? $request->get('direction') : 'asc';
        // filter bepaalde sorteringen uit, die voor export niet mogelijk zijn
        $sort = array_filter(($request->get('sort') ? explode("+", $request->get('sort')) : array()), function($value) {
            return substr($value, 0, 2) !== 'f.';
        });
        if (count($sort) === 0) {
            $sort = array('t.state', 't.lastReplyDate');
        }
        $pagination = $paginator->paginate($query, 1, PHP_INT_MAX, array(
            'sortFieldParameterName' => uniqid('sort'), // workaround: voorkom dat de paginator parameters uit de url gebruikt
            'defaultSortFieldName' => implode("+", $sort), 'defaultSortDirection' => $direction, 'wrap-queries' => true,
        ));

        $pdf = new Letter();
        $pdf->addPlainBody($this->t->trans("service.rma.export.text", array('%date%' => date('d-m-Y H:i'))));
        $pdf->Ln();

        $table = array();

        foreach($pagination as $item) {
            /** @var Ticket $item */

            if ($item->getRma() instanceof Rma) {
                foreach($item->getRma()->getRmaProducts() as $rmaProduct)
                $table[] = array(
                    "RMA" => $item->getRma()->getId(),
                    "Serienummer" => $rmaProduct->getBarcode(),
                    "Product" => $rmaProduct->getTitle(),
                );
            }
        }
        $pdf->printHorizontalTable($table);

        return new Response($pdf->Output('S'), Response::HTTP_OK, array(
            'Content-Type'=>'application/pdf',
            'Content-Disposition'=>'attachment; filename=export-rma-'.date('YmdHis').'.pdf;'
        ));
    }

    /**
     * @Route("/ticket/{rmaId}", name="rmaGotoTicket")
     * @param int $rmaId
     * @return RedirectResponse
     */
    public function gotoTicketAction($rmaId)
    {
        /** @var EntityRepository $rmaRepository */
        $rmaRepository = $this->getDoctrine()->getRepository(Rma::class);
        /** @var Rma $rma */
        $rma = $rmaRepository->find($rmaId);
        if (!$rma) {
            throw new NotFoundHttpException("RMA ".$rmaId." not found!");
        }
        return $this->redirectToRoute('ticketShow', array('id' => $rma->getTicket()->getId()));
    }

    /**
     * @Route("/check/{rma}", name="rmaCheck")
     * @param Rma $rma
     * @return JsonResponse
     */
    public function ajaxScanAction(Rma $rma)
    {
        $response = [
            'rmaId' => $rma->getId(),
            'zip' => $rma->getCustomerPostalcode(),
            'houseNr' => $rma->getCustomerHouseNr(),
            'statusTitle' => $rma->getStatus()->getTitle(),
            'status' => 'not ok'
        ];

        if ($rma->getStatus()->getId() === $this->container->getParameter('rma_status_not_received')) {
            $response['status'] = 'ok';
        }

        return new JsonResponse($response);
    }

    /**
     * @Route("/process/{rma}", name="rmaProcess")
     * @param Rma $rma
     * @return JsonResponse
     */
    public function scanProcessAction(Rma $rma)
    {
        $rmaNotReceived = $this->container->getParameter('rma_status_not_received');

        if ($rma->getStatus()->getId() === $rmaNotReceived) {
            $entityManager = $this->getDoctrine()->getManager();
            $workflow = $rma->getStatus()->getWorkflowForTag($rma->getRmaTag())->current();
            if ($workflow instanceof RmaWorkflow) {
                $nextStatus = $workflow->getNextStatus();

                if ($nextStatus instanceof RmaStatus && null !== $workflow->getKey()) {
                    $scanAction = 'rma.scan.action.' . strtolower($workflow->getKey());
                    if ($this->has($scanAction)) {
                        $scanAction = $this->get($scanAction);
                        if ($scanAction instanceof ScanActionInterface) {
                            $scanAction->execute($rma, $workflow, $this->getUser());
                            $rma->setStatus($nextStatus);
                            $entityManager->persist($rma);
                            $entityManager->flush();
                        }
                    }
                    return new JsonResponse(['processed' => true]);
                }
            }
        }
        return new JsonResponse(['processed' => false]);
    }

    /**
     * @Route("/pdascan", name="pdaScan")
     * @param Request $request
     * @return Response
     */
    public function pdaScanAction(Request $request)
    {
        $searchForm = $this->createForm(RmaSearchType::class);
        return $this->render(
            '@ServicetoolService/Rma/pdascan.html.twig',
            [
                'searchRmaForm' => $searchForm->createView(),
            ]
        );
    }

    /**
     * @Route("/scan", name="rmaScan")
     * @param Request $request
     * @return Response
     */
    public function scanAction(Request $request)
    {
        $searchForm = $this->createForm(RmaSearchType::class);
        $searchForm->handleRequest($request);

        /** @var Rma $rma */
        $rma = null;
        if ($searchForm->isSubmitted() && $searchForm->isValid()) {
            $data = $searchForm->getData();
            $rmaRepository = $this->getDoctrine()->getRepository(Rma::class);
            $rma = $rmaRepository->find($data['query']);

            if ($rma instanceof Rma) {
                if ($rma->getStatus() instanceof RmaStatus && $rma->getStatus()->getWorkflowForTag($rma->getRmaTag())->count()) {
                    return $this->redirectToRoute('rmaSwitchStatus', ['rmaId' => $rma->getId()]);
                } else {
                    return $this->redirectToRoute('ticketShow', array('id' => $rma->getTicket()->getId()));
                }
            }
        }

        return $this->render(
            '@ServicetoolService/Rma/scan.html.twig',
            [
                'searchRmaForm' => $searchForm->createView(),
            ]
        );
    }

    /**
     * @Route("/statusChange/{rmaId}", name="rmaSwitchStatus")
     * @param int $rmaId
     * @param Request $request
     * @return Response
     */
    public function switchStatusAction($rmaId, Request $request)
    {
        $rmaRepository = $this->getDoctrine()->getRepository(Rma::class);
        /** @var Rma $rma */
        $rma = $rmaRepository->find($rmaId);
        if (!$rma instanceof Rma || $rma->getStatus() === null) {
            throw new NotFoundHttpException('Rma niet gevonden of ongeldige status');
        }
        $workflows = $rma->getStatus()->getWorkflowForTag($rma->getRmaTag());

        $updateRmaForm = $this->createForm(RmaUpdateStatusType::class, $rma);
        $updateRmaForm->handleRequest($request);
        if ($updateRmaForm->isSubmitted() && $updateRmaForm->isValid()) {

            if ($updateRmaForm->getClickedButton()->getName() === 'submit') {
                $this->getDoctrine()->getManager()->persist($rma);
                $this->getDoctrine()->getManager()->flush();

                // find workflow
                foreach($workflows as $workflow) {
                    if ($workflow->getNextStatus() === $rma->getStatus()) {
                        if (null !== $workflow->getKey()) {
                            if ($this->has('rma.scan.action.' . strtolower($workflow->getKey()))) {
                                $scanAction = $this->get('rma.scan.action.' . strtolower($workflow->getKey()));
                                if ($scanAction instanceof ScanActionInterface) {
                                    $scanAction->execute($rma, $workflow, $this->getUser());
                                }
                            }
                        }
                        break;
                    }
                }
            }

            if ($updateRmaForm->get('return')->getData()) {
                return $this->redirectToRoute('ticketShow', array('id' => $rma->getTicket()->getId()));
            } else {
                return $this->redirectToRoute('rmaScan');
            }
        }

        return $this->render(
            '@ServicetoolService/Rma/switch-status.html.twig',
            [
                'updateRmaForm' => isset($updateRmaForm) ? $updateRmaForm->createView() : null,
                'rma' => $rma,
            ]
        );
    }

    /**
     * @Route("/auto/{action}/{rmaId}", name="rmaAutomation")
     * @param Request $request
     * @param string $action
     * @param int $rmaId
     * @return Response
     * @throws Exception
     */
    public function automationAction(Request $request, $action, $rmaId)
    {
        $entityManager = $this->getDoctrine()->getManager();
        $rmaRepository = $entityManager->getRepository(Rma::class);
        $externalContactRepository = $this->getDoctrine()->getRepository(ExternalContact::class);
        $unknownServicePartnerName = $this->getParameter('webdsign.service_partner_unknown_name');
        $rma = $rmaRepository->find($rmaId);
        if (!$rma instanceof Rma) {
            throw new NotFoundHttpException('Invalid RMA:' . $rmaId);
        }

        switch ($action) {
            case 'process-return':
            case 'process-vendiro-return':
            case 'process-return-money':
            case 'process-vendiro-return-money':
                $orders = $rma->getTicket()->getOrdersForTicket();
                $canExecute = true;
                if (count($orders) !== 1 || !$orders[0]->getInvoiceNumber()) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-order-bound'));
                    $canExecute = false;
                } elseif (!$rma->hasRmaProductWithStockItem()) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-stock-items'));
                    $canExecute = false;
                } elseif ($rma->getOrderRma()->count() > 1) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.max-one-rma-order-bound'));
                    $canExecute = false;
                }
                if (!$canExecute) {
                    return $this->render('popup.html.twig', [
                        'redirectParent' => $this->get('router')->generate('ticketShow', [
                            'id' => $rma->getTicket()->getId()
                        ]),
                        'close' => true,
                    ]);
                }
                $rmaAction = $this->get('rma.button.action.process_return');
                $returnAction = $rmaAction::ACTION_ONLY_RETURN;
                if (in_array($action, ['process-return-money', 'process-vendiro-return-money'])) {
                    $returnAction = $rmaAction::ACTION_MONEY_RETURN;
                }
                try {
                    $returnUrl = $rmaAction->execute($rma, $orders[0], $this->getUser(), $returnAction);
                } catch (InvalidPaymentMethodException $exception) {
                    return $this->render('error.html.twig', [
                        'page_title' => $this->t->trans('service.rma.action.error.invalid-payment-method')
                    ]);
                } catch (OrderInfoCustomerNotFoundException $exception) {
                    return $this->render('error.html.twig', [
                        'page_title' => sprintf(
                            $this->t->trans('service.rma.error.noCustomerFound'),
                            $exception->getOrderInfo()->getId()
                        )
                    ]);
                }
                return $this->redirect($returnUrl);

            case 'process-doa-product-return':
            case 'process-doa-money-return':
            case 'process-vendiro-doa-product-return':
            case 'process-vendiro-doa-money-return':
                $canExecute = true;
                $servicePartner = $rma->getTicket()->getServicePartner();
                $orders = $rma->getTicket()->getOrdersForTicket();
                if (!$servicePartner instanceof ExternalContact) {
                    // eerst kijken of we wellicht een automagische koppeling kunnen maken
                    $servicePartner = $externalContactRepository->findForRma($rma, true);
                    $rma->getTicket()->setServicePartner($servicePartner);
                    $entityManager->persist($rma->getTicket());
                    $entityManager->flush();

                    if (!$servicePartner instanceof ExternalContact) {
                        $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-servicePartner-bound'));
                        $canExecute = false;
                    }
                } elseif (count($orders) !== 1 || !$orders[0]->getInvoiceNumber()) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-order-bound'));
                    $canExecute = false;
                } elseif (!$rma->hasRmaProductWithStockItem()) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-stock-items'));
                    $canExecute = false;
                } elseif ($rma->getOrderRma()->count() > 1 && $action === 'process-doa-product-return') {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.max-one-rma-order-bound'));
                    $canExecute = false;
                } elseif ($orders[0] instanceof OrderInfo && !$orders[0]->getPaymentMethod() instanceof PaymentMethod) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-paymentMethod'));
                    $canExecute = false;
                }

                if (!$canExecute) {
                    return $this->render('popup.html.twig', [
                        'redirectParent' => $this->get('router')->generate('ticketShow', ['id' => $rma->getTicket()->getId()]),
                        'close' => true,
                    ]);
                }

                $rmaAction = $this->get('rma.button.action.process_doa');
                $doaAction = $rmaAction::ACTION_PRODUCT_RETURN;
                if (in_array($action, ['process-doa-money-return', 'process-vendiro-doa-money-return'])) {
                    $doaAction = $rmaAction::ACTION_MONEY_RETURN;

                    // Zet ticket 3 werkdagen in behandeling
                    $ticket = $rma->getTicket();
                    $ticket->setWaitUntil(new \DateTime('+' . Ticket::DEFAULT_WAITING_DAYS . ' days'));

                    $em = $this->getDoctrine()->getManager();
                    $em->persist($ticket);
                    $em->flush();
                }
                $returnUrl = $rmaAction->execute($rma, $orders[0], $this->getUser(), $doaAction);

                return $this->redirect($returnUrl);

            case 'request-rma':
            case 'request-vendiro-rma':
                $servicePartner = $rma->getTicket()->getServicePartner();
                if (!$servicePartner instanceof ExternalContact) {
                    // eerst kijken of we wellicht een automagische koppeling kunnen maken
                    $servicePartner = $externalContactRepository->findForRma($rma, true);
                    $rma->getTicket()->setServicePartner($servicePartner);
                    $entityManager->persist($rma->getTicket());
                    $entityManager->flush();

                    if (!$servicePartner instanceof ExternalContact) {
                        $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-servicePartner-bound'));
                        return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
                    }
                } elseif ($servicePartner->getName() === $unknownServicePartnerName) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.unknown-servicePartner-bound'));
                    return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
                }

                $ticket = $rma->getTicket();
                $ticket->setWaitUntil(new DateTime('+1 week'));
                $ticket->setState(Ticket::STATE_PENDING);
                $ticket->setAssignee(null);
                $entityManager->flush();

                $rmaAction = $this->get('rma.button.action.request_rma');

                $returnUrl = $rmaAction->execute($rma, $this->getUser());
                return $this->redirect($returnUrl);

            case 'process-rma':
            case 'process-vendiro-rma':
                $servicePartner = $rma->getTicket()->getServicePartner();
                if (!$servicePartner instanceof ExternalContact) {
                    // eerst kijken of we wellicht een automagische koppeling kunnen maken
                    $servicePartner = $externalContactRepository->findForRma($rma, true);
                    $rma->getTicket()->setServicePartner($servicePartner);
                    $entityManager->persist($rma->getTicket());
                    $entityManager->flush();

                    if (!$servicePartner instanceof ExternalContact) {
                        $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-servicePartner-bound'));
                        return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
                    }
                } elseif ($servicePartner->getName() === $unknownServicePartnerName) {
                    $this->addFlash('warning', $this->t->trans('service.rma.action.notice.unknown-servicePartner-bound'));
                }

                $rmaAction = $this->get('rma.button.action.process_rma');
                $mailToCustomer = true;
                if (in_array(strtolower($rma->getRmaTag()->getName()), ['doa', 'doa (leverancier)', 'vendiro doa'])) {
                    $mailToCustomer = false;
                }

                $ticket = $rma->getTicket();
                $ticket->setWaitUntil(new \DateTime('+' . Ticket::DEFAULT_WAITING_DAYS . ' days'));
                $ticket->setState(Ticket::STATE_PENDING);
                $ticket->setAssignee(null);
                $entityManager->flush();

                $orders = $rma->getTicket()->getOrdersForTicket();
                $orderInfo = $orders[0] ?? null;
                $returnUrl = $rmaAction->execute($rma, $this->getUser(), $orderInfo, $mailToCustomer);

                if ($returnUrl === false) {
                    $this->addFlash('danger', $this->t->trans('service.rma.action.error.no-email'));
                    return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
                } elseif (null === $returnUrl) {
                    return $this->redirectToRoute('ticketShow', ['id' => $rma->getTicket()->getId()]);
                }

                return $this->redirect($returnUrl);

            default:
                throw new NotFoundHttpException('Unknown action:' . $action);
        }
    }
}
