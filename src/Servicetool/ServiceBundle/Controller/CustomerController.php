<?php

declare(strict_types=1);

namespace Servicetool\ServiceBundle\Controller;

use CatBundle\Producer\Robin\RobinHookProducer;
use CatBundle\Producer\SqueezelyCRMUpdate\SqueezelyCRMUpdateProducer;
use CatBundle\Service\Customer\CustomerHelper;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\ORMException;
use Doctrine\Persistence\ManagerRegistry;
use ErrorException;
use Knp\Component\Pager\PaginatorInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Servicetool\ServiceBundle\Services\OpenInvoiceHelper;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\MapQueryParameter;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;
use Webdsign\GlobalBundle\Controller\AbstractWebdsignController;
use Webdsign\GlobalBundle\Entity\Country;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\Customer\Segment;
use Webdsign\GlobalBundle\Entity\Customer\SegmentRepository;
use Webdsign\GlobalBundle\Entity\CustomerAddress;
use Webdsign\GlobalBundle\Entity\CustomerContact;
use Webdsign\GlobalBundle\Entity\InstantQuote\Quote;
use Webdsign\GlobalBundle\Entity\MailingTypes;
use Webdsign\GlobalBundle\Entity\Payment\PaymentPeriod;
use Webdsign\GlobalBundle\Entity\Payment\PaymentPeriodRepository;
use Webdsign\GlobalBundle\Entity\Postalcode;
use Webdsign\GlobalBundle\Entity\Ticket;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\WebuserMailingTypes;
use Webdsign\GlobalBundle\Form\Type\SaveCustomerType;
use Webdsign\GlobalBundle\Form\Type\SaveAddressType;
use Webdsign\GlobalBundle\Form\Type\SaveContactType;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Webdsign\GlobalBundle\Entity\Mail;
use Webdsign\GlobalBundle\Form\Type\SaveMailingPreferencesType;
use Webdsign\GlobalBundle\Logger;
use Webdsign\GlobalBundle\Model\Search;
use Webdsign\GlobalBundle\Model\SearchRepository;
use Webdsign\GlobalBundle\Form\Type\AutocompleteType;
use Symfony\Component\HttpFoundation\JsonResponse;
use Servicetool\ServiceBundle\Services\mailPreferencesLog;
use Webdsign\GlobalBundle\Entity\SkreprcallPhone;
use Webdsign\GlobalBundle\Services\ClangWebHook;

/**
 * @Route("/customers")
 */
class CustomerController extends AbstractWebdsignController
{
    public function __construct(
        HttpKernelInterface $httpKernel,
        TranslatorInterface $t,
        Logger $l,
        TokenStorageInterface $tokenStorage,
        AuthorizationCheckerInterface $authorizationChecker,
        CsrfTokenManagerInterface $csrfTokenManager,
        ManagerRegistry $registry,
        PaginatorInterface $paginator,
        Environment $twig,
        RouterInterface $router,
        RequestStack $requestStack,
        FormFactoryInterface $formFactory,
        SerializerInterface $serializer,
        private readonly SqueezelyCRMUpdateProducer $squeezelyCRMUpdateProducer,
        $mustHaveResource = false,
    ) {
        parent::__construct(
            $httpKernel,
            $t,
            $l,
            $tokenStorage,
            $authorizationChecker,
            $csrfTokenManager,
            $registry,
            $paginator,
            $twig,
            $router,
            $requestStack,
            $formFactory,
            $serializer,
            $mustHaveResource
        );
    }

    /**
     * @Route("/", name="customerList")
     * @throws NotFoundExceptionInterface
     * @throws ContainerExceptionInterface
     */
    public function listAction(Request $request): Response
    {
        $search = new Search();
        $search->setAutocomplete(true);
        $search->setLimit(20);

        $searchRepository = new SearchRepository($this->container->get('fos_elastica.finder.app_customer'));

        $form = $this->createForm(AutocompleteType::class, $search, [
            'type' => 'customer',
        ]);

        $form->handleRequest($request);
        $search = $form->getData();
        $results = $searchRepository->search($search);

        if ($request->isXmlHttpRequest()) {
            return new JsonResponse([
                'content' => $this->renderView('@ServicetoolService/Customer/ajax.html.twig', [
                    'results' => $results,
                    'edit' => true,
                ])
            ]);
        }

        return $this->render('@ServicetoolService/Customer/ajaxlist.html.twig', [
            'page_title' => $this->t->trans('customer.list.header'),
            'form' => $form->createView(),
        ]);
    }

    public function searchForCustomer($request, $excludeId = false): RedirectResponse|array
    {
        $data = $items = [];
        $form = $this->createFormBuilder($data)
            ->add('name', TextType::class, ['required' => false, 'label' => 'customer.search.name'])
            ->add('email', TextType::class, ['required' => false, 'label' => 'customer.search.email'])
            ->add('adres', TextType::class, ['required' => false, 'label' => 'customer.search.adres'])
            ->add('Zoek', SubmitType::class, ['label' => 'customer.search.submit'])
            ->getForm();

        $pagination = $totalCustomers = $maxPages = $thisPage = false;

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);
            $data = $form->getData();
            if (count($data) > 0) {
                $page = 1;
                $this->get('session')->set('searchData', $data);
            }
        }

        if (count($data) == 0 && $request->query->get('page') !== null) {
            $data = $this->get('session')->get('searchData');
        }

        if (is_array($data) && count($data) > 0) {
            $repository = $this->getDoctrine()->getRepository(Customer::class);
            $repository->addAddress()->setSearchData($data);

            if ($excludeId != false) {
                $repository->setExcludeId($excludeId);
            }

            if (!isset($page)) {
                $page = ($request->query->get('page')) ? $request->query->get('page') : 1; // get a $_GET parameter
            }

            $repository->setPaginate(true);
            $customers = $repository->getCustomers()->get();

            $paginator = $this->get('knp_paginator');
            $pagination = $paginator->paginate($customers, $page);
            $totalCustomers = $pagination->getTotalItemCount();

            $limit = 10;
            $maxPages = ceil($totalCustomers / $limit);
            $thisPage = $page;

            /** @todo wat is dit? */
            if ($totalCustomers == 112) { //1 klant, overzicht niet nodig.
                foreach ($customers as $customer) {
                    return $this->redirectToRoute('customerShow', ['id' => $customer->getId()]);
                }
            }
        }

        return [
            'customers' => $pagination,
            'totalCustomers' => $totalCustomers,
            'maxPages' => $maxPages,
            'thisPage' => $thisPage,
            'form' => $form,
        ];
    }

    /**
     * @Route("/add", name="customerAdd", defaults={"id" = false, "ticketId" = false, "mailId" = false})
     * @Route("/addForTicket/{ticketId}", name="customerAddForTicket", defaults={"id" = false, "mailId" = false})
     * @Route("/addForMail/{mailId}", name="customerAddForMail", defaults={"id" = false, "ticketId" = false})
     * @Route("/edit/{id}", name="customerEdit", defaults={"ticketId" = false, "mailId" = false})
     * @throws ORMException
     */
    public function saveAction(
        $id,
        $ticketId,
        $mailId,
        Request $request,
        #[MapQueryParameter] int $quoteId = null
    ): Response {
        $repository = $this->getDoctrine()->getRepository(Customer::class);
        $segmentRepository = $this->getDoctrine()->getRepository(Segment::class);
        $ticket = $defaultPaymentPeriodId = null;

        $paymentPeriodRepository = $this->getDoctrine()->getRepository(PaymentPeriod::class);
        if ($paymentPeriodRepository->getDefault() instanceof PaymentPeriod) {
            $defaultPaymentPeriodId = $paymentPeriodRepository->getDefault()->getId();
        }

        $em = $this->getDoctrine()->getManager();

        if ($id === false) {
            $entity = new Customer();
            $pageTitle = $this->t->trans('customer.add');

            $entity->setTstamp(date('Y-m-d H:i:s'));
            $entity->setFlags(128);
            $entity->setPassword(substr(str_shuffle('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 64));
            $entity->setIdentifyHash(str_replace(['/', '=', '+'], '', base64_encode(openssl_random_pseudo_bytes(12))));

            if ($ticketId) {
                $ticketRepository = $this->getDoctrine()->getRepository(Ticket::class);
                $ticket = $ticketRepository->find($ticketId);
                if ($ticket) {
                    $entity->setEmail($ticket->getFromAddress());
                    $entity->setOrigin($ticket->getOrigin());
                }
            }

            if ($mailId) {
                $mailRepository = $this->getDoctrine()->getRepository(Mail::class);
                $mail = $mailRepository->find($mailId);
                if ($mail instanceof Mail) {
                    $entity->setFirstName($mail->getFromname());
                    $entity->setEmail($mail->getFromaddress());
                    $entity->setOrigin($mail->getOrigin());
                }
            }

            //set default mailingTypes options for new customers
            $mailingTypesRepository = $this->get('mailing_types_repository');
            $defaultMailingTypes = $mailingTypesRepository->getAllForNewUsers();

            /** @var WebuserMailingTypes $mailingTypes */
            foreach ($defaultMailingTypes as $selectedMailingType) {
                $webUserMailingType = new WebuserMailingTypes();
                $entity->setMailable(1);
                $webUserMailingType->setMailingTypeId($selectedMailingType);
                $webUserMailingType->setWebUser($entity);
                $em->persist($webUserMailingType);
            }
        } else {
            $entity = $repository->find($id);
            if (!$entity) {
                throw new NotFoundHttpException($this->t->trans('core.pagenotfound'));
            }

            $pageTitle = $this->t->trans('customer.edit');
        }

        /** @var User $user */
        $user = $this->get('security.token_storage')->gettoken()->getuser();

        $readonly = $entity->isReadOnly();
        $userCanEdit = $user->hasAccess('edit-customer');
        $administrationUser = $user->hasRole('administratie');
        $purchaserUser = $user->hasRole('inkoop');
        $kccUser = $user->hasRole('service-afdeling');

        if ($readonly === true) {
            $readonly = !$userCanEdit;
        }

        if ($quoteId) {
            $request->getSession()->set('add_customer_to_quote', $quoteId);
        }

        $form = $this->createForm(SaveCustomerType::class, $entity, [
            'ticket' => $ticket,
            'editOrigin' => (bool)$id,
            'readonly' => $readonly,
            'userCanEdit' => $userCanEdit,
            'administrationUser' => $administrationUser,
            'purchaserUser' => $purchaserUser,
            'kccUser' => $kccUser,
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->persist($entity);
            $em->flush();

            $closeWindow = false;
            $quoteId = $request->getSession()->get('add_customer_to_quote');
            if ($quoteId) {
                $quote = $em->getRepository(Quote::class)->find($quoteId);
                if ($quote instanceof Quote) {
                    $quote->setCustomer($entity);
                    $em->flush();
                    $closeWindow = true;
                }
            }

            /** @var RobinHookProducer $producer */
            $producer = $this->get('producer.create_robin_hook');
            $producer->publish(json_encode(['customerId' => $entity->getId()]));

            /** @var ClangWebHook $clangWebHook */
            $clangWebHook = $this->get('webdsign.clang_web_hook');
            $clangWebHook->handleData(['customerId' => $entity->getId()], 'customer');

            $this->addFlash('success', $this->t->trans('core.save.success'));

            if ($form->has('addTicket') && $form->get('addTicket')->getData()) {
                $ticket = $form->get('ticket')->getData();
                if ($ticket) {
                    $ticket->setCustomer($entity);
                    $em->persist($ticket);
                    $em->flush();
                    return $this->redirectToRoute('ticketShow', ['id' => $ticket->getId()]);
                }
            }

            $this->squeezelyCRMUpdateProducer->publish(json_encode(['customerId' => $entity->getId()]));

            return $this->redirectToRoute('customerShow', ['id' => $entity->getId(), 'closeWindow' => $closeWindow]);
        }

        return $this->render('@ServicetoolService/Customer/save.html.twig', [
            'page_title'    => $pageTitle,
            'form'          => $form->createView(),
            'fromDate'      => date('Y-m-d', strtotime('-110 year', time())),
            'curDate'       => $entity->getBirthday(),
            'newCustomer'   => !$id,
            'segments'      => $segmentRepository->getRootNodesAsEntities(),
            'defaultPaymentPeriodId' => $defaultPaymentPeriodId,
        ]);
    }

    /**
     * @Route("/addAddress/{customerId}", name="addressAdd", defaults={"id" = false})
     * @Route("/editAddress/{id}", name="addressEdit", defaults={"customerId" = false})
     * @throws ORMException
     */
    public function saveAddressAction($customerId, $id, Request $request): Response
    {
        $repository = $this->getDoctrine()->getRepository(CustomerAddress::class);

        if ($id !== false) {
            $entity = $repository->find($id);
            if (!$entity) {
                throw new NotFoundHttpException($this->t->trans('customer.404'));
            }
            $pageTitle = $this->t->trans('customer.address.edit');
            $type = 'edit';
        } elseif ($customerId !== false) {
            $entity = new CustomerAddress();
            $customer = $this->getDoctrine()->getRepository(Customer::class)->find($customerId);
            if ($customer !== null) {
                $entity->setCustomer($customer);
                $pageTitle = $this->t->trans('customer.address.add');
                $type = 'add';
                $entity->setName($customer->getName());
                $entity->setFirstName($customer->getFirstName());
                $entity->setLastNamePrefix($customer->getLastNamePrefix());
                $entity->setLastName($customer->getLastName());
                $entity->setSex($customer->getSex());
            } else {
                throw new NotFoundHttpException($this->t->trans('customer.user.notexist'));
            }
        } else {
            throw new NotFoundHttpException($this->t->trans('customer.404'));
        }

        // Get postal code url and pass to form
        $url = $this->generateUrl('postalcode');
        $form = $this->createForm(SaveAddressType::class, $entity, ['url' => $url]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em = $this->getDoctrine()->getManager();
            $values = $request->request->all()['saveaddressbox'] ?? [];
            if (array_key_exists('isSendAddress', $values)) {
                $changeAddresses = $repository->findByWebuserid($entity->getWebuserid());
                foreach ($changeAddresses as $checkAddress) {
                    if ($checkAddress->getId() === $entity->getId()) {
                        continue;
                    }
                    if ($checkAddress->isSendAddress()) {
                        $checkAddress->setIsSendAddress(false);
                        $em->persist($checkAddress);
                    }
                }
            }
            if (array_key_exists('isMainAddress', $values)) {
                $changeAddresses = $repository->findByWebuserid($entity->getWebuserid());
                foreach ($changeAddresses as $checkAddress) {
                    if ($checkAddress->getId() === $entity->getId()) {
                        continue;
                    }
                    if ($checkAddress->isMainAddress()) {
                        $checkAddress->setIsMainAddress(false);
                        $em->persist($checkAddress);
                    }
                }
            }

            $em = $this->getDoctrine()->getManager();
            $em->persist($entity);
            $em->flush();

            /** @var ClangWebHook $clangWebHook */
            $clangWebHook = $this->get('webdsign.clang_web_hook');
            $clangWebHook->handleData(['customerId' => $entity->getWebuserid()], 'customer');

            if ($type === 'add') {
                $this->addFlash('success', $this->t->trans('customer.address.saved'));
            } else {
                $this->addFlash('success', $this->t->trans('customer.changes.success'));
            }

            if ($entity->getCustomer() !== false) {
                return $this->redirectToRoute('customerShow', ['id' => $entity->getCustomer()->getId()]);
            }
        }

        return $this->render('@ServicetoolService/Customer/save.html.twig', [
            'page_title'    => $pageTitle,
            'form'          => $form->createView(),
            'fromDate'      => date('Y-m-d', strtotime('-110 years', time())),
        ]);
    }

    /**
     * @Route("/deleteAddress/{id}", name="addressDelete")
     */
    public function deleteAddressAction($id): Response
    {
        $repository = $this->getDoctrine()->getRepository(CustomerAddress::class);
        $em = $this->getDoctrine()->getManager();
        $entity = $repository->find($id);
        $t = $this->get('translator');

        if (!$entity) {
            throw new NotFoundHttpException($t->trans('core.pagenotfound'));
        } else {
            $em->remove($entity);
            $em->flush();

            $this->addFlash(
                'success',
                $t->trans('customer.address.deleted')
            );

            return $this->redirectToRoute('customerShow', ['id' => $entity->getCustomer()->getId()]);
        }
    }


    /**
     * @Route("/addContact/{customerId}", name="contactAdd", defaults={"id" = false})
     * @Route("/editContact/{id}", name="contactEdit", defaults={"customerId" = false})
     */
    public function saveContactAction($customerId, $id, Request $request): Response
    {
        $type = false;
        $entity = $customer = null;
        $pageTitle = $this->t->trans('customer.contact.add');

        if ($id !== false) {
            $repository = $this->getDoctrine()->getRepository(CustomerContact::class);
            $entity = $repository->find($id);
            if (!$entity) {
                throw new NotFoundHttpException($this->t->trans('customer.404'));
            }
            $customer = $entity->getCustomer();
            $pageTitle = $this->t->trans('customer.contact.edit');
            $type = 'edit';
        } elseif ($customerId !== false) {
            $entity = new CustomerContact();
            $type = 'add';

            $customer = $this->getDoctrine()->getRepository(Customer::class)->find($customerId);
            if ($customer !== null) {
                $entity->setCustomer($customer);
            } else {
                throw new NotFoundHttpException($this->t->trans('customer.user.notexist'));
            }
        }

        $form = $this->createForm(SaveContactType::class, $entity);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var User $user */
            $user = $this->get('security.token_storage')->gettoken()->getuser();
            $entity->setUid($user->getId());
            $em = $this->getDoctrine()->getManager();
            $em->persist($entity);
            $em->flush();

            if ($type === 'add') {
                $this->addFlash('success', $this->t->trans('customer.contact.saved'));
            } else {
                $this->addFlash('success', $this->t->trans('customer.changes.success'));
            }

            return $this->redirectToRoute('customerShow', [
                'id' => $customer->getId() ?? 0
            ]);
        }

        return $this->render('@ServicetoolService/Customer/save.html.twig', [
            'page_title'    => $pageTitle,
            'form'          => $form->createView(),
        ]);
    }

    /**
     * @Route("/deleteContact/{id}", name="contactDelete")
     */
    public function deleteContactAction($id): Response
    {
        $repository = $this->getDoctrine()->getRepository(CustomerContact::class);
        $em = $this->getDoctrine()->getManager();
        $entity = $repository->find($id);
        $t = $this->get('translator');

        if (!$entity) {
            throw new NotFoundHttpException($t->trans('core.pagenotfound'));
        } else {
            $em->remove($entity);
            $em->flush();

            $this->addFlash(
                'success',
                $t->trans('customer.contact.delete')
            );

            return $this->redirectToRoute('customerShow', [
                'id' => $entity->getCustomer()->getId()
            ]);
        }
    }

    /**
     * @Route("/show/{id}", name="customerShow")
     */
    public function showAction(
        int $id,
        Request $request,
        #[MapQueryParameter] bool $closeWindow = false
    ): Response {
        if ($id === false) {
            throw new NotFoundHttpException($this->t->trans('customer.user.notexist'));
        } else {
            $repository = $this->getDoctrine()->getRepository(Customer::class);
            /*** @var Customer $entity */
            $entity = $repository->find($id);
            if (!$entity) {
                throw new NotFoundHttpException($this->t->trans('customer.user.notexist'));
            }

            $startView = 'tickets';

            $paginator = $this->get('knp_paginator');

            $ticketsRepository = $this->getDoctrine()->getRepository(Ticket::class);
            $ticketsRepository->setPaginate(true);
            $ticketsRepository->forCustomer($entity);
            $ticketsRepository->isRma(false);
            $ticketsRepository->addOrder('t.lastReplyDate', 'DESC');
            $tickets = $ticketsRepository->get();

            $ticketsRepository->getQueryBuilder('t', true);
            $ticketsRepository->setPaginate(true);
            $ticketsRepository->forCustomer($entity);
            $ticketsRepository->isRma(true);
            $ticketsRepository->addOrder('t.lastReplyDate', 'DESC');
            $rmas = $ticketsRepository->get();

            $ticketsPagination = $paginator->paginate($tickets, (int)$request->get('page', 1), 10, ['pageParameterName' => 'page']);
            $ticketsPagination->setParam('startView', 'tickets');

            $rmaPagination = $paginator->paginate($rmas, (int)$request->get('page2', 1), 10, ['pageParameterName' => 'page2']);
            $rmaPagination->setParam('startView', 'rma');

            $orders = $entity->getOrders();
            $orderPagination = $paginator->paginate($orders, (int)$request->get('page3', 1), 10, ['pageParameterName' => 'page3']);
            $orderPagination->setParam('startView', 'orders');

            if ($request->query->get('startView')) {
                $startView = $request->query->get('startView');
            }

            if (!$entity) {
                throw new NotFoundHttpException($this->t->trans('customer.404'));
            }
            $pageTitle = $this->t->trans('customer.overview.title');
        }
        $searchData = $this->searchForCustomer($request, $id);

        $search = new Search();
        $search->setAutocomplete(true);
        $search->setLimit(20);
        $searchRepository = new SearchRepository($this->container->get('fos_elastica.finder.app_customer'));

        $form = $this->createForm(AutocompleteType::class, $search, ['type' => 'customer']);
        $form->handleRequest($request);
        $search = $form->getData();
        $results = $searchRepository->search($search);

        $adminLink = $this->getParameter('admin_link');
        $phoneName = null;
        $phoneIp = null;
        $userPhone = $this->getUser()->getUserPhone();
        if ($userPhone instanceof SkreprcallPhone) {
            $phoneName = $userPhone->getName();
            $phoneIp = $userPhone->getIp();
        }

        $divider = $this->get('servicetool.order_divider');
        $dividedOrders = $divider->divideOrdersForCustomer($entity);

        /*** @var OpenInvoiceHelper $openInvoiceHelper */
        $openInvoiceHelper = $this->get('servicetool.open_invoice_helper');
        $openInvoices = $openInvoiceHelper->getOpenInvoicesForCustomer($entity);

        if ($request->isXmlHttpRequest()) {
            return new JsonResponse([
                'content' => $this->renderView('@ServicetoolService/Customer/ajax.html.twig', [
                    'results'   => $results,
                    'user' => $entity,
                    'merge' => true,
                ])
            ]);
        } else {
            return $this->render('@ServicetoolService/Customer/show.html.twig', [
                'user' => $entity,
                'dividedOrders' => $dividedOrders,
                'openInvoices' => $openInvoices,
                'orders' => $orderPagination,
                'tickets' => $ticketsPagination,
                'phoneIp' => $phoneIp,
                'phoneName' => $phoneName,
                'adminLink' => $adminLink,
                'rmas' => $rmaPagination,
                'page_title' => $pageTitle,
                'customers' => $searchData['customers'],
                'totalCustomers' => $searchData['totalCustomers'],
                'maxPages' => $searchData['maxPages'],
                'thisPage' => $searchData['thisPage'],
                'form' => $form->createView(),
                'webdsignLogs' => $this->l->getLogs(),
                'actionMenu' => ['customer' => $entity->getId()],
                'startView' => $startView,
                'closeWindow' => $closeWindow,
            ]);
        }
    }

    /**
     * @Route("/merge/{id}/{confirm}", name="customerMerge", defaults={"id" = false, "confirm" = false})
     */
    public function mergeAction(Request $request, int $id, $confirm): Response
    {
        $mergeIds = $request->get('mergeIds');
        if (!$id) {
            $id = $request->get('headMergeId');
        }

        $ticketId = (int)$request->get('ticketId');

        $repository = $this->getDoctrine()->getRepository(Customer::class);
        $customer = $repository->findOneById($id);
        $customerOrigin = $customer->getOrigin();
        $customerOriginParent = $customerOrigin->getParent();

        $ticketRepository = $this->getDoctrine()->getRepository(Ticket::class);

        if ($customer && is_array($mergeIds) && count($mergeIds)) {
            foreach ($mergeIds as $key => $mergeId) {
                if ($customer->getId() == $mergeId) {
                    unset($mergeIds[$key]);
                }
            }
            if (is_array($mergeIds) && count($mergeIds)) {
                $mergeCustomers = $repository->findById($mergeIds);
            } else {
                throw new NotFoundHttpException($this->t->trans('customer.404'));
            }
        } else {
            throw new NotFoundHttpException($this->t->trans('customer.404'));
        }

        if ($confirm) {
            /* @var CustomerHelper $customerHelper */
            $customerHelper = $this->get('customer_helper');

            //Precheck of deze klanten wel allemaal dezelfde herkomst parent hebben.
            foreach ($mergeCustomers as $mergeCustomer) {
                $mergeCustomerOrigin = $mergeCustomer->getOrigin();
                $mergeCustomerOriginParent = $mergeCustomerOrigin->getParent();
                if ($mergeCustomerOriginParent !== $customerOriginParent) {
                    throw new ErrorException($this->t->trans('customer.merge.origin.error'));
                }
            }

            foreach ($mergeCustomers as $mergeCustomer) {
                $user = $this->get('security.token_storage')->getToken()->getUser();

                try {
                    $customerHelper->merge($mergeCustomer, $customer, $user);
                } catch (AccessDeniedException) {
                    $this->addFlash('warning', $this->t->trans('customer.merge.origin.access_denied_error'));
                    return new JsonResponse($this->generateUrl('customerShow', [
                        'id' => $customer->getId()
                    ]));
                }
            }

            if ($ticketId && isset($ticketRepository)) {
                $ticketRepository = $this->getDoctrine()->getRepository(Ticket::class);
                $ticket = $ticketRepository->find($ticketId);
                if ($ticket) {
                    $ticket->setCustomer($customer);
                    $this->getDoctrine()->getManager()->flush();
                }
                $redirectUrl = $this->generateUrl('ticketShow', ['id' => $ticketId]);
            } else {
                $redirectUrl = $this->generateUrl('customerShow', ['id' => $customer->getId()]);
            }

            return new JsonResponse($redirectUrl);
        } else {
            return $this->render('@ServicetoolService/Customer/merge.html.twig', [
                'page_title'     => $this->t->trans('customer.merge.heading'),
                'customer'       => $customer,
                'mergeCustomers' => $mergeCustomers,
                'ticketId'       => $ticketId,
            ]);
        }
    }

    /**
     * @Route("/mailCheck/{email}/{origin}", name="customerMailCheck", defaults={"email" = false, "origin" = false})
     */
    public function mailCheckAction($email, $origin): Response
    {
        $repository = $this->getDoctrine()->getRepository(Customer::class);
        $customer = $repository->findOneBy(['email' => $email, 'origin' => $origin]);

        return new JsonResponse(($customer instanceof Customer));
    }

    /**
     * @Route("/resetPass/{emailAddress}", name="resetPass")
     */
    public function sendPasswordResetMailAction(string $emailAddress): Response
    {
        $success = false;
        $customerRepository = $this->getDoctrine()->getRepository(Customer::class);
        $customer = $customerRepository->findOneBy(['email' => $emailAddress]);

        if ($customer !== null) {
            $clangWebhook = $this->get('webdsign.clang_web_hook');
            $language = in_array($customer->getLanguage(), ['nl', 'en']) ? $customer->getLanguage() : 'nl';

            $data = [
                'email' => $emailAddress,
                'externalId' => $customer->getId(),
                'firstname' => $customer->getFirstName(),
                'middlename' => $customer->getLastNamePrefix(),
                'lastname' => $customer->getLastName(),
                'Preflanguage' => $language,
            ];

            $clangWebhook->handleData($data, 'passwordReset', true);
            $clangWebhook->handleData($data, 'passwordResetNew', true);

            $success = true;
        }

        return new JsonResponse($success);
    }

    /**
     * @Route("/postalcode/", name="postalcode")
     */
    public function postalCodeAction(Request $request): Response
    {
        if ($request->isXmlHttpRequest()) {
            $all = $request->request->all();
            $repository = $this->getDoctrine()->getRepository(Postalcode::class);
            $postalcode = $repository->findOneByPostalcode($all['zipcode']);

            if (is_object($postalcode)) {
                $countryRepository = $this->getDoctrine()->getRepository(Country::class);
                $country = $countryRepository->findOneBy(['code' => $postalcode->getCountry()]);

                $result = [
                    'postalcode' => $postalcode->getPostalcode(),
                    'town' => $postalcode->getTown(),
                    'province' => $postalcode->getProvince(),
                    'street' => $postalcode->getStreet(),
                    'country' => $postalcode->getCountry(),
                    'countryId' => $country instanceof Country ? $country->getId() : '',
                ];
                return new JsonResponse($result);
            } else {
                $t = ['error' => $this->t->trans('customer.zipcode.error')];
                return new JsonResponse($t);
            }
        } else {
            throw new AccessDeniedHttpException('Request not possible');
        }
    }

    /**
     * @Route("/exportData/{customer}", name="exportCustomerData")
     */
    public function dataExportAction(Customer $customer): Response
    {
        $customerData = $this->get('webdsignservice.customerpersonaldata')
            ->getAllPersonalFlatData($customer);
        $name = 'cameranu_data_' . $customer->getEmail();

        $response = new Response($customerData);
        $response->headers->set('Content-Type', 'text/plain');
        $response->headers->set('Content-Disposition', "attachment; filename={$name}.txt");

        return $response;
    }

    /**
     * @Route("/deleteConfirmation/{customer}", name="deleteConfirmation")
     */
    public function deleteConfirmation(Customer $customer): Response
    {
        $repository = $this->getDoctrine()->getRepository(Customer::class);
        $mergedCustomers = $repository->getAllMergedCustomers($customer);

        return $this->render('@ServicetoolService/Customer/deleteData.html.twig', [
            'customer' => $customer,
            'mergedCustomers' => $mergedCustomers,
        ]);
    }

    /**
     * @Route("/deleteData/{customer}", name="deleteCustomerData", methods={"POST"})
     */
    public function dataDeleteAction(Customer $customer): Response
    {
        $this
            ->get('webdsignservice.customerdeletepersonaldata')
            ->deleteAllPersonalCustomersData($customer);

        $this->addFlash('success', 'Klant gegevens zijn geanonimiseerd');

        return $this->redirectToRoute('customerShow', [
            'id' => $customer->getId()
        ]);
    }

    /**
     * @Route("/createCallTicket/{customer}")
     */
    public function createCallTicketAction(Customer $customer): Response
    {
        $t = $this->get('translator');
        $em = $this->getDoctrine()->getManager();
        if (!is_a($customer, Customer::class)) {
            $customerRepository = $this->getDoctrine()->getRepository(Customer::class);
            $customer = $customerRepository->find($customer);
        }

        if (!$customer) {
            throw new NotFoundHttpException($t->trans('core.pagenotfound'));
        }

        $user = $this->getUser();
        $hash = $this->get('webdsignservice.tickethash')->getHash();

        $newTicket = new Ticket();
        $newTicket->setState('open');
        $newTicket->setAssignee($user);
        $newTicket->setCustomer($customer);
        $newTicket->setTitle($t->trans('service.ticket.call.title'));
        $newTicket->setSubject($t->trans('service.ticket.call.subject'));
        $newTicket->setHash($hash);
        $em->persist($newTicket);
        $em->flush();

        return $this->redirectToRoute('ticketShow', ['id' => $newTicket->getId(), 'internal' => 1]);
    }

    /**
     * @Route("/editMailingPreferences/{customer}", name="editCustomerMailingPreferences")
     */
    public function editMailingPreferencesAction(Customer $customer, Request $request): Response
    {
        $pageTitle = $this->t->trans('customer.editMailingPreferences');

        $em = $this->getDoctrine()->getManager();
        $webUserMailingTypesRepository = $this->get('webuser_mailing_types_repository');
        $mailingTypesRepository = $this->get('mailing_types_repository');

        $form = $this->createForm(SaveMailingPreferencesType::class, null, ['mailable' => $customer->getMailable()]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $currentMailTypes = $webUserMailingTypesRepository->findBy(['webUser' => $customer]);
            $currentMailTypeIds = $allMailTypeIds = [];

            /** @var WebuserMailingTypes $currentMailType */
            foreach ($currentMailTypes as $currentMailType) {
                $currentMailTypeIds[] = $currentMailType->getMailingTypeId()->getId();
            }

            $allMailTypes = $mailingTypesRepository->findBy(['active' => true]);

            /** @var MailingTypes $mailType */
            foreach ($allMailTypes as $mailType) {
                $allMailTypeIds[] = $mailType->getId();
            }

            $setMailable = false;
            // Loop trough all mailing types in form
            foreach ($form->get('webuserMailingTypes') as $mailingType) {
                $em->persist($customer);

                if (!is_null($customer->getId())) {
                    // Delete old join-table preferences from database
                    $webUserMailingTypesRepository->removeAllWebuserMailingTypes($customer);
                }

                // Create and persist new email preferences
                if ($mailingType->getData() === true) {
                    $webUserMailingType = new WebuserMailingTypes();

                    $mailingType = $mailingTypesRepository->find($mailingType->getViewData());

                    if ($mailingType === null) {
                        continue;
                    }

                    $setMailable = true;
                    $webUserMailingType->setMailingTypeId($mailingType);
                    $webUserMailingType->setWebUser($customer);
                    $em->persist($webUserMailingType);
                }
            }

            // Als opt-in handmatig is aangepast overschrijf dan de automatische
            $mailable = $form->get('mailable')->getData();
            if ($mailable !== $customer->getMailable()) {
                $setMailable = $mailable;
            }

            $customer->setMailable((int)$setMailable);
            $em->persist($customer);
            $em->flush();

            //ophalen van nieuwe preferences voor preference log
            $newMailTypes = $webUserMailingTypesRepository->findBy(['webUser' => $customer]);

            $newMailTypeIds = [];
            /** @var WebuserMailingTypes $newMailType */
            foreach ($newMailTypes as $newMailType) {
                $newMailTypeIds[] = $newMailType->getMailingTypeId()->getId();
            }

            $log = new mailPreferencesLog();
            $ip = $request->getClientIp();

            //check of mail preferences veranderd zijn
            foreach ($allMailTypeIds as $mailTypeId) {
                //mailpreference ophalen aan de hand van id
                foreach ($allMailTypes as $mailType) {
                    if ($mailType->getId() == $mailTypeId) {
                        $type = $mailType;
                    }
                }

                //check of vinkje is uitgezet
                if (!in_array($mailTypeId, $newMailTypeIds) && in_array($mailTypeId, $currentMailTypeIds)) {
                    //mail preference loggen met permissie op 0
                    $mailingPreferenceLog = $log->mailPreferenceLog($type, 0, $customer, $ip);
                    $em->persist($mailingPreferenceLog);
                }

                //check of vinkje is aangezet
                if (in_array($mailTypeId, $newMailTypeIds) && !in_array($mailTypeId, $currentMailTypeIds)) {
                    //mail preference loggen met permissie op 1
                    $mailingPreferenceLog = $log->mailPreferenceLog($type, 1, $customer, $ip);
                    $em->persist($mailingPreferenceLog);
                }
            }

            if (isset($mailingPreferenceLog)) {
                $em->flush();
            }

            $this->addFlash('success', $this->t->trans('core.save.success'));

            return $this->redirectToRoute('customerShow', [
                'id' => $customer->getId()
            ]);
        }

        // Haal de geselecteerde mailing preferences op en zet ze na het maken van de form
        $selectedWebuserMailingTypes = $webUserMailingTypesRepository->getSelectedWebuserMailingTypes($customer);

        foreach ($selectedWebuserMailingTypes as $selectedWebuserMailingType) {
            if ((bool)$selectedWebuserMailingType->getMailingTypeId()->getActive() === false) {
                continue;
            }

            /** @var WebuserMailingTypes $selectedWebuserMailingType */
            $form
                ->get('webuserMailingTypes')
                ->get((string)$selectedWebuserMailingType->getMailingTypeId()->getId())
                ->setData(true)
            ;
        }

        return $this->render('@ServicetoolService/Customer/saveMailingPreferences.html.twig', [
            'page_title' => $pageTitle,
            'form' => $form->createView(),
        ]);
    }
}
