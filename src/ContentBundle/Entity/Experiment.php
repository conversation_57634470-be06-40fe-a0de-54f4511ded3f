<?php

declare(strict_types=1);

namespace ContentBundle\Entity;

use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * A/B test experiment
 */
#[ORM\Entity(repositoryClass: ExperimentRepository::class)]
#[ORM\Table('cameranu.experiment')]
class Experiment
{
    public const int CONVERSION_ORIENTED_PRODUCT_TEXTS_EXPERIMENT = 839;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private $id;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'isActive', type: 'boolean')]
    private $active;

    /**
     * @var DateTime|null $startDate
     */
    #[ORM\Column(name: 'start_date', type: 'datetime')]
    private ?DateTime $startDate = null;

    /**
     * @var DateTime|null $endDate
     */
    #[ORM\Column(name: 'end_date', type: 'datetime')]
    private ?DateTime $endDate = null;

    /**
     * @var string
     */
    #[ORM\Column(name: 'name', type: 'text')]
    private $name;

    /**
     * @var int
     */
    #[ORM\Column(name: 'variations', type: 'integer')]
    private $variations;

    #[ORM\Column(name: 'winner_variation', type: 'string', length: 10)]
    private ?string $winnerVariation;

    /**
     * @var bool
     */
    #[ORM\Column(name: 'deleted', type: 'boolean')]
    private bool $deleted;

    /**
     * @param int $id
     * @return Experiment
     */
    public function setId(int $id): Experiment
    {
        $this->id = $id;
        return $this;
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @param bool $isActive
     * @return Experiment
     */
    public function setActive(bool $isActive): Experiment
    {
        $this->active = $isActive;
        return $this;
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->active;
    }

    /**
     * @return DateTime|null
     */
    public function getStartDate(): ?DateTime
    {
        return $this->startDate;
    }

    /**
     * @param DateTime|null $startDate
     * @return Experiment
     */
    public function setStartDate(?DateTime $startDate): Experiment
    {
        $this->startDate = $startDate;
        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getEndDate(): ?DateTime
    {
        return $this->endDate;
    }

    /**
     * @param DateTime|null $endDate
     * @return Experiment
     */
    public function setEndDate(?DateTime $endDate): Experiment
    {
        $this->endDate = $endDate;
        return $this;
    }

    /**
     * @param string $name
     * @return Experiment
     */
    public function setName(string $name): Experiment
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return int
     */
    public function getVariations(): int
    {
        return $this->variations;
    }

    /**
     * @param int $variations
     * @return Experiment
     */
    public function setVariations(int $variations): Experiment
    {
        $this->variations = $variations;
        return $this;
    }

    public function getWinnerVariation(): ?string
    {
        return $this->winnerVariation;
    }

    public function setWinnerVariation(?string $winnerVariation): Experiment
    {
        $this->winnerVariation = $winnerVariation;
        return $this;
    }

    /**
     * @return bool
     */
    public function isDeleted(): bool
    {
        return $this->deleted;
    }

    /**
     * @param bool $deleted
     * @return Experiment
     */
    public function setDeleted(bool $deleted): Experiment
    {
        $this->deleted = $deleted;
        return $this;
    }
}
