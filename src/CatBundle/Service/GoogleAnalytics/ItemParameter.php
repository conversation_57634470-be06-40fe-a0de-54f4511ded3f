<?php

declare(strict_types=1);

namespace CatBundle\Service\GoogleAnalytics;

class ItemParameter extends \Br33f\Ga4\MeasurementProtocol\Dto\Parameter\ItemParameter
{
    protected ?string $itemStock = null;

    public function getItemStock(): ?string
    {
        return $this->itemStock;
    }

    public function setItemStock(?string $itemStock): ItemParameter
    {
        $this->itemStock = $itemStock;
        return $this;
    }

    /**
     * @param array $blueprint
     */
    public function hydrate($blueprint): void
    {
        parent::hydrate($blueprint);

        if (array_key_exists('item_stock', $blueprint)) {
            $this->setItemStock($blueprint['item_stock']);
        }
    }

    public function export(): array
    {
        $preparedExportableObject = parent::export();
        $preparedExportableObject['item_stock'] = $this->getItemStock();

        return $preparedExportableObject;
    }
}
