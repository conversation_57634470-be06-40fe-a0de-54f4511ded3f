<?php

declare(strict_types=1);

namespace CatBundle\Service\Stock;

use DateTime;
use DateTimeImmutable;
use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Logging\Middleware;
use Doctrine\ORM\EntityManagerInterface;
use League\Csv\InvalidArgument;
use League\Csv\Reader;
use Psr\Log\NullLogger;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\ProductLog;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\Stock;
use Webdsign\GlobalBundle\Entity\StockInLocation;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\StockInLocationRepository;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Entity\StockLogAction;
use Webdsign\GlobalBundle\Entity\StockRepository;
use Webdsign\GlobalBundle\Entity\Subgroup;
use Webdsign\GlobalBundle\Entity\Supplier;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\UserRepository;

class StockHelper
{
    private User $systemUser;

    private const SWAP_CUSTOMER = 71;
    private const MAIN_GROUP_PASS_PHOTO = 2594;
    private const MAIN_GROUP_IMPORT_LOADS = 15382;

    public const SKIP_SUPPLIERS = [
        self::SWAP_CUSTOMER,
    ];

    public const SKIP_GROUPS = [
        self::MAIN_GROUP_IMPORT_LOADS,
        self::MAIN_GROUP_PASS_PHOTO
    ];

    private array $stockLocations;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly StockRepository $stockRepository,
        private readonly StockLocationRepository $stockLocationRepository,
        private readonly StockInLocationRepository $stockInLocationRepository,
        private readonly ProductRepository $productRepository,
        private readonly UserRepository $userRepository
    ) {
        $this->systemUser = $userRepository->findSystemUser();
        $this->setStockLocations();
    }

    /**
     * @param OutputInterface $output
     * @return void
     */
    public function generateLocationSnapShop(OutputInterface $output): bool
    {
        $this->entityManager->getConnection()->getConfiguration()->setSQLLogger(null);

        $now = new DateTimeImmutable();
        $startOfDay = $now->format('Y-m-d 00:00:00');
        $endOfDay = $now->format('Y-m-d 23:59:59');

        $conn = $this->entityManager->getConnection();

        $sql = <<<SQL
INSERT INTO cameranu.stock_locations_history (stock_id, stock_location_id, date_added, location)
SELECT
    v0_.id,
    v0_.stock_location_id,
    CURRENT_TIMESTAMP(),
    (
        SELECT
            GROUP_CONCAT(
                DISTINCT CONCAT_WS(
                    '',
                    cl.vak,
                    IF(lcn.subvak IS NOT NULL AND lcn.subvak != '', CONCAT('-', lcn.subvak), '')
                ) SEPARATOR ','
            ) AS location
        FROM cameranu.voorraad s2
        LEFT JOIN cameranu.artikelen p ON s2.artikel_id = p.id
        LEFT JOIN cameranu.locatieVakkenNodes lcn ON lcn.artikel_id = p.id
        LEFT JOIN cameranu.locatieVakken cl ON lcn.locatie_id = cl.id
        LEFT JOIN cameranu.stock_locations sl ON cl.stock_location_id = sl.id
        WHERE sl.id IN (:warehouseUrk, :shopUrk)
        AND s2.id = v0_.id
        GROUP BY s2.id
    ) AS location
FROM cameranu.voorraad v0_
LEFT JOIN cameranu.artikelen a2_ ON v0_.artikel_id = a2_.id
LEFT JOIN cameranu.subgroepen s3_ ON a2_.subgroep_id = s3_.id
LEFT JOIN cameranu.bestelling_naw b6_ ON v0_.bestelling_id = b6_.id
LEFT JOIN cameranu.stock_locations_history s7_
    ON v0_.id = s7_.stock_id
    AND (s7_.date_added BETWEEN :startOfDay AND :endOfDay)
WHERE
    s3_.id <> :excludedSubgroupId
    AND (b6_.id IS NULL OR b6_.factuurnummer = 0)
    AND s7_.id IS NULL
    AND v0_.date_out IS NULL;
SQL;

        $tries = 0;
        $maxTries = 5;
        $success = false;
        $output->writeln('<info>Start creating StockLocations snapshot.</info>');

        while ($tries < $maxTries) {
            $tries++;
            try {
                $conn->executeStatement($sql, [
                    'startOfDay' => $startOfDay,
                    'endOfDay' => $endOfDay,
                    'excludedSubgroupId' => Subgroup::PRINT_SHOP_ID,
                    'warehouseUrk' => StockLocation::MAGAZIJN_URK,
                    'shopUrk' => StockLocation::WINKEL_URK,
                ]);

                $output->writeln('<info>StockLocation snapshot generated successfully.</info>');
                $success = true;
            } catch (Throwable) {
                $output->writeln('<error>Something went wrong retry.</error>');
                sleep(1);
                continue;
            }

            break;
        }

        return $success;
    }

    /**
     * Fill StockLog with first in stock entry's
     *
     * @param OutputInterface $output
     * @param DateTime $start
     * @param DateTime $end
     * @return void
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws Exception
     */
    public function fillStockLogWithFirstInEntries(OutputInterface $output, DateTime $start, DateTime $end): void
    {
        $this->entityManager->getConnection()->getConfiguration()->setMiddlewares([new Middleware(new NullLogger())]);
        $output->writeln('Collecting stockItems for ' . $start->format('Y-m-d') . ' to ' . $end->format('Y-m-d'));
        $stockItems = $this->stockRepository
            ->findForLogInit($start, $end);

        if (count($stockItems) === 0) {
            $output->writeln('No stockItems found for ' . $start->format('Y-m-d') . ' to ' . $end->format('Y-m-d'));
            return;
        }

        $output->writeln('start generating logs ...');

        $progressBar = new ProgressBar($output, count($stockItems));
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
        $progressBar->start();

        $query = '
            INSERT INTO `cameranu`.`stock_log` (
                `user_id`,
                `stock_id`,
                `product_id`,
                `action_id`,
                `timestamp`,
                `from_location_id`,
                `to_stock_location_id`,
                `extra_info`
            ) VALUES ';

        $counter = 0;
        $stmt = $query;
        /**
         * @var Stock $stockItem
         */
        foreach ($stockItems as $stockItem) {
            $counter++;
            $progressBar->advance();

            $toLocation = null;

            $timestampIn = $stockItem['timestampIn'];
            $insertedBy = $stockItem['insertedBy'];
            if ($stockItem['firstLogTimestamp'] === null) {//No first log
                if (
                    !$stockItem['isMainLocation'] &&
                    $stockItem['shop'] === null &&
                    $stockItem['isScannableByBarcode'] === true
                ) {
                    $toLocation = $this->stockLocations[$stockItem['locationId']]['parentMainLocation'];
                } else {
                    $toLocation = $stockItem['locationId'];
                }
            } else {//stock has a first log
                if (
                    $stockItem['firstLogInfo'] !== null &&
                    strpos($stockItem['firstLogInfo'], 'In voorraad --> ') !== false
                ) {
                    continue;
                }

                $ts1 = strtotime($stockItem['firstLogTimestamp']);
                $ts2 = strtotime($timestampIn);
                $seconds_diff = ($ts2 - $ts1) / 3600;

                if (abs($seconds_diff) < 2) {
                    continue;
                }

                $checkMain = false;

                if ($stockItem['firstLogFromLocation'] === null) {//No from location on first log
                    if ($stockItem['firstLogToLocation'] === null) {//No from and to location on first log
                        $location = null;
                        if ($stockItem['firstLogInfo'] !== null) {
                            if (strpos($stockItem['firstLogInfo'], 'Handmatig ') !== false) {
                                $location = trim(substr($stockItem['firstLogInfo'], strlen('Handmatig '), 1));
                            }

                            if (strpos($stockItem['firstLogInfo'], 'Artikel > ') !== false) {
                                $location = trim(substr($stockItem['firstLogInfo'], strlen('Artikel > ')));
                            }

                            if (strpos($stockItem['firstLogInfo'], 'Artikel(en) productie > ') !== false) {
                                $location = trim(substr($stockItem['firstLogInfo'], strlen('Artikel(en) productie > '), 1));
                                $checkMain = true;
                            }
                        }

                        if ($location !== null) {
                            if (!is_numeric($location)) {
                                $locations = array_flip(array_column($this->stockLocations, 'code', 'id'));
                                if (array_key_exists($location, $locations)) {
                                    $location = $this->stockLocations[$locations[$location]]['location'];
                                }
                            }
                        }

                        if (!$location instanceof StockLocation) {
                            if ($stockItem['firstLogInfo'] !== null && strpos($stockItem['firstLogInfo'], '>') !== false) {
                                $location = trim(strtok($stockItem['firstLogInfo'], '>'));
                                $locations = array_flip(array_column($this->stockLocations, 'code', 'description'));
                                if (array_key_exists($location, $locations)) {
                                    $location = $this->stockLocations[$locations[$location]]['location'];
                                }
                            }
                        }

                        if ($location instanceof StockLocation) {
                            if ($checkMain === true && !$location->isMainLocation() && $location->getShop() === null) {
                                $location = $this->stockLocations[$this->stockLocations[$location->getId()]['parentMainLocationId']]['location'];
                            }

                            $toLocation = $location->getId();
                        }
                    } else {//No from location on first log
                        if ((bool)$stockItem['firstLogToLocationIsMain'] !== false) {
                            $toLocation = $stockItem['firstLogToLocation'];
                        } else {
                            continue;
                        }
                    }
                } else {
                    if ((bool)$stockItem['firstLogFromLocationIsMain'] === false) {
                        $toLocation = $stockItem['firstLogFromLocation'];
                    } else {
                        continue;
                    }
                }
            }

            $this->entityManager->clear();
            $extraInfo = 'In voorraad --> ' . $toLocation;
            $action = StockLogAction::VOORRAADVERPLAATSING;

            $stmt .= '("' . $insertedBy . '", "' . $stockItem['stockId'] . '", "' . $stockItem['productId'] . '", "' . $action . '", "' . $timestampIn . '", NULL, "' . $toLocation . '", "' . $extraInfo . '"),';

            if ($counter % 100 === 0) {
                $statement = $this->entityManager->getConnection()->prepare(rtrim($stmt, ','));
                $statement->executeQuery();
                $stmt = $query;
            }
        }

        if ($stmt !== $query) {
            $statement = $this->entityManager->getConnection()->prepare(rtrim($stmt, ','));
            $statement->executeQuery();
        }

        $progressBar->finish();
        echo PHP_EOL;
    }

    private function setStockLocations(): void
    {
        $locations = [];
        $stockLocations = $this->stockLocationRepository->findAll();
        foreach ($stockLocations as $stockLocation) {
            $locations[$stockLocation->getId()] = [
                'id' => $stockLocation->getId(),
                'code' => $stockLocation->getCode(),
                'parent' => $stockLocation->getParent(),
                'shop' => $stockLocation->getShop(),
                'isMainLocation' => $stockLocation->isMainLocation(),
                'parentMainLocationId' => $this->stockLocationRepository->findMainLocationForParent($stockLocation)[0]->getId(),
                'location' => $stockLocation,
            ];
        }

        $this->stockLocations = $locations;
    }

    /**
     * @param string $filePath
     * @param int $stockLocationId
     * @return array
     * @throws \League\Csv\Exception
     * @throws InvalidArgument
     * @throws Exception
     */
    public function importMinimalStockUpdate(string $filePath, int $stockLocationId): array
    {
        // Get the stock location
        $stockLocation = $this->stockLocationRepository->find($stockLocationId);
        if (!$stockLocation instanceof StockLocation) {
            throw new Exception('Stock location id "' . $stockLocationId . '" could not be found!', 500);
        }

        // Read the file from the file path
        $csv = Reader::createFromPath($filePath, 'r');
        $csv->setHeaderOffset(0);
        $csv->setDelimiter(';');
        $records = $csv->jsonSerialize();
        $data = $errors = [];
        $success = 0;

        foreach ($records as $record) {
            if (!isset($record['artikel_id'], $record['minimale_voorraad'])) {
                continue;
            }

            $productId = (int)$record['artikel_id'];

            if (array_key_exists($productId, $data)) {
                $errors[] = 'Product: "' . $productId . '" found double in import file. minimal stock value overwritten by last found row.';
            }

            $data[$productId] = (int)$record['minimale_voorraad'];
        }

        foreach ($data as $productId => $minimalStockValue) {
            $product = $this->productRepository->find($productId);
            if (!$product instanceof Product) {
                $errors[] = 'Product: "' . $productId . '" could not be found!';
                continue;
            }

            $articleStockPerLocation = $this->stockInLocationRepository->findOneBy([
                'product' => $product,
                'stockLocation' => $stockLocation,
            ]);

            if (!$articleStockPerLocation instanceof StockInLocation) {
                $articleStockPerLocation = new StockInLocation();
                $articleStockPerLocation
                    ->setProduct($product)
                    ->setStockLocation($stockLocation)
                    ->setMinimalStock($minimalStockValue)
                    ->setStock(0)
                    ->setVirtualStock(0);

                $this->entityManager->persist($articleStockPerLocation);
            } else {
                $articleStockPerLocation->setMinimalStock($minimalStockValue);
            }

            $this->entityManager->flush();
            $success++;
        }

        return [
            'success' => $success,
            'errors' => $errors,
        ];
    }

    public function updateMinimalStockForLocation(
        OutputInterface $output,
        StockLocation $location,
        Supplier $startStockSupplier,
        ?Supplier $startSupplierDeadStock
    ): array {
        $success = $dead = 0;
        $output->writeln('Producten verzamelen...');
        $deadStock = $startSupplierDeadStock instanceof Supplier ? $this->stockRepository->getProductStockAmountBySupplier($startSupplierDeadStock) : [];
        $data = $this->stockRepository->getProductStockAmountBySupplier($startStockSupplier);
        $count = count($data);
        $output->writeln('Minimale voorraad voor: ' . $count . ' producten updaten');

        if ($count > 0) {
            $progressBar = new ProgressBar($output, $count);
            $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
            $progressBar->start();

            foreach ($data as $info) {
                $minStock = in_array($info['id'], array_column($deadStock, 'id')) ? 0 : $info['amount'];
                $info['amount'] = $minStock;

                $this->updateMinStock($info, $location);

                $progressBar->advance();
                if ($success % 100 === 0) {
                    $this->entityManager->flush();
                }
                $success++;
            }

            $this->entityManager->flush();
            $progressBar->finish();
        }

        //min stock op 0 voor dode voorraad
        if ($startSupplierDeadStock instanceof Supplier) {
            $count = count($deadStock);
            $output->writeln(PHP_EOL . 'Minimale voorraad voor: ' . $count . ' dode producten updaten');

            if ($count > 0) {
                $progressBar = new ProgressBar($output, $count);
                $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
                $progressBar->start();
                foreach ($deadStock as $info) {
                    $info['amount'] = 0;

                    $this->updateMinStock($info, $location);

                    $progressBar->advance();
                    if ($dead % 100 === 0) {
                        $this->entityManager->flush();
                    }
                    $dead++;
                }

                $progressBar->finish();
                $this->entityManager->flush();
            }
        }

        return [
            'success' => $success,
            'dead' => $dead,
        ];
    }

    private function updateMinStock($info, stockLocation $location): void
    {
        $product = $this->productRepository->find($info['id']);
        if (!$product instanceof Product) {
            return;
        }

        $articleStockPerLocation = $this->stockInLocationRepository->findOneBy([
            'product' => $product,
            'stockLocation' => $location,
        ]);

        if (!$articleStockPerLocation instanceof StockInLocation) {
            $articleStockPerLocation = new StockInLocation();
            $articleStockPerLocation
                ->setProduct($product)
                ->setStockLocation($location)
                ->setStock(0)
                ->setVirtualStock(0);

            $this->entityManager->persist($articleStockPerLocation);
        }

        $articleStockPerLocation->setMinimalStock((int)$info['amount']);
        $articleStockPerLocation->setMinimalStockLog('Init minimale voorraad obv beginvoorraad');
    }

    /**
     * @throws Exception
     */
    public function resetMinimalStockForLocation(int $stockLocationId): void
    {
        // Get the stock location
        $stockLocation = $this->stockLocationRepository->find($stockLocationId);
        if (!$stockLocation instanceof StockLocation) {
            throw new Exception('Stock location id "' . $stockLocationId . '" could not be found!', 500);
        }

        $articleStockPerLocation = $this->stockInLocationRepository->findBy([
            'stockLocation' => $stockLocation,
        ]);

        foreach ($articleStockPerLocation as $index => $stockInLocation) {
            if (empty($stockInLocation->getMinimalStock())) {
                continue;
            }

            // Add Product Log
            $action = 'min_voorraad ' . $stockInLocation->getStockLocation()->getDescription() . ': ';
            $action .= $stockInLocation->getMinimalStock() . ' => 0';

            $productLog = new ProductLog();
            $productLog
                ->setProduct($stockInLocation->getProduct())
                ->setUser($this->systemUser)
                ->setTimestamp(new DateTime())
                ->setAction($action);
            $this->entityManager->persist($productLog);

            // Set Minimal stock to 0
            $stockInLocation->setMinimalStock(0);

            if ($index % 100 === 0) {
                $this->entityManager->flush();
            }
        }

        $this->entityManager->flush();
    }
}
