<?php

declare(strict_types=1);

namespace CatBundle\Service;

use UnexpectedValueException;
use Webdsign\GlobalBundle\Entity\CartItem;
use Webdsign\GlobalBundle\Entity\DiscountSets\DiscountSet;
use Webdsign\GlobalBundle\Entity\DiscountSets\DiscountSetBrand;
use Webdsign\GlobalBundle\Entity\DiscountSets\DiscountSetPosition;
use Webdsign\GlobalBundle\Entity\DiscountSets\DiscountSetRepository;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\ProductRecommendedAccessoryRepository;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\Rootgroup;
use Webdsign\GlobalBundle\Entity\SpecsArticleProfile;
use Webdsign\GlobalBundle\Entity\SpecsArticleProfileRepository;
use Webdsign\GlobalBundle\Entity\SpecsArticleSpecification;
use Webdsign\GlobalBundle\Entity\SpecsArticleSpecificationRepository;
use Webdsign\GlobalBundle\Entity\SpecsProfile;
use Webdsign\GlobalBundle\Exception\NoAccessoriesException;
use Webdsign\GlobalBundle\Exception\NoDiscountSetException;

class ProductDiscountSet
{
    private const PRODUCT_LIST_LIMIT = 18;

    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly DiscountSetRepository $discountSetRepository,
        private readonly ProductRecommendedAccessoryRepository $productRecommendedAccessoryRepository,
        private readonly SpecsArticleProfileRepository $specsArticleProfileRepository,
        private readonly SpecsArticleSpecificationRepository $articleSpecificationRepository,
    ) {}

    public function forProduct(
        Product $product,
        ?string $country = 'NL',
        ?string $fallbackCountry = 'NL'
    ): array {
        if (!$product->isOrderable()) {
            throw new NoDiscountSetException(
                'Product is niet bestelbaar: ' . $product->getId()
            );
        }

        $discountSet = $this->discountSetRepository->forProduct($product, $country, $fallbackCountry);

        if (!$discountSet instanceof DiscountSet) {
            throw new NoDiscountSetException(
                'Er is geen voordeelset template gevonden voor product ' . $product->getId()
            );
        }

        $discountSetPositions = $this->getDiscountSetPositions(
            $discountSet,
            $product,
            false
        );

        $discountSetPositionsOutOfStock = $this->getDiscountSetPositions(
            $discountSet,
            $product,
            true
        );

        return [
            'id' => $discountSet->getId(),
            'name' => $discountSet->getName(),
            'discounts' => $discountSet->getDiscountSetDiscounts(),
            'positions' => $discountSetPositions,
            'outOfStock' => $discountSetPositionsOutOfStock,
        ];
    }

    private function getDiscountSetPositions(
        DiscountSet $discountSet,
        Product $product,
        bool $outOfStock
    ): array {

        $accessories = $this->accessoriesForProduct($product);
        // id => stock
        $accessoriesAndStock = $this->productRepository->getDispatchableStockForProductIds(
            $accessories,
            excludedGroups: [Rootgroup::OCCASIONS_ID, Rootgroup::RENTAL_ID]
        );

        if ($outOfStock) {
            $accessories = array_filter($accessoriesAndStock, static fn($stock) => $stock < 1);
        } else {
            $accessories = array_filter($accessoriesAndStock, static fn($stock) => $stock > 0);
        }
        $accessories = array_keys($accessories);

        // index of this array matches up with $accessoriesWithStock
        $specsArticleProfilesForAccessories = $this
            ->specsArticleProfileRepository
            ->byProductsWithSpecsProfile($accessories);

        $discountSetPositions = [];
        /** @var DiscountSetPosition $discountSetPosition */
        foreach ($discountSet->getDiscountSetPositions() as $discountSetPosition) {
            $positionSpecsSpec = $discountSetPosition->getSpecsSpecification();
            if ($positionSpecsSpec === null) {
                $positionProfile = $discountSetPosition->getSpecsProfile();
                $accessoriesForPosition = array_filter(
                    $accessories,
                    static fn(int $i) => array_key_exists($i, $specsArticleProfilesForAccessories) && $specsArticleProfilesForAccessories[$i]
                        ->getSpecsProfile()->getId() === $positionProfile->getId(),
                    ARRAY_FILTER_USE_KEY
                );
            } else {
                $accessoriesArticleSpecs = $this->articleSpecificationRepository->findby([
                    'product' => $accessories,
                    'specId' => $positionSpecsSpec->getId(),
                ]);

                $accessoriesForPosition = array_filter(
                    $accessoriesArticleSpecs,
                    static function (SpecsArticleSpecification $spec) use ($discountSetPosition) {
                        return $spec->getValue() === $discountSetPosition->getSpecsArticleSpecificationsValue();
                    }
                );
                $accessoriesForPosition = array_map(static function (SpecsArticleSpecification $spec) {
                    return $spec->getProduct()->getId();
                }, $accessoriesForPosition);
            }

            if ($outOfStock) {
                $this->addAccessoriesToPositionOutOfStock($discountSetPosition, $accessoriesForPosition);
            } else {
                $this->addAccessoriesToPositionInStock($discountSetPosition, $accessoriesForPosition);
            }

            $discountSetPositions[] = $discountSetPosition;
        }

        return $discountSetPositions;
    }

    /**
     * For accessories, we have 2 sources. The explicit recommended accessories take precedence. So we put them
     * in front of the 'normal' accessories
     *
     * @param Product $product
     * @return int[]
     */
    private function accessoriesForProduct(Product $product): array
    {
        $recommendedAccessories = $this->productRecommendedAccessoryRepository->getRecommendedAccessoriesIds($product);
        $productAccessories = $product->getAccessories(true);

        if (!$productAccessories) {
            throw new NoAccessoriesException('Dit product heeft geen gekoppelde accessoires');
        }

        $sortedAccessories = [
            // This sorts the original products accessories by the order in $recommendedAccessories
            ...array_values(array_intersect($recommendedAccessories, $productAccessories)),

            // But it excludes products that arent in both sets, which is good
            // because the recommended accessories table can still have products
            // which arent in the actual accessories set anymore, so we instead
            // grab all the excluded ids and append them with this.
            ...array_values(array_diff($productAccessories, $recommendedAccessories)),
        ];

        return $sortedAccessories;
    }

    private function addAccessoriesToPosition(DiscountSetPosition $discountSetPosition, array $accessories): array
    {
        $returnAccessories = [];

        // This will hold accessories per brand position, preserving their original sorting
        $brandsAccessories = [];
        $nonBrandAccessories = [];

        $brandsForAccessories = $this->productRepository->getBrandData($accessories, Product::FLAG_SHOW, true);

        foreach ($accessories as $accessory) {
            $matched = false;

            foreach ($discountSetPosition->getBrands() as $brandPosition => $brand) {
                if (($brandsForAccessories[$accessory] ?? '') === $brand->getSpecsSpecificationValue()) {
                    $brandsAccessories[$brandPosition][] = $accessory;
                    $matched = true;
                    break; // Stop at the first matched brand
                }
            }

            if (!$matched) {
                $nonBrandAccessories[] = $accessory;
            }
        }

        // Maintain the sorting: preferred brands first (lower position = higher priority), then non-brand matches
        ksort($brandsAccessories); // Ensure ascending order of brand priorities
        foreach ($brandsAccessories as $brandGroup) {
            $returnAccessories = array_merge($returnAccessories, $brandGroup);
        }

        $returnAccessories = array_merge($returnAccessories, $nonBrandAccessories);

        if ($discountSetPosition->getProduct() !== null) {
            array_unshift($returnAccessories, $discountSetPosition->getProduct()->getId());
        }

        // We might have added way too much because of brands handling or preferred product
        return array_slice($returnAccessories, 0, self::PRODUCT_LIST_LIMIT);
    }

    private function addAccessoriesToPositionInStock(DiscountSetPosition $discountSetPosition, array $accessories) {
        $discountSetPosition->accessories = [];
        $discountSetPosition->accessories = $this->addAccessoriesToPosition($discountSetPosition, $accessories);
    }

    private function addAccessoriesToPositionOutOfStock(DiscountSetPosition $discountSetPosition, array $accessories) {

        $discountSetPosition->outOfStockAccessories = [];
        $discountSetPosition->outOfStockAccessories = $this->addAccessoriesToPosition($discountSetPosition, $accessories);
    }

    public function getCrossSellingProductsForOrder(OrderInfo $order): array
    {
        $crossSellingProducts = [];
        $cartItems = $order->getCartItems();

        $productIdsInOrder = array_map(function (CartItem $cartItem) {
            return $cartItem->getProductId();
        }, $cartItems->toArray());

        $orderProductsInfo = array_map(function (CartItem $cartItem) {
            $specsProfile = $cartItem->getProduct()?->getSpecsProfiles()?->current();
            return [
                $cartItem->getProductId() => [
                    'specProfile' => $specsProfile instanceof SpecsProfile ? $specsProfile->getId() : 0,
                    'price' => $cartItem->getPrice()
                ]
            ];
        }, $cartItems->toArray());

        // Per specProfiel 1 setje crossSellingProducten ophalen.
        // Bij meerdere producten met zelfde profiel pakken we de duurste van dit profiel.
        $result = array_values(array_reduce($orderProductsInfo, function ($carry, $product) {
            foreach ($product as $id => $data) {
                $specProfile = $data['specProfile'];
                $price = $data['price'];

                if (!isset($carry[$specProfile]) || $price > $carry[$specProfile]['price']) {
                    $carry[$id] = [
                        'id' => $id,
                        'specProfile' => $specProfile,
                        'price' => $price
                    ];
                }
            }

            return $carry;
        }, []));

        $filteredProductsInfo = [];
        foreach ($result as $product) {
            $filteredProductsInfo[$product['id']] = $product;
        }

        foreach ($cartItems as $cartItem) {
            $cartItemProduct = $cartItem->getProduct();

            //product heeft geen crossSelling setje nodig als hij niet in de filteredProductsInfo zit
            if (!array_key_exists($cartItemProduct->getId(), $filteredProductsInfo)) {
                continue;
            }

            try {
                $discountSets = $this->forProduct($cartItemProduct);
            } catch (NoDiscountSetException|UnexpectedValueException|NoAccessoriesException) {
                continue; //Geen discountSet of accessoires
            }

            $cartItemCrossSellingProducts = [];
            foreach ($discountSets['positions'] as $position) {
                $products = $position->accessories;
                foreach ($products as $productId) {
                    if (
                        in_array($productId, $productIdsInOrder) ||//zit al in de order
                        array_key_exists($productId, $crossSellingProducts)//zit al in de crossSelling
                    ) {
                        continue;
                    }

                    $product = $this->productRepository->find($productId);
                    $specProfileId = $product->getSpecsProfiles()?->current()?->getId() ?? 0;
                    // Als er bijv. al een product met specProfiel geheugenkaart in de order zit,
                    // dan gaan we niet nog een geheugenkaart aan de crossSelling toevoegen
                    if ($filteredProductsInfo[$cartItemProduct->getId()]['specProfile'] === $specProfileId) {
                        continue 2; //Een discountSet positie bevat producten met hetzelfde profiel we kunnen door naar de volgende positie.
                    }

                    $cartItemCrossSellingProducts[$productId] = $product;
                    continue 2;//1 product per discountSet positie
                }
            }

            //eventueel aanvullen tot 3 met accessoires
            if (count($cartItemCrossSellingProducts) < 3) {
                $accessories = $this->accessoriesForProduct($cartItemProduct);

                foreach ($accessories as $accessory) {
                    if (
                        in_array($accessory, $productIdsInOrder) ||//zit al in de order
                        array_key_exists($accessory, $cartItemCrossSellingProducts) ||//zit al in de crossSelling voor dit product
                        array_key_exists($accessory, $crossSellingProducts)//zit al in de crossSelling voor de order
                    ) {
                        continue;
                    }

                    $cartItemCrossSellingProducts[$accessory] = $this->productRepository->find($accessory);

                    if (count($cartItemCrossSellingProducts) === 3) {
                        break;
                    }
                }
            }

            foreach ($cartItemCrossSellingProducts as $productId => $crossSellingProduct) {
                $crossSellingProducts[$productId] = $crossSellingProduct;
            }
        }

        return $crossSellingProducts;
    }
}
