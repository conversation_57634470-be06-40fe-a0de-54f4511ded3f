<?php

declare(strict_types=1);

namespace CatBundle\Service\StockSnapshot;

use CatB<PERSON>le\Exception\StockSnapshot\PartitionNotCreatedException;
use CatBundle\Exception\StockSnapshot\PartitionNotRemovedException;
use CatBundle\Exception\StockSnapshot\SnapshotNotCreatedException;
use DateTimeImmutable;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Webdsign\GlobalBundle\Services\Telegram;

readonly class Snapshot
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private Telegram $telegram
    ) {
    }

    /**
     * Generate a stock snapshot of unsold items and items sold or deleted after a given date.
     *
     * @throws SnapshotNotCreatedException
     */
    public function createSnapshot(DateTimeImmutable $date): int
    {
        $sql = <<<SQL
        INSERT INTO `cameranu_analysis`.`stock_snapshots` (
            id,
            snapshot_year,
            artikel_id,
            datum_in,
            date_out,
            tstamp_in,
            factuur_leverancier,
            barcode, ordernummer,
            bestelling_id,
            prijs_verkoop,
            prijs_verkoop_ex,
            prijs_inkoop,
            prijs_korting,
            prijs_verwbijdrage,
            stock_value,
            btw,
            leverancier_id,
            retour_parent,
            info,
            user_id,
            ingescand_door,
            inserted_stock_location_id,
            locatie,
            bestelmemo_id,
            flags,
            stock_location_id,
            location_compartment_node_id
        )
        SELECT
            v.id,
            :year,
            v.artikel_id,
            v.datum_in,
            v.date_out,
            v.tstamp_in,
            v.factuur_leverancier,
            v.barcode,
            v.ordernummer,
            v.bestelling_id,
            v.prijs_verkoop,
            v.prijs_verkoop_ex,
            v.prijs_inkoop,
            v.prijs_korting,
            v.prijs_verwbijdrage,
            v.stock_value,
            v.btw,
            v.leverancier_id,
            v.retour_parent,
            v.info,
            v.user_id,
            v.ingescand_door,
            v.inserted_stock_location_id,
            v.locatie,
            v.bestelmemo_id,
            v.flags,
            v.stock_location_id,
            v.location_compartment_node_id
        FROM `cameranu`.`voorraad` AS v
        LEFT JOIN `cameranu`.`bestelling_naw` as bn on bn.id = v.bestelling_id
        WHERE (bn.id IS NULL or (bn.datum_factuur >= :invoiceDate OR bn.datum_factuur IS NULL))
        AND (v.date_out >= :dateOut OR v.date_out IS NULL)
        SQL;

        try {
            $result = $this->entityManager->getConnection()->executeStatement($sql, [
                'year' => $date->format('Y'),
                'invoiceDate' => $date->format('Y-m-d'),
                'dateOut' => $date->format('Y-m-d'),
            ]);

            $this->telegram->sendServerMessage(
                sprintf('Voorraadsnapshot gemaakt voor %s met %d regels', $date->format('Y'), $result)
            );
        } catch (Exception $exception) {
            $this->telegram->sendServerMessage(
                'Er is een fout opgetreden bij het maken van een voorraadsnapshot: ' . PHP_EOL . $exception->getMessage()
            );

            throw new SnapshotNotCreatedException($exception->getMessage());
        }

        return $result;
    }

    /**
     * Create a partition for stock based on a given year.
     *
     * @throws PartitionNotCreatedException
     */
    public function createPartition(int $year): bool
    {
        $sql = <<<SQL
        ALTER TABLE `cameranu_analysis`.`stock_snapshots` REORGANIZE PARTITION p9999 INTO (
            PARTITION p%d VALUES LESS THAN (%d),
            PARTITION p9999 VALUES LESS THAN MAXVALUE
        )
        SQL;

        try {
            $this->entityManager->getConnection()->executeStatement(sprintf($sql, $year, $year + 1));

            $this->telegram->sendServerMessage(sprintf('Er is een voorraadsnapshotpartitie gemaakt voor %d', $year));
        } catch (Exception $exception) {
            $this->telegram->sendServerMessage(
                'Er is een fout opgetreden bij het maken van een partitie na een voorraadsnapshot: ' . PHP_EOL . $exception->getMessage()
            );

            throw new PartitionNotCreatedException($exception->getMessage());
        }

        return true;
    }

    /**
     * Remove old partitions based on a given year.
     *
     * @throws PartitionNotRemovedException
     */
    public function removePartition(int $year): bool
    {
        $sql = <<<SQL
        ALTER TABLE `cameranu_analysis`.`stock_snapshots` DROP PARTITION IF EXISTS p%d
        SQL;

        try {
            $this->entityManager->getConnection()->executeStatement(sprintf($sql, $year));

            $this->telegram->sendServerMessage(sprintf('Er is een voorraadsnapshotpartitie verwijderd voor %d', $year));
        } catch (Exception $exception) {
            $this->telegram->sendServerMessage(
                'Er is een fout opgetreden bij het verwijderen van een partitie na een voorraadsnapshot: ' . PHP_EOL . $exception->getMessage()
            );

            throw new PartitionNotRemovedException($exception->getMessage());
        }

        return true;
    }

    /**
     * @throws Exception
     */
    public function findLatestSnapshot(): int
    {
        $result = $this->entityManager->getConnection()->executeQuery('SELECT MAX(ss.snapshot_year) FROM `cameranu_analysis`.`stock_snapshots` as ss');

        return $result->fetchOne();
    }
}
