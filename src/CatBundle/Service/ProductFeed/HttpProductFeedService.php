<?php

declare(strict_types=1);

namespace CatBundle\Service\ProductFeed;

use Doctrine\DBAL\Driver\Exception;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\MenuItem;
use Webdsign\GlobalBundle\Entity\ProductFeed;
use Webdsign\GlobalBundle\Entity\ProductFeedFields;

class HttpProductFeedService extends ProductFeedService
{
    /**
     * @param string $hash
     * @param int|null $article
     * @param OutputInterface|null $output
     * @return string
     * @throws Exception|\Doctrine\DBAL\Exception
     */
    public function generateProductFeed(string $hash, ?int $article, ?OutputInterface $output = null): string
    {
        $productFeed = $this->checkFeed($hash);
        $jsonData = [];
        $jsonData['attributes'] = [];
        $jsonData['categories'] = [];

        if (!$productFeed instanceof ProductFeed) {
            return 'Geen feed bekend voor deze partner';
        }

        $fields = $productFeed->getFields();

        foreach ($fields as $field) {
            switch ($field->getKey()) {
                case 'importantSpecs':
                    $this->getImportantSpecs($article);
                    break;
                case 'specifications':
                    $this->getSpecs($article);
                    break;
                case 'secondhandinfo':
                    $this->getSecondhandProducts($article);
                    break;
                case 'actieLabel':
                    $this->getProductPromotions($article);
                    $this->getActionTagsByProduct($article);
                    break;
                case 'stockstatus':
                    $this->getStockStatusData($article);
                    break;
                case 'brand':
                    $this->getBrandData($article);
                    break;
                case 'tags':
                    $this->getTags($article);
                    break;
                case 'tagIds':
                    $this->getTagsWithIds($article);
                    break;
            }
        }

        $config = $this->createConfig($fields);
        $products = current($this->getProduct($article, $config));

        if ($products !== false) {
            $product = current($products);
            if ($product !== false) {
                /**
                 * @var ProductFeedFields $field
                 */
                foreach ($fields as $field) {
                    switch ($field->getKey()) {
                        case 'id':
                            $value = (string)$product['id'];
                            $jsonData['articleNumber'] = $this->convertMaxCharacters($value);
                            break;
                        case 'price':
                            $value = '';

                            if (array_key_exists('price', $products)) {
                                $value = (string)$products['price'];
                            }

                            $jsonData[$field->getName()] = $this->convertMaxCharacters($value);
                            break;
                        case 'stock':
                            $value = (string)$product['inStock'];
                            $jsonData[$field->getName()] = $this->convertMaxCharacters($value);
                            break;
                        case 'name':
                            $value = $product['name'];
                            $jsonData[$field->getName()] = $this->convertMaxCharacters($value);
                            break;
                        case 'brand':
                            $value = $this->brandData[$product['id']] ?? '';
                            $jsonData[$field->getName()] = $this->convertMaxCharacters(str_replace('&', '&amp;', $value));
                            break;
                        case 'image':
                            $value = '';

                            if (array_key_exists('image', $products) && is_string($products['image'])) {
                                $value = $this->imgPath . substr($products['image'], 0, 3) . '/' . $products['image'];
                            }

                            $jsonData[$field->getName()] = $this->convertMaxCharacters($value);
                            break;
                        case 'specifications':
                            $productSpecs = array_key_exists($product['id'], $this->specs) ? $this->specs[$product['id']] : [];
                            foreach ($productSpecs as $spec) {
                                if (is_array($spec)) {
                                    $jsonData['attributes'][] = [
                                        'key' => str_replace('&', '&amp;', $spec['name']),
                                        'values' => [
                                            $this->convertMaxCharacters(str_replace('&', '&amp;', $spec['value']))
                                        ],
                                    ];
                                }
                            }

                            foreach ($fields as $subField) {
                                switch ($subField->getKey()) {
                                    case 'reviewData':
                                        $jsonData['attributes'][] = [
                                            'key' => 'review_amount',
                                            'values' => [
                                                $this->convertMaxCharacters((string)$product['numberOfReviews'])
                                            ]
                                        ];

                                        $jsonData['attributes'][] = [
                                            'key' => 'review_rating',
                                            'values' => [
                                                $this->convertMaxCharacters((string)$product['reviewRating'])
                                            ]
                                        ];
                                        break;
                                    case 'importantSpecs':
                                        $importantSpecData = array_key_exists($product['id'], $this->importantSpecs) ? $this->importantSpecs[$product['id']] : [];
                                        $count = 0;
                                        $spanList = '';

                                        foreach ($importantSpecData as $name => $value) {
                                            if ($value === '') {
                                                continue;
                                            }
                                            $count++;

                                            $span = '<span class="li">';
                                            $span .= '<span>' . str_replace('&', '&amp;', $name) . ': ' . '</span>';
                                            $span .= '<span>' . str_replace('&', '&amp;', $value) . '</span>';
                                            $span .= '</span>';
                                            $spanList .= $span;

                                            if ($count === 4) {
                                                break;
                                            }
                                        }

                                        $jsonData['attributes'][] = [
                                            'key' => str_replace('&', '&amp;', $subField->getName()),
                                            'values' => [
                                                $this->convertMaxCharacters($spanList)
                                            ]
                                        ];

                                        break;
                                    case 'secondhandinfo':
                                        $secondhandProducts = array_key_exists($product['id'], $this->secondhandProducts) ? $this->secondhandProducts[$product['id']] : [];
                                        foreach ($secondhandProducts as $secondhandProduct) {
                                            $jsonData['attributes'][] = [
                                                'key' => 'Tweedehands',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $secondhandProduct['url']))
                                                ]
                                            ];
                                            break; // voor nu alleen de goedkoopste
                                        }
                                        break;
                                    case 'actieLabel':
                                        $productPromotions = array_key_exists($product['id'], $this->promotions['productPromotions']) ? $this->promotions['productPromotions'][$product['id']] : [];
                                        $prev = [];
                                        $discount = 0.;

                                        foreach ($productPromotions as $promotion) {
                                            $jsonData['attributes'][] = [
                                                'key' => 'Label',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $promotion['title']))
                                                ]
                                            ];

                                            $discount += $this->getPromotionDiscount(
                                                $promotion,
                                                (float)$products['price']
                                            );
                                            $prev[] = $promotion['title'];
                                        }

                                        $tagPromotions = [];
                                        $actionTags = array_key_exists($product['id'], $this->actionTagProducts)
                                            ? $this->actionTagProducts[$product['id']] : [];

                                        foreach ($actionTags as $tagId) {
                                            if (array_key_exists($tagId, $this->promotions['promotions'])) {
                                                $tagPromotions[] = $this->promotions['promotions'][$tagId];
                                            }
                                        }

                                        if (count($tagPromotions) > 0) {
                                            $tagPromotions = current($tagPromotions);
                                            foreach ($tagPromotions as $promotion) {
                                                if (in_array($promotion['title'], $prev)) {
                                                    continue;
                                                }

                                                $jsonData['attributes'][] = [
                                                    'key' => 'Label',
                                                    'values' => [
                                                        $this->convertMaxCharacters(str_replace('&', '&amp;', $promotion['title']))
                                                    ]
                                                ];

                                                $discount += $this->getPromotionDiscount(
                                                    $promotion,
                                                    (float)$products['price']
                                                );
                                            }
                                            //samenvoegen voor naam op label bepaling
                                            array_push($productPromotions, current($tagPromotions));
                                        }

                                        usort($productPromotions, function ($promo1, $promo2) {
                                            return $promo1['pos'] <=> $promo2['pos'];
                                        });

                                        $promo = current($productPromotions);
                                        if (is_array($promo) && array_key_exists('title', $promo)) {
                                            $jsonData['attributes'][] = [
                                                'key' => 'Label',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $promo['title']))
                                                ],
                                            ];
                                        }

                                        if ($discount > 0) {
                                            $priceAfterPromotion = number_format(
                                                $products['price'] - $discount,
                                                2,
                                                ',',
                                                '.'
                                            );

                                            if (strpos($priceAfterPromotion, ',00') !== false) {
                                                $priceAfterPromotion = str_replace(
                                                    ',00',
                                                    ',<em>-</em>',
                                                    $priceAfterPromotion
                                                );
                                            }
                                            $promotionName = $this->getPromotionName($productPromotions);

                                            $actieLabel = '<div class="cashback-price-wrap catalog-cashback">';
                                            $actieLabel .= '<span class="cashback-price">';
                                            $actieLabel .= $priceAfterPromotion . '&nbsp;' . $promotionName;
                                            $actieLabel .= '</span>';
                                            $actieLabel .= '</div>';

                                            $jsonData['attributes'][] = [
                                                'key' => 'priceAfter',
                                                'values' => [
                                                    $this->convertMaxCharacters($actieLabel)
                                                ],
                                            ];
                                        }

                                        break;
                                    case 'stockstatus':
                                        $stockStatus = array_key_exists($product['id'], $this->stockStatusData) ? $this->stockStatusData[$product['id']] : 'Tijdelijk uitverkocht';
                                        $jsonData['attributes'][] = [
                                            'key' => 'Voorraadstatus',
                                            'values' => [
                                                $this->convertMaxCharacters(str_replace('&', '&amp;', $stockStatus))
                                            ]
                                        ];
                                        break;
                                    case 'url':
                                        $this
                                            ->productUrlService
                                            ->setLanguage(null)
                                            ->setName($product['name'])
                                            ->setSlug($product['slug'])
                                            ->setId($product['id']);
                                        $url = $this->productUrlService->generateUrl();

                                        $jsonData['attributes'][] = [
                                            'key' => 'Url',
                                            'values' => [
                                                $this->convertMaxCharacters(str_replace('&', '&amp;', $url))
                                            ]
                                        ];
                                        break;
                                    case 'ean':
                                        $ean = $products['ean'];
                                        $jsonData['attributes'][] = [
                                            'key' => 'EAN',
                                            'values' => [
                                                $this->convertMaxCharacters(str_replace('&', '&amp;', $ean))
                                            ]
                                        ];
                                        break;
                                    case 'priceFromTo':
                                        $priceFromTo = $products['priceFromTo'];
                                        if ($priceFromTo > 0 && $priceFromTo !== null) {
                                            $jsonData['attributes'][] = [
                                                'key' => 'vanVoorPrijs',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $priceFromTo))
                                                ],
                                            ];
                                        }
                                        break;
                                    case 'keywords':
                                        $keywords = $product['keywords'];
                                        if (count($keywords) > 0) {
                                            $keywords = implode(',', array_column($keywords, 'keyword'));
                                            $jsonData['attributes'][] = [
                                                'key' => 'keywords',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $keywords))
                                                ],
                                            ];
                                        }
                                        break;
                                    case 'tags':
                                        $tags = array_key_exists(
                                            $product['id'],
                                            $this->tags
                                        ) ? $this->tags[$product['id']] : [];

                                        foreach ($tags as $tag) {
                                            $jsonData['attributes'][] = [
                                                'key' => 'tags',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $tag))
                                                ],
                                            ];
                                        }
                                        break;
                                    case 'tagIds':
                                        $tags = array_key_exists(
                                            $product['id'],
                                            $this->tagsWithIds
                                        ) ? $this->tagsWithIds[$product['id']] : [];

                                        // We moeten dit 2x toevoegen aan de feed omdat Tweakwise niet per filter andere opties
                                        // ondersteunt. We hebben namelijk een OF en een EN filter nodig. Dit geldt ook voor de specIds
                                        // hieronder.
                                        foreach ($tags as $tag) {
                                            $attributeValue = (string)$tag;

                                            $jsonData['attributes'][] = [
                                                'key' => $subField->getName() .  'Or',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $attributeValue))
                                                ],
                                            ];

                                            $jsonData['attributes'][] = [
                                                'key' => $subField->getName() .  'And',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', $attributeValue))
                                                ],
                                            ];
                                        }
                                        break;
                                    case 'specIds':
                                        $productSpecs = array_key_exists(
                                            $product['id'],
                                            $this->specs
                                        ) ? $this->specs[$product['id']] : [];

                                        foreach ($productSpecs as $spec) {
                                            if (is_array($spec)) {
                                                $attributeValue = $spec['specId'] . '-' . str_replace('&', '&amp;', $spec['value']);

                                                $jsonData['attributes'][] = [
                                                    'key' => $subField->getName() .  'Or',
                                                    'values' => [
                                                        $this->convertMaxCharacters(str_replace('&', '&amp;', $attributeValue))
                                                    ],
                                                ];

                                                $jsonData['attributes'][] = [
                                                    'key' => $subField->getName() .  'And',
                                                    'values' => [
                                                        $this->convertMaxCharacters(str_replace('&', '&amp;', $attributeValue))
                                                    ],
                                                ];
                                            }
                                        }

                                        if (array_key_exists('profileId', $productSpecs)) {
                                            $jsonData['attributes'][] = [
                                                'key' => 'specProfileId',
                                                'values' => [
                                                    $this->convertMaxCharacters(str_replace('&', '&amp;', (string)$productSpecs['profileId']))
                                                ],
                                            ];
                                        }
                                        break;
                                }
                            }
                            break;
                        case 'category':
                            /**
                             * @var  MenuItem $menuItem
                             */
                            foreach ($product['productMenuItems'] as $productMenuItem) {
                                $jsonData['categories'][] = $this->convertMaxCharacters((string)$productMenuItem['id']);
                            }
                            break;
                    }
                }
            }
        }

        return json_encode($jsonData);
    }

    /**
     * @param string $value
     * @param int $limit
     * @return string
     */
    private function convertMaxCharacters(string $value, int $limit = 400): string
    {
        return strlen($value) <= $limit ? $value : trim(substr($value, 0, ($limit - 3))) . '...';
    }

    /**
     * @param int|null $productId
     *
     * @return array
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    protected function getStockStatusData(?int $productId = null): array
    {
        $config = $this->configManager->getActive();
        $this->stockStatusData = $this->productRepository->getStockStatusData($config, $productId);

        return $this->stockStatusData;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getImportantSpecs(?int $productId = null): array
    {
        $this->importantSpecs = $this->productRepository->getImportantProductSpecs($productId);

        return$this->importantSpecs;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getSpecs(?int $productId = null): array
    {
        $this->specs = $this->productRepository->getProductSpecs($productId);
        return $this->specs;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getSecondhandProducts(?int $productId = null): array
    {
        $this->secondhandProducts = $this->productRepository->getSecondhandProducts($productId);
        return $this->secondhandProducts;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getProductPromotions(?int $productId = null): array
    {
        $this->promotions = $this->productRepository->getProductsPromotions($productId);
        return $this->promotions;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getActionTagsByProduct(?int $productId = null): array
    {
        $this->actionTagProducts = $this->productRepository->getActionTagsByProduct($productId);
        return $this->actionTagProducts;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getBrandData(?int $productId = null): array
    {
        $this->brandData = $this->productRepository->getBrandData([$productId]);
        return $this->brandData;
    }

    protected function getTags(?int $productId = null): array
    {
        if ($this->tags === null) {
            $this->tags = $this->productRepository->getProductTags($productId);
        }

        return $this->tags;
    }

    protected function getTagsWithIds(?int $productId = null): array
    {
        if ($this->tagsWithIds === null) {
            $this->tagsWithIds = $this->productRepository->getProductTags($productId, true);
        }

        return $this->tagsWithIds;
    }
}
