<?php

declare(strict_types=1);

namespace CatBundle\Service\ProductFeed;

use CatBundle\Service\ProductUrlService;
use DateInterval;
use DateTime;
use DateTimeInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use DOMDocument;
use Exception;
use HTMLPurifier;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemException;
use phpseclib3\Net\SFTP;
use SimpleXMLElement;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;
use Webdsign\GlobalBundle\DTO\WebsiteConfigurator\ActiveConfigDataObject;
use Webdsign\GlobalBundle\Entity\Event\Product as EventProduct;
use Webdsign\GlobalBundle\Entity\Event\ProductRepository as EventProductRepository;
use Webdsign\GlobalBundle\Entity\Event\RegistrationRepository;
use Webdsign\GlobalBundle\Entity\Event\Theme;
use Webdsign\GlobalBundle\Entity\MenuI18n;
use Webdsign\GlobalBundle\Entity\MenuItem;
use Webdsign\GlobalBundle\Entity\MenuItemRepository;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\ProductFeed;
use Webdsign\GlobalBundle\Entity\ProductFeedFields;
use Webdsign\GlobalBundle\Entity\ProductFeedRepository;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\ShipmentMethod;
use Webdsign\GlobalBundle\Entity\ShipmentMethodRepository;
use Webdsign\GlobalBundle\Form\DataTransformer\Event\EventTransformer;
use Webdsign\GlobalBundle\Services\Utility;
use Webdsign\GlobalBundle\Services\WebsiteConfigurator\ConfigManager;
use XSLTProcessor;

class ProductFeedService
{
    protected ?array $tags;
    private ?array $config;
    protected ?array $specs;
    protected string $imgPath;
    protected ?array $menuInfo;
    protected ?array $products;
    protected ?array $brandData;
    protected string $websiteUrl;
    protected ?array $promotions;
    protected ?array $spiderPromotions;
    private ?array $shippingCosts;
    private ?string $remoteDirectory;
    protected ?array $importantSpecs;
    protected ?array $temporaryPrices;
    protected ?array $stockStatusData;
    protected ?array $deliveryData;
    protected ?OutputInterface $output;
    protected ?array $stockPerLocation;
    protected ?array $actionTagProducts;
    protected ?array $secondhandProducts;
    private ActiveConfigDataObject $activeConfig;
    private array $powerReviewsSFTP;
    protected ?array $tagsWithIds;
    /**
     * @var null|EventProduct[]
     */
    protected ?array $events;
    protected ?array $content;
    protected ?array $purchaseOrderAmounts;

    public function __construct(
        protected readonly Filesystem $fileSystem,
        protected readonly ConfigManager $configManager,
        protected readonly EventTransformer $eventTransformer,
        protected readonly ProductRepository $productRepository,
        protected readonly ProductUrlService $productUrlService,
        protected readonly MenuItemRepository $menuItemRepository,
        protected readonly ProductFeedRepository $productFeedRepository,
        protected readonly EventProductRepository $eventsProductRepository,
        protected readonly ShipmentMethodRepository $shipmentMethodRepository,
        protected readonly RegistrationRepository $eventRegistrationRepository,
        string $imgPath,
        string $websiteUrl,
        array $powerReviewsSFTP,
        string $remoteDirectory = null
    ) {
        $this->imgPath = $imgPath;
        $this->websiteUrl = $websiteUrl;
        $this->powerReviewsSFTP = $powerReviewsSFTP;
        $this->remoteDirectory = $remoteDirectory;
        $this->activeConfig = $this->configManager->getActive();
        $this->setDefaults();
    }

    private function setDefaults(): void
    {
        $this->config = null;
        $this->products = null;
        $this->brandData = null;
        $this->importantSpecs = null;
        $this->promotions = null;
        $this->spiderPromotions = null;
        $this->actionTagProducts = null;
        $this->secondhandProducts = null;
        $this->stockStatusData = null;
        $this->deliveryData = null;
        $this->specs = null;
        $this->stockPerLocation = null;
        $this->menuInfo = null;
        $this->shippingCosts = null;
        $this->tags = null;
        $this->tagsWithIds = null;
        $this->temporaryPrices = null;
        $this->events = null;
        $this->content = null;
        $this->purchaseOrderAmounts = null;
    }

    /**
     * @param string $hash
     * @return string|null
     * @throws FilesystemException
     */
    public function getFeed(string $hash, string $type): ?string
    {
        if ($this->checkFeed($hash) === null) {
            return null;
        }

        $filesystem = $this->fileSystem;
        $feedName = $hash . '.' . $type;

        if ($this->remoteDirectory !== null) {
            $feedName = $this->remoteDirectory . '/' . $feedName;
        }

        if (!$filesystem->has($feedName)) {
            return null;
        }

        ini_set('memory_limit', '1G');

        return $filesystem->read($feedName);
    }

    /**
     * @param string $hash
     * @param int|null $article
     * @param OutputInterface|null $output
     * @return string
     * @throws Exception|\Doctrine\DBAL\Driver\Exception
     * @throws FilesystemException
     */
    public function generateProductFeed(string $hash, ?int $article, ?OutputInterface $output = null): string
    {
        $this->output = $output;
        $productId = null; // for debugging

        $productFeed = $this->checkFeed($hash);
        if (!$productFeed instanceof ProductFeed) {
            return 'Geen feed bekend voor deze partner';
        }

        $type = $productFeed->getFileType();

        switch ($type) {
            case 'xml':
            default:
                $rootItem = $this->initXML($productFeed);
                $items = $rootItem->addChild('items');
                break;
        }

        $fields = $productFeed->getFields();
        $config = $this->createConfig($fields);
        $this->output->writeln('Collecting products');
        $products = $this->getProducts($config, $productId);
        $this->output->writeln(count($products) . ' products collected');

        foreach ($fields as $field) {
            switch ($field->getKey()) {
                case 'importantSpecs':
                    $this->output->writeln('Collecting important specs');
                    $this->getImportantSpecs($productId);
                    $this->output->writeln('Important specs collected');
                    break;
                case 'specs':
                case 'specifications':
                    $this->output->writeln('Collecting specs');
                    $this->getSpecs($productId);
                    $this->output->writeln('Specs collected');
                    break;
                case 'secondhandinfo':
                case 'newPrice':
                    $this->output->writeln('Collecting secondhandProducts');
                    $this->getSecondhandProducts($productId);
                    $this->output->writeln('SecondhandProducts collected');
                    break;
                case 'actieLabel':
                case 'priceAfter':
                case 'priceAfterBe':
                case 'priceAfterDiscountBe':
                    $this->output->writeln('Collecting promotions');
                    $this->getProductPromotions($productId);
                    $this->getActionTagsByProduct($productId);
                    $this->getTemporaryPrices($productId);
                    $this->output->writeln('Promotions collected');
                    break;
                case 'spiderInfo':
                    $this->output->writeln('Collecting promotions');
                    $this->getSpiderProductPromotions($productId);
                    $this->getActionTagsByProduct($productId);
                    $this->output->writeln('Promotions collected');
                    break;
                case 'stockstatus':
                case 'deliveryInfo':
                    $this->output->writeln('Collecting stockData');
                    $this->getStockStatusData($productId);
                    $this->output->writeln('StockData collected');
                    break;
                case 'availabilityDate':
                    $this->output->writeln('Collecting deliveryData');
                    $this->getDeliveryData($productId);
                    $this->output->writeln('DeliveryData collected');
                    break;
                case 'brand':
                    $this->output->writeln('Collecting brandData');
                    $this->getBrandData($productId);
                    $this->output->writeln('brandData collected');
                    break;
                case 'stockPerLocation':
                case 'stockAmountPerLocation':
                    $this->output->writeln('Collecting stockPerLocation');
                    $this->getStockPerLocation($productId);
                    $this->output->writeln('stockPerLocation collected');
                    break;
                case 'breadcrumb':
                case 'categorie':
                    $this->output->writeln('Collecting menuInfo');
                    $this->getMenuInfo();
                    $this->output->writeln('menuInfo collected');
                    break;
                case 'shippingCosts':
                    $this->output->writeln('Collecting shippingCosts');
                    $this->getShippingCosts();
                    $this->output->writeln('shippingCosts collected');
                    break;
                case 'tags':
                    $this->output->writeln('Collecting tags');
                    $this->getTags($productId);
                    $this->output->writeln('Tags collected');
                    break;
                case 'tagIds':
                    $this->output->writeln('Collecting tags with ids (used for filtering)');
                    $this->getTagsWithIds();
                    $this->output->writeln('Tags with ids collected');
                    break;
                case 'events':
                case 'events_plain':
                    $this->output->writeln('Collecting events');
                    $this->getEvents();
                    $this->output->writeln('Events collected');
                    break;
                case 'content':
                    $this->output->writeln('Collecting content');
                    $this->getContent($productId);
                    $this->output->writeln('Content collected');
                    break;
                case 'amount_in_purchase_order':
                    $this->output->writeln('Collecting amounts in open PurchaseOrders');
                    $this->getPurchaseOrderAmounts($productId);
                    $this->output->writeln('Amounts in open PurchaseOrders collected');
                    break;
            }
        }

        if (count($products) > 0) {
            $progressBar = new ProgressBar($this->output, count($products));
            $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
            $progressBar->start();
        }

        foreach ($products as $info) {
            /** @var array $product */
            $product = current($info);
            $item = $items->addChild('item');
            /**
             * @var ProductFeedFields $field
             */
            foreach ($fields as $field) {
                switch ($type) {
                    case 'xml':
                    default:
                        switch ($field->getKey()) {
                            case 'id':
                            case 'name':
                                $value = (string)$product[$field->getKey()];
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $value));
                                break;
                            case 'nameShort':
                                $value = !empty((string)$product[$field->getKey()]) ? (string)$product[$field->getKey()] : (string)$product['name'];
                                $value = str_replace('&', '&amp;', $value);
                                $value = str_replace('"', '""', $value);
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'page_id_variant':
                            case 'page_id':
                                $value = (string)$info[$field->getKey()];
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $value));
                                break;
                            case 'price':
                            case 'priceEx':
                            case 'priceBe':
                                $value = $info[$field->getKey()] ?? '0.0';
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'stock':
                                $value = (string)$product['inStock'];
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $value));
                                break;
                            case 'priceFrom':
                                $priceFromTo = $info['priceFromTo'] ?? '0.0';
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $priceFromTo));
                                break;
                            case 'priceFromBe':
                                $priceFromTo = $info['priceFromToBe'] ?? '0.0';
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $priceFromTo));
                                break;
                            case 'productCode':
                                $value = $info[$field->getKey()] ?? '';
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $value));
                                break;
                            case 'priceAfter':
                                $promotionDiscount = $this->calculateDiscount($product['id'], (float)$info['price'], $item);
                                $priceAfterPromotion = number_format($info['price'] - $promotionDiscount, 2, '.', '');
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $priceAfterPromotion));
                                break;
                            case 'priceAfterBe':
                                $promotionDiscount = $this->calculateDiscount($product['id'], (float)$info['priceBe'], $item);
                                $priceAfterPromotion = number_format($info['priceBe'] - $promotionDiscount, 2, '.', '');
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $priceAfterPromotion));
                                break;
                            case 'priceAfterDiscount':
                                $discount = $this->calculateDiscount($product['id'], (float)$info['price'], $item, false, false);
                                $priceAfterDiscount = number_format($info['price'] - $discount, 2, '.', '');
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $priceAfterDiscount));
                                break;
                            case 'priceAfterDiscountBe':
                                $discount = $this->calculateDiscount($product['id'], (float)$info['priceBe'], $item, false, false);
                                $priceAfterDiscount = number_format($info['priceBe'] - $discount, 2, '.', '');
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $priceAfterDiscount));
                                break;
                            case 'EAN':
                                $eanOccasion = array_key_exists(
                                    'productCodeOccasion',
                                    $info
                                ) ? $info['productCodeOccasion'] : '';

                                $value = $info['ean'] ?? $eanOccasion;
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $value ?? ''));
                                break;
                            case 'description':
                                $value = $product['descriptionShort'];
                                $descriptionItem = $item->addChild($field->getName());
                                $node = dom_import_simplexml($descriptionItem);
                                $no = $node->ownerDocument;
                                $node->appendChild($no->createCDATASection($value ?? ''));
                                break;
                            case 'spiderInfo':
                                $value = '';
                                $promotions = array_key_exists(
                                    $product['id'],
                                    $this->spiderPromotions['productPromotions']
                                ) ? $this->spiderPromotions['productPromotions'][$product['id']] : [];

                                $actionTags = array_key_exists(
                                    $product['id'],
                                    $this->actionTagProducts
                                ) ? $this->actionTagProducts[$product['id']] : [];

                                foreach ($actionTags as $tagId) {
                                    if (array_key_exists($tagId, $this->spiderPromotions['promotions'])) {
                                        $promotions[] = current($this->spiderPromotions['promotions'][$tagId]);
                                    }
                                }

                                usort($promotions, function ($a, $b) {
                                    return $a['pos'] <=> $b['pos'];
                                });

                                foreach ($promotions as $key => $promotion) {
                                    if (str_contains($promotion['spiderinfo'], '%korting%')) {
                                        $priceFromTo = array_key_exists(
                                            'priceFromTo',
                                            $info
                                        ) ? $info['priceFromTo'] : 0;

                                        if ($priceFromTo > $info['price']) {
                                            $discount = abs($info['price'] - $priceFromTo);
                                            if ($discount === 0.) {
                                                unset($promotions[$key]);
                                                continue;
                                            }

                                            $spiderInfo = str_replace(
                                                '%korting%',
                                                Utility::formatPrice(
                                                    $discount,
                                                    Utility::PRICEFORMAT_DEFAULT,
                                                    null,
                                                    '',
                                                    false
                                                ),
                                                $promotion['spiderinfo']
                                            );

                                            $spiderInfo = str_replace(',-,-', ',-', $spiderInfo);

                                            $promotions[$key]['spiderinfo'] = $spiderInfo;
                                        } else {
                                            unset($promotions[$key]);
                                        }
                                    }
                                }

                                if (!empty($promotions)) {
                                    $promotion = array_shift($promotions);
                                    $value = str_replace('&', 'en', $promotion['spiderinfo']);
                                    $value = strip_tags($value);
                                }

                                $spiderInfoItem = $item->addChild($field->getName());
                                $node = dom_import_simplexml($spiderInfoItem);
                                $no = $node->ownerDocument;
                                $node->appendChild($no->createCDATASection($value));
                                break;
                            case ProductFeedFields::KEY_OP_IS_OP:
                                $value = ($product['flags2'] & Product::FLAG2_GONE) === Product::FLAG2_GONE ? 'Ja' : 'Nee';
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'SKU':
                                $value = (string)$product['sku'];
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $value));
                                break;
                            case 'warranty':
                                $value = $info[$field->getKey()];
                                $item->addChild($field->getName(), (string)$value);
                                break;
                            case 'contractModel':
                                $value = ($product['flags'] & Product::FLAG_CONTRACT_MODEL) === Product::FLAG_CONTRACT_MODEL ? 'Ja' : 'Nee';
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'images':
                                $images = $product[$field->getKey()];
                                foreach ($images as $key => $image) {
                                    $name = $field->getName() . $key;
                                    $value = $this->imgPath . substr($image['image'], 0, 3) . '/' . $image['image'];
                                    $item->addChild($name, $value);
                                }
                                break;
                            case 'teaser':
                                $value = $product[$field->getKey()];
                                $teaserItem = $item->addChild($field->getName());
                                $node = dom_import_simplexml($teaserItem);
                                $no = $node->ownerDocument;
                                $node->appendChild($no->createCDATASection($value ?? ''));
                                break;
                            case 'content':
                                $contents = array_key_exists(
                                    $product['id'],
                                    $this->content
                                ) ? $this->content[$product['id']] : [];

                                $value = implode('<br/>', $contents);
                                $value = $this->sanitizeHtml($value);

                                $contentItem = $item->addChild($field->getName());
                                $node = dom_import_simplexml($contentItem);
                                $no = $node->ownerDocument;
                                $node->appendChild($no->createCDATASection($value));
                                break;
                            case 'deliveryInfo':
                                $deliveryInfo = array_key_exists(
                                    $product['id'],
                                    $this->stockStatusData
                                ) ? $this->stockStatusData[$product['id']] : 'Tijdelijk uitverkocht';

                                $item->addChild($field->getName(), str_replace('&', '&amp;', $deliveryInfo));
                                break;
                            case 'availabilityDate':
                                $deliveryDate = $this->deliveryData[$product['id']] ?? null;
                                $inStock = $product['inStock'] ?? 0;
                                $productEntity = $this->productRepository->find($product['id']);

                                if (
                                    $deliveryDate instanceof DateTime &&
                                    (
                                        $inStock <= 0 ||
                                        $productEntity->isPreOrder() ||
                                        $this->stockStatusData[$product['id']] === 'backorder'
                                    )
                                ) {
                                    $item->addChild($field->getName(), $deliveryDate->format(DateTimeInterface::ATOM));
                                }
                                break;
                            case 'stockPerLocation':
                                $stockPerLocation = array_key_exists(
                                    $product['id'],
                                    $this->stockPerLocation
                                ) ? $this->stockPerLocation[$product['id']] : [];

                                foreach ($stockPerLocation as $location => $stock) {
                                    $item->addChild('voorraad' . ucfirst($location), (string)$stock);
                                }
                                break;
                            case 'breadcrumb':
                                $default = [
                                    'id' => 0,
                                    'name' => '',
                                    'url' => '',
                                ];

                                $defaults = [
                                    'rootgroup_website' => $default,
                                    'maingroup_website' => $default,
                                    'subgroup_website' => $default,
                                    'subgroupsub_website' => $default,
                                ];

                                $menuInfo = array_key_exists(
                                    $product['id'],
                                    $this->menuInfo
                                ) ? $this->menuInfo[$product['id']] : $defaults;

                                $breadcrumb = '';
                                foreach ($menuInfo as $menuItem) {
                                    if (!empty($menuItem['name'])) {
                                        $breadcrumb .= empty($breadcrumb) ? $menuItem['name'] : ' > ' . $menuItem['name'];
                                    }
                                }

                                $item->addChild($field->getName(), str_replace('&', '&amp;', $breadcrumb));
                                break;
                            case 'categorie':
                                $item->addChild('rootgroup', str_replace('&', '&amp;', $info['rootgroup_name']));
                                $item->addChild('maingroup', str_replace('&', '&amp;', $info['maingroup_name']));
                                $item->addChild('subgroup', str_replace('&', '&amp;', $info['subgroup_name']));
                                $default = [
                                    'id' => 0,
                                    'name' => '',
                                    'url' => '',
                                ];
                                $defaults = [
                                    'rootgroup_website' => $default,
                                    'maingroup_website' => $default,
                                    'subgroup_website' => $default,
                                    'subgroupsub_website' => $default,
                                ];
                                $menuInfo = array_key_exists(
                                    $product['id'],
                                    $this->menuInfo
                                ) ? $this->menuInfo[$product['id']] : $defaults;

                                foreach ($menuInfo as $key => $menuItem) {
                                    // Breadcrumb name
                                    $pathItem = $item->addChild($key);
                                    $node = dom_import_simplexml($pathItem);
                                    $no = $node->ownerDocument;
                                    $node->appendChild($no->createCDATASection($menuItem['name']));

                                    // Breadcrumb url
                                    $urlItem = $item->addChild($key . '_url');
                                    $node = dom_import_simplexml($urlItem);
                                    $no = $node->ownerDocument;
                                    $url = $menuItem['url'] !== '' ? $this->websiteUrl . $menuItem['url'] : '';
                                    $node->appendChild($no->createCDATASection($url));
                                }
                                break;
                            case 'brand':
                                $value = $this->brandData[$product['id']] ?? '';

                                $item->addChild($field->getName(), str_replace('&', '&amp;', $value));
                                break;
                            case 'combo':
                                $value = $info['cat_name'] === 'artikel' ? 'Nee' : 'Ja';
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'shippingCosts':
                                $shippingCosts = array_column($this->shippingCosts, 'price', 'id');
                                if ($product['flagsDelivery'] & ShipmentMethod::FLAG_POSTNL_FREE) {
                                    $shipping = (float)$shippingCosts[ShipmentMethod::CANCELLED];
                                } else {
                                    if ($product['flagsDelivery'] & ShipmentMethod::FLAG_POSTNL_PAID) {
                                        $shipping = (float)$shippingCosts[ShipmentMethod::ID_POSTNL_PAID];
                                    } else {
                                        $shipping = (float)$shippingCosts[ShipmentMethod::ID_COURIER];
                                    }
                                }

                                $threshold = $this->activeConfig->shipping->threshold->freeShipping ?? ShipmentMethod::THRESHOLD;
                                // Gratis boven de X euro
                                if (
                                    (
                                        $product['flagsDelivery'] & ShipmentMethod::FLAG_POSTNL_FREE ||
                                        $product['flagsDelivery'] & ShipmentMethod::FLAG_POSTNL_PAID
                                    ) &&
                                    $info['price'] >= $threshold
                                ) {
                                    $shipping = (float)$shippingCosts[ShipmentMethod::CANCELLED];
                                }

                                $item->addChild($field->getName(), (string)number_format($shipping, 2, '.', ''));
                                break;
                            case 'newPrice':
                                $k = 'id';
                                $r = [];
                                $parentId = false;
                                foreach ($this->secondhandProducts as $secondhandKey => $secondhandProduct) {
                                    array_walk_recursive(
                                        $secondhandProduct,
                                        function ($item, $key) use ($k, &$r, $product, $secondhandKey) {
                                            if ($key == $k && (int)$item === (int)$product['id']) {
                                                $r[] = $secondhandKey;
                                            }
                                        }
                                    );

                                    if (count($r) > 0) {
                                        $parentId = current($r);
                                        break;
                                    }
                                }

                                $secondhandInfo = $parentId !== false &&
                                array_key_exists(
                                    $parentId,
                                    $this->secondhandProducts
                                ) ? $this->secondhandProducts[$parentId] : [];

                                $newPrice = is_array($secondhandInfo) &&
                                is_array(current($secondhandInfo)) ? current($secondhandInfo)['newPrice'] : '';

                                $item->addChild($field->getName(), (string)$newPrice);
                                break;
                            case 'image':
                                $value = $this->imgPath . substr(
                                        $info[$field->getKey()],
                                        0,
                                        3
                                    ) . '/' . $info[$field->getKey()];
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'margin':
                                $productMargin = $product[$field->getKey()] ?? [];
                                $margin = array_key_exists(
                                    $field->getKey(),
                                    $productMargin
                                ) ? $productMargin[$field->getKey()] : 0.;
                                $marginPercentage = array_key_exists(
                                    'percentage',
                                    $productMargin
                                ) ? $productMargin['percentage'] : 0.;

                                $item->addChild('bruto_productmarge', (string)number_format($margin, 2, '.', ''));
                                $item->addChild(
                                    'procentuele_bruto_product_marge',
                                    (string)number_format($marginPercentage, 2, '.', '')
                                );
                                break;
                            case 'custom_label':
                                $amount = substr($field->getName(), -1);
                                for ($i = 0; $i <= $amount; $i++) {
                                    $labelName = $field->getKey() . '_' . $i;
                                    $item->addChild($labelName);
                                }
                                break;
                            case 'productUrl':
                                $this
                                    ->productUrlService
                                    ->setName($product['name'])
                                    ->setLanguage(null)
                                    ->setSlug($product['slug'])
                                    ->setId($product['id']);
                                $url = $this->productUrlService->generateUrl(false, true);
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $url));
                                break;
                            case 'productUrlBe':
                                $this
                                    ->productUrlService
                                    ->setName($product['name'])
                                    ->setLanguage('nl')
                                    ->setSlug($product['slug'])
                                    ->setId($product['id']);
                                $url = $this->productUrlService->generateUrl(false, true, 'be');
                                $item->addChild($field->getName(), str_replace('&', '&amp;', $url));
                                break;
                            case 'sales':
                                $value = $info['salesAmount'];
                                $item->addChild($field->getName(), (string)$value);
                                break;
                            case 'hasKickBackPremium':
                                $value = $info['hasKickBack'] ? 'true' : 'false';
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'purchase_price_margin_based':
                                $productMargin = $product['margin'] ?? [];
                                $margin = array_key_exists('margin', $productMargin) ? $productMargin['margin'] : 0.;
                                $value = $info['priceEx'] - $margin;
                                $item->addChild($field->getName(), number_format($value, 2, '.', ''));
                                break;
                            case 'purchase_price':
                                $value = number_format((float)$info['purchase_price'], 3, '.', '');
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'sellOutPremium':
                                $value = number_format((float)$info['sellOutAmount'], 2, '.', '');
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'sellInPremium':
                                $value = number_format((float)$info['sellInAmount'], 2, '.', '');
                                $item->addChild($field->getName(), $value);
                                break;
                            case 'amount_in_purchase_order':
                                $amountInOpenOrders = array_key_exists(
                                    $product['id'],
                                    $this->purchaseOrderAmounts
                                ) ? $this->purchaseOrderAmounts[$product['id']] : [];

                                $totalAmount = $amountInOpenOrders['amount_in_purchase_order'] ?? 0;
                                $totalScanned = ($amountInOpenOrders['scanned_amount'] ?? 0) + ($amountInOpenOrders['split_scanned_amount'] ?? 0);
                                $amountToScan = $totalAmount - $totalScanned;
                                $item->addChild($field->getName(), (string)$amountToScan);
                                break;
                            case 'specs':
                                $productSpecs = array_key_exists(
                                    $product['id'],
                                    $this->specs
                                ) ? $this->specs[$product['id']] : [];

                                $validSpecs = [
                                    'productlijn',
                                    'model_camera',
                                    'type_camera',
                                    'lensmount',
                                    'lensmount_camera',
                                    'lensmount_objectieven',
                                    'vergroting_kijker',
                                    'serie_kijker',
                                    'type_lens',
                                    'brandpuntsafstand',
                                    'filtermaat_objectief',
                                    'maximaal_diafragma',
                                    'minimaal_diafragma',
                                    'lensserie',
                                    'kleur',
                                    'type_product',
                                    'type_sensor_camera',
                                    'type_accu_camera',
                                    'serie_accu',
                                    'geschikt_voor_draagriem_voor_welk_apparaat',
                                    'type_geheugen',
                                    'type_geheugen_geheugenkaart_usb_harde_schijf',
                                    'kaarttype_geheugenkaart',
                                    'kaarttype',
                                    'inclusief_objectief',
                                ];

                                $specRename = [
                                    'type_accu_camera' => 'serie_accu',
                                    'geschikt_voor_draagriem_voor_welk_apparaat' => 'geschikt_voor',
                                    'type_geheugen_geheugenkaart_usb_harde_schijf' => 'type_geheugen',
                                    'kaarttype_geheugenkaart' => 'kaarttype',
                                ];

                                $prev = [];
                                foreach ($productSpecs as $key => $productSpec) {
                                    $specName = is_array($productSpec) ? strtolower(
                                        str_replace([' ', '(', ')', '/'], ['_', '', '', '_'], $productSpec['name'])
                                    ) : '';

                                    if (in_array($specName, $validSpecs) && !in_array($specName, $prev)) {
                                        $specName = array_key_exists(
                                            $specName,
                                            $specRename
                                        ) ? $specRename[$specName] : $specName;
                                        $specItem = $item->addChild($specName);
                                        $node = dom_import_simplexml($specItem);
                                        $no = $node->ownerDocument;
                                        $node->appendChild($no->createCDATASection($productSpec['value'] ?? ''));
                                        $prev[] = $specName;
                                    }

                                    if ($key === 'profile' && $productSpec !== null) {
                                        $specItem = $item->addChild('specificatieprofiel');
                                        $node = dom_import_simplexml($specItem);
                                        $no = $node->ownerDocument;
                                        $node->appendChild($no->createCDATASection($productSpec));
                                    }

                                    if ($key === 'type') {
                                        $specItem = $item->addChild($key);
                                        $node = dom_import_simplexml($specItem);
                                        $no = $node->ownerDocument;
                                        $node->appendChild($no->createCDATASection($productSpec));
                                    }
                                }

                                foreach ($fields as $subField) {
                                    if ($subField->getKey() === 'nameShort') {
                                        $item->addChild(
                                            $subField->getName(),
                                            str_replace('&', '&amp;', $product[$subField->getKey()])
                                        );
                                    }
                                }

                                break;
                            case 'specifications':
                                $specsItem = $item->addChild('attributes');
                                $productSpecs = array_key_exists(
                                    $product['id'],
                                    $this->specs
                                ) ? $this->specs[$product['id']] : [];
                                foreach ($productSpecs as $spec) {
                                    if (is_array($spec)) {
                                        $specItem = $specsItem->addChild('attribute');
                                        $specItem->addChild('name', str_replace('&', '&amp;', $spec['name']));
                                        $specItem->addChild('value', str_replace('&', '&amp;', $spec['value']));
                                    }
                                }

                                foreach ($fields as $subField) {
                                    switch ($subField->getName()) {
                                        case 'nameShort':
                                            $nameShortItem = $specsItem->addChild('attribute');
                                            $nameShortItem->addChild(
                                                'name',
                                                str_replace('&', '&amp;', $subField->getName())
                                            );
                                            $valueItem = $nameShortItem->addChild('value');
                                            $node = dom_import_simplexml($valueItem);
                                            $no = $node->ownerDocument;
                                            $node->appendChild($no->createCDATASection($product['nameShort']));
                                            break;
                                        case 'reviewData':
                                            $specItem = $specsItem->addChild('attribute');
                                            $specItem->addChild('name', 'review_amount');
                                            $specItem->addChild('value', (string)($info['amountOfReviews'] ? $info['amountOfReviews'] : 0));
                                            $specItem = $specsItem->addChild('attribute');
                                            $specItem->addChild('name', 'review_rating');
                                            $specItem->addChild('value', (string)($info['reviewRating'] ? $info['reviewRating'] : 0));
                                            break;
                                        case 'importantSpecs':
                                            $importantSpecData = array_key_exists(
                                                $product['id'],
                                                $this->importantSpecs
                                            ) ? $this->importantSpecs[$product['id']] : [];

                                            $count = 0;
                                            $spanList = '';

                                            foreach ($importantSpecData as $name => $value) {
                                                if ($value === '') {
                                                    continue;
                                                }
                                                $count++;

                                                $span = '<span class="li">';
                                                $span .= '<span>' . str_replace('&', '&amp;', $name) . ': ' . '</span>';
                                                $span .= '<span>' . str_replace('&', '&amp;', $value ?? '') . '</span>';
                                                $span .= '</span>';
                                                $spanList .= $span;

                                                if ($count === 4) {
                                                    break;
                                                }
                                            }

                                            $importantSpecItem = $specsItem->addChild('attribute');
                                            $importantSpecItem->addChild(
                                                'name',
                                                str_replace('&', '&amp;', $subField->getName())
                                            );

                                            $valueItem = $importantSpecItem->addChild('value');
                                            $node = dom_import_simplexml($valueItem);
                                            $no = $node->ownerDocument;
                                            $node->appendChild($no->createCDATASection($spanList));
                                            break;
                                        case 'secondhandinfo':
                                            $secondhandProducts = array_key_exists(
                                                $product['id'],
                                                $this->secondhandProducts
                                            ) ? $this->secondhandProducts[$product['id']] : [];

                                            foreach ($secondhandProducts as $secondhandProduct) {
                                                $secondhandProductItem = $specsItem->addChild('attribute');
                                                $secondhandProductItem->addChild('name', 'Tweedehands');
                                                $secondhandProductItem->addChild(
                                                    'value',
                                                    str_replace('&', '&amp;', $secondhandProduct['url'])
                                                );
                                                break; // voor nu alleen de goedkoopste
                                            }
                                            break;
                                        case 'actieLabel':
                                            $productPromotions = array_key_exists(
                                                $product['id'],
                                                $this->promotions['productPromotions']
                                            ) ? $this->promotions['productPromotions'][$product['id']] : [];
                                            $prev = [];
                                            $discount = 0.;
                                            foreach ($productPromotions as $promotion) {
                                                $discount += $this->getPromotionDiscount(
                                                    $promotion,
                                                    (float)$info['price']
                                                );
                                                $prev[] = $promotion['title'];
                                            }

                                            $tagPromotions = [];
                                            $actionTags = array_key_exists(
                                                $product['id'],
                                                $this->actionTagProducts
                                            ) ? $this->actionTagProducts[$product['id']] : [];

                                            foreach ($actionTags as $tagId) {
                                                if (array_key_exists($tagId, $this->promotions['promotions'])) {
                                                    $tagPromotions[] = $this->promotions['promotions'][$tagId];
                                                }
                                            }

                                            if (count($tagPromotions) > 0) {
                                                foreach ($tagPromotions as $promotion) {
                                                    $promotion = current($promotion);
                                                    if (in_array($promotion['title'], $prev)) {
                                                        continue;
                                                    }
                                                    //samenvoegen voor naam op label bepaling
                                                    array_push($productPromotions, $promotion);

                                                    $discount += $this->getPromotionDiscount(
                                                        $promotion,
                                                        (float)$info['price']
                                                    );
                                                }
                                            }

                                            // Black friday
                                            foreach ($productPromotions as $key => $promotion) {
                                                if ($promotion['description_admin'] === 'Black friday korting') {
                                                    if ($info['priceFromTo'] !== 0) {
                                                        $bfDiscount = $info['priceFromTo'] - $info['price'];

                                                        if (intval($bfDiscount) !== $bfDiscount) {
                                                            $promotion['title'] = str_replace(
                                                                ',-',
                                                                '',
                                                                $promotion['title']
                                                            );
                                                        }

                                                        $title = str_replace(
                                                            '%korting%',
                                                            Utility::formatPrice($bfDiscount),
                                                            $promotion['title']
                                                        );
                                                        $description = str_replace(
                                                            '%korting%',
                                                            Utility::formatPrice($bfDiscount),
                                                            $promotion['description']
                                                        );

                                                        $productPromotions[$key]['pos'] = -10;
                                                        $productPromotions[$key]['title'] = '<span class="black-friday-label">' . $title . '</span>';
                                                        $productPromotions[$key]['description'] = $description;
                                                    } else {
                                                        unset($productPromotions[$key]);
                                                    }
                                                }
                                            }

                                            usort($productPromotions, function ($promo1, $promo2) {
                                                return $promo1['pos'] <=> $promo2['pos'];
                                            });

                                            $promo = current($productPromotions);
                                            if (is_array($promo)) {
                                                $promotionsItem = $specsItem->addChild('attribute');
                                                $promotionsItem->addChild('name', 'Label');
                                                $promotionsItem->addChild(
                                                    'value',
                                                    str_replace('&', '&amp;', $promo['title'])
                                                );
                                            }

                                            if ($discount > 0) {
                                                $priceAfterPromotion = number_format(
                                                    $info['price'] - $discount,
                                                    2,
                                                    ',',
                                                    '.'
                                                );

                                                if (str_contains($priceAfterPromotion, ',00')) {
                                                    $priceAfterPromotion = str_replace(',00', ',<em>-</em>', $priceAfterPromotion);
                                                }
                                                $promotionName = $this->getPromotionName($productPromotions);

                                                $actieLabel = '<div class="cashback-price-wrap catalog-cashback">';
                                                $actieLabel .= '<span class="cashback-price">';
                                                $actieLabel .= $priceAfterPromotion . '&nbsp;' . $promotionName;
                                                $actieLabel .= '</span>';
                                                $actieLabel .= '</div>';

                                                $priceAfterPromotionItem = $specsItem->addChild('attribute');
                                                $priceAfterPromotionItem->addChild('name', 'priceAfter');
                                                $valueItem = $priceAfterPromotionItem->addChild('value');
                                                $node = dom_import_simplexml($valueItem);
                                                $no = $node->ownerDocument;
                                                $node->appendChild($no->createCDATASection($actieLabel));
                                            }
                                            break;
                                        case 'stockstatus':
                                            $stockStatus = array_key_exists(
                                                $product['id'],
                                                $this->stockStatusData
                                            ) ? $this->stockStatusData[$product['id']] : 'Tijdelijk uitverkocht';
                                            $stockStatusItem = $specsItem->addChild('attribute');
                                            $stockStatusItem->addChild('name', 'Voorraadstatus');
                                            $stockStatusItem->addChild(
                                                'value',
                                                str_replace('&', '&amp;', $stockStatus)
                                            );
                                            break;
                                        case 'verhouding_x':
                                            $productMargin = $product['margin'] ?? [];
                                            $margin = array_key_exists('margin', $productMargin) ? $productMargin['margin'] : 0.;
                                            $marginPercentage = array_key_exists(
                                                'percentage',
                                                $productMargin
                                            ) ? $productMargin['percentage'] : 0;

                                            $indication = $margin === 0. ? 0 : (abs($margin) * abs($marginPercentage)) / 100;

                                            $indicationItem = $specsItem->addChild('attribute');
                                            $indicationItem->addChild('name', $subField->getName());
                                            $indicationItem->addChild('value', (string)$indication);
                                            break;
                                        case 'stockAmountPerLocation':
                                            $stockPerLocation = array_key_exists(
                                                $product['id'],
                                                $this->stockPerLocation
                                            ) ? $this->stockPerLocation[$product['id']] : [];

                                            foreach ($stockPerLocation as $location => $stock) {
                                                if (in_array($location, ['zonder_urk', 'status'])) {
                                                    continue;
                                                }

                                                $stockItem = $specsItem->addChild('attribute');
                                                $stockItem->addChild('name', 'voorraad_' . $location);
                                                $stockItem->addChild('value', (string)$stock);
                                            }
                                            break;
                                        case 'url':
                                            $this
                                                ->productUrlService
                                                ->setLanguage(null)
                                                ->setName($product['name'])
                                                ->setSlug($product['slug'])
                                                ->setId($product['id']);
                                            $url = $this->productUrlService->generateUrl();
                                            $urlItem = $specsItem->addChild('attribute');
                                            $urlItem->addChild('name', 'Url');
                                            $urlItem->addChild('value', str_replace('&', '&amp;', $url));
                                            break;
                                        case 'ean':
                                            $ean = $info['ean'] ?? '';
                                            $eanItem = $specsItem->addChild('attribute');
                                            $eanItem->addChild('name', 'EAN');
                                            $eanItem->addChild('value', str_replace('&', '&amp;', $ean));
                                            break;
                                        case 'priceFromTo':
                                            $priceFromTo = $info['priceFromTo'];
                                            $check = (float)$priceFromTo - (float)$info['price'];
                                            if ($check > 0.50 && $priceFromTo > 0 && $priceFromTo !== null) {
                                                $eanItem = $specsItem->addChild('attribute');
                                                $eanItem->addChild('name', 'vanVoorPrijs');
                                                $eanItem->addChild('value', str_replace('&', '&amp;', $priceFromTo));
                                            }
                                            break;
                                        case 'keywords':
                                            $keywords = $product['keywords'];
                                            if (count($keywords) > 0) {
                                                $keywords = implode(',', array_column($keywords, 'keyword'));
                                                $keywordsItem = $specsItem->addChild('attribute');
                                                $keywordsItem->addChild('name', 'keywords');
                                                $keywordsItem->addChild('value', str_replace('&', '&amp;', $keywords));
                                            }
                                            break;
                                        case 'tags':
                                            $tags = array_key_exists(
                                                $product['id'],
                                                $this->tags
                                            ) ? $this->tags[$product['id']] : [];

                                            foreach ($tags as $tag) {
                                                $tagItem = $specsItem->addChild('attribute');
                                                $tagItem->addChild('name', $subField->getName());
                                                $tagItem->addChild('value', str_replace('&', '&amp;', $tag));
                                            }
                                            break;
                                        case 'tagIds':
                                            $tags = array_key_exists(
                                                $product['id'],
                                                $this->tagsWithIds
                                            ) ? $this->tagsWithIds[$product['id']] : [];

                                            // We moeten dit 2x toevoegen aan de feed omdat Tweakwise niet per filter andere opties
                                            // ondersteunt. We hebben namelijk een OF en een EN filter nodig. Dit geldt ook voor de specIds
                                            // hieronder.
                                            foreach ($tags as $tag) {
                                                $attributeValue = (string)$tag;

                                                $tagItem = $specsItem->addChild('attribute');
                                                $tagItem->addChild('name', $subField->getName() . 'Or');
                                                $tagItem->addChild('value', $attributeValue);

                                                $tagItem = $specsItem->addChild('attribute');
                                                $tagItem->addChild('name', $subField->getName() . 'And');
                                                $tagItem->addChild('value', $attributeValue);
                                            }
                                            break;
                                        case 'specIds':
                                            $productSpecs = array_key_exists(
                                                $product['id'],
                                                $this->specs
                                            ) ? $this->specs[$product['id']] : [];

                                            foreach ($productSpecs as $spec) {
                                                if (is_array($spec)) {
                                                    $attributeValue = $spec['specId'] . '-' . str_replace(
                                                            '&',
                                                            '&amp;',
                                                            $spec['value']
                                                        );

                                                    $specItem = $specsItem->addChild('attribute');
                                                    $specItem->addChild('name', $subField->getName() . 'Or');
                                                    $specItem->addChild('value', $attributeValue);

                                                    $specItem = $specsItem->addChild('attribute');
                                                    $specItem->addChild('name', $subField->getName() . 'And');
                                                    $specItem->addChild('value', $attributeValue);
                                                }
                                            }

                                            if (array_key_exists('profileId', $productSpecs)) {
                                                $specItem = $specsItem->addChild('attribute');
                                                $specItem->addChild('name', 'specProfileId');
                                                $specItem->addChild('value', (string)$productSpecs['profileId']);
                                            }
                                            break;
                                    }
                                }
                                break;
                            case 'category':
                                $categoryItem = $item->addChild('categories');
                                /**
                                 * @var  MenuItem $menuItem
                                 */
                                foreach ($product['productMenuItems'] as $productMenuItem) {
                                    $categoryItem->addChild('categoryid', (string)$productMenuItem['id']);
                                }
                                break;
                            case 'fulfillment_fee':
                                $productMargin = $product['margin'] ?? [];
                                $margin = array_key_exists('margin', $productMargin) ? $productMargin['margin'] : 0.;
                                $purchasePrice = $info['priceEx'] - $margin;

                                $fulfillmentFeePercentage = 4.5;
                                $value = ($purchasePrice / 100) * $fulfillmentFeePercentage;

                                $item->addChild($field->getName(), number_format($value, 2, '.', ''));
                                break;
                            case 'went_online':
                                if ($product['wentOnline'] instanceof DateTime) {
                                    $item->addChild($field->getName(), (string)$product['wentOnline']->getTimestamp());
                                }
                                break;
                        }
                        break;
                }
            }

            $progressBar->advance();
        }

        if (count($products) > 0) {
            $progressBar->finish();
        }

        $this->output->writeln("\n");

        foreach ($fields as $field) {
            switch ($field->getKey()) {
                case 'categories':
                    $categoriesItem = $rootItem->addChild('categories');
                    $categoryItem = $categoriesItem->addChild('category');
                    $categoryItem->addChild('categoryid', '0');
                    $categoryItem->addChild('name', 'Root CameraNU.nl');
                    $categoryItem->addChild('rank', '0');
                    $this->output->writeln('Collecting menuItems');
                    $menuItems = $this->menuItemRepository->findActiveByMenuAndType(8, 'regular');
                    $this->output->writeln(count($menuItems) . ' menuItems collected');
                    $progressBar = new ProgressBar($this->output, count($menuItems));
                    $progressBar->start();
                    $count = 0;

                    foreach ($menuItems as $info) {
                        $menuItem = current($info);

                        $categoryItem = $categoriesItem->addChild('category');
                        $categoryItem->addChild('categoryid', (string)$menuItem['id']);
                        $categoryItem->addChild('name', str_replace('&', '&amp;', $info['name']));
                        $categoryItem->addChild('rank', (string)$menuItem['depth']);
                        $parentsItem = $categoryItem->addChild('parents');

                        if (
                            array_key_exists('parent', $menuItem) &&
                            is_array($menuItem['parent']) &&
                            array_key_exists('id', $menuItem['parent'])
                        ) {
                            $parentsItem->addChild('categoryid', (string)$menuItem['parent']['id']);
                        } else {
                            $parentsItem->addChild('categoryid', '0');
                        }
                        $progressBar->setProgress($count++);
                    }

                    //neppe categorie voor Events omdat Tweakwise ze anders niet wil inlezen
                    $categoryItem = $categoriesItem->addChild('category');
                    $categoryItem->addChild('categoryid', '4');
                    $categoryItem->addChild('name', 'Events');
                    $categoryItem->addChild('rank', '0');
                    $parentsItem = $categoryItem->addChild('parents');
                    $parentsItem->addChild('categoryid', '0');

                    $progressBar->finish();
                    break;
                case 'events':
                case 'events_plain':
                    if (count($this->events) === 0) {
                        break;
                    }

                    $this->output->writeln(PHP_EOL);
                    $this->output->writeln('Processing events');

                    $progressBar = new ProgressBar($output, count($this->events));
                    $progressBar->start();

                    foreach ($this->events as $event) {
                        $eventItem = $items->addChild('item');
                        switch ($field->getKey()) {
                            case 'events':
                                $this->generateEventProductItem($eventItem, $event);
                                break;
                            case 'events_plain':
                                $this->generatePlainEventProductItem($eventItem, $event);
                                break;
                        }

                        $progressBar->advance();
                    }

                    $progressBar->finish();

                    $this->output->writeln(count($this->events) . ' events processed');
                    break;
            }
        }

        $this->output->writeln("\n");
        $this->output->writeln('Feed wegschrijven naar storage');

        //leeg wat vars om memory vrij te maken om de xml weg te kunnen schrijven
        unset($products);
        $this->setDefaults();

        switch ($type) {
            case 'csv':
                $this->saveCSV($rootItem, $hash, $productFeed);
                break;
            case 'xml':
            default:
                $this->saveXML($rootItem, $hash, $productFeed);
                break;
        }

        return 'Feed weggeschreven';
    }

    public function addAttribute(SimpleXMLElement $attributes, string $name, string $value): void
    {
        $attribute = $attributes->addChild('attribute');
        $attribute->addChild('name', $name);
        $attribute->addChild('value', str_replace('&', '&amp;', $value));
    }

    public function generateEventProductItem(SimpleXMLElement $item, EventProduct $eventProduct): void
    {
        $event = $eventProduct->getEvent();
        $product = $eventProduct->getProduct();
        $productPrice = $product?->getPriceForCountry('NL', false);

        $maximumCapacity = $eventProduct->getMaximumCapacity() ?? 0;
        $registrationCount = $this->eventRegistrationRepository->getCompletedRegistrationsCountByEventProductId(
            $eventProduct->getId()
        );
        $availableCapacity = max(0, $maximumCapacity - $registrationCount);

        $item->addChild('id', (string)($product->getId()));
        $item->addChild('name', str_replace('&', '&amp;', $event->getTitle()));
        $item->addChild('price', $productPrice?->getPrice() ?? '0.0');
        $item->addChild('stock', (string)($availableCapacity));
        $item->addChild('groupcode', (string)$event->getId());

        $categoryItem = $item->addChild('categories');
        $categoryItem->addChild('categoryid', '4');

        $this
            ->productUrlService
            ->setLanguage(null)
            ->setName($event->getTitle())
            ->setId($event->getId());
        $url = $this->productUrlService->generateEventsUrl();

        $attributes = $item->addChild('attributes');
        $this->addAttribute($attributes, 'Url', $url);
        $this->addAttribute($attributes, 'ProductType', 'Event');
        $this->addAttribute($attributes, 'Startdatum', $eventProduct->getStartDateAndTime()->format('Y-m-d H:i'));
        $this->addAttribute($attributes, 'Einddatum', $eventProduct->getEndDateAndTime()->format('Y-m-d H:i'));
        $this->addAttribute($attributes, 'Totale Plaatsen', (string)$maximumCapacity);
        $this->addAttribute($attributes, 'Beschikbare Plaatsen', (string)$availableCapacity);
        $this->addAttribute($attributes, 'Beschikbaar', (string)$availableCapacity > 0 ? 'Ja' : 'Nee');
        $this->addAttribute($attributes, 'Winkel', $event->getStockLocation()->getDescription());
        $this->addAttribute($attributes, 'Locatie', $eventProduct->getLocation()->getName());
        $this->addAttribute($attributes, 'Straat', $eventProduct->getLocation()->getStreet() ?? '');
        $this->addAttribute($attributes, 'Huisnummer', $eventProduct->getLocation()->getHouseNumber() ?? '');
        $this->addAttribute(
            $attributes,
            'Huisnummer Toevoeging',
            $eventProduct->getLocation()->getHouseNumberAddition() ?? ''
        );
        $this->addAttribute($attributes, 'Postcode', $eventProduct->getLocation()->getPostalCode() ?? '');
        $this->addAttribute($attributes, 'Stad', $eventProduct->getLocation()->getCity() ?? '');
        $this->addAttribute($attributes, 'Land', $eventProduct->getLocation()->getCountry() ?? '');
        $this->addAttribute($attributes, 'Provincie', $eventProduct->getLocation()->getProvince() ?? '');
        $this->addAttribute($attributes, 'Type', $event->getCategory()->getName());
        $this->addAttribute($attributes, 'Niveau', $event->getLevel());
        $this->addAttribute(
            $attributes,
            'Thema\'s',
            implode(
                ',',
                array_map(
                    fn (Theme $theme) => $theme->getName(),
                    $event->getThemes()->toArray()
                )
            )
        );

        $startDate = $eventProduct->getStartDateAndTime();
        $endDate = $eventProduct->getEndDateAndTime();
        $duration = $startDate->diff($endDate);

        $this->addAttribute($attributes, 'Tijdsklasse', $this->formatDuration($duration));
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    private function generatePlainEventProductItem(SimpleXMLElement $item, EventProduct $eventProduct): void
    {
        $event = $eventProduct->getEvent();
        $product = $eventProduct->getProduct();
        $productPrice = $product?->getPriceForCountry('NL', false);

        $maximumCapacity = $eventProduct->getMaximumCapacity() ?? 0;
        $registrationCount = $this->eventRegistrationRepository->getCompletedRegistrationsCountByEventProductId(
            $eventProduct->getId()
        );
        $availableCapacity = max(0, $maximumCapacity - $registrationCount);

        $this
            ->productUrlService
            ->setLanguage(null)
            ->setName($event->getTitle())
            ->setId($event->getId());
        $url = $this->productUrlService->generateEventsUrl(true);


        $startDate = $eventProduct->getStartDateAndTime();
        $endDate = $eventProduct->getEndDateAndTime();
        $duration = $startDate->diff($endDate);

       $image = $this->imgPath . ltrim($event->getHeroImage(), '/upload');

        $item->addChild('id', (string)($product->getId()));
        $item->addChild('title', str_replace('&', '&amp;', $event->getTitle()));
        $item->addChild('prijs', $productPrice?->getPrice() ?? '0.0');
        $item->addChild('afbeeldingen0', $image);
        $item->addChild('voorraad', (string)($availableCapacity));
        $item->addChild('url', $url);
        $item->addChild('type_product', 'event');
        $item->addChild('startdatum', $eventProduct->getStartDateAndTime()->format('Y-m-d H:i'));
        $item->addChild('eindDatum', $eventProduct->getEndDateAndTime()->format('Y-m-d H:i'));
        $item->addChild('totalePlaatsen', (string)$maximumCapacity);
        $item->addChild('beschikbarePlaatsen', (string)$availableCapacity);
        $item->addChild('beschikbaar', (string)$availableCapacity > 0 ? 'Ja' : 'Nee');
        $item->addChild('winkel', str_replace('&', '&amp;', $event->getStockLocation()->getDescription()));
        $item->addChild('locatie', str_replace('&', '&amp;', $eventProduct->getLocation()->getName()));
        $item->addChild('straat', str_replace('&', '&amp;', $eventProduct->getLocation()->getStreet() ?? ''));
        $item->addChild('huisnummer', $eventProduct->getLocation()->getHouseNumber() ?? '');
        $item->addChild('huisnummerToevoeging', str_replace('&', '&amp;', $eventProduct->getLocation()->getHouseNumberAddition() ?? ''));
        $item->addChild('postcode', $eventProduct->getLocation()->getPostalCode() ?? '');
        $item->addChild('stad', str_replace('&', '&amp;', $eventProduct->getLocation()->getCity() ?? ''));
        $item->addChild('land', str_replace('&', '&amp;', $eventProduct->getLocation()->getCountry() ?? ''));
        $item->addChild('provincie', str_replace('&', '&amp;', $eventProduct->getLocation()->getProvince() ?? ''));
        $item->addChild('type', str_replace('&', '&amp;', $event->getCategory()->getName()));
        $item->addChild('niveau', str_replace('&', '&amp;', $event->getLevel()));
        $item->addChild(
            'themas',
            implode(
                ',',
                array_map(
                    fn (Theme $theme) => str_replace('&', '&amp;', $theme->getName()),
                    $event->getThemes()->toArray()
                )
            )
        );
        $item->addChild('tijdsklasse', $this->formatDuration($duration));
    }

    protected function formatDuration(DateInterval $duration): string
    {
        $formattedDuration = '';
        if ($duration->d > 0) {
            $formattedDuration .= $duration->d . ' dagen ';
        }
        if ($duration->h > 0) {
            $formattedDuration .= $duration->h . ' uren ';
        }
        if ($duration->i > 0) {
            $formattedDuration .= $duration->i . ' minuten ';
        }
        return trim($formattedDuration);
    }

    /**
     * @param string $hash
     * @param OutputInterface $output
     * @return string
     * @throws Exception
     * @throws FilesystemException
     */
    public function generateMenuItemFeed(string $hash, OutputInterface $output): string
    {
        $this->output = $output;

        $productFeed = $this->checkFeed($hash);
        if (!$productFeed instanceof ProductFeed) {
            return 'Geen feed bekend voor deze partner';
        }

        $type = $productFeed->getFileType();

        switch ($type) {
            case 'xml':
            default:
                $rootItem = $this->initXML($productFeed);
                $items = $rootItem->addChild('items');
                break;
        }

        $fields = $productFeed->getFields();

        $menuItems = $this->menuItemRepository->getItemsForFeed();
        $menuItemData = [];

        $progressBar = new ProgressBar($this->output, count($menuItems));
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
        $progressBar->start();

        /**
         * @var MenuItem $menuItem
         * @var MenuI18n $mi18n
         */
        foreach ($menuItems as $menuItem) {
            $mi18n = $menuItem->getMenuI18n()[0];
            $url = $menuItem->getUrl() !== '' ? $menuItem->getUrl() : '/c' . $menuItem->getId(
                ) . '/' . $menuItem->getPath();
            $fullUrl = $this->websiteUrl . $url;

            $menuItemData[$menuItem->getId()] = [
                'id' => $menuItem->getId(),
                'url' => $fullUrl,
                'name_nl' => $mi18n->getName(),
                'title' => $mi18n->getTitle(),
                'h1_title' => $mi18n->getH1(),
                'meta_description' => $mi18n->getMetaDescription(),
                'parent_title' => '',
                'parent_id' => '',
            ];

            $menuItemData = $this->getMenuItemChildData($menuItemData, $menuItem);

            $progressBar->advance();
        }

        $progressBar->finish();

        foreach ($menuItemData as $menuItem) {
            $item = $items->addChild('item');
            /**
             * @var ProductFeedFields $field
             */
            foreach ($fields as $field) {
                switch ($type) {
                    case 'xml':
                    default:
                        $name = $field->getName();
                        if (array_key_exists($name, $menuItem)) {
                            $value = (string)$menuItem[$name];
                            $item->addChild($name, str_replace('&', '&amp;', $value));
                        }
                }
            }
        }

        $this->output->writeln("\n");
        $this->output->writeln('Feed wegschrijven naar storage');

        switch ($type) {
            case 'xml':
            default:
                $this->saveXML($rootItem, $hash, $productFeed);
                return 'Feed weggeschreven';
        }
    }

    protected function checkFeed(string $hash): ?ProductFeed
    {
        $productFeed = $this->productFeedRepository->findOneBy(['hash' => $hash]);
        if (!$productFeed instanceof ProductFeed) {
            return null;
        }

        return $productFeed;
    }

    /**
     * @param ProductFeed $productFeed
     * @return SimpleXMLElement
     * @throws Exception
     */
    protected function initXml(ProductFeed $productFeed): SimpleXMLElement
    {
        $now = new DateTime();
        $date = $now->format('Y-m-d-H:i:s');
        $partner = strtolower($productFeed->getPartner());
        $xsi = 'http://www.w3.org/2001/XMLSchema-instance';
        $xsd = 'http://www.w3.org/2001/XMLSchema';
        $shop = 'CameraNU.nl';

        $rootElement = '<%s xmlns:xsi="%s" xmlns:xsd="%s" shop="%s" date="%s"></%s>';
        $rootElement = sprintf(
            $rootElement,
            $partner,
            $xsi,
            $xsd,
            $shop,
            $date,
            $partner
        );

        return new SimpleXMLElement($rootElement);
    }

    protected function getProducts(array $config = [], int $productId = null): array
    {
        if ($this->products === null) {
            $this->products = $this->productRepository->getProductFeedData([$productId], $config);
        }

        return $this->products;
    }

    protected function getProduct(int $id, array $config = []): array
    {
        return $this->productRepository->getProductFeedData([$id], $config);
    }


    /**
     * @param int|null $productId
     * @return array
     */
    protected function getSpecs(?int $productId = null): array
    {
        if ($this->specs === null) {
            $this->specs = $this->productRepository->getProductSpecs($productId);
        }

        return $this->specs;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getImportantSpecs(?int $productId = null): array
    {
        if ($this->importantSpecs === null) {
            $this->importantSpecs = $this->productRepository->getImportantProductSpecs($productId);
        }

        return $this->importantSpecs;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getSecondhandProducts(?int $productId = null): array
    {
        if ($this->secondhandProducts === null) {
            $this->secondhandProducts = $this->productRepository->getSecondhandProducts($productId);
        }

        return $this->secondhandProducts;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getProductPromotions(?int $productId = null): array
    {
        if ($this->promotions === null) {
            $this->promotions = $this->productRepository->getProductsPromotions($productId);
        }

        return $this->promotions;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getSpiderProductPromotions(?int $productId = null): array
    {
        if ($this->spiderPromotions === null) {
            $this->spiderPromotions = $this->productRepository->getProductsPromotions($productId, false);
        }

        return $this->spiderPromotions;
    }

    /**
     * @param int|null $productId
     * @return array
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    protected function getStockStatusData(?int $productId = null): array
    {
        if ($this->stockStatusData === null) {
            $activeConfig = $this->configManager->getActive();
            $this->stockStatusData = $this->productRepository->getStockStatusData($activeConfig, $productId);
        }

        return $this->stockStatusData;
    }

    protected function getDeliveryData(?int $productId = null): array
    {
        if (!empty($this->deliveryData)) {
            return $this->deliveryData;
        }

        try {
            $this->deliveryData = $this->productRepository->getStockStatusData(
                $this->configManager->getActive(),
                $productId,
                returnType: 'datetime'
            );
        } catch (Throwable) {
        }

        return $this->deliveryData;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getStockPerLocation(?int $productId = null): array
    {
        if ($this->stockPerLocation === null) {
            $this->stockPerLocation = $this->productRepository->getStockPerLocation($productId);
        }

        return $this->stockPerLocation;
    }

    protected function getMenuInfo(): array
    {
        if ($this->menuInfo === null) {
            $this->menuInfo = $this->productRepository->getCategoryPath();
        }

        return $this->menuInfo;
    }

    protected function getBrandData(?int $productId = null): array
    {
        if ($this->brandData === null) {
            $this->brandData = $this->productRepository->getBrandData([$productId], indexById: true);
        }

        return $this->brandData;
    }

    /**
     * @param SimpleXMLElement $xmlItem
     * @param string $hash
     * @return void
     * @throws FilesystemException
     */
    protected function saveXML(SimpleXMLElement $xmlItem, string $hash, ProductFeed $productFeed): void
    {
        $path = '/tmp/' . $hash . '.xml';
        $dom = new DOMDocument('1.0');
        $dom->loadXML($xmlItem->asXML(), LIBXML_PARSEHUGE);

        //Moet de xml nog geconverteerd worden middels een xsl
        if ($productFeed->getXsl() !== null) {
            $this->output->writeln('Feed converteren met xsl');
            $xsl = new DOMDocument();
            $xsl->loadXML($productFeed->getXsl());

            $xsltProcessor = new XSLTProcessor();
            $xsltProcessor->importStylesheet($xsl);
            $dom = $xsltProcessor->transformToDoc($dom);
            $this->output->writeln('Feed geconverteerd');
        }

        $dom->preserveWhiteSpace = true;
        $dom->formatOutput = true;
        $dom->save($path);

        $filesystem = $this->fileSystem;
        $feedName = $hash . '.xml';

        if ($this->remoteDirectory !== null) {
            $feedName = $this->remoteDirectory . '/' . $feedName;
        }

        $exists = $filesystem->has($feedName);

        if ($exists !== false) {
            $filesystem->delete($feedName);
        }

        $filesystem->writeStream($feedName, fopen($path, 'r+'));
    }

    /**
     * @throws FilesystemException
     */
    protected function saveCSV(SimpleXMLElement $xmlItem, string $hash, ProductFeed $feed): void
    {
        $dom = new DOMDocument('1.0');
        $dom->preserveWhiteSpace = true;
        $dom->formatOutput = true;
        $dom->loadXML($xmlItem->asXML(), LIBXML_PARSEHUGE);

        $path = '/tmp/' . $hash . '.csv';
        $xsl = new DOMDocument;
        $xsl->loadXML($feed->getXsl());

        $filesystem = $this->fileSystem;
        $feedName = $hash . '.csv';

        if ($this->remoteDirectory !== null) {
            $feedName = $this->remoteDirectory . '/' . $feedName;
        }

        $xsltProcessor = new XSLTProcessor();
        $xsltProcessor->importStylesheet($xsl);
        $csvContent = $xsltProcessor->transformToXML($dom);
        file_put_contents($path, $csvContent);

        $exists = $filesystem->has($feedName);

        if ($exists !== false) {
            $filesystem->delete($feedName);
        }

        $filesystem->writeStream($feedName, fopen($path, 'r+'));

        if (in_array($feed->getId(), [ProductFeed::POWERREVIEWS_ID, ProductFeed::POWERREVIEWS_BE_ID])) {
            $sftp = new SFTP($this->powerReviewsSFTP['host'], $this->powerReviewsSFTP['port']);
            $sftp->login($this->powerReviewsSFTP['user'], $this->powerReviewsSFTP['pass']);
            $sftp->put($this->powerReviewsSFTP['filepath'] . $feed->getHash() . '.csv', fopen($path, 'r+'));
        }
    }

    protected function getMenuItemChildData(array $menuItemData, MenuItem $menuItem): array
    {
        /**
         * @var MenuI18n $mi18n
         */
        $mi18n = $menuItem->getMenuI18n()[0];
        $children = $menuItem->getChildren();

        /**
         * @var MenuItem $child
         * @var MenuI18n $childMi18n
         */
        foreach ($children as $child) {
            if ($child->getType() !== 'regular' || $child->isActive() !== true) {
                continue;
            }

            $url = $child->getUrl() !== '' ? $child->getUrl() : '/c' . $child->getId() . '/' . $child->getPath();
            $fullUrl = $this->websiteUrl . $url;

            $childMi18n = $child->getMenuI18n()[0];
            $menuItemData[$child->getId()] = [
                'id' => $child->getId(),
                'url' => $fullUrl,
                'name_nl' => $childMi18n->getName(),
                'title' => $childMi18n->getTitle(),
                'h1_title' => $childMi18n->getH1(),
                'meta_description' => $childMi18n->getMetaDescription(),
                'parent_id' => $child->getParent()->getId(),
                'parent_title' => $mi18n->getTitle(),
            ];

            $grandChildren = $child->getChildren();
            if (count($grandChildren) > 0) {
                $menuItemData = $this->getMenuItemChildData($menuItemData, $child);
            }
        }

        return $menuItemData;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getActionTagsByProduct(?int $productId = null): array
    {
        if ($this->actionTagProducts === null) {
            $this->actionTagProducts = $this->productRepository->getActionTagsByProduct($productId);
        }

        return $this->actionTagProducts;
    }

    protected function getShippingCosts(): array
    {
        if ($this->shippingCosts === null) {
            $this->shippingCosts = $this->shipmentMethodRepository->getDeliveryCosts();
        }

        return $this->shippingCosts;
    }

    protected function getPromotionDiscount(array $promotion, float $price, bool $includeCashBack = true): float
    {
        $discount = 0.;

        if (
            $promotion['reduction_amount'] > 0 ||
            $promotion['discount_amount'] > 0 ||
            $promotion['cashback_amount'] > 0 ||
            $promotion['promotion_amount'] > 0
        ) {
            if (
                $promotion['promoType'] === 'discount' &&
                str_contains($promotion['discount_amount'], '%')
            ) {
                $discountPercentage = str_replace('%', '', $promotion['discount_amount']);
                $thisDiscountAmount = ($price * $discountPercentage) / 100;
                $discount += $thisDiscountAmount;
            } else {
                if ($promotion['promoType'] === 'tab' && $includeCashBack) {
                    $discount += $promotion['cashback_amount'];
                } elseif ($promotion['promoType'] === 'discount') {
                    $discount += $promotion['discount_amount'];
                } elseif ($promotion['promoType'] === 'reduction') {
                    $discount += $promotion['reduction_amount'];
                }
            }
        }

        return $discount;
    }

    protected function getPromotionName(array $promotions): string
    {
        $promotionName = 'na acties';

        // uitfilteren van promotions die geen prijzen hebben ingevuld
        foreach ($promotions as $key => $promotion) {
            if (
                (int)$promotion['reduction_amount'] === 0 &&
                (int)$promotion['discount_amount'] === 0 &&
                (int)$promotion['cashback_amount'] === 0 &&
                (int)$promotion['promotion_amount'] === 0
            ) {
                unset($promotions[$key]);
            }
        }

        // Actie Prijs label naam ophalen
        if (count(array_unique(array_column($promotions, 'promoType'))) === 1) {
            $plural = array_count_values(array_column($promotions, 'promoType'));
            switch (key(array_count_values(array_column($promotions, 'promoType')))) {
                case 'reduction':
                case 'discount':
                    $promotionName = 'na korting';
                    break;
                case 'tab':
                    $promotionName = $plural['tab'] > 1 ? 'na cashbacks' : 'na cashback';
                    break;
                case 'promotion':
                    $promotionName = $plural['promotion'] > 1 ? 'na acties' : 'na actie';
                    break;
            }
        }

        return $promotionName;
    }

    public function createConfig($fields): array
    {
        if ($this->config === null) {
            $config = [];
            foreach ($fields as $field) {
                $config[] = $field->getKey();
            }

            $this->config = $config;
        }

        return $this->config;
    }

    protected function getTags(?int $productId = null): array
    {
        if ($this->tags === null) {
            $this->tags = $this->productRepository->getProductTags($productId);
        }

        return $this->tags;
    }

    /**
     * @param int|null $productId
     * @return array
     */
    protected function getTagsWithIds(?int $productId = null): array
    {
        if ($this->tagsWithIds === null) {
            $this->tagsWithIds = $this->productRepository->getProductTags($productId, true);
        }

        return $this->tagsWithIds;
    }

    protected function getTemporaryPrices(?int $productId = null): array
    {
        if ($this->temporaryPrices === null) {
            $this->temporaryPrices = $this->productRepository->getTemporaryPrices([$productId]);
        }

        return $this->temporaryPrices;
    }

    protected function getPurchaseOrderAmounts(?int $productId = null): array
    {
        if ($this->purchaseOrderAmounts === null) {
            $this->purchaseOrderAmounts = $this->productRepository->getPurchaseOrderAmounts($productId);
        }

        return $this->purchaseOrderAmounts;
    }

    protected function getContent(?int $productId = null): array
    {
        if ($this->content === null) {
            $this->content = $this->productRepository->getContent($productId);
        }

        return $this->content;
    }

    protected function getEvents(): array
    {
        if ($this->events === null) {
            $this->events = $this->eventsProductRepository->getEventsForProductFeed('1 year');
        }

        return $this->events;
    }

    /**
     * @throws Exception
     */
    protected function calculateDiscount(
        int $productId,
        float $price,
        SimpleXMLElement $item,
        bool $addDuration = true,
        bool $includeCashBack = true
    ): float {
        $productPromotions = array_key_exists(
            $productId,
            $this->spiderPromotions['productPromotions']
        ) ? $this->spiderPromotions['productPromotions'][$productId] : [];

        $prev = $startDates = $endDates = [];
        $discount = 0.;
        $dateRange = null;
        foreach ($productPromotions as $promotion) {
            $discountAmount = $this->getPromotionDiscount($promotion, $price, $includeCashBack);
            $discount += $discountAmount;
            $prev[] = $promotion['title'];

            if ($discountAmount > 0) {
                $startDates[] = (int)$promotion['start_timestamp'];
                $endDates[] = (int)$promotion['end_timestamp'];
            }
        }

        $tagPromotions = [];
        $actionTags = array_key_exists(
            $productId,
            $this->actionTagProducts
        ) ? $this->actionTagProducts[$productId] : [];

        foreach ($actionTags as $tagId) {
            if (array_key_exists($tagId, $this->spiderPromotions['promotions'])) {
                $tagPromotions[] = $this->spiderPromotions['promotions'][$tagId];
            }
        }

        if (count($tagPromotions) > 0) {
            foreach ($tagPromotions as $promotion) {
                $promotion = current($promotion);
                if (in_array($promotion['title'], $prev)) {
                    continue;
                }

                $discountAmount = $this->getPromotionDiscount($promotion, $price, $includeCashBack);
                $discount += $discountAmount;
                if ($discountAmount > 0) {
                    $startDates[] = (int)$promotion['start_timestamp'];
                    $endDates[] = (int)$promotion['end_timestamp'];
                }
            }
        }

        if ($addDuration && $discount > 0) {
            $fromDate = new DateTime();
            $toDate = new DateTime();

            if (count($startDates) > 0 && count($endDates) > 0) {
                $from = min($startDates);
                $to = max($endDates);

                if ($from !== false && $to !== false) {
                    $fromDate->setTimestamp($from);
                    $toDate->setTimestamp($to);
                }
            }

            $dateRange = $fromDate->format('Y-m-d H:i') . ' - ' . $toDate->format('Y-m-d H:i');
            $item->addChild('promotion_duration', str_replace('&', '&amp;', $dateRange));
        }

        if ($addDuration && $dateRange === null) {
            $temporaryPrices = array_key_exists(
                $productId,
                $this->temporaryPrices
            ) ? $this->temporaryPrices[$productId] : [];

            foreach ($temporaryPrices as $temporaryPrice) {
                $fromDate = new DateTime($temporaryPrice['start']);
                $toDate = new DateTime($temporaryPrice['end']);
                if ($price <= (float)$temporaryPrice['price']) {
                    $dateRange = $fromDate->format('Y-m-d H:i') . ' - ' . $toDate->format('Y-m-d H:i');
                    $item->addChild('promotion_duration', str_replace('&', '&amp;', $dateRange));
                    break;
                }
            }
        }

        return $discount;
    }

    protected function sanitizeHtml(string $content): string
    {
        // Elements that should be treated as block elements.
        $blockElements = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'div',
        ];
        foreach ($blockElements as $element) {
            $content = preg_replace('/<' . $element . '(\s[^>]*)?>/i', '<div>', $content);
            $content = preg_replace('/<\/' . $element . '>/i', '</div>', $content);
        }

        $purifier = new HTMLPurifier([
            'HTML.Allowed' => 'strong,em,ul,ol,li,br,sub,sup,div,dl,dt,dd',
        ]);
        $value = $purifier->purify($content);

        return $value;
    }
}
