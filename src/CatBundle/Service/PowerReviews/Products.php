<?php

declare(strict_types=1);

namespace CatBundle\Service\PowerReviews;

use GuzzleHttp\Exception\GuzzleException;

class Products extends Client
{
    /**
     * @throws GuzzleException
     */
    public function getReviewSummaries(array $pageIds, string $locale = 'nl_NL'): array
    {
        return json_decode(
            $this->get(
                sprintf(
                    '/v1/products/%s/m/%s/l/%s/summaries',
                    implode(',', $pageIds),
                    $this->getMerchantId(),
                    $locale
                ),
                ['pageLocale' => $locale]
            ),
            true
        );
    }
}
