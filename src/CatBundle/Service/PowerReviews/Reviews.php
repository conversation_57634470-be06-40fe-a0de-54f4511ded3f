<?php

declare(strict_types=1);

namespace CatBundle\Service\PowerReviews;

use GuzzleHttp\Exception\GuzzleException;

class Reviews extends Client
{
    /**
     * @throws GuzzleException
     */
    public function getReviews(int $pageId, ?int $nextPage = null): array
    {
        $params = [
            'page_id' => $pageId,
        ];

        if ($nextPage !== null) {
            $params['next_page'] = $nextPage;
        }

        return json_decode(
            $this->get('/v1/reviews', $params),
            true
        );
    }
}
