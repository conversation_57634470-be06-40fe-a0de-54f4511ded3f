<?php

declare(strict_types=1);

namespace CatBundle\Service\PowerReviews;

use GuzzleHttp\Client as Guzzle;
use GuzzleHttp\Exception\GuzzleException;

abstract class Client
{
    protected Guzzle $client;
    protected string $token;
    protected int $authenticated;

    protected const int TOKEN_EXPIRES = 3540;

    public function __construct(
        private readonly string $merchantId,
        private readonly string $baseUrl,
        private readonly string $clientId,
        private readonly string $clientSecret,
    ) {
        $this->client = new Guzzle(['base_uri' => $this->baseUrl . '/']);
        $this->authenticated = 0;
    }

    /**
     * @throws GuzzleException
     */
    private function authenticate(): void
    {
        if ($this->authenticated === 0 || time() - $this->authenticated >= self::TOKEN_EXPIRES) {
            $this->authenticated = time();

            $result = json_decode(
                $this->client->post('oauth2/token', [
                    'query' => [
                        'grant_type' => 'client_credentials',
                    ],
                    'form_params' => [
                        'client_id' => $this->clientId,
                        'client_secret' => $this->clientSecret,
                    ]
                ])->getBody()->getContents()
            );

            $this->token = 'Bearer ' . $result->access_token;
        }
    }

    protected function getClient(): Guzzle
    {
        return $this->client;
    }

    protected function getMerchantId(): string
    {
        return $this->merchantId;
    }

    /**
     * @throws GuzzleException
     */
    protected function get(string $uri, array $options): string
    {
        $this->authenticate();

        return $this->client->get($uri, ['query' => $options, 'headers' => ['Authorization' => $this->token]])->getBody()->getContents();
    }

    /**
     * @throws GuzzleException
     */
    protected function post(string $uri, array $options): string
    {
        $this->authenticate();

        return $this->client->post($uri, ['query' => $options, 'headers' => ['Authorization' => $this->token]])->getBody()->getContents();
    }
}
