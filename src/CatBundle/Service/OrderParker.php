<?php

declare(strict_types=1);

namespace CatBundle\Service;

use Carbon\Carbon;
use DateTime;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Throwable;
use Webdsign\GlobalBundle\Entity\AdminLog;
use Webdsign\GlobalBundle\Entity\CartItem;
use Webdsign\GlobalBundle\Entity\LocationCompartment;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\OrderWait;
use Webdsign\GlobalBundle\Entity\OrderWaitRepository;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\Parking;
use Webdsign\GlobalBundle\Entity\ParkingRepository;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\UserRepository;

class OrderParker
{
    public const int PARKING_POSTNL = 4;
    public const int PARKING_LETTERS = 3;
    public const int PARKING_TRUNKRS = 1352;
    public const int PARKING_EPS = 575;
    public const int PARKING_UPS = 244;
    public const int PARKING_AMS_PICKUP_LATER = 1787;
    public const int PARKING_CT_PICKUP_LATER = 1842;
    public const int PARKING_GRO_PICKUP_LATER = 1900;
    public const int PARKING_PRT_PICKUP_LATER = 1919;
    public const int PARKING_EHW_PICKUP_LATER = 1782;
    public const int PARKING_ROT_PICKUP_LATER = 1997;
    public const int PARKING_RMA = 16;
    public const int PARKING_RMA_VENDIRO = 1305;
    public const int PARKING_PAYMENT_RETURN_COSTS = 655;
    public const int PARKING_PAYMENT_RETURN_COSTS_PAID = 2315;
    public const int PARKING_CHECK_FRAUD = 1955;
    public const array PARKING_UPSTAIRS = [
        self::PARKING_POSTNL => 1,
        self::PARKING_LETTERS => 1,
        self::PARKING_TRUNKRS => 1882,
    ];
    public const array PARKING_BACKORDER_PAID = [
        self::PARKING_POSTNL => 14,
        self::PARKING_EPS => 585,
        self::PARKING_UPS => 294,
        self::PARKING_AMS_PICKUP_LATER => 1702,
        self::PARKING_CT_PICKUP_LATER => 1847,
        self::PARKING_GRO_PICKUP_LATER => 1917,
        self::PARKING_PRT_PICKUP_LATER => 1702,
        self::PARKING_EHW_PICKUP_LATER => 1967,
        self::PARKING_ROT_PICKUP_LATER => 1993,
        Parking::AMS_PRO_LATER => Parking::BACKORDER_AMS_PRO,
        Parking::ROT_PRO_LATER => Parking::BACKORDER_ROT_PRO,
        Parking::ANT_LATER => Parking::BACKORDER_ANT,
        Parking::UTR_LATER => Parking::BACKORDER_UTR,
    ];
    public const int PARKING_VAT_FREE = 1921;
    public const array SKIP_PARKING_IDS = [
        self::PARKING_CHECK_FRAUD,
        self::PARKING_RMA,
        self::PARKING_RMA_VENDIRO,
        Parking::LARGE_ORDER_AMOUNT,
        Parking::CHECK_LOST_SHIPMENTS,
    ];

    public const array PARKING_SHIP_FROM_STORE = [
        StockLocation::AMS_STORE => 2433,
        StockLocation::APL_STORE => 2434,
        StockLocation::GRO_STORE => 2435,
        StockLocation::ROT_STORE => 2436,
        StockLocation::UTR_STORE => 2438,
        StockLocation::ANT_STORE => 2437,
    ];

    /**
     * @var User
     */
    private User $systemUser;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly OrderWaitRepository $waitRepository,
        private readonly ParkingRepository $parkingRepository,
        private readonly ProductRepository $productRepository,
        UserRepository $userRepository,
        private readonly LoggerInterface $orderParkerLogger
    ) {
        $this->systemUser = $userRepository->findSystemUser();
    }

    public function parkOrder(OrderInfo $order, ?int $overwriteParkingId = null): bool
    {
        try {
            return $this->handelParking($order, $overwriteParkingId);
        } catch (Throwable $exception) {
            $message = sprintf('Error bij parkeren van order: %s %s', $order->getId(), $exception->getMessage());
            $this->orderParkerLogger->error($message);
        }

        return false;
    }

    /**
     * @throws Exception
     * @throws \Exception
     */
    private function handelParking(OrderInfo $order, ?int $overwriteParkingId = null): bool
    {
        if ($order->isFinishedOrder()) {
            $this->addToOrderLog($order, 'Order niet geparkeerd omdat deze al is afgerond');
            return false;
        }

        if (
            $order->getParking() instanceof Parking &&
            in_array($order->getParking()->getId(), self::SKIP_PARKING_IDS)
        ) {
            return false;
        }

        $today = Carbon::createMidnightDate();
        $backorder = false;
        $upstairs = false;
        $parkingId = null;
        $isPickup = false;
        $stock = true;
        $extraInfo = '';
        $containsSecondhand = false;
        $checkLostShipments = false;
        $secondHandCartItems = [];

        $shipment = $order->getShippingMethod();

        switch ($shipment->getName()) {
            case 'pakketdienst':
            case 'pakketdienst_gratis':
            case 'pakjegemak':
                $parkingId = self::PARKING_POSTNL;
                $checkLostShipments = $order->getCustomer()?->hasLostShipment() ?? false;
                break;
            case 'briefpost':
            case 'briefpost_gratis':
                $parkingId = self::PARKING_LETTERS;
                break;
            case 'trunkrs':
                $parkingId = self::PARKING_TRUNKRS;
                break;
            case 'pakketdienst_be_de':
            case 'pakketdienst_europa':
                $parkingId = self::PARKING_EPS;
                break;
            case 'ups_express':
            case 'ups':
            case 'ups_bede':
            case 'ups_rollen':
                $parkingId = self::PARKING_UPS;
                break;
            case 'ams_vandaag_afhalen':
            case 'ams_later_afhalen':
                $parkingId = self::PARKING_AMS_PICKUP_LATER;
                $isPickup = true;
                break;
            case 'ct_vandaag_afhalen':
            case 'ct_later_afhalen':
                $parkingId = self::PARKING_CT_PICKUP_LATER;
                $isPickup = true;
                break;
            case 'gro_vandaag_afhalen':
            case 'gro_later_afhalen':
                $parkingId = self::PARKING_GRO_PICKUP_LATER;
                $isPickup = true;
                break;
            case 'prt_vandaag_afhalen':
            case 'prt_later_afhalen':
                $parkingId = self::PARKING_PRT_PICKUP_LATER;
                $isPickup = true;
                break;
            case 'cfe_vandaag_afhalen':
            case 'cfe_later_afhalen':
                $parkingId = self::PARKING_EHW_PICKUP_LATER;
                $isPickup = true;
                break;
            case 'rot_vandaag_afhalen':
            case 'rot_later_afhalen':
                $parkingId = self::PARKING_ROT_PICKUP_LATER;
                $isPickup = true;
                break;
            case '072W_vandaag_afhalen':
            case '072W_later_afhalen':
                $parkingId = Parking::AMS_PRO_LATER;
                $isPickup = true;
                break;
            case '082W_vandaag_afhalen':
            case '082W_later_afhalen':
                $parkingId = Parking::ROT_PRO_LATER;
                $isPickup = true;
                break;
            case '092W_vandaag_afhalen':
            case '092W_later_afhalen':
                $parkingId = Parking::ANT_LATER;
                $isPickup = true;
                break;
            case '102w_vandaag_afhalen':
            case '102w_later_afhalen':
                $parkingId = Parking::UTR_LATER;
                $isPickup = true;
                break;
            default:
                $extraInfo = 'Verzendwijze: ' . $shipment->getShipmentMethodShort() . ' is niet ingesteld voor parkeren.';
        }

        $isPaid = $order->isPaid(true, true);

        // check VAT free order
        if (($order->getFlags() & OrderInfo::FLAG_NO_VAT) === OrderInfo::FLAG_NO_VAT) {
            $parkingId = self::PARKING_VAT_FREE;
        } else {
            $cartItems = $order->getCartitems();

            foreach ($cartItems as $cartItem) {
                $cartItemStatus = $cartItem->getStatus();

                if (
                    $cartItemStatus & CartItem::FLAG_IS_PARENT_WITHOUT_OWN_STOCK ||
                    $cartItemStatus & CartItem::FLAG_IS_COMBO_PARENT
                ) {
                    continue;
                }

                if ($cartItem->getProduct()->hasToBeDividedBySalesTeam()) {
                    $parkingId = null;
                    $extraInfo = 'Artikel: ' . $cartItem->getProductId() . ' is gemarkeerd als verdeling team sales';
                    break;
                }

                $includeInvisible = false;
                if ($cartItem->getProduct()->isOccasion()) {
                    $includeInvisible = true;
                    $containsSecondhand = true;
                    $secondHandCartItems[] = $cartItem;
                }

                // checken of product niet op voorraad is
                if ($cartItem->getStatus() & CartItem::FLAG_WAS_NOT_IN_STOCK) {
                    // backorder check of order wel betaald is
                    if (
                        array_key_exists($parkingId, self::PARKING_BACKORDER_PAID) &&
                        !$order->hasPartialShipping()
                    ) {
                        $stockPerLocation = $this
                            ->productRepository
                            ->getStockPerLocation($cartItem->getProductId(), includeInvisible: $includeInvisible)
                            [$cartItem->getProductId()] ?? [];

                        $origin = $order->getOrigin()->getParent();
                        $location = $origin === Origin::CAMERANU ? StockLocation::URK : str_replace('cameranu_', '', $origin);

                        $dispatchAble = 0;
                        if (
                            is_array($stockPerLocation) &&
                            array_key_exists(StockLocation::URK, $stockPerLocation)
                        ) {
                            $dispatchAble = $location === StockLocation::URK ?
                                $stockPerLocation[StockLocation::URK] :
                                $stockPerLocation[StockLocation::URK] + $stockPerLocation['zonder_urk'];
                        }

                        if ($isPaid) {
                            // Als er geen echte voorraad op locatie is, gaat hij naar backorder.
                            // Mits er voorraad beschikbaar in ander locaties, dan mag hij gewoon door.
                            // Dit wordt dan verder door de koeriers lijsten opgepakt.
                            if ($dispatchAble <= 0) {
                                $stock = false;
                            }

                            $backorder = true;
                        } elseif ($dispatchAble <= 0 && $overwriteParkingId === null) {
                            //$isPaid is false, this shouldn't happen because the orderParker is called after creating a payment.
                            //if there is a payment and the order isn't fully paid $overwriteParking should be filled.
                            $backorderParking = $this->parkingRepository->findOneBy(['id' => self::PARKING_BACKORDER_PAID[$parkingId]]);

                            $this->addToOrderLog($order,
                                sprintf(
                                    'Intentie om order te verplaatsen naar "%s" maar er was iets mis met de betaling',
                                    $backorderParking->getDescription()
                                )
                            );
                        }

                        continue;
                    }

                    // anders een log aan de order toevoegen
                    if ($order->hasPartialShipping()) {
                        $message = 'Order in delen verzenden';
                    }

                    if (isset($message)) {
                        $this->addToOrderLog($order, $message);
                        $this->entityManager->flush();
                        return false;
                    }
                }

                $product = $cartItem->getProduct();
                $locationNodes = $product->getLocationCompartmentNode();

                foreach ($locationNodes as $locationNode) {
                    $location = $locationNode->getLocation();

                    if (
                        $location !== null &&
                        $location->getStockLocation() instanceof StockLocation &&
                        $location->getStockLocation()->getId() === StockLocation::MAGAZIJN_URK
                    ) {
                        $locationCompartment = $location->getCompartment();

                        // Moeten we 'boven' parkeren?
                        $locationCompartmentPrefix = substr($locationCompartment, 0, 1);
                        if (in_array($locationCompartmentPrefix, LocationCompartment::UPSTAIRS_LOCATION_PREFIXES)) {
                            $upstairs = true;
                        }
                    }
                }

                // Als product op voorraad was ten tijde van bestelling
                // En hij is niet backorder en hij hoeft niet naar boven
                // En het is een afhaalorder dan hoeft hij niet geparkeerd te worden
                if (
                    $cartItemStatus & cartItem::FLAG_WAS_IN_STOCK &&
                    $upstairs !== true &&
                    $backorder !== true &&
                    $isPickup === true
                ) {
                    $parkingId = null;
                }
            }


            //Ship From Store check
            if ($containsSecondhand && !$isPickup && $isPaid) {
                $origin = $order->getOrigin()->getParent();
                $location = $origin === Origin::CAMERANU ? StockLocation::URK : str_replace('cameranu_', '', $origin);
                $secondHandProductIds = [];

                foreach ($secondHandCartItems as $secondHandCartItem) {
                    //op voorraad Urk? dan kijken we niet naar SFS
                    $stockPerLocation = $this
                        ->productRepository
                        ->getStockPerLocation($secondHandCartItem->getProductId(), includeInvisible: true)[$secondHandCartItem->getProductId()] ?? [];

                    $dispatchableUrk = 0;
                    if (
                        is_array($stockPerLocation) &&
                        array_key_exists(StockLocation::URK, $stockPerLocation) &&
                        $location === StockLocation::URK
                    ) {
                        $dispatchableUrk = $stockPerLocation[StockLocation::URK];
                    }

                    if ($dispatchableUrk < $secondHandCartItem->getAmount()) {
                        //2e-hands producten niet voorradig op Urk
                        $secondHandProductIds[] = $secondHandCartItem->getProduct()->getId();
                    }
                }

                // we hebben nu een array met 2e-hans producten die niet voorradig zijn op Urk,
                // we gaan nu kijken welke SFS-locaties de volledige order kunnen uitleveren.
                if (count($secondHandProductIds) > 0) {
                    $stockArray = [];
                    foreach (StockLocation::SHIP_FROM_STORE_LOCATIONS as $shipFromStoreLocation) {
                        $stockArray[$shipFromStoreLocation]['dispatchable'] = true;
                        $stockArray[$shipFromStoreLocation]['oldestStockDate'] = null;
                        $shipFromStoreStockLocation = (new StockLocation())->setId($shipFromStoreLocation);

                        foreach ($cartItems as $cartItem) {
                            $unsoldShipFromStoreStockItems = $cartItem
                                ->getProduct()
                                ->getUnsoldStock(stockLocation: $shipFromStoreStockLocation);

                            if (count($unsoldShipFromStoreStockItems) < $cartItem->getAmount()) {
                                //een product ligt niet in deze SFS-locatie de order kan dus niet volledig vanuit deze locatie komen.
                                $stockArray[$shipFromStoreLocation]['dispatchable'] = false;
                                //we gaan door met de volgende SFS-locatie.
                                continue 2;
                            }

                            //voor 2e-hands producten gaan we de oudste datumIn bijhouden
                            foreach ($unsoldShipFromStoreStockItems as $unsoldShipFromStoreStockItem) {
                                if (in_array($unsoldShipFromStoreStockItem->getProduct()->getId(), $secondHandProductIds)) {
                                    $timestamp = $unsoldShipFromStoreStockItem->getDateIn()->getTimestamp();

                                    if ($stockArray[$shipFromStoreLocation]['oldestStockDate'] === null) {
                                        $stockArray[$shipFromStoreLocation]['oldestStockDate'] = $timestamp;
                                    } elseif ($timestamp < $stockArray[$shipFromStoreLocation]['oldestStockDate']) {
                                        $stockArray[$shipFromStoreLocation]['oldestStockDate'] = $timestamp;
                                    }
                                }
                            }
                        }
                    }

                    //haal locaties eruit die de order niet volledig kunnen uitleveren.
                    $filteredArray = array_filter($stockArray, function ($item) {
                        return $item['dispatchable'] === true;
                    });

                    //sorteer de SFS-locaties zodat de locatie met de oudste 2e-hands voorraad voorrang krijgt.
                    uasort($filteredArray, function ($a, $b) {
                        return $a['oldestStockDate'] <=> $b['oldestStockDate'];
                    });

                    //de eerste key is de locatie die de order volledig kan uitleveren en het oudste 2e-hands item bevat.
                    $stockLocation = array_key_first($filteredArray);

                    if (array_key_exists($stockLocation, self::PARKING_SHIP_FROM_STORE)) {
                        $stock = true;
                        $parkingId = self::PARKING_SHIP_FROM_STORE[$stockLocation];
                    }
                }
            }

            if ($backorder === true && $stock === false) {
                $parkingId = self::PARKING_BACKORDER_PAID[$parkingId] ?? null;
            } elseif ($upstairs === true) {
                $parkingId = self::PARKING_UPSTAIRS[$parkingId] ?? $parkingId;
            }

            // Er is geen voorraad in het magazijn, kijken of er voorraad is op andere locaties, parkeren in 'Later sturen Urk'
            if ($backorder === true && $stock === false && $parkingId === Parking::BACKORDER_POSTNL_PAID) {
                // check of er voorraad is op andere locaties, check is simpel, welke winkel er gekozen word gebeurt in command
                // hier worden ook koerierslijsten aangemaakt om voorraad naar Urk te halen
                foreach ($cartItems as $cartItem) {
                    if ($cartItem->getStatus() & CartItem::FLAG_WAS_NOT_IN_STOCK) {
                        $stockInLocations = $cartItem->getProduct()->getStockInLocations();

                        foreach ($stockInLocations as $stockInLocation) {
                            if (
                                $stockInLocation->getStockLocation()->getShop() !== null &&
                                $stockInLocation->getStockLocation()->getId() !== StockLocation::WINKEL_URK &&
                                $stockInLocation->getStock() >= $cartItem->getAmount()
                            ) {
                                $parkingId = Parking::SEND_LATER_URK;
                                $extraInfo = ucfirst($stockInLocation->getStockLocation()->getShop()) . ' had voorraad';
                                break 2;
                            }
                        }
                    }
                }
            }
        }

        if (
            $order->getParking() instanceof Parking &&
            $order->getParking()->getId() === self::PARKING_PAYMENT_RETURN_COSTS &&
            $isPaid
        ) {
            $parkingId = self::PARKING_PAYMENT_RETURN_COSTS_PAID;
        }

        if ($overwriteParkingId !== null) {
            $parkingId = $overwriteParkingId;
        }

        if ($checkLostShipments) {
            $parkingId = Parking::CHECK_LOST_SHIPMENTS;
        }

        $parking = $this->parkingRepository->findOneBy(['id' => $parkingId]);

        if ($parking instanceof Parking) {
            // Staat de order in de wacht?
            $orderWait = $this->waitRepository->findOneBy(['order' => $order]);

            // Als de bestelling al in wacht staat moet de update daar plaatsvinden, tenzij de order
            // vandaag de deur uit moet
            $updateWait = false;

            if ($orderWait instanceof OrderWait) {
                $sendDate = $orderWait->getSendDate();

                if ($sendDate instanceof DateTime) {
                    $sendDate = Carbon::instance($sendDate);

                    $diff = $today->diffInDays($sendDate, false);

                    if ($diff > 0) {
                        $updateWait = true;
                    }
                }
            }

            if ($updateWait === true) {
                $this->addToOrderLog(
                    $order,
                    'Intentie om order te verplaatsen van bestelling wacht naar "' .
                    $parking->getDescription() . '".' .
                    ' Daadwerkelijke verplaatsing vindt pas plaats wanneer de order verwerkt moet worden.'
                );
                $orderWait->setParking($parking);
            } else {
                $order->setParking($parking);

                $this->entityManager->flush();
                $this->entityManager->refresh($order);
                //add logging if order is successfully parked
                if ($order->getParking() === $parking) {
                    $this->addToOrderLog(
                        $order,
                        'Order geparkeerd in "' . $parking->getDescription() . '" ' . $extraInfo
                    );
                } else {
                    $this->addToOrderLog(
                        $order,
                        'Er waren problemen met parkeren naar "' . $parking->getDescription() . '" neem contact op met de programmeurs.'
                    );
                }
            }
        } else {
            $this->addToOrderLog($order, 'Order niet geparkeerd omdat er geen parkeermap gevonden kon worden.' . PHP_EOL . $extraInfo);
        }

        $this->entityManager->flush();

        return true;
    }

    /**
     * @param OrderInfo $order
     * @param string $message
     * @return void
     */
    private function addToOrderLog(OrderInfo $order, string $message): void
    {
        $adminLog = new AdminLog();
        $adminLog
            ->setOrderId($order->getId())
            ->setType('bestelling')
            ->setText('(order parker) ' . $message)
            ->setCreator($this->systemUser);

        $this->entityManager->persist($adminLog);
    }
}
