<?php

declare(strict_types=1);

namespace CatBundle\Service\Mollie\Notification;

use CatB<PERSON>le\DTO\Mollie\NotificationDataObject;
use CatB<PERSON>le\Exception\Mollie\UnknownResourceException;
use CatB<PERSON>le\Service\Monolog\LogContextStore;
use CatB<PERSON>le\Service\PSP\LogManager;
use Doctrine\ORM\EntityManagerInterface;
use Mollie\Api\Exceptions\ApiException;
use Mollie\Api\Resources\BaseResource;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Lock\Lock;
use Symfony\Component\Lock\LockFactory;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Entity\PSP\Log;

abstract class AbstractHandler implements HandlerInterface
{
    private const MAX_HANDLE_ATTEMPTS = 5;

    /**
     * Access to the entitymanager is hidden to prevent subclasses from flushing during the transaction in
     * `AbstractHandler::tryHandle()`.
     */
    private readonly EntityManagerInterface $entityManager;
    protected readonly LoggerInterface $mollieLogger;
    protected readonly LogManager $logManager;
    protected readonly OrderInfoRepository $orderInfoRepository;
    protected readonly LockFactory $lockFactory;

    /** @var array<string, Lock> */
    private array $locks = [];
    private LogContextStore $logContextStore;

    public function __construct(ContainerInterface $locator) {
        $this->entityManager = $locator->get('doctrine.orm.entity_manager');
        $this->mollieLogger = $locator->get('monolog.logger.mollie');
        $this->logManager = $locator->get(LogManager::class);
        $this->orderInfoRepository = $this->entityManager->getRepository(OrderInfo::class);
        $this->lockFactory = $locator->get('lock.lock_factory');
        $this->logContextStore = $locator->get(LogContextStore::class);
    }

    public function supportsNotification(NotificationDataObject $notification): bool
    {
        return $notification->resource->value === $this->getResourceName();
    }

    public abstract function supportsResource(BaseResource $resource): bool;

    /**
     * Processes a Mollie resource based on the type of resource and its ID.
     * The actual resource is fetched by the handler class.
     *
     * @throws UnknownResourceException
     * @throws ApiException
     */
    public function handleNotification(NotificationDataObject $notification): bool
    {
        $this->logContextStore->addContext('mollie', [
            'handler' => get_class($this),
            'notificationType' => $notification->resource->value,
            'resourceId' => $notification->id,
        ]);

        if ($this->lock($notification->id) === false) {
            return false;
        }

        try {
            $resource = $this->fetchResource($notification->id);
        } catch (ApiException $exception) {
            $this->mollieLogger->error(
                sprintf(
                    'API error during handling of %s with id \'%s\': %s',
                    $this->getResourceName(),
                    $notification->id,
                    $exception->getPlainMessage(),
                )
            );

            if (!str_contains($exception->getMessage(), 'Invalid')
                && !str_contains($exception->getMessage(), 'Unknown')) {
                throw $exception;
            }

            return false;
        }

        $resourceHandled = $this->handleResource($resource);
        $this->unlock($notification->id);
        return $resourceHandled;
    }

    /**
     * Processes a complete Mollie resource object.
     *
     * @param BaseResource $resource
     * @return bool
     */
    public function handleResource(BaseResource $resource): bool
    {
        $this->logContextStore->addContext('mollie', [
            'handler' => get_class($this),
            'resourceId' => $resource->id,
        ]);

        if (property_exists($resource, 'status')) {
            $this->logContextStore->addContext('mollie', ['resourceStatus' => $resource->status]);
        }

        if ($this->lock($resource->id) === false) {
            return false;
        }

        $order = $this->getOrder($resource);

        if ($order === null) {
            $this->mollieLogger->warning(
                sprintf(
                    'Could not find order for %s with id \'%s\'',
                    $this->getResourceName(),
                    $resource->id,
                )
            );
            return false;
        }

        $this->logContextStore->addContext('mollie', ['orderId' => $order->getId()]);

        $data = (array) $resource;
        unset($data["\0*\0client"]); // Unset key of BaseResource::$client property

        $log = new Log(
            $order->getPaymentMethod(),
            $this->getResourceName(),
            $data,
            $resource->id,
            $this->getStatus($resource),
            $order,
            true,
        );

        $this->tryHandle($resource, $order, $log);

        $this->unlock($resource->id);
        return true;
    }

    private function tryHandle(BaseResource $resource, OrderInfo $order, Log $log): void {
        $this->entityManager->beginTransaction();

        $attempts = 0;
        while ($attempts < self::MAX_HANDLE_ATTEMPTS) {
            try {
                if ($this->logManager->persist($log) !== false) {
                    $this->handle($resource, $order, $log);
                    $this->entityManager->flush();
                    $this->entityManager->commit();
                } else {
                    $this->mollieLogger->notice(
                        sprintf(
                            'Skipped processing %s with id \'%s\'; last log matches current data',
                            $this->getResourceName(),
                            $resource->id,
                        )
                    );
                }

                return;
            } catch (\PDOException $e) {
                $reason = 'PDOException encountered';
                if (strpos($e->getMessage(), 'wsrep aborted transaction') !== false) {
                    $reason = 'wsrep aborted transaction';
                }

                $this->mollieLogger->warning(sprintf(
                    'Retrying processing of %s with id \'%s\'; %s',
                    $this->getResourceName(),
                    $resource->id,
                    $reason,
                ));
            }

            $attempts += 1;
        }

        $this->entityManager->rollback();
    }

    protected abstract function handle(
        BaseResource $resource,
        OrderInfo $order,
        Log $log,
    ): bool;

    public abstract function getResourceName(): string;

    /**
     * @throws ApiException
     * @throws UnknownResourceException
     */
    protected abstract function fetchResource(string $id): BaseResource;

    protected function getOrder(BaseResource $resource): ?OrderInfo
    {
        if (!isset($resource->metadata) || $resource->metadata === null) {
            return null;
        }

        $metadata = json_decode($resource->metadata, true);
        if ($metadata === null || !isset($metadata['orderId'])) {
            return null;
        }

        return $this->orderInfoRepository->find($metadata['orderId']);
    }

    protected abstract function getStatus(BaseResource $resource): ?string;

    /**
     * This function exists to allow classes inheriting from AbstractHandler to persist entities.
     */
    protected function persist(object $entity): void
    {
        $this->entityManager->persist($entity);
    }

    private function lock(string $id): bool
    {
        if (isset($this->locks[$id]) && $this->locks[$id] instanceof Lock) {
            if ($this->locks[$id]->isAcquired()) {
                return true;
            }
        } else {
            $lock = $this->lockFactory->createLock(
                $this->getResourceName() . '-' . $id,
                30.0,
            );
        }

        if ($lock->acquire() === false) {
            $this->mollieLogger->warning(
                sprintf(
                    'Could not acquire lock; skipping handling of %s notification with id \'%s\'',
                    $this->getResourceName(),
                    $id,
                )
            );

            return false;
        }

        $this->locks[$id] = $lock;
        return true;
    }

    private function unlock($id): void
    {
        if (isset($this->locks[$id]) && $this->locks[$id] instanceof Lock) {
            if ($this->locks[$id]->isAcquired() === true) {
                $this->locks[$id]->release();
            }
            unset($this->locks[$id]);
        }
    }
}
