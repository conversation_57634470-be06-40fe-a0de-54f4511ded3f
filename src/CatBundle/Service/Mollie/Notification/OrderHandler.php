<?php

declare(strict_types=1);

namespace CatBundle\Service\Mollie\Notification;

use CatB<PERSON>le\Exception\Mollie\InvalidMollieProfileException;
use CatBundle\Exception\Mollie\UnknownResourceException;
use CatBundle\Service\Mollie\ClientFactory;
use Mollie\Api\Exceptions\ApiException;
use Mollie\Api\Resources\BaseResource;
use Mollie\Api\Resources\Order as MollieOrder;
use Psr\Container\ContainerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Webdsign\GlobalBundle\Entity\Event\RegistrationRepository;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\PSP\Log;

class OrderHandler extends AbstractHandler
{
    public function __construct(
        #[Autowire('@cat.mollie.service_locator.base_handler')] ContainerInterface $locator,
        private readonly ClientFactory $mollieClientFactory,
        private readonly PaymentHandler $paymentHandler,
        private readonly RegistrationRepository $registrationRepository,
    ) {
        parent::__construct($locator);
    }

    public function supportsResource(BaseResource $resource): bool
    {
        return $resource instanceof MollieOrder;
    }

    protected function handle(
        BaseResource $resource,
        OrderInfo $order,
        Log $log,
    ): bool {
        if (!$resource instanceof MollieOrder) {
            return false;
        }

        if ($order->getPspReference() !== $resource->id) {
            $order->setPspReference($resource->id);
        }

        if ($order->isEventOrder()) {
            foreach ($this->registrationRepository->findBy(['order' => $order->getId()]) as $registration) {
                $registration->setPspReference($resource->id);
            }
        }

        $payments = $resource->payments();
        if ($payments !== null) {
            foreach ($payments as $payment) {
                if (!$this->paymentHandler->handle($payment, $order, $log)) {
                    return false;
                }
            }
        }

        return true;
    }

    public function getResourceName(): string
    {
        return 'order';
    }

    /**
     * @throws ApiException
     * @throws InvalidMollieProfileException
     * @throws UnknownResourceException
     */
    protected function fetchResource(string $id): MollieOrder
    {
        $client = $this->mollieClientFactory->getClientForResourceId($id);
        return $client->orders->get($id, ['embed' => 'payments']);
    }

    protected function getStatus(BaseResource $resource): ?string
    {
        if (!$resource instanceof MollieOrder) {
            return null;
        }

        return $resource->status;
    }
}
