<?php

declare(strict_types=1);

namespace CatBundle\Service\Mollie\Notification;

use CatBundle\Exception\Mollie\InvalidMollieProfileException;
use CatBundle\Exception\Mollie\UnknownResourceException;
use CatBundle\Service\Mollie\ClientFactory;
use CatBundle\Service\OrderParker;
use DateTime;
use Mollie\Api\Exceptions\ApiException;
use Mollie\Api\Resources\BaseResource;
use Mollie\Api\Resources\Payment as MolliePayment;
use Psr\Container\ContainerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Webdsign\GlobalBundle\Entity\AdminLog;
use Webdsign\GlobalBundle\Entity\Event\RegistrationRepository;
use Webdsign\GlobalBundle\Entity\ExternalPayment;
use Webdsign\GlobalBundle\Entity\ExternalPaymentRepository;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\OrderPayments;
use Webdsign\GlobalBundle\Entity\OrderPaymentsRepository;
use Webdsign\GlobalBundle\Entity\Parking;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\PaymentType;
use Webdsign\GlobalBundle\Entity\PaymentTypeRepository;
use Webdsign\GlobalBundle\Entity\PSP\Log;
use Webdsign\GlobalBundle\Entity\PSP\LogRepository;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\UserRepository;
use Webdsign\GlobalBundle\Exception\InvalidPaymentTypeException;

class PaymentHandler extends AbstractHandler
{
    public const PAYMENT_CONFIRM_STATES = ['paid', 'authorized'];
    public const REFUND_CONFIRM_STATES = ['refunded', 'pending', 'processing'];
    private User $systemUser;

    public function __construct(
        #[Autowire('@cat.mollie.service_locator.base_handler')] ContainerInterface $locator,
        private readonly ClientFactory $mollieClientFactory,
        private readonly RefundHandler $refundHandler,
        private readonly LogRepository $logRepository,
        private readonly ExternalPaymentRepository $externalPaymentRepository,
        private readonly OrderPaymentsRepository $orderPaymentsRepository,
        private readonly PaymentTypeRepository $paymentTypeRepository,
        private readonly OrderParker $orderParker,
        UserRepository $userRepository,
        private readonly RegistrationRepository $registrationRepository,
    ) {
        parent::__construct($locator);
        $this->systemUser = $userRepository->findSystemUser();
    }

    public function supportsResource(BaseResource $resource): bool
    {
        return $resource instanceof MolliePayment;
    }

    protected function handle(
        BaseResource $resource,
        OrderInfo $order,
        Log $log,
    ): bool {
        if (!$resource instanceof MolliePayment) {
            return false;
        }

        $amount = (float) $resource->amount->value;
        $amountEx = (float) ($resource->amount->value / (1 + ($order->getVatPercentage() / 100)));
        $now = new DateTime();

        $this->writeExternalPayment($resource, $order, $amount, $amountEx, $now, $log);

        // On authorized or paid, check for orderpayment row and write if not present
        if (in_array($resource->status, PaymentHandler::PAYMENT_CONFIRM_STATES)) {
            $payment = $this->writeOrderPayment($resource, $order, $log, $amount, $amountEx, $now);

            if ($resource->hasRefunds()) {
                $this->writeRefundPayments($resource, $amount, $order, $payment, $log, $now);
            }

            if ($order->isEventOrder()) {
                foreach ($this->registrationRepository->findBy(['order' => $order->getId()]) as $registration) {
                    $registration->setPspReference($resource->id);
                }
            }
        }

        return true;
    }

    public function getResourceName(): string
    {
        return 'payment';
    }

    /**
     * @throws ApiException
     * @throws InvalidMollieProfileException
     * @throws UnknownResourceException
     */
    protected function fetchResource(string $id): MolliePayment
    {
        $client = $this->mollieClientFactory->getClientForResourceId($id);
        $embed = ['refunds'];

        return $client->payments->get($id, [
            'embed' => implode(',', $embed),
        ]);
    }

    protected function getStatus(BaseResource $resource): ?string
    {
        if (!$resource instanceof MolliePayment) {
            return null;
        }

        return $resource->status;
    }

    private function writeExternalPayment(
        MolliePayment $resource,
        OrderInfo $order,
        float $amount,
        float $amountEx,
        DateTime $now,
        Log $log,
    ): void {
        $externalPayment = $this->externalPaymentRepository->findOneBy([
            'token' => $resource->id
        ]);
        if ($externalPayment === null) {
            $externalPayment = new ExternalPayment();
            $externalPayment->setOrder($order);
            $externalPayment->setPtype1(ExternalPayment::PTYPE_MOLLIE);
            $externalPayment->setToken($resource->id);
            $externalPayment->setAmount($amount);
            $externalPayment->setAmountEx($amountEx);
            $externalPayment->setFlags(0);
            $this->persist($externalPayment);
        }

        $externalPayment->setDate($now);
        $externalPayment->setLog(json_encode($log->getData()));
        $externalPayment->setStatus($this->getStatus($resource));

        if (in_array($resource->status, PaymentHandler::PAYMENT_CONFIRM_STATES)) {
            $externalPayment->setFlags($externalPayment->getFlags() | ExternalPayment::FLAG_SUCCESS);
        }
    }

    /**
     * @throws InvalidPaymentTypeException
     */
    public function writeOrderPayment(
        MolliePayment $resource,
        OrderInfo $order,
        Log $log,
        float $amount,
        float $amountEx,
        DateTime $now,
    ): ?OrderPayments {
        $payment = $this->orderPaymentsRepository->findOneBy([
            'pspReference' => $resource->id
        ]);
        if ($payment === null) {
            $paymentType = $this->paymentTypeRepository->find(PaymentType::TYPE_MOLLIE);
            if ($paymentType === null) {
                throw new InvalidPaymentTypeException();
            }

            $payment = new OrderPayments();
            $payment->setOrder($order);
            $payment->setPaymentType($paymentType);
            $payment->setPspLog($log);
            $payment->setPspType($resource->method);
            $payment->setAmount($amount);
            $payment->setAmountEx($amountEx);
            $payment->setPspReference($resource->id);
            $payment->setDate($now);
            $payment->setPaymentDate($now);
            $payment->setRefunded(false);

            $order->setPayed();

            $this->persist($payment);

            // Park in payment problems if total amount paid is not equal to order value
            // Otherwise, park according to OrderParker logic
            $totalValue = $order->getTotalValue(includeDiscountCosts: true, checkVatFree: true, calculateDiscount: true);
            $diff = abs(round($totalValue - $amount, 2));
            $overWriteParkingId = $amount > 0 && !($diff <= 0.01) ? Parking::ADYEN_PAYMENT_PROBLEMS : null;

            $this->orderParker->parkOrder($order, $overWriteParkingId);
        }

        return $payment;
    }

    /**
     * @throws ApiException
     */
    public function writeRefundPayments(
        MolliePayment $paymentResource,
        float $amount,
        OrderInfo $order,
        ?OrderPayments $payment,
        Log $log,
        DateTime $now,
    ): void {
        $refundLogs = $this->logRepository->getRefundLogsForPayment($paymentResource->id, PaymentMethod::ID_MOLLIE);
        $logMap = [];
        foreach ($refundLogs as $refundLog) {
            $logMap[$refundLog->getData()['id']] = $refundLog;
        }

        $embeddedRefunds = [];
        if (isset($paymentResource->_embedded->refunds)) {
            foreach ($paymentResource->_embedded->refunds as $embeddedRefund) {
                $embeddedRefunds[$embeddedRefund->id] = $embeddedRefund;
            }
        }

        foreach ($paymentResource->refunds() as $refund) {
            // Fix refund status with embedded refunds, since these seem to be more up to date
            if (array_key_exists($refund->id, $embeddedRefunds)) {
                $refund->status = $embeddedRefunds[$refund->id]->status;
            }

            // Create logs for refunds added in Mollie dashboard
            if (array_key_exists($refund->id, $refundLogs) === false) {
                $this->refundHandler->handleResource($refund);
                $logMap[$refund->id] = array_filter(
                    $this->logRepository->getRefundLogsForPayment($refund->paymentId, PaymentMethod::ID_MOLLIE),
                    fn(Log $log) => $log->getData()['id']
                )[0];
            }

            if (in_array($refund->status, PaymentHandler::REFUND_CONFIRM_STATES)) {
                // Check for existing refund
                $orderPayment = $this->orderPaymentsRepository->findOneBy(['pspRefundReference' => $refund->id]);
                if (!$orderPayment instanceof OrderPayments) {
                    // No existing refund, create OrderPayment
                    $refundAmount = (float)$refund->amount->value;
                    $refundAmountEx = $amount / (1 + ($order->getVatPercentage() / 100));

                    $refundPayment = new OrderPayments();
                    $refundPayment->setOrder($order);
                    $refundPayment->setPaymentType($payment->getPaymentType());
                    $refundPayment->setPspLog($log);
                    $refundPayment->setPspType($paymentResource->method);
                    $refundPayment->setAmount(-$refundAmount);
                    $refundPayment->setAmountEx(-$refundAmountEx);
                    $refundPayment->setPspReference($paymentResource->id);
                    $refundPayment->setPspRefundReference($refund->id);
                    $refundPayment->setDate($now);
                    $refundPayment->setPaymentDate($now);
                    $refundPayment->setRefunded(false);

                    $this->persist($refundPayment);

                    $refundLog = $logMap[$refund->id];
                    $refundLog->setProcessed(true);
                    $refundLog->setStatus($refund->status);

                    $this->addToOrderLog(
                        $order,
                        sprintf(
                            'Refund van %s  succesvol verwerkt.',
                            number_format($refundAmount, 2, ',', '.')
                        ),
                        'refund'
                    );
                }
            }
        }

        $payment->setRefunded((float) $paymentResource->amountRefunded->value >= (float) $paymentResource->amount->value);
    }

    private function addToOrderLog(OrderInfo $order, string $message, string $type = 'payment'): void
    {
        $adminLog = new AdminLog();
        $adminLog
            ->setOrderId($order->getId())
            ->setType('bestelling')
            ->setText('(Mollie '. $type . ') ' . $message)
            ->setCreator($this->systemUser);

        $this->persist($adminLog);
    }
}
