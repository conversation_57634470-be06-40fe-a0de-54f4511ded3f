<?php

declare(strict_types=1);

namespace CatBundle\Service\SecondHand;

use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Webdsign\GlobalBundle\Entity\Domain;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\ProductPrice;
use Webdsign\GlobalBundle\Entity\ProductPriceLog;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandGenericFormula;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandGenericFormulaRepository;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandFormula;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandFormulaRepository;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandPrice;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandPriceUpdate;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandStateRepository;
use Webdsign\GlobalBundle\Entity\User;

class SecondHandPriceManager
{
    private EntityManagerInterface $entityManager;
    private SecondHandStateRepository $secondHandStateRepository;
    private ProductRepository $productRepository;
    private SecondHandFormulaRepository $secondHandFormulaRepository;
    private SecondHandGenericFormulaRepository $genericFormulaRepository;
    private TokenStorageInterface $tokenStorage;
    private User $systemUser;

    public function __construct(
        EntityManagerInterface $entityManager,
        SecondHandStateRepository $secondHandStateRepository,
        ProductRepository $productRepository,
        SecondHandFormulaRepository $secondHandFormulaRepository,
        SecondHandGenericFormulaRepository $genericFormulaRepository,
        TokenStorageInterface $tokenStorage,
        User $systemUser
    ) {
        $this->entityManager = $entityManager;
        $this->secondHandStateRepository = $secondHandStateRepository;
        $this->productRepository = $productRepository;
        $this->secondHandFormulaRepository = $secondHandFormulaRepository;
        $this->genericFormulaRepository = $genericFormulaRepository;
        $this->tokenStorage = $tokenStorage;
        $this->systemUser = $systemUser;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setBasePriceForProduct(Product $baseProduct, float $price, bool $flush = true): array
    {
        $currentDateTime = new DateTime();
        $basePrice = $baseProduct->getSecondHandPrice(true);
        $data = [];

        if (!$basePrice instanceof SecondHandPrice) {
            $basePrice = new SecondHandPrice();
            $basePrice
                ->setProduct($baseProduct)
                ->setIntakePrice($price)
                ->setSalesPrice($price)
                ->setDateCreated($currentDateTime)
                ->setIsBasePrice(true)
                ->setDateChanged($currentDateTime);

            $this->entityManager->persist($basePrice);

            $this->createFormulasForBasePrice($basePrice);
            //Refresh zodat formules vanaf de nieuwe prijs benaderbaar zijn in doctrine
            $this->entityManager->refresh($basePrice);
        }

        $basePrice->setIntakePrice($price);
        $basePrice->setSalesPrice($price);

        $this->logIntakePriceUpdate($basePrice->getProduct(), $basePrice->getIntakePrice(), $currentDateTime, 'Base prijs update');

        // voor elke formula het product ophalen en prijs (her)berekenen
        $formulas = $basePrice->getFormulas();

        foreach ($formulas as $formula) {
            $product = $this->productRepository->findSecondHandProductByParentAndState($baseProduct, $formula->getState());

            if (!$product instanceof Product) {
                continue;
            }

            $secondHandPrice = $product->getSecondHandPrice();
            if (!$secondHandPrice instanceof SecondHandPrice) {
                $secondHandPrice = new SecondHandPrice();
                $secondHandPrice
                    ->setProduct($product)
                    ->setIsBasePrice(false)
                    ->setDateCreated($currentDateTime);

                $this->entityManager->persist($secondHandPrice);
            }

            $intakePriceCalculated = $this->calculatePrice($formula->getIntakeFormula(), $price);
            $salesPriceCalculated = $this->calculatePrice($formula->getSalesFormula(), $intakePriceCalculated, 'sales');

            $this->saveSalesPriceForProduct($product, $salesPriceCalculated);

            $secondHandPrice
                ->setIntakePrice($intakePriceCalculated)
                ->setSalesPrice($salesPriceCalculated)
                ->setDateChanged($currentDateTime);

            $this->logIntakePriceUpdate($secondHandPrice->getProduct(), $secondHandPrice->getIntakePrice(), $currentDateTime, 'Base prijs update');

            $data[$product->getId()]['intakePrice'] = $intakePriceCalculated;
            $data[$product->getId()]['salesPrice'] = $salesPriceCalculated;

        }

        $basePrice->setDateChanged($currentDateTime);

        if ($flush === true) {
            $this->entityManager->flush();
        }

        return $data;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function createFormulasForBasePrice(SecondHandPrice $basePrice): void
    {
        $states = $this->secondHandStateRepository->findBy([], ['quality' => 'DESC']);

        $start = 0;
        $salesStart = 150;
        foreach ($states as $state) {
            $genericFormula = $state->getGenericFormula();
            $formula = new SecondHandFormula();
            $formula
                ->setPrice($basePrice)
                ->setProductState($state)
                ->setIntakeFormula($start)
                ->setSalesFormula($salesStart);
            $start += 10;
            $salesStart += 10;

            if ($genericFormula instanceof SecondHandGenericFormula) {
                $formula
                    ->setIntakeFormula($genericFormula->getIntakeFormula())
                    ->setSalesFormula($genericFormula->getSalesFormula())
                    ->setIsGeneric(true);
            }

            $this->entityManager->persist($formula);
        }

        $this->entityManager->flush();
    }

    public function calculatePrice(int $formula, float $price, string $type = 'intake'): float
    {
        $percentage = 100 - $formula;

        if ($percentage === 0 || $price === 0.) {
            return 0.;
        }

        if ($percentage === 100) {
            return $price;
        }

        switch ($type) {
            case 'sales':
                $percentage = $formula / 100;
                return round($price * $percentage);
            case 'intake':
            default:
                return $price / (100 / $percentage);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function recalculateGenericIntakePrices(): void
    {
        $genericFormulas = $this->genericFormulaRepository->findAll();
        $counter = 0;

        foreach ($genericFormulas as $genericFormula) {
            $formulas = $this->secondHandFormulaRepository->findGenericFormulaByState($genericFormula->getState());
            foreach ($formulas as $formula) {
                if (
                    $formula->getSalesFormula() !== $genericFormula->getSalesFormula() ||
                    $formula->getIntakeFormula() !== $genericFormula->getIntakeFormula()
                ) {
                    $formula->setIntakeFormula($genericFormula->getIntakeFormula());
                    $formula->setSalesFormula($genericFormula->getSalesFormula());

                    $price = $formula->getPrice();
                    if ($price->isBasePrice()) {
                        $counter++;
                        $this->setBasePriceForProduct($price->getProduct(), $price->getIntakePrice(), false);
                    }
                }

                if (($counter % 50) === 0) {
                    $this->entityManager->flush();
                }
            }
        }

        $this->entityManager->flush();
    }

    /**
     * @throws ORMException
     */
    public function saveSalesPriceForProduct(Product $product, float $salesPriceCalculated): void
    {
        $currentPrices = $product->getPrices();
        $currentPrice = $currentPrices->filter(
            fn (ProductPrice $price) =>
                $price->getDomainId() === Domain::CAMERANU &&
                strtolower($price->getCountry()) === 'nl'
        )->current();

        $oldSalesPricePrice = null;
        if (!$currentPrice instanceof ProductPrice) {
            $currentPrice = new ProductPrice();
            $currentPrice->setProduct($product);
            $currentPrice->setDomainId(Domain::CAMERANU);

            $this->entityManager->persist($currentPrice);

            $currentPrices->add($currentPrice);
        } else {
            $oldSalesPricePrice = $currentPrice->getPrice();
        }

        $currentPrice->setCountry('NL');
        $currentPrice->setPrice($salesPriceCalculated);

        if ($oldSalesPricePrice === null || (float) $oldSalesPricePrice !== $salesPriceCalculated) {
            $token = $this->tokenStorage->getToken();
            $user = $this->systemUser;

            if ($token) {
                $user = $token->getUser();
            }

            $productPriceLog = new ProductPriceLog();
            $productPriceLog
                ->setDomainId($currentPrice->getDomainId())
                ->setInsertedBy($user)
                ->setProduct($product)
                ->setNewPrice($currentPrice->getPrice())
                ->setNewPriceEx($currentPrice->getPriceEx())
                ->setCountry($currentPrice->getCountry())
                ->setInsertedTime(new DateTime())
                ->setLogMessage('Aangepast via tweedehands tool');

            $this->entityManager->persist($productPriceLog);
        }
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function setBestMarketIntakePriceForProduct(Product $product, float $bestMarketPrice): void
    {
        //marketPrices are based on state 8 so we first need to calculate back to state 10
        $basePrice = $product->getSecondHandPrice(true);
        $currentDateTime = new DateTime();

        if (!$basePrice instanceof SecondHandPrice) {
            $basePrice = new SecondHandPrice();
            $basePrice
                ->setProduct($product)
                ->setIntakePrice($bestMarketPrice)
                ->setSalesPrice($bestMarketPrice)
                ->setDateCreated($currentDateTime)
                ->setIsBasePrice(true)
                ->setDateChanged($currentDateTime);

            $this->logIntakePriceUpdate($basePrice->getProduct(), $basePrice->getIntakePrice(), $currentDateTime, 'Beste marktprijs update');

            $this->entityManager->persist($basePrice);

            $this->createFormulasForBasePrice($basePrice);
            //Refresh zodat formules vanaf de nieuwe prijs benaderbaar zijn in doctrine
            $this->entityManager->refresh($basePrice);
        }

        $formulas = $basePrice->getFormulas();

        $usedStateFormula = $formulas->filter(fn (SecondHandFormula $formula) => $formula->getState()->getQuality() === SecondHandStockFactorCalculator::USE_QUALITY)->current();
        $percent = $usedStateFormula->getIntakeFormula();
        $newBasePrice = round($bestMarketPrice / (1 - $percent / 100));
        $basePrice->setIntakePrice($newBasePrice);
        $basePrice->setSalesPrice($newBasePrice);

        $this->logIntakePriceUpdate($basePrice->getProduct(), $basePrice->getIntakePrice(), $currentDateTime, 'Beste marktprijs update');

        foreach ($formulas as $formula) {
            $stateProduct = $this->productRepository->findSecondHandProductByParentAndState(
                $product,
                $formula->getState()
            );

            $secondHandPrice = $stateProduct->getSecondHandPrice();
            if (!$secondHandPrice instanceof SecondHandPrice) {
                $secondHandPrice = new SecondHandPrice();
                $secondHandPrice
                    ->setProduct($product)
                    ->setIsBasePrice(false)
                    ->setDateCreated($currentDateTime);

                $this->entityManager->persist($secondHandPrice);
            }

            $newIntakePrice = round($this->calculatePrice($formula->getIntakeFormula(), $newBasePrice));
            $newSalesPriceFormula = $formula->getSalesFormula();
            //Recalculate a new sales percentage if needed
            if ($secondHandPrice->getSalesPrice() > 0) {
                $newSalesPriceFormula = (int)round(($secondHandPrice->getSalesPrice() / $newIntakePrice) * 100);
            }

            //if the percentage between intake and sales price changes we update the sales formula
            if ($newSalesPriceFormula !== $formula->getSalesFormula()) {
                $formula->setSalesFormula($newSalesPriceFormula);
            }

            //intake price is 0 so it's created based on the generic formula, so we need to calculate the new sales price
            $newSalesPrice = $secondHandPrice->getSalesPrice();
            if ($secondHandPrice->getSalesPrice() === 0.) {
                $newSalesPrice = $this->calculatePrice($newSalesPriceFormula, $newIntakePrice, 'sales');
                $this->saveSalesPriceForProduct($product, $newSalesPrice);
            }

            $secondHandPrice
                ->setIntakePrice($newIntakePrice)
                ->setSalesPrice($newSalesPrice)
                ->setDateChanged($currentDateTime);

            $this->logIntakePriceUpdate($secondHandPrice->getProduct(), $secondHandPrice->getIntakePrice(), $currentDateTime, 'Beste marktprijs update');
        }

        $basePrice->setDateChanged($currentDateTime);
        $this->entityManager->flush();
    }

    public function logIntakePriceUpdate(
        Product $product,
        float $newIntakePrice,
        DateTime $dateChanged,
        string $description
    ): void {
        $token = $this->tokenStorage->getToken();
        $user = $this->systemUser;

        if ($token) {
            $user = $token->getUser();
        }

        $secondHandPriceUpdate = new SecondHandPriceUpdate();
        $secondHandPriceUpdate->setUser($user);
        $secondHandPriceUpdate->setProduct($product);
        $secondHandPriceUpdate->setPrice($newIntakePrice);
        $secondHandPriceUpdate->setDateChanged($dateChanged);
        $secondHandPriceUpdate->setDescription($description);

        $this->entityManager->persist($secondHandPriceUpdate);
    }
}
