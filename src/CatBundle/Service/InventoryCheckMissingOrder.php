<?php

declare(strict_types=1);

namespace CatBundle\Service;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use Exception;
use Webdsign\GlobalBundle\DTO\InventoryCheckDataObject;
use Webdsign\GlobalBundle\Entity\CustomerRepository;
use Webdsign\GlobalBundle\Entity\OrderHistory;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\Origin;
use Webdsign\GlobalBundle\Entity\OriginRepository;
use Webdsign\GlobalBundle\Entity\Parking;
use Webdsign\GlobalBundle\Entity\ParkingRepository;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\PaymentMethodRepository;
use Webdsign\GlobalBundle\Entity\CartItem;
use Webdsign\GlobalBundle\Entity\Customer;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\PurchaseOrderRepository;
use Webdsign\GlobalBundle\Entity\ShipmentMethod;
use Webdsign\GlobalBundle\Entity\ShipmentMethodRepository;
use Webdsign\GlobalBundle\Entity\Stock;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Entity\Supplier;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\UserRepository;

class InventoryCheckMissingOrder
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;
    /**
     * @var CustomerRepository
     */
    private $customerRepository;
    /**
     * @var ShipmentMethodRepository
     */
    private $shipmentMethodRepository;
    /**
     * @var PaymentMethodRepository
     */
    private $paymentMethodRepository;
    /**
     * @var UserRepository
     */
    private $userRepository;
    /**
     * @var OriginRepository
     */
    private $originRepository;
    /**
     * @var ParkingRepository
     */
    private $parkingRepository;
    /**
     * @var ProductRepository
     */
    private $productRepository;
    /**
     * @var StockLocationRepository
     */
    private $stockLocationRepository;
    /**
     * @var PurchaseOrderRepository
     */
    private $purchaseOrderRepository;

    /**
     * @param EntityManagerInterface $entityManager
     * @param CustomerRepository $customerRepository
     * @param ShipmentMethodRepository $shipmentMethodRepository
     * @param PaymentMethodRepository $paymentMethodRepository
     * @param UserRepository $userRepository
     * @param OriginRepository $originRepository
     * @param ParkingRepository $parkingRepository
     * @param ProductRepository $productRepository
     * @param StockLocationRepository $stockLocationRepository
     */
    public function __construct(
        EntityManagerInterface $entityManager,
        CustomerRepository $customerRepository,
        ShipmentMethodRepository $shipmentMethodRepository,
        PaymentMethodRepository $paymentMethodRepository,
        UserRepository $userRepository,
        OriginRepository $originRepository,
        ParkingRepository $parkingRepository,
        ProductRepository $productRepository,
        StockLocationRepository $stockLocationRepository
    ) {
        $this->entityManager = $entityManager;
        $this->customerRepository = $customerRepository;
        $this->shipmentMethodRepository = $shipmentMethodRepository;
        $this->paymentMethodRepository = $paymentMethodRepository;
        $this->userRepository = $userRepository;
        $this->originRepository = $originRepository;
        $this->parkingRepository = $parkingRepository;
        $this->productRepository = $productRepository;
        $this->stockLocationRepository = $stockLocationRepository;
    }

    /**
     * Zoekbon voor missende producten maken
     *
     * @param ArrayCollection $missingStock
     * @param Supplier $supplier
     * @param StockLocation $stockLocation
     * @return OrderInfo
     * @throws ORMException
     */
    public function createOrder(ArrayCollection $missingStock, Supplier $supplier, StockLocation $stockLocation): OrderInfo
    {
        /**
         * Klantnummer Meindert
         *
         * @var Customer $customer
         */
        $customer = $this->customerRepository->findOneById(7902715);
        /**
         * Contant
         *
         * @var ShipmentMethod $shippingMethod
         */
        $shippingMethod = $this->shipmentMethodRepository->findOneById(5);
        /**
         * Contant
         *
         * @var PaymentMethod $paymentMethod
         */
        $paymentMethod = $this->paymentMethodRepository->findOneById(3);
        /**
         * @var User $user
         */
        $user = $this->userRepository->findOneBy(['id' => User::SYSTEM_USER_ADMIN]);
        /**
         * CameraNU Apeldoorn
         *
         * @var Origin $origin
         */
        $origin = $stockLocation->getOrigin();
        /**
         * Eigen orders
         *
         * @var Parking $parking
         */
        $parking = $this->parkingRepository->findOneById(6);

        $orderDate = new DateTime();

        $order = (new OrderInfo())
            ->setCustomer($customer)
            ->setTaxNumber('')
            ->setSex('')
            ->setFirstName('Zoekbon')
            ->setLastName($stockLocation->getDescription())
            ->setNameInsertion('')
            ->setName('')
            ->setCountry('NL')
            ->setCity('')
            ->setAddress('')
            ->setAddress2('')
            ->setHousenr('')
            ->setHousenrext('')
            ->setZipcode('')
            ->setZipcode2('')
            ->setCompany('')
            ->setPhonenr('')
            ->setMobile('')
            ->setEmail('<EMAIL>')
            ->setDeliveryFirstName('')
            ->setDeliveryLastName('')
            ->setDeliveryNameInsertion('')
            ->setDeliveryName('')
            ->setDeliveryCountry('')
            ->setDeliveryCity('')
            ->setDeliveryAddress('')
            ->setDeliveryAddress2('')
            ->setDeliveryHousenr('')
            ->setDeliveryHousenrext('')
            ->setDeliveryZipcode('')
            ->setDeliveryZipcode2('')
            ->setDeliveryCompany('')
            ->setDeliveryPhonenr('')
            ->setOurComment('Zoekbon ' . $stockLocation->getDescription())
            ->setShippingMethod($shippingMethod)
            ->setPaymentMethod($paymentMethod)
            ->setOrderDate($orderDate->format('Y-m-d'))
            ->setOrderTime($orderDate->format('H:i:s'))
            ->setDateInvoice($orderDate)
            ->setDateHandled($orderDate)
            ->setLatlong('')
            ->setFlags(OrderInfo::FLAG_WAITING_FOR_PAYMENT)
            ->setLanguage('nl')
            ->setHandledBy($user)
            ->setOrigin($origin)
            ->setParking($parking)
            ->setCourierTime(new DateTime('00:00'))
            ->setPreventAutomaticCancel(true)
            ->setIssuerId('')
            ->setVatPercentage('21')
            ->setReference('')
            ->setIpNumber('');

        $rfmSegment = $customer->getCustomerRFMScore()?->getSegment();
        $order->setRfmSegment($rfmSegment ?? 'Unknown');

        // Only set the payment period if the payment method is bank transfer
        if ($paymentMethod->getId() === PaymentMethod::BANKTRANSFER) {
            $order->setPaymentPeriod($customer->getPaymentPeriod());
        }

        $this->entityManager->persist($order);

        /**
         * Create Order History
         */
        $orderHistory = new OrderHistory();
        $orderHistory
            ->setOrder($order)
            ->setUser($user)
            ->setUserTeam($user->getUserTeam());
        $this->entityManager->persist($orderHistory);

        $this->entityManager->flush();

        $parent = $this->entityManager->getReference(CartItem::class, 0);

        /**
         * @var InventoryCheckDataObject $item
         */
        foreach ($missingStock as $productId => $item) {
            $product = $this->productRepository->findOneBy(['id' => $productId]);
            $amount = $item->getAmount();

            // Links, shopcartregels aanmaken
            if ($product instanceof Product) {
                $cartItem = (new CartItem())
                    ->setSessionId($order->getOrdernr())
                    ->setOrder($order)
                    ->setProduct($product)
                    ->setParent($parent)
                    ->setAmount($amount)
                    ->setPrice($item->getStockValue())
                    ->setPriceEx($item->getStockValue())
                    ->setPriceDiscount(0)
                    ->setPriceRemovalcost(0)
                    ->setTax(0)
                    ->setStatus(1)
                    ->setIpnr('0.0.0.0')
                    ->setFlags(0)
                    ->setUser($user);

                $this->entityManager->persist($cartItem);

                // Rechts, voorraadregels aanmaken
                for ($i = 0; $i < $amount; $i++) {
                    $stock = (new Stock())
                        ->setProduct($product)
                        ->setDateIn(new DateTime())
                        ->setTimestampIn(new DateTime())
                        ->setInvoiceSupplier('')
                        ->setOrdernumber($order->getOrdernr())
                        ->setBarcode('')
                        ->setOrder($order)
                        ->setSalesPrice($item->getStockValue())
                        ->setSalesPriceEx($item->getStockValue())
                        ->setPurchasePrice($item->getStockValue())
                        ->setDiscount(0)
                        ->setRemovalContribution(0)
                        ->setStockValue($item->getStockValue())
                        ->setVat(0)
                        ->setSupplier($supplier)
                        ->setRetourParent(null)
                        ->setInfo('')
                        ->setInsertedBy($user)
                        ->setLocation($stockLocation)
                        ->setFlags(1);

                    $this->entityManager->persist($stock);
                }
            }
        }

        $this->entityManager->flush();

        return $order;
    }
}
