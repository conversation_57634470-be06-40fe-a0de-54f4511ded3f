<?php

declare(strict_types=1);

namespace CatBundle\Service;

use CatBundle\DTO\StockScanList\IndexItemDataObject;
use CatBundle\Enum\StockScanListLogAction;
use CatBundle\Enum\StockScanListStatus;
use CatBundle\Exception\StockScanList\InvalidLocationInputException;
use CatBundle\Exception\StockScanList\NoLocationFoundException;
use CatBundle\Exception\StockScanList\NoProductsFoundException;
use CatBundle\Form\DataObject\StockScanList\Index\FilterDataObject;
use CatBundle\Form\DataObject\StockScanList\StockScanListCreateData;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Exception;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;
use Doctrine\ORM\NonUniqueResultException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Webdsign\GlobalBundle\DTO\StockLog\StockLogDataObject;
use Webdsign\GlobalBundle\Entity\LocationCompartment;
use Webdsign\GlobalBundle\Entity\LocationCompartmentNode;
use Webdsign\GlobalBundle\Entity\LocationCompartmentNodeRepository;
use Webdsign\GlobalBundle\Entity\Maingroup;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\PurchasePriceCalculated;
use Webdsign\GlobalBundle\Entity\Rootgroup;
use Webdsign\GlobalBundle\Entity\Stock;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Entity\StockLogAction;
use Webdsign\GlobalBundle\Entity\StockLogActionRepository;
use Webdsign\GlobalBundle\Entity\StockLogN;
use Webdsign\GlobalBundle\Entity\StockRepository;
use Webdsign\GlobalBundle\Entity\StockScanList;
use Webdsign\GlobalBundle\Entity\StockScanListLog;
use Webdsign\GlobalBundle\Entity\StockScanListLogRepository;
use Webdsign\GlobalBundle\Entity\StockScanListProduct;
use Webdsign\GlobalBundle\Entity\StockScanListRepository;
use Webdsign\GlobalBundle\Entity\Subgroup;
use Webdsign\GlobalBundle\Entity\Supplier;
use Webdsign\GlobalBundle\Entity\SupplierRepository;
use Webdsign\GlobalBundle\Entity\TransitCartRepository;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\UserRepository;

readonly class StockScanListManager
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private TokenStorageInterface $tokenStorage,
        private LocationCompartmentNodeRepository $locationCompartmentNodeRepository,
        private StockScanListRepository $stockScanListRepository,
        private PaginatorInterface $paginator,
        private StockRepository $stockRepository,
        private StockLogActionRepository $stockLogActionRepository,
        private StockScanListLogRepository $stockScanListLogRepository,
        private SupplierRepository $supplierRepository,
        private StockLocationRepository $stockLocationRepository,
        private TransitCartRepository $transitCartRepository,
        private UserRepository $userRepository
    ) {
    }

    /**
     * @return array<StockScanList>
     */
    public function findAll(): array
    {
        return $this->entityManager->getRepository(StockScanList::class)->findBy([], ['createdAt' => 'DESC']);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function getArrayPagination(?FilterDataObject $filter = null, ?int $page = 1, ?int $limit = 10, ?string $sort = 'locatie_en_vak', ?string $direction = 'asc'): PaginationInterface
    {
        $where = $having = [];
        $whereString = $havingString = $stockString = $sortString = '';

        if ($filter !== null) {
            if ($filter->stockLocation instanceof StockLocation) {
                $where[] = sprintf('lv.stock_location_id = %d', $filter->stockLocation->getId());
                $stockString = 'and v.stock_location_id = 12';
            }

            if ($filter->list !== null) {
                $having[] = sprintf('locatie_en_vak like "%s%%"', $filter->list);
            }

            if ($filter->barcode !== null) {
                $where[] = sprintf('sslp.barcodes_in_stock like "%%%s%%"', $filter->barcode);
            }

            if ($filter->updatedBy instanceof User) {
                $where[] = sprintf('ssl.updated_by = %d', $filter->updatedBy->getId());
            }

            if ($filter->checkedBy instanceof User) {
                $where[] = sprintf('ssl.checked_by = %d', $filter->checkedBy->getId());
            }

            if (($filter->status !== null) && $filter->status !== StockScanListStatus::ARCHIVED) {
                $where[] = sprintf('ssl.status = "%s"', $filter->status->value);
            }
        }

        if (count($where)) {
            $whereString = 'where (' . implode(') and (', $where) . ')';
        }

        if (count($having)) {
            $havingString = 'having ' . implode(' and ', $having);
        }

        if ($sort !== null) {
            $sortString = sprintf('order by %s %s', $sort, $direction);
        }

        $sql = <<<SQL
        select
            if(lvn.subvak <> '' and lvn.subvak <> '0', concat(lv.vak, '-', lvn.subvak), lv.vak) as locatie_en_vak,
            max(`ssl`.id) as stock_scan_list_id,
            max(`ssl`.status) as stock_scan_list_status,
            max(`ssl`.created_by) as created_by,
            max(`ssl`.created_at) as created_at,
            max(`ssl`.updated_at) as updated_at,
            max(`ssl`.checked_by) as checked_by,
            max(`ssl`.checked_at) as checked_at,
            sum(p.prijs) / count(p.id) as average_sales_price
            -- sum(v.stock_value) / count(v.id) as stock_value
        from cameranu.locatieVakkenNodes as lvn
        inner join cameranu.locatieVakken as lv on lv.id = lvn.locatie_id
        left join cameranu.stock_scan_list as `ssl` on (`ssl`.location_compartment_id = lv.id and `ssl`.location_compartment_node_id = lvn.id) || (`ssl`.location_compartment_id = lv.id and `ssl`.location_compartment_node_id is null)
        left join cameranu.stock_scan_list_product as sslp on `ssl`.id = sslp.stock_scan_list_id
        -- left join cameranu.voorraad as v on v.location_compartment_node_id = lvn.id and v.bestelling_id = 0 and v.date_out is null {$stockString}
        left join cameranu.prijzen as p on p.artikel_id = lvn.artikel_id and p.land = 'NL'
        {$whereString}
        group by locatie_en_vak
        {$havingString}
        {$sortString}
        SQL;

        $query = $this->entityManager->getConnection()->prepare($sql);

        $items = $query->executeQuery()->fetchAllAssociative();

        if (($filter->status !== null) && $filter->status === StockScanListStatus::ARCHIVED) {
            $items = array_filter($items, static fn(array $item) => $item['stock_scan_list_status'] === StockScanListStatus::ARCHIVED->value || $item['stock_scan_list_status'] === null);
        }

        // needed if we want to sort using knp. This is too slow for large arrays
        // $items = array_map(static fn(array $item) => (object) $item, $items);

        return $this->paginator->paginate($items, $page, $limit);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function getStatistics(?FilterDataObject $filter = null): array
    {
        $where = [];
        $whereString = '';

        if ($filter !== null && $filter->stockLocation instanceof StockLocation) {
            $where[] = sprintf('lv.stock_location_id = %d', $filter->stockLocation->getId());
        }

        if (count($where)) {
            $whereString = 'where (' . implode(') and (', $where) . ')';
        }

        $sql = <<<SQL
        select
          count(*) as total_rows,
          sum(if(stock_scan_list_status = 'ongoing', 1, 0)) as ongoing,
          sum(if(stock_scan_list_status = 'done', 1, 0)) as done,
          sum(if(stock_scan_list_status = 'checked', 1, 0)) as checked,
          sum(if(stock_scan_list_status = 'archived' or stock_scan_list_status is null, 1, 0)) as open
        from (
          select
            if(lvn.subvak <> '' and lvn.subvak <> '0', concat(lv.vak, '-', lvn.subvak), lv.vak) as locatie_en_vak,
            max(`ssl`.status) as stock_scan_list_status
          from cameranu.locatieVakkenNodes as lvn
          inner join cameranu.locatieVakken as lv on lv.id = lvn.locatie_id
          left join cameranu.stock_scan_list as `ssl` on (`ssl`.location_compartment_id = lv.id and `ssl`.location_compartment_node_id = lvn.id) || (`ssl`.location_compartment_id = lv.id and `ssl`.location_compartment_node_id is null)
          left join cameranu.stock_scan_list_product as sslp on `ssl`.id = sslp.stock_scan_list_id
          {$whereString}
          group by locatie_en_vak
        ) as inventory_status
        SQL;

        $query = $this->entityManager->getConnection()->prepare($sql);
        return current($query->executeQuery()->fetchAllAssociative());
    }

    public function getPagination(?FilterDataObject $filter = null, ?int $page = 1, ?int $limit = 10): PaginationInterface
    {
        $queryBuilder = $this->createQueryBuilder($filter);
        $pagination = $this->paginator->paginate($queryBuilder, $page, $limit);

        $pagination->setItems(
            array_map(
                static fn(array $item) => new IndexItemDataObject(
                    $item[0],
                    (float) $item['avgSalesValue'],
                    (float) $item['missingPurchaseValue'],
                ),
                $pagination->getItems(),
            )
        );

        return $pagination;
    }

    private function createQueryBuilder(?FilterDataObject $filter = null): QueryBuilder
    {
        $barcodesInStockAmount = '(LENGTH(sslp.barcodesInStock) - LENGTH(REPLACE(sslp.barcodesInStock, \',\', \'\')) + 1)';
        $barcodesScannedAmount = '(CASE WHEN LENGTH(sslp.barcodesScanned) > 0 THEN (LENGTH(sslp.barcodesScanned) - LENGTH(REPLACE(sslp.barcodesScanned, \',\', \'\')) + 1) ELSE 0 END)';
        $queryBuilder = $this->stockScanListRepository->createQueryBuilder('ssl');
        $queryBuilder
            ->addSelect(strtr(
                'SUM(pp.price * $barcodesInStockAmount) / SUM($barcodesInStockAmount) AS avgSalesValue',
                [
                    '$barcodesInStockAmount' => $barcodesInStockAmount,
                ],
            ))
            ->addSelect(strtr(
                'SUM(ppc.price * ($barcodesInStockAmount - $barcodesScannedAmount)) AS missingPurchaseValue',
                [
                    '$barcodesInStockAmount' => $barcodesInStockAmount,
                    '$barcodesScannedAmount' => $barcodesScannedAmount,
                ],
            ))
            ->leftJoin('ssl.location', 'sl')
            ->leftJoin('ssl.createdBy', 'u')
            ->leftJoin('ssl.products', 'sslp')
            ->leftJoin('sslp.product', 'p')
            ->leftJoin('p.prices', 'pp')
            ->leftJoin(PurchasePriceCalculated::class, 'ppc', Join::WITH, 'IDENTITY(ppc.product) = p.id')
            ->where('pp.domainId = :productPriceDomainId')
            ->andWhere('pp.country = :productPriceCountry')
            ->groupBy('ssl.id')
            ->orderBy('ssl.createdAt', 'DESC')
            ->setParameters([
                'productPriceDomainId' => 1,
                'productPriceCountry' => 'NL',
            ])
        ;

        if (!($filter instanceof FilterDataObject)) {
            return $queryBuilder;
        }

        if ($filter->stockLocation instanceof StockLocation) {
            $queryBuilder->andWhere('ssl.location = :stockLocation');
            $queryBuilder->setParameter('stockLocation', $filter->stockLocation);
        }

        if ($filter->list !== null) {
            $queryBuilder->andWhere('ssl.productGroup LIKE :term');
            $queryBuilder->setParameter('term', '%' . $filter->list . '%');
        }

        if ($filter->barcode !== null) {
            $queryBuilder->andWhere('sslp.barcodesInStock LIKE :barcode');
            $queryBuilder->setParameter('barcode', '%' . $filter->barcode . '%');
        }

        if ($filter->updatedBy instanceof User) {
            $queryBuilder->andWhere('ssl.updatedBy = :updatedBy');
            $queryBuilder->setParameter('updatedBy', $filter->updatedBy);
        }

        if ($filter->checkedBy instanceof User) {
            $queryBuilder->andWhere('ssl.checkedBy = :checkedBy');
            $queryBuilder->setParameter('checkedBy', $filter->checkedBy);
        }

        if ($filter->status !== null) {
            $queryBuilder->andWhere('ssl.status = :status');
            $queryBuilder->setParameter('status', $filter->status);
        }

        return $queryBuilder;
    }

    /**
     * Create StockScanList from create form data
     *
     * @throws InvalidLocationInputException
     * @throws NoLocationFoundException
     * @throws NoProductsFoundException
     */
    public function create(StockScanListCreateData $data): ?StockScanList
    {
        $stockLocation = $data->getStockLocation();

        if ($stockLocation !== null && $data->getMethod() === StockScanListCreateData::METHOD_PRODUCT_GROUP) {
            ['products' => $products, 'listName' => $listName] =
                $this->getProductsByGroup($stockLocation, $data->getProductGroup());
        } elseif ($data->getMethod() === StockScanListCreateData::METHOD_LOCATION) {
            ['products' => $products, 'listName' => $listName, 'stockLocation' => $stockLocation] =
                $this->getProductsByLocation($data->getLocation() ?? '', $stockLocation);
        } else {
            return null;
        }

        if ($products === null || count($products) === 0) {
            throw new NoProductsFoundException();
        }

        $productIds = array_map(static function (Product $product) {
            return $product->getId();
        }, $products);

        $stockRepo = $this->entityManager->getRepository(Stock::class);
        $barcodesQuery = $stockRepo->createQueryBuilder('s')
            ->select('p.id AS productId')
            ->addSelect('s.barcode AS barcode')
            ->leftJoin('s.product', 'p')
            ->leftJoin('s.order', 'o')
            ->where('p.id IN (:productIds)')
            ->andWhere('s.location = :location')
            ->andWhere('o.id IS NULL')
            ->andWhere('s.dateOut IS NULL')
            ->setParameters([
                'productIds' => $productIds,
                'location' => $stockLocation,
            ]);

        $locationCompartment = $data->getLocationCompartment();
        $locationCompartmentNode = $data->getLocationCompartmentNode();

        if ($locationCompartment !== null && $data->getMethod() === StockScanListCreateData::METHOD_LOCATION) {
            $locationCompartmentNodes = [];

            if ($locationCompartmentNode === null) {
                $locationCompartmentNodes = $this->locationCompartmentNodeRepository->findBy([
                    'location' => $locationCompartment,
                ]);
            } else {
                $locationCompartmentNodes[] = $locationCompartmentNode;
            }

            $barcodesQuery
                ->andWhere('s.locationCompartmentNode IN (:locationCompartmentNodes)')
                ->setParameter('locationCompartmentNodes', $locationCompartmentNodes);
        }

        $barcodesResult = $barcodesQuery->getQuery()->getResult();

        $barcodesByProductId = [];
        foreach ($barcodesResult as $productStock) {
            $pId = $productStock['productId'];
            if (array_key_exists($pId, $barcodesByProductId) === false) {
                $barcodesByProductId[$pId] = [];
            }
            $barcodesByProductId[$pId][] = $productStock['barcode'];
        }

        /** @var User $user */
        $user = $this->tokenStorage->getToken()?->getUser();
        $stockScanList = new StockScanList($stockLocation, $listName, $user, $data->getLocationCompartment(), $data->getLocationCompartmentNode());

        $stockScanListProducts = [];
        foreach ($products as $product) {
            if (array_key_exists($product->getId(), $barcodesByProductId) === false) {
                continue;
            }

            $barcodes = $barcodesByProductId[$product->getId()];
            $stockScanListProduct = new StockScanListProduct($stockScanList, $product, $barcodes);
            $this->entityManager->persist($stockScanListProduct);
            $stockScanListProducts[] = $stockScanListProduct;
        }

        if (count($stockScanListProducts) === 0) {
            throw new NoProductsFoundException();
        }

        $this->entityManager->persist($stockScanList);
        $this->entityManager->flush();

        return $stockScanList;
    }

    /**
     * @param StockScanList $stockScanList
     * @param array<string, string> $data
     * @return StockScanList
     */
    public function update(StockScanList $stockScanList, array $data): StockScanList
    {
        $allowChecked = true;

        foreach ($stockScanList->getProducts() as $scanListProduct) {
            $scanListProduct->clearScannedBarcodes();
            $productId = $scanListProduct->getProduct()->getId();
            $dataKey = 'barcodes-scanned-' . $productId;
            if (isset($data[$dataKey])) {
                $barcodesInStock = $scanListProduct->getBarcodesInStockArray();
                $barcodesPosted = array_filter(explode(',', $data[$dataKey]), static function ($barcode) {
                    return $barcode !== '';
                });

                foreach ($barcodesPosted as $postedBarcode) {
                    if (in_array($postedBarcode, $barcodesInStock, true)) {
                        $scanListProduct->addScannedBarcode($postedBarcode);
                    }
                }
            }

            if (
                $scanListProduct->getBarcodesScannedAmount() !== $scanListProduct->getBarcodesInStockAmount() ||
                count($scanListProduct->getBarcodesMissing()) > 0
            ) {
                $allowChecked = false;
            }
        }

        if (isset($data['unknown-barcodes-scanned'])) {
            $unknownBarcodes = array_filter(explode(',', $data['unknown-barcodes-scanned']), static function ($barcode) {
                return $barcode !== '';
            });

            foreach ($unknownBarcodes as $unknownBarcode) {
                $stockScanList->addUnknownBarcode($unknownBarcode);
            }

            if (count($unknownBarcodes) > 0) {
                $allowChecked = false;
            }
        }

        if (isset($data['unknown-barcodes-removed'])) {
            $unknownBarcodes = $stockScanList->getUnknownBarcodesArray();
            $removeUnknownBarcodes = array_filter(explode(',', $data['unknown-barcodes-removed']), static function ($barcode) {
                return $barcode !== '';
            });

            foreach ($removeUnknownBarcodes as $removeUnknownBarcode) {
                if (in_array($removeUnknownBarcode, $unknownBarcodes, true)) {
                    $key = array_search($removeUnknownBarcode, $unknownBarcodes, true);
                    unset($unknownBarcodes[$key]);
                }
            }

            $stockScanList->setUnknownBarcodes(implode(StockScanListProduct::BARCODE_DELIMITER, $unknownBarcodes));
        }

        $stockScanList->setUpdatedAt(new DateTime());

        if ($allowChecked === true) {
            $status = StockScanListStatus::CHECKED;
        } else {
            $status = StockScanListStatus::DONE;
        }

        $this->setStatusByIds([$stockScanList->getId()], $status, $this->userRepository->findSystemUser());

        $this->entityManager->flush();

        return $stockScanList;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function getUnknownBarcodesForStockScanList(StockScanList $stockScanList): array
    {
        $productsNotInList = [];
        $unknownBarcodes = [];

        foreach ($stockScanList->getUnknownBarcodesArray() as $unknownBarcode) {
            $stock = $this->stockRepository->findByBarcodeAndStockLocation($unknownBarcode, $stockScanList->getLocation());

            if ($stock === null) {
                $stock = $this->stockRepository->findByBarcodeAndStockLocation($unknownBarcode);
            }

            if ($stock !== null) {
                $productId = $stock->getProduct()->getId();
                if (!isset($productsNotInList[$productId])) {
                    $productsNotInList[$productId] = [
                        'product' => $stock->getProduct(),
                        'barcodesScanned' => [
                            $unknownBarcode => [$unknownBarcode]
                        ],
                        'locationCompartment' => $stock->getLocationCompartmentNode()?->getInferredName(),
                        'stockLocation' => $stock->getLocation()->getDescription(),
                    ];
                } else {
                    $productsNotInList[$productId]['barcodesScanned'][$unknownBarcode][] = $unknownBarcode;
                }
            } else {
                $unknownBarcodes[] = $unknownBarcode;
            }
        }

        return ['productsNotInList' => $productsNotInList, 'unknownBarcodes' => $unknownBarcodes];
    }

    /**
     * @return null|array<Product>
     */
    private function getProductsByGroup(StockLocation $stockLocation, object $productGroup): ?array
    {
        /** @var ProductRepository $productRepo */
        $productRepo = $this->entityManager->getRepository(Product::class);
        $productsQb = $productRepo->createQueryBuilder('p')
            ->join('p.subgroup', 'sg')
            ->innerJoin('p.stock', 'ps')
            ->where('ps.location = :location');

        if ($productGroup instanceof Subgroup) {
            $productsQb
                ->andWhere('sg.id = :groupId');

            $maingroup = $productGroup->getMaingroup();
            $rootgroup = $maingroup->getRootgroup();
            $productGroupName =
                $rootgroup->getName() . ' - ' .
                $maingroup->getName() . ' - ' .
                $productGroup->getName();
        } elseif ($productGroup instanceof Maingroup) {
            $productsQb
                ->join('sg.maingroup', 'mg')
                ->andWhere('mg.id = :groupId');

            $rootgroup = $productGroup->getRootgroup();
            $productGroupName = $rootgroup->getName() . ' - ' . $productGroup->getName();
        } elseif ($productGroup instanceof Rootgroup) {
            $productsQb
                ->join('sg.maingroup', 'mg')
                ->join('mg.rootgroup', 'rg')
                ->andWhere('rg.id = :groupId');

            $productGroupName = $productGroup->getName();
        } else {
            return null;
        }

        $productsQb->setParameters([
            'location' => $stockLocation->getId(),
            'groupId' => $productGroup->getId(),
        ]);

        return [
            'products' => $productsQb->getQuery()->getResult(),
            'listName' => $productGroupName,
        ];
    }

    /**
     * @throws InvalidLocationInputException
     * @throws NoLocationFoundException
     * @throws NoProductsFoundException
     */
    private function getProductsByLocation(string $location, StockLocation $stockLocation = null): ?array
    {
        $compartmentRepo = $this->entityManager->getRepository(LocationCompartment::class);
        $compartmentNodeRepo = $this->entityManager->getRepository(LocationCompartmentNode::class);

        $matches = [];
        if (preg_match('/^[A-Z]{2}\d{4}$/i', $location, $matches) === 1) {
            $conditions = [
                'compartment' => $location,
            ];

            if ($stockLocation !== null) {
                $conditions['stockLocation'] = $stockLocation;
            }

            $compartment = $compartmentRepo->findOneBy($conditions);

            if ($compartment === null) {
                throw new NoLocationFoundException();
            }

            $stockLocation = $compartment->getStockLocation();

            $nodes = $compartmentNodeRepo->findBy(['location' => $compartment]);
            if (count($nodes) === 0) {
                throw new NoProductsFoundException();
            }

            $products = array_map(static function (LocationCompartmentNode $node) {
                return $node->getProduct();
            }, $nodes);
        } elseif (preg_match('/^([A-Z]{2}\d{4})-([A-Z0-9]+)/i', $location, $matches)) {
            $conditions = [
                'compartment' => $matches[1],
            ];

            if ($stockLocation !== null) {
                $conditions['stockLocation'] = $stockLocation;
            }

            $compartment = $compartmentRepo->findOneBy($conditions);

            if ($compartment === null) {
                throw new NoLocationFoundException();
            }

            $stockLocation = $compartment->getStockLocation();

            $nodeQb = $compartmentNodeRepo->createQueryBuilder('lcn');
            $nodeQb
                ->where('lcn.location = :location')
                ->andWhere('lcn.subCompartment = :subCompartment')
                ->setParameters([
                    'location' => $compartment,
                    'subCompartment' => $matches[2],
                ])
            ;

            $nodes = $nodeQb->getQuery()->getResult();

            $products = array_map(static function (LocationCompartmentNode $node) {
                return $node->getProduct();
            }, $nodes);
        } else {
            throw new InvalidLocationInputException();
        }

        return [
            'products' => $products,
            'listName' => $location,
            'stockLocation' => $stockLocation,
        ];
    }

    /**
     * @param array<int> $ids
     * @return array<StockScanList> Array of StockScanLists of which the status could not be updated
     */
    public function setStatusByIds(array $ids, StockScanListStatus $status, ?UserInterface $user = null): array
    {
        if ($user === null) {
            $user = $this->tokenStorage->getToken()?->getUser();
        }

        $stockScanLists = $this->stockScanListRepository->findBy(['id' => $ids]);
        $skipped = [];
        foreach ($stockScanLists as $stockScanList) {
            if (!in_array($status, $stockScanList->getValidTransitions())) {
                $skipped[] = $stockScanList;
                continue;
            }

            $stockScanList->setStatus($status);

            if ($status === StockScanListStatus::CHECKED) {
                $stockScanList->setCheckedBy($user);
                $stockScanList->setCheckedAt(new DateTime());
            }
        }

        $this->entityManager->flush();
        return $skipped;
    }

    public function removeBarcodeFromList(StockScanList $stockScanList, string $barcode, string $type, ?StockScanListProduct $stockScanListProduct = null, bool $flush = true): bool
    {
        switch ($type) {
            case 'unknown':
                // Completely unknown barcode
                $unknownBarcodes = explode(StockScanListProduct::BARCODE_DELIMITER, $stockScanList->getUnknownBarcodes());

                if (in_array($barcode, $unknownBarcodes, true)) {
                    unset($unknownBarcodes[array_search($barcode, $unknownBarcodes, true)]);
                    $stockScanList->setUnknownBarcodes(implode(StockScanListProduct::BARCODE_DELIMITER, $unknownBarcodes));
                }
                break;
            case 'extra':
            case 'scanned':
                if ($stockScanListProduct !== null) {
                    // Known product and in this list
                    $scannedBarcodes = $stockScanListProduct->getBarcodesScannedArray();

                    if (in_array($barcode, $scannedBarcodes, true)) {
                        unset($scannedBarcodes[array_search($barcode, $scannedBarcodes, true)]);
                        $stockScanListProduct->setBarcodesScanned(implode(StockScanListProduct::BARCODE_DELIMITER, $scannedBarcodes));
                    }
                } else {
                    // Known product but not in this list
                    $unknownBarcodes = explode(StockScanListProduct::BARCODE_DELIMITER, $stockScanList->getUnknownBarcodes());

                    if (in_array($barcode, $unknownBarcodes, true)) {
                        unset($unknownBarcodes[array_search($barcode, $unknownBarcodes, true)]);
                        $stockScanList->setUnknownBarcodes(implode(StockScanListProduct::BARCODE_DELIMITER, $unknownBarcodes));
                    }
                }
                break;
        }

        if ($flush === true) {
            $this->entityManager->flush();
        }

        return true;
    }

    public function removeBarcodeFromStock(string $barcode, StockScanListProduct $stockScanListProduct): bool
    {
        $stockItems = $this->stockRepository->findByBarcodeLocationAndProduct($stockScanListProduct->getProduct(), [$stockScanListProduct->getStockScanList()->getLocation()], $barcode);
        $locationCompartment = $stockScanListProduct->getStockScanList()->getLocationCompartment();
        /** @var User $user */
        $user = $this->tokenStorage->getToken()?->getUser();
        $zLocation = $this->stockLocationRepository->find(
            StockLocation::SEARCH_LOCATIONS[$stockScanListProduct->getStockScanList()->getLocation()->getParent()]
        );

        if ($locationCompartment !== null && $zLocation !== null && count($stockItems)) {
            foreach ($stockItems as $stockItem) {
                $locationCompartmentId = $stockItem->getLocationCompartmentNode()?->getLocation()?->getId();

                if ($locationCompartment->getId() === $locationCompartmentId) {
                    $stockItem->setLocation($zLocation);

                    $stockLogAction = $this->stockLogActionRepository->find(StockLogAction::VOORRAADREGEL_VERWIJDERD);

                    $stockLog = new StockLogN();
                    $stockLogData = new StockLogDataObject($this->entityManager);
                    $stockLogData->timestamp = new DateTime();
                    $stockLogData->user = $user;
                    $stockLogData->stockLogAction = $stockLogAction;
                    $stockLogData->stock = $stockItem;
                    $stockLogData->extraInfo = 'Vanuit inventarislijst ' . $stockScanListProduct->getStockScanList()->getId();
                    $stockLogData->save($stockLog, true);

                    $barcodesInStock = $stockScanListProduct->getBarcodesInStockArray();

                    if (in_array($barcode, $barcodesInStock, true)) {
                        unset($barcodesInStock[array_search($barcode, $barcodesInStock, true)]);
                        $stockScanListProduct->setBarcodesInStock(implode(StockScanListProduct::BARCODE_DELIMITER, $barcodesInStock));
                    }

                    $this->entityManager->flush();
                    break;
                }
            }
        }

        return true;
    }

    public function addBarcodeToProduct(string $barcode, StockScanListProduct $stockScanListProduct, bool $flush = true): bool
    {
        $stockScanListProduct->addScannedBarcode($barcode);

        if ($flush === true) {
            $this->entityManager->flush();
        }

        return true;
    }

    /**
     * @throws ORMException
     * @throws Exception
     */
    public function addBarcodeToStock(string $barcode, StockScanListProduct $stockScanListProduct): bool
    {
        $product = $stockScanListProduct->getProduct();
        $stockLocation = $stockScanListProduct->getStockScanList()->getLocation();
        $supplier = $this->supplierRepository->find(Supplier::FOUND_BY_PARENT[$stockLocation->getParent()]);

        if ($product !== null && $supplier !== null) {
            /** @var User $user */
            $user = $this->tokenStorage->getToken()?->getUser();
            $zeroOrder = $this->entityManager->getReference(OrderInfo::class, 0);

            $stockValue = $product->getCurrentStockValue();

            $locationCompartmentNode = $stockScanListProduct->getStockScanList()->getLocationCompartmentNode();

            if ($locationCompartmentNode === null) {
                $locationCompartmentNode = $this->locationCompartmentNodeRepository->findOneBy([
                    'location' => $stockScanListProduct->getStockScanList()->getLocationCompartment(),
                    'product' => $product,
                ]);
            }

            if ($locationCompartmentNode === null) {
                return false;
            }

            $stock = new Stock();
            $stock
                ->setProduct($product)
                ->setProductId($product->getId())
                ->setDateIn(new DateTime())
                ->setTimestampIn(new DateTime())
                ->setInvoiceSupplier('')
                ->setBarcode($barcode)
                ->setOrder($zeroOrder)
                ->setOrdernumber('')
                ->setSalesPrice(0.)
                ->setSalesPriceEx(0.)
                ->setPurchasePrice($product->getCurrentStockValue())
                ->setDiscount('0.00')
                ->setRemovalContribution('0.00')
                ->setStockValue($stockValue)
                ->setInfo('')
                ->setFlags(0)
                ->setVat($product->getVat())
                ->setSupplier($supplier)
                ->setInsertedBy($user)
                ->setLocation($stockLocation)
                ->setLocationCompartmentNode($locationCompartmentNode);

            $this->entityManager->persist($stock);

            $addStockLogAction = $this->stockLogActionRepository->find(StockLogAction::IN_VOORRAAD_DOOR_INVENTARISATIE);

            $stockLog = new StockLogN();
            $stockLogData = new StockLogDataObject($this->entityManager);
            $stockLogData->timestamp = new DateTime();
            $stockLogData->user = $user;
            $stockLogData->stockLogAction = $addStockLogAction;
            $stockLogData->stock = $stock;
            $stockLogData->save($stockLog, true);

            $barcodesInStock = $stockScanListProduct->getBarcodesInStockArray();
            $barcodesInStock[] = $barcode;
            $stockScanListProduct->setBarcodesInStock(implode(StockScanListProduct::BARCODE_DELIMITER, $barcodesInStock));

            $this->entityManager->flush();
        }

        return true;
    }

    public function addLogAction(StockScanList $stockScanList, string $barcode, StockScanListLogAction $stockScanListLogAction): bool
    {
        /** @var User $user */
        $user = $this->tokenStorage->getToken()?->getUser();
        $stockScanListLog = new StockScanListLog();
        $stockScanListLog->setCreatedBy($user);
        $stockScanListLog->setStockScanList($stockScanList);
        $stockScanListLog->setAction($stockScanListLogAction);
        $stockScanListLog->setBarcode($barcode);
        $this->entityManager->persist($stockScanListLog);
        $this->entityManager->flush();

        return true;
    }

    /**
     * @return StockScanList[]
     */
    public function findStockScanListsById(array $stockScanListIds): array
    {
        return $this->stockScanListRepository->findAndIndexById($stockScanListIds);
    }

    /**
     * @return User[]
     */
    public function findUsersById(array $userIds): array
    {
        return $this->userRepository->findAndIndexById($userIds);
    }

    public function findLocationCompartmentNodesByStockScanList(StockScanList $stockScanList): array
    {
        return array_reduce(
            $this->locationCompartmentNodeRepository->findByStockScanList($stockScanList),
            static function (array $productMap, $locationCompartmentNode): array {
                $productMap[$locationCompartmentNode->getProduct()->getId()] = $locationCompartmentNode->getInferredName();
                return $productMap;
            },
            []
        );
    }
}
