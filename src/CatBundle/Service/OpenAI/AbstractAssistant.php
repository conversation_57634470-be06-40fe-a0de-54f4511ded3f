<?php

declare(strict_types=1);

namespace CatBundle\Service\OpenAI;

use CatBundle\DTO\OpenAI\AssistantResponseInterface;
use <PERSON><PERSON>\Serializer\SerializerInterface;
use OpenAI;

abstract class AbstractAssistant
{
    protected OpenAI\Client $client;

    public function __construct(
        protected readonly SerializerInterface $serializer,
        protected readonly AssistantFactory $assistantFactory,
        private readonly string $apiKey,
        protected readonly string $model,
        protected string $role,
        protected readonly ?string $assistantId = null
    ) {
        $this->client = OpenAI::client($this->apiKey);
    }

    protected function getAssistant(string $class): AbstractAssistant
    {
        return $this->assistantFactory->getAssistant($class);
    }

    public function setRole(string $role): void
    {
        $this->role = $role;
    }

    public function addInput(array $input): void
    {
    }

    abstract public function execute(): AssistantResponseInterface|string;
}
