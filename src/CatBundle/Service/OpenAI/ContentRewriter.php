<?php

declare(strict_types=1);

namespace CatBundle\Service\OpenAI;

use Cat<PERSON><PERSON>le\DTO\OpenAI\AssistantResponseInterface;
use <PERSON>B<PERSON>le\DTO\OpenAI\ContentWriterResponse;
use <PERSON>Bundle\Enum\ProOrConType;

class ContentRewriter extends ContentAssistant
{
    /**
     * @return string
     */
    public function execute(): string
    {
        $messages = $this->prepareMessages();
        $messages = $this->addPdfToConversation($messages);

        if ($this->text !== null) {
            $messages[] = ['role' => 'user', 'content' => ContentAssistant::INFO_TAG . $this->text];
        }

        if ($this->product !== null) {
            $messages[] = ['role' => 'user', 'content' => ContentAssistant::INFO_TAG . $this->contentSummary->getContent($this->product)];
        }

        $response = $this->client->chat()->create([
            'model' => $this->model,
            'messages' => $messages,
        ]);

        $responseContent = $response->choices[0]->message->content;

        return $responseContent;
    }

    protected function getJsonSchema(): array
    {
        return [];
    }
}
