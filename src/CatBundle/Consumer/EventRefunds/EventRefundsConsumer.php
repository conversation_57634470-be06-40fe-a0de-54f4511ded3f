<?php

declare(strict_types=1);

namespace CatBundle\Consumer\EventRefunds;

use JsonException;
use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use RuntimeException;
use Throwable;
use Webdsign\GlobalBundle\Services\Event\RegistrationManager;
use Webdsign\GlobalBundle\Services\Telegram;

class EventRefundsConsumer implements ConsumerInterface
{
    private Telegram $telegram;
    private RegistrationManager $registrationManager;

    public function __construct(
        Telegram $telegram,
        RegistrationManager $registrationManager
    ) {
        $this->telegram = $telegram;
        $this->registrationManager = $registrationManager;
    }

    /**
     * @throws JsonException
     */
    public function execute(AMQPMessage $msg): int
    {
        $data = json_decode($msg->getBody(), true, 512, JSON_THROW_ON_ERROR);

        try {
            $registrationId = $data['registrationId'] ?? null;

            if ($registrationId === null) {
                throw new RuntimeException('Invalid registration ID');
            }

            $registrationRepository = $this->registrationManager->getRepository();
            $registration = $registrationRepository->find((int)$registrationId);

            if ($registration === null) {
                throw new RuntimeException('Registration not found');
            }

            $this->registrationManager->performRefund($registration);
        } catch (Throwable $exception) {
            $this->telegram->sendServerMessage(
                'Er is een EventRefunds queue message naar de dead letter verplaatst!' .
                PHP_EOL . PHP_EOL . $exception->getMessage()
            );

            return self::MSG_REJECT;
        }

        return self::MSG_ACK;
    }
}
