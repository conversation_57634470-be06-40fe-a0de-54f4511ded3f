<?php

declare(strict_types=1);

namespace CatBundle\Consumer\SqueezelyCRMUpdate;

use CatBundle\Service\Squeezely\PostCRMUpdate;
use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Throwable;
use Webdsign\GlobalBundle\Entity\CustomerRepository;
use Webdsign\GlobalBundle\Services\Telegram;

class SqueezelyCRMUpdateConsumer implements ConsumerInterface
{
    public function __construct(
        private readonly Telegram $telegram,
        private readonly CustomerRepository $customerRepository,
        private readonly PostCRMUpdate $postCRMUpdate,
    ) {
    }

    public function execute(AMQPMessage $msg): int
    {
        $data = json_decode($msg->getBody(), true);
        try {
            $customer = $this->customerRepository->find($data['customerId']);

            $this->postCRMUpdate->postCRMUpdate($customer);
        } catch (Throwable $exception) {
            $this->telegram->sendServerMessage(
                'Er is een SqueezelyCRMUpdate queue message naar de dead letter verplaatst!' .
                PHP_EOL . PHP_EOL . $exception->getMessage()
            );

            return self::MSG_REJECT;
        }

        return self::MSG_ACK;
    }
}
