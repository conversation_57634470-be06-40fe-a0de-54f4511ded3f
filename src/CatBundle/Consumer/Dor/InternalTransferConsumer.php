<?php

declare(strict_types=1);

namespace CatBundle\Consumer\Dor;

use CatBundle\Service\Dor\InternalTransfer;
use Exception;
use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Webdsign\GlobalBundle\Services\Telegram;

class InternalTransferConsumer implements ConsumerInterface
{
    public function __construct(
        private readonly Telegram $telegram,
        private readonly InternalTransfer $internalTransfer,
    ) {
    }

    public function execute(AMQPMessage $msg): int
    {
        $data = json_decode($msg->getBody(), true);
        if (!is_array($data) || !array_key_exists('stockId', $data)) {
            return self::MSG_ACK;
        }

        try {
            $this->internalTransfer->execute($data['stockId']);
        } catch (Exception $exception) {
            $this->telegram->sendServerMessage(
                'Er is een DorInternalTransfer queue message naar de dead letter verplaatst!' .
                PHP_EOL . PHP_EOL . $exception->getMessage()
            );

            return self::MSG_REJECT;
        }

        return self::MSG_ACK;
    }
}
