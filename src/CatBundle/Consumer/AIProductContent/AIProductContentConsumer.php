<?php

declare(strict_types=1);

namespace CatBundle\Consumer\AIProductContent;

use CatBundle\Service\OpenAI\ContentRewriter;
use ContentBundle\Entity\Experiment;
use ContentBundle\Entity\ExperimentRepository;
use DateTime;
use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Throwable;
use Webdsign\GlobalBundle\Entity\ProductContent;
use Webdsign\GlobalBundle\Entity\ProductContentRepository;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Services\Telegram;

readonly class AIProductContentConsumer implements ConsumerInterface
{
    public function __construct(
        private Telegram $telegram,
        private ContentRewriter $contentRewriter,
        private ProductRepository $productRepository,
        private ProductContentRepository $productContentRepository,
        private ExperimentRepository $experimentRepository,
    ) {
    }

    public function execute(AMQPMessage $msg): int
    {
        $data = json_decode($msg->getBody(), true);
        try {
            $product = $this->productRepository->findOneBy(['id' => $data['productId']]);
            $openAIBlock = $this->productContentRepository->findOneBy(['product' => $product, 'title' => 'Content door OpenAI']);

            if ($openAIBlock instanceof ProductContent) {
                return self::MSG_ACK;
            }

            $experiment = $this->experimentRepository->findOneBy(['id' => Experiment::CONVERSION_ORIENTED_PRODUCT_TEXTS_EXPERIMENT]);
            $lastContent = $this->productContentRepository->findOneBy(['product' => $product], ['position' => 'DESC']);

            $this->contentRewriter->addInput([
                'product' => $product,
            ]);
            $content = $this->contentRewriter->execute();

            $productContent = new ProductContent();
            $productContent->setPosition($lastContent->getPosition() + 10);
            $productContent->setTab(2);
            $productContent->setProduct($product);
            $productContent->setContent($content);
            $productContent->setTitle('Content door OpenAI');
            $productContent->setExperiment($experiment);
            $productContent->setStartDate(new DateTime());
            $productContent->setEndDate(new DateTime('0000-00-00'));

            $this->productRepository->getEntityManager()->persist($productContent);
            $this->productRepository->getEntityManager()->flush();
        } catch (Throwable $exception) {
            $this->telegram->sendServerMessage(
                'Er is een AIProductContent queue message naar de dead letter verplaatst!' .
                PHP_EOL . PHP_EOL . $exception->getMessage()
            );

            return self::MSG_REJECT;
        }

        return self::MSG_ACK;
    }
}
