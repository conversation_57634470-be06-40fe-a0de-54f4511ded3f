<?php

declare(strict_types=1);

namespace CatBundle\Consumer\NewsletterSubscribe;

use OldSound\RabbitMqBundle\RabbitMq\ConsumerInterface;
use PhpAmqpLib\Message\AMQPMessage;
use Throwable;
use Webdsign\GlobalBundle\Services\ClangWebHook;
use Webdsign\GlobalBundle\Services\Telegram;

class NewsletterSubscribeConsumer implements ConsumerInterface
{
    /** @var Telegram $telegram */
    private Telegram $telegram;

    /** @var ClangWebHook $clangWebHook */
    private ClangWebHook $clangWebHook;

    /**
     * @param Telegram $telegram
     * @param ClangWebHook $clangWebHook
     */
    public function __construct(Telegram $telegram, ClangWebHook $clangWebHook)
    {
        $this->telegram = $telegram;
        $this->clangWebHook = $clangWebHook;
    }

    /**
     * @param AMQPMessage $msg
     * @return int
     */
    public function execute(AMQPMessage $msg): int
    {
        $data = json_decode($msg->getBody(), true);
        try {
            $this->clangWebHook->handleData($data, 'activateNewsletter');
        } catch (Throwable $exception) {
            $this->telegram->sendServerMessage(
                'Er is een Customer_update queue message naar de dead letter verplaatst!' .
                PHP_EOL . PHP_EOL . $exception->getMessage()
            );

            return self::MSG_REJECT;
        }

        return self::MSG_ACK;
    }
}
