<?php

declare(strict_types=1);

namespace CatBundle\DTO\DiscountCode;

use DateTime;
use Doctrine\Common\Collections\Collection;
use Webdsign\GlobalBundle\Entity\DiscountCode;

class DiscountCodeBatchDataObject
{
    use HasProfileAndArticleValueFields;

    public ?int $id = null;
    public string $name = '';
    public string $description = '';
    public string $discount = '';
    public string $prefix = '';
    public ?DateTime $startDate = null;
    public ?DateTime $endDate = null;
    public int $availableCodes = 1;
    public ?float $minimalAmount = null;
    public string $ledgerCode = '';
    public string $origin = DiscountCode::ORIGIN_CAMERANU;
    public string $type = DiscountCode\DiscountCodeBatch::TYPE_SITE_WIDE;
    public string $info = '';
    public bool $onlyOnePerProduct = true;
    public bool $onlyOnePerOrderLine = false;
    public ?Collection $conditionTags = null;
    public ?array $productSearch = null;
    public ?string $conditionProducts = null;
    public ?Collection $conditionTagsExcluded = null;
    public int $totalCodes = 1;
    public bool $archived = false;
    public bool $allSpecs = false;
    public bool $allTags = false;

    public function __construct()
    {
        $this->startDate = (new DateTime())->setTime(0, 0);
        $this->endDate = (new DateTime())->setTime(23, 59, 59);
    }
}
