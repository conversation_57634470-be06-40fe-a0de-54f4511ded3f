<?php

declare(strict_types=1);

namespace CatBundle\DTO\Event;

use Webdsign\GlobalBundle\Entity\Event\Category;
use Webdsign\GlobalBundle\Entity\Event\Instructor;
use Webdsign\GlobalBundle\Entity\Event\Location;

class OverviewFilter
{
    public function __construct(
        public readonly ?Instructor $instructor,
        public readonly ?Location $location,
        public readonly ?Category $category,
        public readonly ?bool $visible,
        public readonly ?bool $expired,
        public readonly ?string $province,
        public readonly ?bool $softDelete,
    ) {
    }
}
