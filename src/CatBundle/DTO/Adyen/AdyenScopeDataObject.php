<?php

declare(strict_types=1);

namespace CatBundle\DTO\Adyen;

use DateTime;

class AdyenScopeDataObject
{
    private ?int $orderId = null;
    private ?int $orderNumber = null;
    private ?int $customerId = null;
    private ?string $orderDate = null;
    private ?string $isFinished = null;
    private ?string $isCancelled = null;
    private ?DateTime $invoiceDate = null;
    private ?int $invoiceNumber = null;
    private ?string $parentOrigin = null;
    private ?string $paymentName = null;
    private ?string $deliveryMethodName = null;
    /*** @var null|float|string */
    private $stockValueInc = null;
    /*** @var null|float|string */
    private $ledgerValueInc = null;
    /*** @var null|float|string */
    private $totalPaymentInc = null;
    private ?string $paymentMethod = null;
    /*** @var null|float|string */
    private $paymentAmount = null;
    private ?string $paymentDate = null;
    private ?string $paymentAdyenType = null;
    private ?string $paymentAdyenReference = null;

    /**
     * @return int|null
     */
    public function getOrderId(): ?int
    {
        return $this->orderId;
    }

    /**
     * @param int|null $orderId
     * @return AdyenScopeDataObject
     */
    public function setOrderId(?int $orderId): AdyenScopeDataObject
    {
        $this->orderId = $orderId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getOrderNumber(): ?int
    {
        return $this->orderNumber;
    }

    /**
     * @param int|null $orderNumber
     * @return AdyenScopeDataObject
     */
    public function setOrderNumber(?int $orderNumber): AdyenScopeDataObject
    {
        $this->orderNumber = $orderNumber;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getCustomerId(): ?int
    {
        return $this->customerId;
    }

    /**
     * @param int|null $customerId
     * @return AdyenScopeDataObject
     */
    public function setCustomerId(?int $customerId): AdyenScopeDataObject
    {
        $this->customerId = $customerId;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getOrderDate(): ?string
    {
        return $this->orderDate;
    }

    /**
     * @param string|null $orderDate
     * @return AdyenScopeDataObject
     */
    public function setOrderDate(?string $orderDate): AdyenScopeDataObject
    {
        $this->orderDate = $orderDate;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getIsFinished(): ?string
    {
        return $this->isFinished;
    }

    /**
     * @param string|null $isFinished
     * @return AdyenScopeDataObject
     */
    public function setIsFinished(?string $isFinished): AdyenScopeDataObject
    {
        $this->isFinished = $isFinished;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getIsCancelled(): ?string
    {
        return $this->isCancelled;
    }

    /**
     * @param string|null $isCancelled
     * @return AdyenScopeDataObject
     */
    public function setIsCancelled(?string $isCancelled): AdyenScopeDataObject
    {
        $this->isCancelled = $isCancelled;
        return $this;
    }

    /**
     * @return DateTime|null
     */
    public function getInvoiceDate(): ?DateTime
    {
        return $this->invoiceDate;
    }

    /**
     * @param DateTime|null $invoiceDate
     * @return AdyenScopeDataObject
     */
    public function setInvoiceDate(?DateTime $invoiceDate): AdyenScopeDataObject
    {
        $this->invoiceDate = $invoiceDate;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getInvoiceNumber(): ?int
    {
        return $this->invoiceNumber;
    }

    /**
     * @param int|null $invoiceNumber
     * @return AdyenScopeDataObject
     */
    public function setInvoiceNumber(?int $invoiceNumber): AdyenScopeDataObject
    {
        $this->invoiceNumber = $invoiceNumber;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getParentOrigin(): ?string
    {
        return $this->parentOrigin;
    }

    /**
     * @param string|null $parentOrigin
     * @return AdyenScopeDataObject
     */
    public function setParentOrigin(?string $parentOrigin): AdyenScopeDataObject
    {
        $this->parentOrigin = $parentOrigin;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPaymentName(): ?string
    {
        return $this->paymentName;
    }

    /**
     * @param string|null $paymentName
     * @return AdyenScopeDataObject
     */
    public function setPaymentName(?string $paymentName): AdyenScopeDataObject
    {
        $this->paymentName = $paymentName;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getDeliveryMethodName(): ?string
    {
        return $this->deliveryMethodName;
    }

    /**
     * @param string|null $deliveryMethodName
     * @return AdyenScopeDataObject
     */
    public function setDeliveryMethodName(?string $deliveryMethodName): AdyenScopeDataObject
    {
        $this->deliveryMethodName = $deliveryMethodName;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getStockValueInc(): ?float
    {
        return (float)$this->stockValueInc;
    }

    /**
     * @param float|null $stockValueInc
     * @return AdyenScopeDataObject
     */
    public function setStockValueInc(?float $stockValueInc): AdyenScopeDataObject
    {
        $this->stockValueInc = $stockValueInc;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getLedgerValueInc(): ?float
    {
        return (float)$this->ledgerValueInc;
    }

    /**
     * @param float|null $ledgerValueInc
     * @return AdyenScopeDataObject
     */
    public function setLedgerValueInc(?float $ledgerValueInc): AdyenScopeDataObject
    {
        $this->ledgerValueInc = $ledgerValueInc;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getTotalPaymentInc(): ?float
    {
        return (float)$this->totalPaymentInc;
    }

    /**
     * @param float|null $totalPaymentInc
     * @return AdyenScopeDataObject
     */
    public function setTotalPaymentInc(?float $totalPaymentInc): AdyenScopeDataObject
    {
        $this->totalPaymentInc = $totalPaymentInc;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    /**
     * @param string|null $paymentMethod
     * @return AdyenScopeDataObject
     */
    public function setPaymentMethod(?string $paymentMethod): AdyenScopeDataObject
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

    /**
     * @return float|null
     */
    public function getPaymentAmount(): ?float
    {
        return (float)$this->paymentAmount;
    }

    /**
     * @param float|null $paymentAmount
     * @return AdyenScopeDataObject
     */
    public function setPaymentAmount(?float $paymentAmount): AdyenScopeDataObject
    {
        $this->paymentAmount = $paymentAmount;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPaymentDate(): ?string
    {
        return $this->paymentDate;
    }

    /**
     * @param string|null $paymentDate
     * @return AdyenScopeDataObject
     */
    public function setPaymentDate(?string $paymentDate): AdyenScopeDataObject
    {
        $this->paymentDate = $paymentDate;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPaymentAdyenType(): ?string
    {
        return $this->paymentAdyenType;
    }

    /**
     * @param string|null $paymentAdyenType
     * @return AdyenScopeDataObject
     */
    public function setPaymentAdyenType(?string $paymentAdyenType): AdyenScopeDataObject
    {
        $this->paymentAdyenType = $paymentAdyenType;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getPaymentAdyenReference(): ?string
    {
        return $this->paymentAdyenReference;
    }

    /**
     * @param string|null $paymentAdyenReference
     * @return AdyenScopeDataObject
     */
    public function setPaymentAdyenReference(?string $paymentAdyenReference): AdyenScopeDataObject
    {
        $this->paymentAdyenReference = $paymentAdyenReference;
        return $this;
    }

    public function set($key, $value)
    {
        $this->{$key} = $value;
    }
}
