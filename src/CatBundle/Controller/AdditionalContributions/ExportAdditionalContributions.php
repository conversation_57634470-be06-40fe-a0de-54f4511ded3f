<?php

declare(strict_types=1);

namespace CatB<PERSON>le\Controller\AdditionalContributions;

use CatB<PERSON><PERSON>\Form\DataObject\AdditionalContributionExportDataObject;
use CatB<PERSON>le\Form\Type\AdditionalContributionExport;
use CatBundle\Service\AdditionalContributionService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\ORMException;
use Exception;
use League\Csv\CannotInsertRecord;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface;
use Twig\Environment as TwigEnvironment;
use Symfony\Contracts\Translation\TranslatorInterface;

class ExportAdditionalContributions
{
    /**
     * @var TwigEnvironment
     */
    private TwigEnvironment $templating;

    /**
     * @var FormFactoryInterface
     */
    private FormFactoryInterface $formFactory;

    /**
     * @var TranslatorInterface
     */
    private TranslatorInterface $translator;

    /**
     * @var FlashBagInterface
     */
    private FlashBagInterface $flashBag;

    /**
     * @var AdditionalContributionService
     */
    private AdditionalContributionService $additionalContributionService;

    /**
     * @param TwigEnvironment $templating
     * @param FormFactoryInterface $formFactory
     * @param TranslatorInterface $translator
     * @param FlashBagInterface $flashBag
     * @param AdditionalContributionService $additionalContributionService
     */
    public function __construct(
        TwigEnvironment $templating,
        FormFactoryInterface $formFactory,
        TranslatorInterface $translator,
        FlashBagInterface $flashBag,
        AdditionalContributionService $additionalContributionService
    ) {
        $this->templating = $templating;
        $this->formFactory = $formFactory;
        $this->translator = $translator;
        $this->flashBag = $flashBag;
        $this->additionalContributionService = $additionalContributionService;
    }

    /**
     * @param Request $request
     * @return Response
     * @throws CannotInsertRecord
     * @throws ORMException
     */
    public function __invoke(Request $request): Response
    {
        $form = $this->formFactory->create(
            AdditionalContributionExport::class,
            new AdditionalContributionExportDataObject()
        );

        $form->handleRequest($request);
        if ($form->isSubmitted()) {
            try {
                return $this->additionalContributionService->exportContributions($form->getData());
            } catch (Exception $exception) {
                $this->flashBag->add(
                    'danger',
                    sprintf(
                        $this->translator->trans('additional_contribution.form.error'),
                        $exception->getMessage()
                    )
                );
            }
        }

        return new Response($this->templating->render(
            '@Cat/AdditionalContribution/export.html.twig',
            [
                'form' => $form->createView()
            ]
        ));
    }
}
