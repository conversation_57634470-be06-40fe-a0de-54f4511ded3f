<?php

declare(strict_types=1);

namespace CatBundle\Controller\CourierListBins;

use CatBundle\Service\CourierListBins;
use JMS\Serializer\SerializationContext;
use JMS\Serializer\SerializerInterface;
use Symfony\Component\HttpFoundation\Response;

class FetchController
{
    public function __construct(
        private readonly CourierListBins $courierListBin,
        private readonly SerializerInterface $serializer
    ) {
    }

    /**
     * @return Response
     */
    public function __invoke(): Response
    {
        return new Response(
            $this->serializer->serialize(
                $this->courierListBin->getBinsForUser(),
                'json',
                SerializationContext::create()->enableMaxDepthChecks()
            )
        );
    }
}
