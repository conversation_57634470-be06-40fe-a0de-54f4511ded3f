<?php

declare(strict_types=1);

namespace CatBundle\Controller\SecondHand\Product;

use Webdsign\GlobalBundle\Entity\Product;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandPriceUpdateRepository;

class IntakePriceLog extends AbstractController
{
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly SecondHandPriceUpdateRepository $secondHandPriceUpdateRepository,
    ) {
    }

    #[Route('second-hand/product/intake_price_log/{product}', name: 'second_hand_intake_price_log')]
    public function __invoke(
        Request $request,
        Product $product
    ): JsonResponse {
        $priceLogs = [];
        $children = $this->productRepository->findChildSecondHandProducts($product);

        foreach ($children as $child) {
            $logs = $this->secondHandPriceUpdateRepository->findBy(['product' => $child]);
            $quality = $child->getSecondHandProductState()->current()?->getSecondHandState()?->getQuality();

            foreach ($logs as $log) {
                $insertedTime = $log->getDateChanged();
                $timestamp = $insertedTime->getTimestamp();

                $priceLogs[$timestamp]['formatted'] = $insertedTime->format('Y-m-d H:i');
                $priceLogs[$timestamp]['data'][$quality][] = $log;

                if (
                    !isset($priceLogs[$timestamp]['maxQuality']) ||
                    $quality > $priceLogs[$timestamp]['maxQuality']
                ) {
                    $priceLogs[$timestamp]['maxQuality'] = $quality;
                    $priceLogs[$timestamp]['maxPrice'] = $log->getPrice();
                }
            }
        }

        krsort($priceLogs);

        return new JsonResponse([
            'html' => $this->render(
                '@Cat/SecondHand/Product/Panels/priceLog.html.twig',
                [
                    'product' => $product,
                    'priceLogs' => $priceLogs,
                ]
            )->getContent(),
        ]);
    }
}
