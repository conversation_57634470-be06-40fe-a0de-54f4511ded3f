<?php

declare(strict_types=1);

namespace CatBundle\Controller\SecondHand\Product\Import;

use League\Csv\Writer;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Webdsign\GlobalBundle\Entity\ProductRepository;

class Export
{
    private const array HEADER = [
        'productId',
        'parentProductId',
        'name',
        'state',
        'hoofdgroep',
        'subgroep',
        'brand',
        'intakePrice',
        'salesPrice',
        'intakeable',
        'originalProductId',
        'originalProductName',
        'originalSalesPrice',
        'rankedNr',
        'stock',
        'ASR',
        'DSI_Quarter',
        'DSI_Year',
    ];

    public function __construct(
        private readonly ProductRepository $productRepository,
    ) {
    }

    #[Route('second-hand/product-export', name: 'instant_quote_product_export')]
    public function __invoke(): Response
    {
        $parentProductIds = $this->productRepository->findMainSecondHandProductIds();
        $childProductInfo = $this->productRepository->getSecondhandExportInfoByParentIds($parentProductIds);

        $writer = Writer::createFromString();
        $writer->insertOne(self::HEADER);
        $writer->insertAll($childProductInfo);

        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            'secondhand-export.csv'
        );

        return new Response(
            $writer->toString(),
            Response::HTTP_OK,
            [
                'Content-Encoding' => 'none',
                'Content-Type' => 'text/csv; charset=UTF-8',
                'Content-Disposition' => $disposition,
                'Content-Description' => 'File Transfer',
            ]
        );
    }
}
