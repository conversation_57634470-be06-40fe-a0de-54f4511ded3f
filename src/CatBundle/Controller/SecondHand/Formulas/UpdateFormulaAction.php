<?php

declare(strict_types=1);

namespace CatBundle\Controller\SecondHand\Formulas;

use CatBundle\Service\SecondHand\SecondHandPriceManager;
use DateTime;
use Doctrine\ORM\ORMException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandFormula;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandFormulaRepository;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandPrice;
use Webdsign\GlobalBundle\Entity\SecondHand\SecondHandProductState;

class UpdateFormulaAction
{
    public function __construct(
        private readonly SecondHandFormulaRepository $intakeFormulaRepository,
        private readonly SecondHandPriceManager $priceManager,
        private readonly ProductRepository $productRepository
    ) {
    }

    /**
     * @throws ORMException
     */
    public function __invoke(Request $request): JsonResponse
    {
        $data = $request->request->all()['formulas'];
        $returnData = [];

        foreach ($data as $formula) {
            $newFormula = $formula['formula'];
            $formulaType = $formula['type'];
            $formula = $this->intakeFormulaRepository->find($formula['id']);

            if (!$formula instanceof SecondHandFormula) {
                continue;
            }

            switch ($formulaType) {
                case 'sales':
                    $formula->setSalesFormula((int)$newFormula);
                    break;
                case 'intake':
                    $formula->setIntakeFormula((int)$newFormula);
                    break;
            }

            $formula->setisGeneric(false);

            $basePrice = $formula->getPrice();
            $product = $basePrice->getProduct();
            $children = $this->productRepository->findChildSecondHandProducts($product);
            $price = null;

            foreach ($children as $child) {
                $productState = $child->getSecondHandProductState()->current();
                if ($productState instanceof SecondHandProductState && $productState->getSecondHandState() === $formula->getState()) {
                    $price = $child->getSecondHandPrice(false);
                    break;
                }
            }

            if ($price instanceof SecondHandPrice) {
                $newPrice = $this->priceManager->calculatePrice($formula->getIntakeFormula(), $basePrice->getIntakePrice());
                $price->setIntakePrice($newPrice);

                $this->priceManager->logIntakePriceUpdate($price->getProduct(), $newPrice, new DateTime(), 'Aangepast via tweedehands tool');

                $newSalesPrices =  $this->priceManager->calculatePrice($formula->getSalesFormula(), $newPrice, 'sales');

                if ($price->getSalesPrice() !== $newSalesPrices) {
                    $this->priceManager->saveSalesPriceForProduct($price->getProduct(), $newSalesPrices);
                    $price->setSalesPrice($newSalesPrices);
                }

                $returnData[$price->getProduct()->getId()]['intakeFormula'] = $formula->getIntakeFormula();
                $returnData[$price->getProduct()->getId()]['intakePrice'] = $newPrice;
                $returnData[$price->getProduct()->getId()]['salesFormula'] = $formula->getSalesFormula();
                $returnData[$price->getProduct()->getId()]['salesPrice'] = $newSalesPrices;
            }
        }

        $em = $this->intakeFormulaRepository->getEntityManager();
        $em->flush();

        return new JsonResponse($returnData);
    }
}
