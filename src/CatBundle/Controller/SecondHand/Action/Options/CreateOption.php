<?php

declare(strict_types=1);

namespace CatBundle\Controller\SecondHand\Action\Options;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Webdsign\GlobalBundle\DTO\InstantQuote\QuoteAdditionalTradeInOptionDataObject;
use Webdsign\GlobalBundle\Entity\InstantQuote\QuoteAdditionalTradeInAction;
use Webdsign\GlobalBundle\Services\InstantQuote\QuoteAdditionalTradeInActionManager;

readonly class CreateOption
{
    public function __construct(
        private FlashBagInterface $flashBag,
        private TranslatorInterface $translator,
        private RouterInterface $router,
        private QuoteAdditionalTradeInActionManager $manager,
    ) {
    }

    #[Route(
    '/second-hand/action/create-option/{quoteAdditionalTradeInAction}',
    name: 'quote_trade_in_action_option_create',
    )]
    public function __invoke(QuoteAdditionalTradeInAction $quoteAdditionalTradeInAction, Request $request)
    {
        $type = $request->request->get('type') ?? null;
        $value = $request->request->get('value') ?? null;
        $name = $request->request->get('name') ?? null;
        $description = $request->request->get('description') ?? null;

        $parameters = [
            'quoteAdditionalTradeInAction' => $quoteAdditionalTradeInAction->getId(),
        ];

        if (!in_array($type, QuoteAdditionalTradeInAction::VALID_TYPES) || (empty($value) && $type !== QuoteAdditionalTradeInAction::OPTION_ALL)) {
            $this->flashBag->add(
                'danger',
                $this->translator->trans('Geen geldige optie ingevoerd')
            );

            return new RedirectResponse(
                $this->router->generate('quote_additional_intake_action_save', $parameters),
            );
        }

        if ($type === QuoteAdditionalTradeInAction::OPTION_ALL) {
            $value = $type;
            $name = 'Geen voorwaarden';
            $description = 'Actie geldt voor alle innames';
        }

        $dto = new QuoteAdditionalTradeInOptionDataObject();
        $dto
            ->setType($type)
            ->setValue($value)
            ->setName($name)
            ->setDescription($description)
            ->setAction($quoteAdditionalTradeInAction);
        $this->manager->createOption($dto);

        return new RedirectResponse(
            $this->router->generate('quote_additional_intake_action_save', $parameters),
        );
    }
}
