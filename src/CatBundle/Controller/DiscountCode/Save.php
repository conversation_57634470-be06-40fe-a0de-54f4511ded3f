<?php

declare(strict_types=1);

namespace CatBundle\Controller\DiscountCode;

use Throwable;
use Psr\Log\LoggerInterface;
use Twig\Environment as TwigEnvironment;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Webdsign\GlobalBundle\Entity\DiscountCode;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Constraints\Callback;
use CatBundle\Form\Type\DiscountCode\DiscountCodeType;
use CatBundle\DTO\DiscountCode\DiscountCodeDataObject;
use CatBundle\Service\DiscountCode\DiscountCodeManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Validator\Context\ExecutionContextInterface;


class Save extends AbstractController
{
    public function __construct(
        private readonly TwigEnvironment $templating,
        private readonly FormFactoryInterface $formFactory,
        private readonly LoggerInterface $discountCodeLogger,
        private readonly EntityManagerInterface $entityManager,
        private readonly DiscountCodeManager $discountCodeManager,
    ) {
    }

    #[Route(
        '/discount-codes/save/{discountCode}',
        name: 'discount-code-edit',
        defaults: ['discountCode' => null]
    )]
    public function __invoke(?DiscountCode $discountCode, Request $request): Response|JsonResponse
    {
        if ($request->isXmlHttpRequest()) {
            $results = $this->discountCodeManager->productSearcher($request->request->all('discount_code'));

            return new JsonResponse([
                'content' => $this->templating->render(
                    '@CatBundle/DiscountCode/search_product.html.twig',
                    [
                        'results' => $results,
                    ]
                )
            ]);
        }

        $dto = $this->discountCodeManager->get($discountCode);

        $form = $this->formFactory->create(
            DiscountCodeType::class,
            $dto,
            [
                'specs_profile_specifications_searcher' => $this->discountCodeManager->specsProfileSpecificationsSearcher(),
                'specs_article_specification_searcher' => $this->discountCodeManager->specsArticleSpecificationSearcher(),
                'constraints' => [
                    new Callback(
                        fn (
                            DiscountCodeDataObject $dto,
                            ExecutionContextInterface $context,
                            mixed $payload
                        ) => DiscountCodeType::validateProfileWithArticleSpecValue(
                            $dto,
                            $context,
                            $discountCode !== null,
                            $this->entityManager->getRepository(DiscountCode::class)
                        )
                    ),
                ]
            ]
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $dataObject = $form->getData();
            try {
                $discountCode = $this->discountCodeManager->save($dataObject);
            } catch(Throwable $e) {
                if ($dto->id !== null) {
                    $message = 'Er is iets misgegaan met aanpassen kortingscode %s (%s)';
                    $this->addFlash('error', sprintf($message, $dto->code, $e->getMessage()));
                    $this->discountCodeLogger->error($message);

                    return $this->redirectToRoute(
                        'discount-code-edit',
                        ['discountCode' => $dto->id]
                    );
                }

                $message = 'Er is iets misgegaan met aanmaken van kortingscode %s (%s)';
                $this->addFlash('danger', sprintf($message, $dto->code, $e->getMessage()));
                $this->discountCodeLogger->error($message);

                return $this->redirectToRoute(
                    'discount-code-edit',
                    ['discountCode' => null]
                );
            }

            $message = 'Er is iets misgegaan met aanmaken van kortingscode %s%s';
            $affix = '';
            $messageType = 'danger';

            if ($discountCode instanceof DiscountCode) {
                $message = 'Kortingscode %s is succesvol %s';
                $messageType = 'success';
                $affix = $dto->id === null ? 'aangemaakt' : 'aangepast';
            }

            $this->addFlash($messageType, sprintf($message, $dataObject->code, $affix));

            return $this->redirectToRoute(
                'discount-code-edit',
                ['discountCode' => $discountCode->getDiscountId()]
            );
        }

        return $this->render(
            '@Cat/DiscountCode/save.html.twig', [
                'discountCode' => $discountCode,
                'form' => $form->createView(),
            ],
        );
    }
}
