<?php

declare(strict_types=1);

namespace CatBundle\Controller\Event\Event;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Twig\Environment;
use Twig\Error\Error;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\Entity\Event\CategoryRepository;
use Webdsign\GlobalBundle\Entity\Event\Instructor;
use Webdsign\GlobalBundle\Entity\Event\InstructorRepository;
use Webdsign\GlobalBundle\Entity\Event\Location;
use Webdsign\GlobalBundle\Entity\Event\LocationRepository;
use Webdsign\GlobalBundle\Traits\UserTrait;

class Overview
{
    use UserTrait;

    public function __construct(
        private readonly TokenStorageInterface $tokenStorage,
        private readonly Environment $templating,
        private readonly InstructorRepository $instructorRepository,
        private readonly LocationRepository $locationRepository,
        private readonly CategoryRepository $categoryRepository
    ) {
    }

    #[Route('/events/event', name:'events_event_overview', methods: ['GET'])]
    #[Route('/events/soft-delete', name:'events_softdelete_overview', defaults:['screen' => 'softdelete'], methods: ['GET'])]
    public function __invoke(
        $screen = 'event'
    ): ?Response
    {
        $this->hasAccess($this->tokenStorage, [EventConstants::RESOURCE_EVENTS]);

        $instructors = $this->instructorRepository->findAll();
        usort($instructors, static fn (Instructor $a, Instructor $b) => $a->getFullName() <=> $b->getFullName());
        array_unshift($instructors, null);

        $locations = $this->locationRepository->findAll();
        usort($locations, static fn (Location $a, Location $b) => $a->getName() <=> $b->getName());
        array_unshift($locations, null);

        $categories = $this->categoryRepository->findAll();
        $categories = array_map(static fn ($category) => $category->getName(), $categories);
        sort($categories);
        array_unshift($categories, null);

        $provinces = $this->locationRepository->provinces();
        usort($provinces, static fn (string $a, string $b) => $a <=> $b);
        array_unshift($provinces, null);

        return new Response($this->templating->render('@Cat/Event/Event/overview.html.twig', [
            'instructors' => $instructors,
            'locations' => $locations,
            'categories' => $categories,
            'provinces' => $provinces,
            'screen' => $screen
        ]));
    }
}
