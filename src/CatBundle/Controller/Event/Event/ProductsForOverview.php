<?php

declare(strict_types=1);

namespace CatBundle\Controller\Event\Event;

use CatBundle\DTO\Event\OverviewFilter;
use CatBundle\Service\ProductUrlService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\ValueResolver;
use Symfony\Component\Routing\Attribute\Route;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\Entity\Event\EventInstructorRepository;
use Webdsign\GlobalBundle\Entity\Event\Product;
use Webdsign\GlobalBundle\Entity\Event\ProductRepository;
use Webdsign\GlobalBundle\Entity\Event\RegistrationRepository;
use Webdsign\GlobalBundle\Entity\ProductRepository as NormalProductRepository;
use Webdsign\GlobalBundle\Entity\User;

class ProductsForOverview extends AbstractController
{
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly NormalProductRepository $normalProductRepository,
        private readonly RegistrationRepository $registrationRepository,
        private readonly ProductUrlService $productUrlService,
        private readonly EventInstructorRepository $eventInstructorRepository,
    ) {
        $this->productUrlService->setLanguage(null);
    }

    #[Route('/events/products-for-overview', methods: ['GET'])]
    public function __invoke(
        #[ValueResolver('event.overview.filter')] OverviewFilter $overviewFilter,
    ): Response {
        /** @var User $user */
        $user = $this->getUser();
        $canEdit = $user->hasAccess(EventConstants::RESOURCE_ADD_EVENTS);
        $canCancel = $user->hasAccess(EventConstants::RESOURCE_CANCEL_EVENTS);

        $eventProducts = $this->productRepository->getForOverview($overviewFilter);
        $normalProducts = $this->normalProductRepository->findForEventProducts($eventProducts);
        $registrationCounts = $this->registrationRepository->getReservedRegistrationsForProducts($eventProducts);

        $products = array_map(function (Product $product) use ($canEdit, $canCancel, $normalProducts, $registrationCounts): array {
            $this
                ->productUrlService
                ->setName($product->getEvent()->getTitle())
                ->setId($product->getEvent()->getId());

            $product->setProduct($normalProducts[$product->getId()] ?? null);

            return [
                'id' => $product->getId(),
                'title' => $product->getEvent()->getTitle(),
                'url' => $this->generateUrl('events_event_product_view', ['product' => $product->getId()]),
                'external' => $product->getExternalLink() !== null,
                'date' => sprintf('<span style="display: none">%s</span><span>%s</span>', $product->getStartDateAndTime()->format('U'), $product->getStartDateAndTime()->format('d-m-Y H:i')),
                'instructor' =>  $this->eventInstructorRepository->getInstructorNamesForEvent($product->getEvent()),
                'location' => $product->getLocation()->getName(),
                'price' => $product->getPrice(),
                'reservedRegistrations' => $registrationCounts[$product->getId()] ?? 0,
                'maximumCapacity' => $product->getMaximumCapacity(),
                'editUrl' => $canEdit
                    ? $this->generateUrl('events_event_save', ['product' => $product->getId()])
                    : null,
                'copyUrl' => $canEdit
                    ? $this->generateUrl('events_event_copy', ['product' => $product->getId()])
                    : null,
                'websiteUrl' => $canEdit
                    ? $this->productUrlService->generateEventsUrl(true)
                    : null,
                'cancelUrl' => $canCancel
                    ? $this->generateurl('events_event_delete', ['product' => $product->getId()])
                    : null,
            ];
        }, $eventProducts);
        return $this->json($products);
    }
}
