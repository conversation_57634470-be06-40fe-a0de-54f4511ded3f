<?php

declare(strict_types=1);

namespace CatBundle\Controller\Event\Category;

use CatBundle\Form\Type\Event\CategoryType;
use Doctrine\ORM\EntityManagerInterface;

use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Twig\Environment;
use Twig\Error\Error;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\Entity\Event\Category;
use Webdsign\GlobalBundle\Traits\UserTrait;

class Save
{
    use UserTrait;

    private TokenStorageInterface $tokenStorage;
    private Environment $templating;
    private FormFactoryInterface $formFactory;
    private EntityManagerInterface $entityManager;
    private FlashBagInterface $flashBag;

private RouterInterface $router;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        Environment $templating,
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        FlashBagInterface $flashBag,
        RouterInterface $router
    ) {
        $this->tokenStorage = $tokenStorage;
        $this->templating = $templating;
        $this->formFactory = $formFactory;
        $this->entityManager = $entityManager;
        $this->flashBag = $flashBag;
        $this->router = $router;
    }

    /**
     * @throws Error
     */
    public function __invoke(?Category $category, Request $request): ?Response
    {
        $this->hasAccess($this->tokenStorage, [
            EventConstants::RESOURCE_EVENTS,
            EventConstants::RESOURCE_ADD_EVENTS,
            EventConstants::RESOURCE_ADD_CATEGORIES,
        ]);

        $form = $this->formFactory->create(CategoryType::class, $category);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $category = $form->getData();
            $this->entityManager->persist($category);
            $this->entityManager->flush();

            $this->flashBag->add(
                'success',
                'De categorie is opgeslagen'
            );

            return new RedirectResponse($this->router->generate('events_category_overview'));
        }

        return new Response($this->templating->render('@Cat/Event/Category/save.html.twig', [
            'category' => $category,
            'form' => $form->createView(),
        ]));
    }
}
