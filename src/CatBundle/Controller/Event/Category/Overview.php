<?php

declare(strict_types=1);

namespace CatBundle\Controller\Event\Category;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Twig\Environment;
use Twig\Error\Error;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\Entity\Event\CategoryRepository;
use Webdsign\GlobalBundle\Traits\UserTrait;

class Overview
{
    use UserTrait;

    private TokenStorageInterface $tokenStorage;
    private Environment $templating;
    private CategoryRepository $categoryRepository;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        Environment $templating,
        CategoryRepository $categoryRepository
    ) {
        $this->tokenStorage = $tokenStorage;
        $this->templating = $templating;
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * @throws Error
     */
    public function __invoke(): ?Response
    {
        $this->hasAccess($this->tokenStorage, [
            EventConstants::RESOURCE_EVENTS,
            EventConstants::RESOURCE_ADD_EVENTS,
            EventConstants::RESOURCE_ADD_CATEGORIES,
        ]);

        return new Response($this->templating->render('@Cat/Event/Category/overview.html.twig', [
            'categories' => $this->categoryRepository->findAll(),
        ]));
    }
}
