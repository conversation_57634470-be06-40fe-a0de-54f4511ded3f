<?php

declare(strict_types=1);

namespace CatBundle\Controller\Event\Category;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Throwable;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\Entity\Event\Category;
use Webdsign\GlobalBundle\Traits\UserTrait;

class Delete
{
    use UserTrait;

    private TokenStorageInterface $tokenStorage;
    private EntityManagerInterface $entityManager;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        EntityManagerInterface $entityManager
    ) {
        $this->tokenStorage = $tokenStorage;
        $this->entityManager = $entityManager;
    }

    /**
     * @todo
     * - controleren of een categorie nog actieve events bevat
     */
    public function __invoke(Category $category): JsonResponse
    {
        $this->hasAccess($this->tokenStorage, [
            EventConstants::RESOURCE_EVENTS,
            EventConstants::RESOURCE_ADD_EVENTS,
            EventConstants::RESOURCE_ADD_CATEGORIES,
            EventConstants::RESOURCE_CANCEL_EVENTS,
        ]);

        $httpCode = Response::HTTP_OK;

        try {
            $this->entityManager->remove($category);
            $this->entityManager->flush();
        } catch (Throwable) {
            $httpCode = Response::HTTP_BAD_REQUEST;
        }

        return new JsonResponse(null, $httpCode);
    }
}
