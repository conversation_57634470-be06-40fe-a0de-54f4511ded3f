<?php

declare(strict_types=1);

namespace CatBundle\Controller\Event\Theme;

use CatBundle\Form\Type\Event\ThemeType;
use Doctrine\ORM\EntityManagerInterface;

use Symfony\Component\Routing\RouterInterface;
use Twig\Environment;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Twig\Error\Error;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\Entity\Event\Theme;
use Webdsign\GlobalBundle\Entity\Tag;
use Webdsign\GlobalBundle\Traits\UserTrait;

class Save
{
    use UserTrait;

    private TokenStorageInterface $tokenStorage;
    private Environment $templating;
    private FormFactoryInterface $formFactory;
    private EntityManagerInterface $entityManager;
    private FlashBagInterface $flashBag;

private RouterInterface $router;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        Environment $templating,
        FormFactoryInterface $formFactory,
        EntityManagerInterface $entityManager,
        FlashBagInterface $flashBag,
        RouterInterface $router
    ) {
        $this->tokenStorage = $tokenStorage;
        $this->templating = $templating;
        $this->formFactory = $formFactory;
        $this->entityManager = $entityManager;
        $this->flashBag = $flashBag;
        $this->router = $router;
    }

    /**
     * @throws Error
     */
    public function __invoke(?Theme $theme, Request $request): ?Response
    {
        $this->hasAccess($this->tokenStorage, [
            EventConstants::RESOURCE_EVENTS,
            EventConstants::RESOURCE_ADD_EVENTS,
        ]);

        $form = $this->formFactory->create(ThemeType::class, $theme);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $theme = $form->getData();
            $this->entityManager->persist($theme);

            if (!$theme->getTag() instanceof Tag) {
                $tag = new Tag();
                $tag
                    ->setValue($theme->getName() . ' events')
                    ->setTagTypeId(Tag::TYPE_ID_MENU);
                $this->entityManager->persist($tag);
                $theme->setTag($tag);
            }

            $this->entityManager->flush();

            $this->flashBag->add(
                'success',
                'Het thema is opgeslagen'
            );

            return new RedirectResponse($this->router->generate('events_theme_overview'));
        }

        return new Response($this->templating->render('@Cat/Event/Theme/save.html.twig', [
            'theme' => $theme,
            'form' => $form->createView(),
        ]));
    }
}
