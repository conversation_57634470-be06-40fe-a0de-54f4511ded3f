<?php

declare(strict_types=1);

namespace CatBundle\Controller\SupplierBackorders;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;
use Webdsign\GlobalBundle\Entity\PurchaseOrderRepository;

class Export extends AbstractController
{
    private const array EXCLUDED_COLUMNS = [
        'id',
        'description',
        'flags',
        'reference',
        'checkDate',
        'flagsArtikel',
        'leverancierId',
    ];

    public function __construct(
        private readonly PurchaseOrderRepository $purchaseOrderRepository,
    ) {
    }

    #[Route('/supplier-backorders/export/', name: 'supplier_backorders_export', methods: ['POST', 'GET'])]
    public function __invoke(Request $request): Response
    {
        $ids = json_decode($request->getContent());
        $purchaseOrders = $this->purchaseOrderRepository->forSupplierBackordersOverview($ids);

        foreach ($purchaseOrders as $key => $purchaseOrder) {
            foreach (self::EXCLUDED_COLUMNS as $column) {
                unset($purchaseOrders[$key][$column]);
            }
        }

        $response = new StreamedResponse(function () use ($purchaseOrders) {
            $out = fopen('php://output', 'w');
            if (!empty($purchaseOrders)) {
                $headers = array_keys(current($purchaseOrders));
                $replacements = [
                    'amountInOrders' => 'customer backorders',
                    'scannedAmount' => 'amount scanned',
                    'splitScannedAmount' => 'amount kit split scanned',
                    'ean2' => 'supplier id',
                    'description' => 'remark',
                ];

                $headers = array_map(function ($value) use ($replacements) {
                    return array_key_exists($value, $replacements) ? $replacements[$value] : $value;
                }, $headers);

                fputcsv($out, $headers);
            }

            foreach ($purchaseOrders as $purchaseOrder) {
                $purchaseOrder['orderDate'] = $purchaseOrder['orderDate'] ? $purchaseOrder['orderDate']->format('d-m-Y') : '';
                $purchaseOrder['deliveryDate'] = $purchaseOrder['deliveryDate'] ? $purchaseOrder['deliveryDate']->format('d-m-Y') : '';
                $purchaseOrder['price'] = number_format((float)$purchaseOrder['price'], 2, ',', '');

                fputcsv($out, $purchaseOrder);
            }
            fclose($out);
        });

        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="leveranciers-besteld.csv"');

        return $response;
    }
}
