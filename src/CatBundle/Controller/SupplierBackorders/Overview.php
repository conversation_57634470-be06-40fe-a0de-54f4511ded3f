<?php

declare(strict_types=1);

namespace CatBundle\Controller\SupplierBackorders;

use DateTime;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Webdsign\GlobalBundle\Entity\PurchaseOrder;
use Webdsign\GlobalBundle\Entity\PurchaseOrderRepository;
use Webdsign\GlobalBundle\Entity\SupplierGroup;
use Webdsign\GlobalBundle\Entity\SupplierGroupRepository;
use Webdsign\GlobalBundle\Entity\WexMaingroup;
use Webdsign\GlobalBundle\Entity\WexMaingroupRepository;

class Overview extends AbstractController
{
    public function __construct(
        private readonly PurchaseOrderRepository $purchaseOrderRepository,
        private readonly SupplierGroupRepository $supplierGroupRepository,
        private readonly WexMaingroupRepository $wexMaingroupRepository,
    ) {
    }

    #[Route('/supplier-backorders/{interval}/{supplierGroup}/{sortOption}/{eigCategory}/{deliveryDate}', name: 'supplier_backorders')]
    public function __invoke(
        Request $request,
        int $interval = 0,
        SupplierGroup $supplierGroup = null,
        string $sortOption = null,
        string $deliveryDate = null,
        WexMaingroup $eigCategory = null,
    ): Response {
        $result = [];
        if ($request->isXmlHttpRequest()) {
            $data = json_decode($request->getContent());
            if (count($data->ids) > 0) {
                $purchaseOrders = $this->purchaseOrderRepository->findBy(['id' => $data->ids]);

                foreach ($purchaseOrders as $purchaseOrder) {
                    if ($data->type === 'check-date') {
                        $purchaseOrder->setCheckDate((new DateTime($data->date))->format('d-m-Y'));
                    } elseif ($data->type === 'delivery-date') {
                        $purchaseOrder->setDeliveryDate(new DateTime($data->date));
                    }
                }
            } else {
                $purchaseOrder = $this->purchaseOrderRepository->findOneBy(['id' => $data->id]);

                if ($purchaseOrder instanceof PurchaseOrder) {
                    if ($data->type === 'check-date') {
                        $purchaseOrder->setCheckDate((new DateTime($data->date))->format('d-m-Y'));
                    } elseif ($data->type === 'delivery-date') {
                        $purchaseOrder->setDeliveryDate(new DateTime($data->date));
                    }
                }
            }

            $this->purchaseOrderRepository->getEntityManager()->flush();
        } else {
            $result = $this->purchaseOrderRepository->forSupplierBackordersOverview(
                interval: $interval,
                supplierGroup: $supplierGroup,
                sortOption: $sortOption,
                deliveryDate: $deliveryDate,
                eigCategory: $eigCategory,
            );
            $suppliers = $this->supplierGroupRepository->findBy([], ['name' => 'ASC']);
            $eigCategories = $this->wexMaingroupRepository->findBy([], ['description' => 'ASC']);
        }

        return $this->render('@Cat/SupplierBackorders/overview.html.twig', [
            'backorders' => $result,
            'suppliers' => $suppliers ?? [],
            'interval' => $interval,
            'selectedSupplierGroup' => $supplierGroup,
            'sortOption' => $sortOption,
            'deliveryDate' => $deliveryDate,
            'eigCategories' => $eigCategories ?? [],
            'eigCategory' => $eigCategory,
        ]);
    }
}
