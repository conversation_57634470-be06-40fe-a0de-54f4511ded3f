<?php

declare(strict_types=1);

namespace CatBundle\Controller\ClaimsAndFees;

use CatBundle\Service\ClaimsAndFees\ProductFinder;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class FindProducts
{
    public function __construct(private readonly ProductFinder $productFinder)
    {}

    public function __invoke(Request $request): JsonResponse
    {
        // @TODO: Refactor non-scalar values
        $tagIds = $request->query->all()['tags'] ?? [];
        $groups = $request->query->all()['groups'] ?? [];

        $products = $this->productFinder->findForTagsAndGroups($tagIds, $groups);

        return new JsonResponse($products);
    }
}
