<?php

namespace CatBundle\Controller\ClaimsAndFees\Report;

use CatBundle\Form\DataObject\ClaimsAndFees\Report\FilterDataObject;
use CatBundle\Form\Type\ClaimsAndFees\Report\FilterType;
use CatBundle\Service\ClaimsAndFeesHelper;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Twig\Environment as TwigEnvironment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;
use Webdsign\GlobalBundle\Entity\ClaimsAndFees;
use Webdsign\GlobalBundle\Entity\ClaimsAndFeesReportsRepository;
use Webdsign\GlobalBundle\Entity\ClaimsAndFeesRepository;

class Index
{
    public function __construct(
        private readonly TwigEnvironment $templating,
        private readonly FormFactoryInterface $formFactory,
        private readonly ClaimsAndFeesHelper $claimsAndFeesHelper,
        private readonly ClaimsAndFeesRepository $claimsAndFeesRepository,
        private readonly ClaimsAndFeesReportsRepository $claimsAndFeesReportsRepository
    ) {
    }

    /**
     * @throws RuntimeError
     * @throws SyntaxError
     * @throws LoaderError
     */
    public function __invoke(Request $request): Response
    {
        $claimsAndFeesReports = [];

        $filter = new FilterDataObject();
        $form = $this->formFactory->create(
            FilterType::class,
            $filter,
            $this->claimsAndFeesHelper->getOptions()
        );

        $form->handleRequest($request);

        $claimAndFeeId = (int)$request->query->get('claimsAndFee') ?? null;

        if (!empty($claimAndFeeId)) {
            $claimAndFee = $this->claimsAndFeesRepository->find($claimAndFeeId);

            if ($claimAndFee instanceof ClaimsAndFees) {
                $claimsAndFeesReports = $this->claimsAndFeesReportsRepository->containingClaimAndFee($claimAndFee);
            }
        } else {
            $claimsAndFeesReports = $this->claimsAndFeesHelper->getClaimsAndFeesReports($filter);
        }

        return new Response($this->templating->render('@Cat/ClaimsAndFees/Reports/list.html.twig', [
            'filterForm' => $form->createView(),
            'reports' => $claimsAndFeesReports,
        ]));
    }
}
