<?php

declare(strict_types=1);

namespace CatBundle\Controller\Api\OpenAI;

use CatBundle\Service\OpenAI\ContentRewriter;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use Throwable;
use Webdsign\GlobalBundle\Entity\Product;

#[Route('/api/open-ai/content/rewrite/{product}', name: 'open-ai-content-rewrite-product', methods: ['POST'])]
readonly class ContentRewriteProduct
{
    public function __construct(
        private ContentRewriter $contentRewriter
    ) {
    }

    public function __invoke(
        Product $product,
        Request $request
    ): JsonResponse {
        $file = $request->files->get('file');
        $prompt = $request->request->get('prompt');
        $text = $request->request->get('text');

        if ($prompt !== null) {
            $this->contentRewriter->setRole($prompt);
        }

        $this->contentRewriter->addInput([
            'file' => $file,
            'product' => $product,
            'text' => $text,
        ]);

        try {
            return new JsonResponse($this->contentRewriter->execute());
        } catch (Throwable $e) {
            return new JsonResponse([
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
