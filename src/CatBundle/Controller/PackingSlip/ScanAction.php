<?php

declare(strict_types=1);

namespace CatBundle\Controller\PackingSlip;

use CatB<PERSON>le\DTO\PackingSlip\ResponseDTO;
use CatB<PERSON>le\DTO\PackingSlip\ScanActionDTO;
use Doctrine\DBAL\Driver\Exception;
use CatBundle\Service\PackingSlip\Scanner;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class ScanAction
{
    private Scanner $scanner;

    public function __construct(Scanner $scanner)
    {
        $this->scanner = $scanner;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function __invoke(Request $request): JsonResponse
    {
        $transitCartId = (int)$request->query->get('transitCartId');

        $scanDto = new ScanActionDTO();
        $scanDto
            ->setSlipIds([$request->query->get('slipId')])
            ->setBarcode($request->query->get('barcode'))
            ->setTransitCartId($transitCartId)
            ->setDoScan(filter_var($request->query->get('scan'), FILTER_VALIDATE_BOOLEAN))
            ->setAmount((int)$request->query->get('amount') ?? 1)
            ->setProductCode($request->query->get('productCode'))
            ->setCodeType($request->query->get('codeType') ?? Scanner::CODE_TYPE_EAN);

        //remember chosen cart for next scan
        $request->getSession()->set('packingSlipLineCartId', $transitCartId);
        $this->scanner->setSessionId($request->getSession()->getId());

        /*** @var ResponseDTO $respnse */
        $response = $this->scanner->scan($scanDto);
        return new JsonResponse($response->toArray());
    }
}
