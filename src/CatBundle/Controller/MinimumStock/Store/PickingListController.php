<?php

declare(strict_types=1);

namespace CatBundle\Controller\MinimumStock\Store;

use CatBundle\Service\MinimumStock\Store\ListGenerator;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Twig\Environment as TwigEnvironment;
use Webdsign\GlobalBundle\Entity\LocationCompartmentNode;
use Webdsign\GlobalBundle\Entity\LocationCompartmentNodeRepository;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Services\Route as RouteService;

/**
 * @package CatBundle\Controller\MinimumStock
 */
class PickingListController
{
    public function __construct(
        private readonly TwigEnvironment $templating,
        private readonly EntityManagerInterface $entityManager,
        private readonly ListGenerator $listGenerator
    ) {
    }

    /**
     * @Route("/minimum-stock/store/picking-list/{location}", name="minimum_stock_picking_list")
     */
    public function __invoke(StockLocation $location): Response
    {
        $list = $this->listGenerator->generateList($location);
        $amounts = [];
        $productIds = array_map(function (array $item) use (&$amounts) {
            $pId = $item['productId'];
            $amounts[$pId]['inStorage'] = $item['stockInStorage'];
            $amounts[$pId]['reserved'] = $item['reserved'];
            $amounts[$pId]['inLocation'] = $item['stock'];
            $amounts[$pId]['minimalStock'] = $item['minimalStock'];
            return $pId;
        }, $list);

        $routeService = new RouteService($this->entityManager);
        $route = $routeService->getForProductIds($productIds, [$location->getMinimumStockTopUpLocation()]);

        $productRepo = $this->entityManager->getRepository(Product::class);
        /** @var LocationCompartmentNodeRepository $nodeRepo */
        $nodeRepo = $this->entityManager->getRepository(LocationCompartmentNode::class);

        $data = [];
        foreach ($route as $productId => $item) {
            $product = $productRepo->find($productId);
            $nodes = $nodeRepo->findByProductAndStockLocation(
                $product,
                [$location, $location->getMinimumStockTopUpLocation()]
            );

            $nodeString = array_map(function (LocationCompartmentNode $node) {
                return $node->getLocation()->getCompartment() . '-' . $node->getSubCompartment();
            }, $nodes);
            $nodeString = implode(', ', $nodeString);

            $data[] = [
                'product' => $product,
                'amount' => $amounts[$productId]['minimalStock'] - $amounts[$productId]['inLocation'],
                'location' => $item['location'],
                'inStorage' => $amounts[$productId]['inStorage'],
                'reserved' => $amounts[$productId]['reserved'],
                'inLocation' => $amounts[$productId]['inLocation'],
                'minimalStock' => $amounts[$productId]['minimalStock'],
                'nodes' => $nodeString,
                'pos' => $item['pos'],
            ];
        }

        usort($data, function ($a, $b) {
            return $a['pos'] < $b['pos'];
        });

        return new Response($this->templating->render('@Cat/MinimumStock/Store/picking-list.html.twig', [
            'location' => $location,
            'route' => $data,
        ]));
    }
}
