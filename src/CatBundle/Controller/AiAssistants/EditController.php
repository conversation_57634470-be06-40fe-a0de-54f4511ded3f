<?php

declare(strict_types=1);

namespace CatBundle\Controller\AiAssistants;

use CatB<PERSON>le\Form\Type\AiAssistant\EditType;
use CatBundle\Service\OpenAI\ContentRewriter;
use CatBundle\Service\OpenAI\ContentWriter;
use CatBundle\Service\OpenAI\FaqWriter;
use CatBundle\Service\OpenAI\ProductImagesDescriber;
use CatBundle\Service\OpenAI\SpecWriter;
use Doctrine\ORM\EntityManagerInterface;
use Jfcherng\Diff\DiffHelper;
use Jfcherng\Diff\Factory\RendererFactory;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Translation\TranslatableMessage;
use Throwable;
use Webdsign\GlobalBundle\Constant\OpenAiConstants;
use Webdsign\GlobalBundle\Entity\AiAssistant;
use Webdsign\GlobalBundle\Entity\Log;
use Webdsign\GlobalBundle\Traits\UserTrait;

#[Route('/ai-assistants/edit/{aiAssistant}', name: 'ai-assistants-edit')]
class EditController extends AbstractController
{
    use UserTrait;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly TokenStorageInterface $tokenStorage
    ) {
    }

    public function __invoke(AiAssistant $aiAssistant, Request $request): Response
    {
        $this->hasAccess($this->tokenStorage, [OpenAiConstants::RESOURCE_EDIT_ASSISTANTS]);

        $form = $this->createForm(EditType::class, $aiAssistant);
        $form->handleRequest($request);

        $testUrls = [
            FaqWriter::class => '/api/open-ai/faq/',
            ContentWriter::class => '/api/open-ai/content/',
            ContentRewriter::class => '/api/open-ai/content/rewrite/',
            SpecWriter::class => '/api/open-ai/spec/',
            ProductImagesDescriber::class => '/api/open-ai/images-product/',
        ];

        $logRepository = $this->entityManager->getRepository(Log::class);

        $logEntities = $logRepository->findBy([
            'type' => 'AiAssistant',
            'typeId' => $aiAssistant->getId(),
        ], [
            'tstamp' => 'desc',
        ]);

        $htmlRenderer = RendererFactory::make('Inline', [
            'detailLevel' => 'word',
            'lineNumbers' => false,
            'ignoreLineEnding' => true,
            'language' => [
                'eng',
                [
                    'old_version' => 'Oud',
                    'new_version' => 'Nieuw',
                    'differences' => 'Verschil',
                ]
            ],
        ]);

        $logs = array_map(static function (Log $log) use ($htmlRenderer) {
            try {
                $message = json_decode($log->getMessage(), false, 512, JSON_THROW_ON_ERROR);
                $field = $message->replace->name;
                $from = $message->replace->from;
                $to = $message->replace->to;

                $jsonDiff = DiffHelper::calculate($from, $to, 'Json');
                $diff = $htmlRenderer->renderArray(json_decode($jsonDiff, true, 512, JSON_THROW_ON_ERROR));

                return [
                    'tstamp' => $log->getTstamp(),
                    'creator' => $log->getCreator(),
                    'field' => $field,
                    'from' => $from,
                    'to' => $to,
                    'diff' => $diff,
            ];
            } catch (Throwable) {
                return false;
            }
        }, $logEntities);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();
            $this->addFlash('success', 'ai_assistants.form.success');

            return $this->redirectToRoute('ai-assistants-list');
        }

        return $this->render('@Cat/AiAssistants/edit.html.twig', [
            'page_title' => new TranslatableMessage('ai_assistants.title.edit'),
            'form' => $form->createView(),
            'logs' => $logs,
            'diff_css' => DiffHelper::getStyleSheet(),
            'test_url' => $testUrls[$aiAssistant->getClass()] ?? null,
        ]);
    }
}
