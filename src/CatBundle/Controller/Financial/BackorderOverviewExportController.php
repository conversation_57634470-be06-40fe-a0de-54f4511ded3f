<?php

declare(strict_types=1);

namespace CatBundle\Controller\Financial;

use CatBundle\Form\DataObject\Financial\BackorderExportDataObject;
use CatBundle\Form\Type\Financial\BackorderExportType;
use CatBundle\Service\Financial\BackorderExporter;
use League\Csv\CannotInsertRecord;
use League\Csv\Exception;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Twig\Environment as TwigEnvironment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

readonly class BackorderOverviewExportController
{
    public function __construct(
        private TwigEnvironment $templating,
        private FormFactoryInterface $formFactory,
        private BackorderExporter $backorderExporter,
    ) {
    }

    /**
     * @throws SyntaxError
     * @throws CannotInsertRecord
     * @throws RuntimeError
     * @throws LoaderError
     * @throws Exception
     */
    #[Route('/financial/export/backorder', name: 'financial_export_backorders')]
    public function __invoke(Request $request): Response
    {
        $data = new BackorderExportDataObject();

        $form = $this->formFactory->create(BackorderExportType::class, $data);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $csv = $this->backorderExporter->createExport($data);

            $disposition = HeaderUtils::makeDisposition(
                HeaderUtils::DISPOSITION_ATTACHMENT,
                'backorder-export-' . $data->getTo()->format('d-m-Y') . '.csv'
            );

            return new Response(
                $csv->toString(),
                Response::HTTP_OK,
                [
                    'Content-Encoding' => 'none',
                    'Content-Type' => 'text/csv; charset=UTF-8',
                    'Content-Disposition' => $disposition,
                    'Content-Description' => 'File Transfer',
                ]
            );
        }

        return new Response($this->templating->render('@Cat/Financial/backorder_export.html.twig', [
            'form' => $form->createView(),
        ]));
    }
}
