<?php

declare(strict_types=1);

namespace CatBundle\Controller\PurchaseOrder;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[AsController]
class NavigationController extends AbstractController
{
    protected const array GROUPS = [
        [
            'items' => [
                [
                    'title' => 'purchase-order.navigation.add_price',
                    'path' => 'purchase_order_index',
                    'access' => '',
                    'active_paths' => [
                        'purchase_order_index',
                    ],
                ],
            ],
        ],
    ];

    #[Route('/purchase-order/_navigation', name: 'purchase_order_navigation')]
    public function __invoke(Request $request): Response
    {
        $navigationGroups = self::GROUPS;

        return $this->render('@Cat/PurchaseOrder/navigation.html.twig', [
            'groups' => $navigationGroups,
            'currentPath' => $request->query->get('currentPath'),
        ]);
    }
}
