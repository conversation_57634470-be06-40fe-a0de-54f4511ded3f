<?php

namespace CatBundle\Resources\Pdf;

use Webdsign\GlobalBundle\Resources\Pdf\AbstractPdf;

class OrderLabelPdf extends AbstractPdf
{
    public function __construct($orderNumber)
    {
        parent::__construct('L', 'mm', [89,36]);
        $this->AddPage();
        $this->SetMargins(0, 0, 0);
        $this->code128(5, 5, $orderNumber, 80, 20);
        $this->SetFont('OpenSans','B',14);
        $this->Text(25,32, $orderNumber);
    }
}