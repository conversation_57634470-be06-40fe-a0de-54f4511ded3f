{% extends webdsign.templates.cat_full %}

{% block body %}
<link rel="stylesheet" href="{{ asset('cat/css/pda.css') }}">
<h1 class="text-center">Opruimen - Locatie scannen</h1>
<div class="menu text-pda">
    <a class="float-left bg-blue col-6 d-inline-block" href="{{ path('PDA-transitbakken', { transitCart: transitCartBarcode }) }}">Terug</a>
    <a class="float-right bg-red col-6 d-inline-block" onclick="window.location.reload()">Ververs</a>
</div>

<div class="inputHolder bg-darkblue col-12 text-pda">
    <div class="col-12 text-center d-inline-block">
        {{ listItem.stock.product.name }}
    </div>

    <form class="col-12 d-inline-block mt-3" id="locationForm" onsubmit="return store(event)" autocomplete="off"
          action="/pda/opruimen/{{ list.id }}/store/::location::/products" method="post">
        <input type="text" id="locatie" name="vak_barcode" data-options="{{ compartments|join(',') }}"
               placeholder="Locatie"
               class="form-control" autofocus>
    </form>
</div>

<div class="row mt-5" id="blocks">
    {% if locations|length > 0 %}
        <div class="col-12 text-center">
            <h2 class="text-dark">Locaties:</h2>
        </div>
        {% for location in locations %}
        <div class="col-4 d-inline-block">
            {% if location.compartment|first is same as('B') %} {# B = boven #}
                {% set bg = 'red' %}
            {% else %}
                {% set bg = location.stock_location.locationNodeColor %}
            {% endif %}

            <a class="bg-{{ bg }}">
                {{ location.compartment }}
            </a>
        </div>
        {% endfor %}
    {% endif %}

    <div class="col-4 d-inline-block">
        <a href="{{ admin }}/pdascan/stellingenv2.php?returnUrl={{ url('PDA-home') }}opruimen/{{ transitCartBarcode }}/location"
           class="inputHolder bg-orange text-center text-pda">
            Stellingen
        </a>
    </div>

    <div class="col-4 d-inline-block">
        <a href="{{ admin }}/pdascan/legeBakken.php"
           class="inputHolder bg-orange text-center text-pda">
            Lege bakken
        </a>
    </div>
</div>
{% include '@Cat/pda/TransitVoorraad/scannedProducts.html.twig' with {'scanned': scanned, 'location': 0 } %}

<input type="hidden" id="locations" value="{{ compartments is defined ? compartments|join(',') : 'none' }}" data-amount="{{ compartments|length }}">
{% endblock %}




