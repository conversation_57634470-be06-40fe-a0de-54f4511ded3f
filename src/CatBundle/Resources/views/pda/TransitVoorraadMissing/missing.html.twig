{% extends webdsign.templates.cat_partial %}
{% block body %}
    <div class="container-fluid">
        <div class="row">
            <h1 class="col-12 text-center">{{ list.user.username }} <small>- lijst {{ list.id }}</small></h1>
            <div class="col-12 table-responsive">
                <table class="table table-hover table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>Item ID</th>
                            <th>Voorraad ID</th>
                            <th>Barcode</th>
                            <th>Artikel ID</th>
                            <th>Artikel</th>
                            <th>Locatie</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                            <tr>
                                <td>{{ item.itemId }}</td>
                                <td>
                                    <a href="{{ admin }}/php/edit_vrd_art.php?vrd_id={{ item.stockId }}&art_id={{ item.productId }}" class="product-popup">
                                        {{ item.stockId }}
                                    </a>
                                </td>
                                <td>
                                    <a href="{{ path('transitLog') }}?barcode={{ item.barcode }}" class="product-popup">
                                        {{ item.barcode }}
                                    </a>
                                </td>
                                <td>{{ item.productId }}</td>
                                <td>
                                    <a href="{{ admin }}/php/artikel_tabs.php?id={{ item.productId }}" class="product-popup">
                                        {{ item.productName }}
                                    </a>
                                </td>
                                <td>{{ item.location }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

        </div>
    </div>
{% endblock %}
