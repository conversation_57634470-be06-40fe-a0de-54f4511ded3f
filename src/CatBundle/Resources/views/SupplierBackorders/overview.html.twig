{% extends webdsign.templates.cat_full %}
{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"/>
    <link rel="stylesheet" href="{{ asset('cat/css/backorder-overview.css') }}">
{% endblock %}
{% block body %}
    <div class="container-fluid" id="overview">
        <div class="row">
            <div class="col">
                <h1>Leveranciers - Besteld</h1>
            </div>
        </div>

        <div class="row">
            <div class="col">
                <p>* = Staat al in Besteloverzicht (nog niet besteld bij leverancier dus)</p>
                <p>
                    <select name="date-filter">
                        <option value="0">Kies interval</option>
                        <option value="2" {{ interval is same as(2) ? 'selected' : '' }}>Ouder dan 2 weken</option>
                        <option value="3" {{ interval is same as(3) ? 'selected' : '' }}>Ouder dan 3 weken</option>
                        <option value="4" {{ interval is same as(4) ? 'selected' : '' }}>Ouder dan 4 weken</option>
                        <option value="8" {{ interval is same as(8) ? 'selected' : '' }}>Ouder dan 8 weken</option>
                        <option value="-1" {{ interval is same as(-1) ? 'selected' : '' }}>Levertijd lager dan vandaag</option>
                    </select>
                    <select name="supplier-filter">
                        <option>Kies Leverancier</option>
                        {% for supplier in suppliers %}
                            {% if supplier.name is not same as ('') %}
                                <option value="{{ supplier.id }}" {{ selectedSupplierGroup is not null and selectedSupplierGroup.id is same as(supplier.id) ? 'selected' : '' }}>{{ supplier.name }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                    Levertijd: <input name="delivery-date-filter" type="date" value="{{ deliveryDate is not null ? deliveryDate|date('Y-m-d') : '' }}" />
                    EIG-categorie:
                    <select name="eig-category-filter">
                        <option>Kies categorie</option>
                        {% for category in eigCategories %}
                            <option value="{{ category.id }}" {{ eigCategory is not null and eigCategory.id is same as(category.id) ? 'selected' : '' }}>{{ category.description }}</option>
                        {% endfor %}
                    </select>
                    <button type="button" class="btn btn-primary btn-sm filter" name="apply-filters">Filter</button>
                </p>
                <p>
                    <select name="sort">
                        <option>Sorteren op</option>
                        <option value="amountInOrders" {{ sortOption is same as('amountInOrders') ? 'selected' : '' }}>Klantenbackorders</option>
                        <option value="suppliers" {{ sortOption is same as('suppliers') ? 'selected' : '' }}>Leveranciers</option>
                        <option value="orderDate" {{ sortOption is same as('orderDate') ? 'selected' : '' }}>Besteldatum</option>
                        <option value="amount" {{ sortOption is same as('amount') ? 'selected' : '' }}>Aantal producten in backorder</option>
                        <option value="price" {{ sortOption is same as('price') ? 'selected' : '' }}>Inkoopwaarde</option>
                        <option value="eig" {{ sortOption is same as('eig') ? 'selected' : '' }}>EIG-categorie</option>
                    </select>
                    <button type="button" class="btn btn-primary btn-sm filter" name="apply-sort">Sorteer</button>
                </p>
                <p>
                    <button type="button" class="btn btn-primary" name="export">Exporteer naar CSV</button>
                </p>
                <table class="table table-striped table-hover table-sm" width="100%">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Naam</th>
                            <th>EAN</th>
                            <th>Artikelcode</th>
                            <th>Artikelnummer</th>
                            <th>Bestel Datum</th>
                            <th>Aantal</th>
                            <th>Prijs</th>
                            <th colspan="2"></th>
                            <th>Opmerking</th>
                            <th>User</th>
                            <th>Referentie</th>
                            <th>Checkdatum</th>
                            <th>Levertijd</th>
                            <th>EIG-categorie</th>
                            <th>Klant backorders</th>
                        </tr>
                        <tr>
                            <th colspan="17" align="left"><input name="select-all" type="checkbox" title="Selecteer alles" /></th>
                        </tr>
                    </thead>
                    <tbody>
                    {% set previousRow = null %}
                    {% set totalBackordersSupplier = 0 %}
                    {% for backorder in backorders %}
                        {% set lastMonth = date('-1 month') %}
                        {% set lastWeek = date('-1 week') %}
                        {% if backorder.orderDate < lastMonth %}
                            {% set bgColor = 'red' %}
                        {% elseif backorder.orderDate < lastWeek %}
                            {% set bgColor = 'orange' %}
                        {% else %}
                            {% set bgColor = 'white' %}
                        {% endif %}
                        {% if previousRow is not null and backorder.leverancierId != previousRow.leverancierId %}
                        <tr class="end-group">
                            <td colspan="16"></td>
                            <td>{{ totalBackordersSupplier }}</td>
                            {% set totalBackordersSupplier = 0 %}
                        </tr>
                        {% endif %}
                        {% if previousRow is null or backorder.leverancierId != previousRow.leverancierId %}
                        <tr class="group">
                            <td><input name="select-all-group" type="checkbox" title="Selecteer leverancier"></td>
                            <td colspan="12">{{ backorder.leverancier }}</td>
                            <td><input name="check-date" type="date" /></td>
                            <td><input name="delivery-date" type="date" /></td>
                            <td colspan="2"></td>
                        </tr>
                        {% endif %}
                        <tr>
                            <td><input name="select" type="checkbox" title="Selecteer" data-id="{{ backorder.id }}"></td>
                            <td><a href="{{ admin }}/?art_id={{ backorder.productId }}" target="_blank">{{ backorder.name }}</a></td>
                            <td>{{ backorder.ean }}</td>
                            <td>{{ backorder.ean2 }}</td>
                            <td>{{ backorder.productId }}</td>
                            <td class="{{ bgColor }}">{{ backorder.orderDate|date('d-m-Y') }}</td>
                            <td>{{ backorder.count - (backorder.scannedAmount + backorder.splitScannedAmount) }}</td>
                            <td>{{ backorder.price }}</td>
                            <td>{{ backorder.flags b-and 7 ? '*' : '' }}</td>
                            <td>
                                <a class="btn btn-primary" href="{{ admin }}/php/voorraad_memo.php?art_id={{ backorder.productId }}" target="_blank">
                                    <i class="fa fa-pencil" aria-hidden="true"></i>
                                </a>
                            </td>
                            <td>
                                {{ backorder.description }}
                            </td>
                            <td>{{ backorder.user }}</td>
                            <td><a href="{{ path('supplier_backorders_detail', { referenceId: backorder.referenceId }) }}" target="_blank">{{ backorder.referenceId }}</td>
                            <td><input name="check-date" type="date" value="{{ backorder.checkDate is not null ? backorder.checkDate|date('Y-m-d') : '' }}" /></td>
                            <td><input name="delivery-date" type="date" value="{{ backorder.deliveryDate is not null ? backorder.deliveryDate|date('Y-m-d') : '' }}" /></td>
                            <td>{{ backorder.EIGCategory }}</td>
                            <td>
                                {% if backorder.amountInOrders != 0 and (previousRow is null or backorder.productId is not same as (previousRow.productId)) %}
                                    {{ backorder.amountInOrders }}
                                    {% set totalBackordersSupplier = totalBackordersSupplier + backorder.amountInOrders %}
                                {% endif %}
                            </td>
                        </tr>
                        {% set previousRow = backorder %}
                    {% endfor %}
                        <tr class="end-group">
                            <td colspan="16"></td>
                            <td>{{ totalBackordersSupplier }}</td>
                            {% set totalBackordersSupplier = 0 %}
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    <script type="text/javascript" src="{{ asset('cat/js/backorder-overview.js') }}"></script>
{% endblock %}
