{% set disabled = has_access(app.user, 'instant-quote-edit-performance-targets') ? '' : 'disabled' %}
<form method="post" action="{{ path('product-performance-target-update', {product: product.id}) }}">
<div class="card card-panel mt-5">
    <div class="card-header">
        <div class="d-flex align-items-center justify-content-between">
            <h2 class="h5 m-0">{{ 'second-hand.product.stock_factors.title'|trans }}</h2>
            <input type="submit" value="Opslaan" class="btn btn-primary loader-btn" {{ disabled }}>
        </div>
    </div>

    {% set hasStockfactor = stockFactors is not null %}
    {% set stockFactor = stockFactors %}
    {% set discrepancyStyling = 'style=color:red' %}
    <div class="card-body">
        <div class="mb-3 d-flex align-items-center gap-5 font-weight-bold">
            <div class="col-5">
                {{ 'second-hand.product.stock_factors.actual'|trans }}: {{ hasStockfactor ? stockFactor.timestamp|date('d-m-Y H:i:s') : '-' }}
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>
                        {{ 'second-hand.product.stock_factors.rank'|trans }}
                    </span>
                    <span>{{ hasStockfactor ? stockFactor.rank : '-' }}</span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.stock'|trans }}</span>
                    <span {{ 'stock_amount' in discrepancies ? discrepancyStyling : '' }}>{{ hasStockfactor ? stockFactor.stock : 0 }} stuks</span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.asr'|trans }}</span>
                    <span {{ 'average_selling_rate' in discrepancies ? discrepancyStyling : '' }}>{{ hasStockfactor ? stockFactor.averageSalesRate|round : 0 }} dagen</span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.dsi_y'|trans }}</span>
                    <span {{ 'days_sales_in_inventory_year' in discrepancies ? discrepancyStyling : '' }}>{{ stockFactor ? stockFactor.daysSalesInInventoryYear|round : 0 }} dagen</span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.dsi_q'|trans }}</span>
                    <span {{ 'days_sales_in_inventory_quarter' in discrepancies ? discrepancyStyling : '' }}>{{ stockFactor ? stockFactor.daysSalesInInventoryQuarter|round : 0 }} dagen</span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.conversion'|trans }}</span>
                    <span {{ 'conversion_rate' in discrepancies ? discrepancyStyling : '' }}>{{ stockFactor ? stockFactor.conversionrate : 0 }}%</span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.margin'|trans }}</span>
                    <span {{ 'minimal_margin' in discrepancies ? discrepancyStyling : '' }}>{{ stockFactor ? stockFactor.marginPercentage : 0 }}%</span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.last_in'|trans }}</span>
                    <span {{ 'last_date_in' in discrepancies ? discrepancyStyling : '' }}>
                        {{ stockFactor and stockFactor.lastIn is not null and stockFactor.lastIn|date('Y') is not same as'-0001' ? stockFactor.lastIn|date('d-m-Y') : '-' }}
                    </span>
                </div>
                <div class="d-flex align-items-center justify-content-between gap-5">
                    <span>{{ 'second-hand.product.stock_factors.last_out'|trans }}</span>
                    <span {{ 'last_date_out' in discrepancies ? discrepancyStyling : '' }}>
                        {{ stockFactor and stockFactor.lastOut is not null and stockFactor.lastOut|date('Y') is not same as '-0001' ? stockFactor.lastOut|date('d-m-Y') : '-' }}
                    </span>
                </div>
            </div>
            <div class="border-left col-6">
                {{ 'second-hand.product.stock_factors.targets'|trans }}:
                {% for target in performanceTargets %}
                    <div class="row col-12">
                        <span class="col-6 text-right">{{ target.performanceTarget.name }}</span>
                        <input type="number"
                               id="target-value-{{ target.id }}"
                               name="targets[{{ target.id }}]"
                               value="{{ target.value }}"
                               class="form-control w-25 col-4"
                               required="required"
                               step="0.1"
                        >&nbsp;
                        {{ target.performanceTarget.extra }}
                    </div>
                    <input type="hidden" name="mainGroup" value="{{ mainGroup is not null ? mainGroup.id : null }}">
                {% endfor %}
            </div>
        </div>
        <div class="mb-3 d-flex align-items-center gap-5 font-weight-bold">
            <iframe frameborder=0 width="1028" height="600" src="https://analytics.zoho.com/open-view/1661891000097530438/20b6d73e5a6f2a05173193e7bb1002e5?ZOHO_CRITERIA=%22query_trade_in_performance%22.%22product%22%3D'{{ product.name|replace({' - Tweedehands': ''})|url_encode }}'"></iframe>
        </div>
    </div>
</div>
</form>
