{% extends webdsign.templates.cat_full %}

{% block body %}
<style>
    ul {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
    }
</style>


<div id="priceLogAccordion">
    <table class="table table-bordered">
        <thead>
        <tr>
            <th>Timestamp</th>
            <th>Prijs</th>
            <th>Action</th>
        </tr>
        </thead>
        <tbody>
        {% for timestamp, entry in priceLogs %}
            {% set formattedTimestamp = entry.formatted %}
            {% set qualities = entry.data %}
            {% set maxPrice = entry.maxPrice %}
            {% set collapseId = 'collapse-' ~ loop.index %}

            <tr class="bg-light">
                <td><strong>{{ formattedTimestamp }}</strong></td>
                <td><strong>{{ maxPrice }}</strong></td>
                <td>
                    <button class="btn btn-sm btn-primary" type="button"
                            data-toggle="collapse"
                            data-target="#{{ collapseId }}"
                            aria-expanded="false"
                            aria-controls="{{ collapseId }}">
                        Toon onderliggend
                    </button>
                </td>
            </tr>

            <tr>
                <td colspan="3" class="p-0 border-0">
                    <div id="{{ collapseId }}"
                         class="collapse"
                         data-parent="#priceLogAccordion">
                        <table class="table table-sm m-0">
                            <thead>
                            <tr>
                                <th>Kwaliteit</th>
                                <th>Prijs</th>
                                <th>Gebruiker</th>
                                <th>Tijd</th>
                                <th>Omschrijving</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for quality, logs in qualities|sort((a, b) => a <=> b) %}
                                {% for log in logs %}
                                    {% if instance_of(log, 'Webdsign\\GlobalBundle\\Entity\\SecondHand\\SecondHandPriceUpdate') %}
                                        <tr>
                                            <td>{{ quality }}</td>
                                            <td>
                                                {{ log.price }}
                                            </td>
                                            <td>{{ log.user.name }}</td>
                                            <td>{{ log.dateChanged|date('H:i:s') }}</td>
                                            <td>{{ log.description }}</td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td>{{ quality }}</td>
                                            <td>
                                                {{ log.newPrice }}
                                            </td>
                                            <td>{{ log.insertedBy.name }}</td>
                                            <td>{{ log.insertedTime|date('H:i:s') }}</td>
                                            <td>{{ log.logMessage }}</td>
                                        </tr>
                                    {% endif %}
                                {% endfor %}
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
