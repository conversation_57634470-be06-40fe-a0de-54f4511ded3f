<div class="card card-panel mt-5">
    <div class="card-header">
        <div class="d-flex align-items-center justify-content-between">
                <h2 class="h5 m-0">{{ 'second-hand.product.info.price-formula'|trans }}</h2>
                <h2 class="h5 m-0">
                    Verkoopprijs nieuw: <i class="bi bi-currency-euro"></i> {{ originalSalesPrice|number_format(2, ',', '.') }}
                </h2>

                <div class="btn-group">
                    <a onclick="showPriceLog(`{{ path('second_hand_intake_price_log', {'product': product.id}) }}`)" class="btn btn-outline-info" href="javascript:void(0)">
                        <i class="bi bi-eye"></i>
                        Inname prijs log
                    </a>
                    <a onclick="showPriceLog(`{{ path('second_hand_product_price_log', {'product': product.id}) }}`)" class="btn btn-outline-info" href="javascript:void(0)">
                        <i class="bi bi-eye"></i>
                        Verkoopprijs log
                    </a>
                {% if app.user.hasRole('second-hand-manager') %}
                    <button
                        id="save-price-list-btn"
                        class="btn btn-primary float-right"
                        onclick="savePrices('{{ path('second_hand_update_price_list') }}', '{{ path('second_hand_update_formula') }}')"
                        disabled
                    >
                        {{ 'second_hand.price_list.btn.save_generic_formula'|trans }}
                    </button>
                {% endif %}
                </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-form">
                <thead>
                <tr>
                    <th>Staat</th>
                    <th>Inname Formule</th>
                    <th>Inname Prijs</th>
                    <th>Verkoop Formule</th>
                    <th>Verkoopprijs</th>
                </tr>
                </thead>
                <tbody>
                {% set disabled = app.user.hasRole('second-hand-manager') ? '' : 'disabled'  %}
                {% for child in productChildren %}
                    {% set productState = child.secondHandProductState ? child.secondHandProductState|first %}
                    {% set state = productState ? productState.secondHandState %}
                    {% set quality = state ? state.quality : '-' %}
                    {% set stateId = state ? state.id : '0' %}
                    {% set formula = 0 %}
                    {% if formulas[stateId] is defined %}
                        {% set formula = formulas[stateId] %}
                    {%  endif %}
                    <tr>
                        <td>{{ quality }}</td>
                        <td class="p-1">
                            <div class="second-hand-percentage-input-container">
                                <div class="input-group">
                                    <input
                                        class="formula-input child-intake-formula-{{ child.id }}"
                                        type="number"
                                        value="{{ formula ? formula.intakeFormula : 0 }}"
                                        data-original-formula="{{ formula ? formula.intakeFormula : 0 }}"
                                        data-formula-id="{{ formula ? formula.id }}"
                                        data-formula-type="intake"
                                        min="0"
                                        max="100"
                                        {{ disabled }}
                                    />
                                    <i class="bi bi-percent"></i>
                                </div>
                            </div>
                        </td>
                        <td class="child-intake-price-{{ child.id }} p-1">
                            <div class="second-hand-price-input-container">
                                <div class="input-group">
                                    <input
                                        class="price-input child-intake-price-{{ child.id }}"
                                        type="number"
                                        step="0.01"
                                        value="{{ child.secondHandPrice ? child.secondHandPrice.intakePrice : 0.00 }}"
                                        data-original-price="{{ child.secondHandPrice ? child.secondHandPrice.intakePrice : 0.00 }}"
                                        data-price-id="{{ child.secondHandPrice ? child.secondHandPrice.id }}"
                                        data-price-type="intake"
                                        {{ disabled }}
                                    />
                                    <i class="bi bi-currency-euro"></i>
                                </div>
                            </div>
                        </td>
                        <td class="p-1">
                            <div class="second-hand-percentage-input-container">
                                <div class="input-group">
                                    <input
                                        class="formula-input child-sales-formula-{{ child.id }}"
                                        type="number"
                                        value="{{ formula ? formula.salesFormula : 0 }}"
                                        data-original-formula="{{ formula ? formula.salesFormula : 0 }}"
                                        data-formula-id="{{ formula ? formula.id }}"
                                        data-formula-type="sales"
                                        {{ disabled }}
                                    />
                                    <i class="bi bi-percent"></i>
                                </div>
                            </div>
                        </td>
                        <td class="child-sales-price-{{ child.id }} p-1">
                            <div class="second-hand-price-input-container">
                                <div class="input-group">
                                    <input
                                        class="price-input child-sales-price-{{ child.id }}"
                                        type="number"
                                        step="0.01"
                                        value="{{ child.secondHandPrice ? child.secondHandPrice.salesPrice : 0.00 }}"
                                        data-original-price="{{ child.secondHandPrice ? child.secondHandPrice.salesPrice : 0.00 }}"
                                        data-price-id="{{ child.secondHandPrice ? child.secondHandPrice.id }}"
                                        data-price-type="sales"
                                        {{ disabled }}
                                    />
                                    <i class="bi bi-currency-euro"></i>
                                </div>
                            </div>
                        </td>
                    </tr>
                {%  endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
