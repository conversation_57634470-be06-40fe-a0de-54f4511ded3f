{% if quote.status in ['products_requested', 'requests_handled'] %}
    {% set bgClass = 'bg-grey' %}
{% elseif quoteProduct.gradeDefinitive is not null and quoteProduct.offerDefinitive is not null %}
    {% set bgClass = 'bg-green' %}
{% else %}
    {% set bgClass = 'bg-red' %}
{% endif %}
{% set IntakableClass = isIntakableAsSecondHand is not true ? 'not-intakeable' : '' %}
<div class="customer-contact-panel mb-3 {{ bgClass }} {{ IntakableClass }}">
    <div class="row">
        <div class="col-9">
            <b class="product-name">{{ quoteProduct.productName }}</b><br>
            {% if quoteProduct.gradeDefinitive is not null and quoteProduct.offerDefinitive is not null %}
                <input type="hidden" class="product-has-definitive-grade"/>
                <span class="grade-definitive">
                    {{ quoteProduct.gradeDefinitive.description }} ({{ quoteProduct.gradeDefinitive.quality }})
                </span>
                &emsp;|&emsp;
                <span class="offer-definitive">
                    {{ (offerDefinitive / constant('Webdsign\\GlobalBundle\\Entity\\InstantQuote\\Quote::PAYOUT_REDUCTION_MODIFIER'))|round|number_format(2, ',', '.') }}
                </span>
            {% elseif quoteProduct.gradeCustomer is not null and quoteProduct.offerCustomer is not null %}
                <span class="grade-customer">
                    Initieel bod: &euro; {{ offerCustomer|round|number_format(2, ',', '.') }} ({{ quoteProduct.gradeCustomer.quality }})
                </span>
            {% endif %}
            {% if(isIntakableAsSecondHand == false) %}
                <span class="grade-definitive">
                    Niet inneembaar.
                </span>
            {% endif %}
            {% if
                quoteProduct.additionalTradeInValues and
                quoteProduct.additionalTradeInValues.additionalPercentage > 0
            %}
                <br><span>+{{ quoteProduct.additionalTradeInValues.additionalPercentage }}% extra inruilwaarde i.v.m. actie: {{ quoteProduct.additionalTradeInValues.quoteAdditionalTradeInAction.description }}</span>
            {% endif %}

            {% if quoteProduct.discountCodeValues %}
                {% for discountCodeValue in  quoteProduct.discountCodeValues %}
                    <br><span>+{{ discountCodeValue.additionalPercentage }}% extra inruilwaarde i.v.m. kortingscode: {{ discountCodeValue.discountCode.description }}</span>
                {% endfor %}
            {% endif %}
        </div>
        <div class="col-3 text-right align-middle">
            {% if quote is null or quote.status in ['draft', 'open', 'sent'] %}
                {% if quoteProduct.status == 'receive' %}
                    <input type="hidden" class="product-is-not-received"/>
                {% endif %}
                {% if isIntakableAsSecondHand == true %}
                    <a href="{{ path('instant-quote-product', { 'quoteProduct': quoteProduct.id }) }}"
                       class="btn btn-outline-primary judge-quote-product {% if quote and quote.status not in ['draft', 'open'] or quoteProduct.status not in ['open', 'judge'] %}d-none{% endif %}">
                        <i class="bi bi-pencil-square"></i>
                    </a>
                {% endif %}
                <button
                    type="button"
                    data-quote-product-receive-url="{{ path('instant-quote-product-receive', { 'quoteProduct': quoteProduct.id }) }}"
                    class="btn btn-outline-primary receive-quote-product {% if quote and quote.status not in ['draft', 'open'] or quoteProduct.status != 'receive' %}d-none{% endif %}">
                    <i class="bi bi-bag-check"></i>
                </button>
                <button
                    type="button"
                    data-quote-product-delete-url="{{ path('instant-quote-product-delete', { 'quoteProduct': quoteProduct.id }) }}"
                    class="btn btn-outline-danger remove-quote-product {% if quote and quote.status not in ['draft', 'open'] %}d-none{% endif %}">
                    <i class="bi bi-trash"></i>
                </button>
            {% endif %}
            {% if quoteProduct.status == 'return' %}
                <button
                    type="button"
                    data-quote-product-return-url="{{ path('instant-quote-product-return', { 'quoteProduct': quoteProduct.id }) }}"
                    class="btn btn-outline-primary return-quote-product">
                    <i class="bi bi-house-check-fill"></i>
                </button>
                <span class="quote-product-returned d-none"><i class="bi bi-reply-all-fill"></i>&nbsp;Geretourneerd</span>
            {% endif %}
            {% if quoteProduct.status == 'recycle' %}
                <button
                    type="button"
                    data-quote-product-recycle-url="{{ path('instant-quote-product-recycle', { 'quoteProduct': quoteProduct.id }) }}"
                    class="btn btn-outline-success recycle-quote-product">
                    <i class="bi bi-recycle"></i>
                </button>
                <span class="quote-product-recycled d-none"><i class="bi bi-recycle"></i>&nbsp;Gerecycled</span>
            {% endif %}
            {% if quoteProduct.status == 'returned' %}
                <span class="quote-product-returned"><i class="bi bi-reply-all-fill"></i>&nbsp;Geretourneerd</span>
            {% endif %}
            {% if quoteProduct.status == 'recycled' %}
                <span class="quote-product-recycled"><i class="bi bi-recycle"></i>&nbsp;Gerecycled</span>
            {% endif %}
            {% if quoteProduct.offerDefinitive is not empty or quoteProduct.offerCustomer is not empty %}
                <button class="btn btn-outline-primary dropdown-questions"><i class="bi bi-chevron-up"></i> </button>
            {% endif %}
        </div>
    </div>
    {% if quoteProduct.offerDefinitive is not empty or quoteProduct.offerCustomer is not empty %}
        <div class="row">
            {% include '@CatBundle/SecondHand/Quote/Panels/product_answers.html.twig' with {
                'quoteProduct': quoteProduct,
            }
            %}
        </div>
    {% endif %}
</div>
