{% set quoteProductTotal = 0 %}

<div class="card card-panel mt-5">
    <div class="card-header">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h2 class="h5 m-0">{{ 'second-hand.quote-save.product.title'|trans }}</h2>
            </div>
            {% if quote.status not in ['products_requested', 'requests_handled'] %}
                <h2 class="h5 m-0 row">
                    Waarde&nbsp;
                    <div id="quote-potential-total-value">
                        &euro; {{ ((quote.potentialPayout + quote.getMissingAccessoryTotal) * constant('Webdsign\\GlobalBundle\\Entity\\InstantQuote\\Quote::PAYOUT_REDUCTION_MODIFIER') - quote.getMissingAccessoryTotal)|number_format(2, ',', '.') }}
                    </div>
                </h2>
            {% endif %}
            <input type="hidden" id="quote-potential-total-value-input" value="{{ (quote.potentialPayout) }}" />
            <input type="hidden" id="quote-potential-missing-accessory-total" value="{{ quote.getMissingAccessoryTotal }}" />
        </div>
    </div>
    <div class="card-body product-card-body">
        {% if (quote and quote.status not in ['draft', 'open']) or quote.products|length > 0 %}<div class="d-none">{% endif %}
            {{ form(productSearchForm) }}
        {% if (quote and quote.status not in ['draft', 'open']) or quote.products|length > 0  %}</div>{% endif %}

        {% for quoteProduct in quote.products %}
            {% set offerDefinitive = ((quoteProduct.offerDefinitive + quoteProduct.getMissingAccessoryTotalDefinitive) * constant('Webdsign\\GlobalBundle\\Entity\\InstantQuote\\Quote::PAYOUT_REDUCTION_MODIFIER') - quoteProduct.getMissingAccessoryTotalDefinitive) %}
            {% set additionalTradeInValueDefinitive = (quoteProduct.additionalTradeInValueDefinitive * constant('Webdsign\\GlobalBundle\\Entity\\InstantQuote\\Quote::PAYOUT_REDUCTION_MODIFIER')) %}
            {% set totalDiscountCodeDiscountDefinitive = (quoteProduct.totalDiscountCodeDiscountDefinitive * constant('Webdsign\\GlobalBundle\\Entity\\InstantQuote\\Quote::PAYOUT_REDUCTION_MODIFIER')) %}
            {% set offerCustomer = ((quoteProduct.potentialPayout + quoteProduct.getMissingAccessoryTotalCustomer) * constant('Webdsign\\GlobalBundle\\Entity\\InstantQuote\\Quote::PAYOUT_REDUCTION_MODIFIER') - quoteProduct.getMissingAccessoryTotalCustomer) %}
            {% set isIntakableAsSecondHand = quoteProduct.product.isIntakeableAsSecondHand() %}

            {% if quoteProduct.gradeDefinitive is not null and quoteProduct.offerDefinitive is not null %}
                {% set quoteProductTotal = quoteProductTotal + offerDefinitive + additionalTradeInValueDefinitive + totalDiscountCodeDiscountDefinitive %}
            {% elseif quoteProduct.gradeCustomer is not null and quoteProduct.offerCustomer is not null %}
                {% set quoteProductPotentialTotal = offerCustomer %}
            {% endif %}

            {% if quoteProduct.quoteProductRequest is same as null or quote.status not in ['products_requested', 'requests_handled'] %}
                {% include '@Cat/SecondHand/Quote/Panels/product.html.twig' with {
                    'quote': quote,
                    'quoteProduct': quoteProduct,
                    'offerDefinitive': offerDefinitive,
                    'offerCustomer': offerCustomer,
                    'isIntakableAsSecondHand': isIntakableAsSecondHand,
                } %}
            {% endif %}
        {% endfor %}
        {% if quote.products|length > 0 and quote.status in ['draft', 'open'] %}
        <div class="row">
            <div class="col-12 text-center align-middle">
                <button class="btn btn-block btn-outline-primary add-product">
                    <i class="bi bi-plus"></i>
                    Nog een product innemen
                </button>
            </div>
        </div>
        {% endif %}
        <input type="hidden" id="quote-total-value-input" value="{{ quoteProductTotal }}" />
    </div>
    <div id="newProductTemplate" class="d-none">
        <div class="customer-contact-panel mb-3">
            <div  class="row">
                <div class="col-10">
                    <b class="product-name"></b><br>
                    <div class="show-product-offer d-none">
                        <span class="grade-definitive"></span>&emsp;|&emsp;<span class="offer-definitive"></span>
                    </div>
                </div>
                <div class="col-2 text-right align-middle">
                    <a href="#"
                       class="btn btn-outline-primary judge-quote-product d-none">
                        <i class="bi bi-pencil-square"></i>
                    </a>
                    <button
                        type="button"
                        data-quote-product-receive-url="#"
                        class="btn btn-outline-primary receive-quote-product d-none">
                        <i class="bi bi-bag-check"></i>
                    </button>
                    <button
                        type="button"
                        data-quote-product-delete-url="#"
                        class="btn btn-outline-danger remove-quote-product">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
