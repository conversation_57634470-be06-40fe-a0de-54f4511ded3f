{% extends '@Cat/PickList/layout.html.twig' %}

{% block content %}
<div class="pick-list-container container-xl py-5">
    <h1>{{ 'pick_list.title.scanned_cleanup'|trans }}</h1>

    <div class="row">
        <div class="col-sm mt-2">
            <div class="form-group d-flex align-items-center gap-2">
                <input id="pick-list-scan-cleanup-barcode"
                       type="text"
                       class="pick-list-input form-control-lg form-control"
                       placeholder="{{ 'pick_list.search.product'|trans }}"
                       autocomplete="off" />
                <button class="btn btn-primary btn-lg"
                        type="button"
                        onclick="location.reload();">
                    {{ 'pick_list.button.refresh'|trans }}
                </button>
            </div>
        </div>
    </div>
    <div id="pick-list-scan-results" class="row d-none">
        <div class="col-sm mt-2">
            <div id="pick-list-scan-cleanup-product" class="h3 alert alert-success"></div>
            <div id="pick-list-scan-cleanup-found-locations" class="h3 alert alert-success font-weight-bold"></div>
        </div>
    </div>
    <div class="row d-none">
        <div class="col-sm mt-2">
            <div class="form-group">
                <input id="pick-list-scan-cleanup-location" type="text" class="pick-list-input form-control-lg w-100" placeholder="{{ 'pick_list.search.location'|trans }}" autocomplete="off" />
            </div>
        </div>
    </div>
    <div id="pick-list-scan-cleanup-status-message" class="h3 mt-2 alert alert-danger d-none"></div>
</div>
{% endblock %}
{% block scripts %}
<script type="text/javascript">
    let productNotFound = '{{ 'pick_list.result.product_not_found'|trans }}';
    let moveSuccess = '{{ 'pick_list.result.move_success'|trans }}';
    let moveFailed = '{{ 'pick_list.result.move_failed'|trans }}';
</script>
{{ parent() }}
{% endblock %}
