<div>
    <div>
        <label for="instructor">Instructeur</label>
        <select id="instructor">
            {% for instructor in instructors %}
                <option value="{{ instructor.id | default(null) }}">{{ instructor.fullName | default('') }}</option>
            {% endfor %}
        </select>
    </div>
    <div>
        <label for="location">Locatie</label>
        <select id="location">
            {% for location in locations %}
                <option value="{{ location.id | default(null) }}">{{ location.name | default ('') }}</option>
            {% endfor %}
        </select>
    </div>
    <div>
        <label for="category">Categorie</label>
        <select id="category">
            {% for category in categories %}
                <option value="{{ category }}">{{ category }}</option>
            {% endfor %}
        </select>
    </div>
    <div>
        {# Not filtering is our valid 3rd option, so no checkbox #}
        <label for="visible">Zichtbaar</label>
        <select id="visible">
            <option value=""></option>
            <option value="0">Onzichtbaar</option>
            <option value="1">Zich<PERSON><PERSON>ar</option>
        </select>
    </div>
    <div>
        {# Not filtering is our valid 3rd option, so no checkbox #}
        <label for="expired">Verlopen</label>
        <select id="expired">
            <option value=""></option>
            <option value="0">Niet verlopen</option>
            <option value="1">Verlopen</option>
        </select>
    </div>
    <div>
        <label for="province">Provincie</label>
        <select id="province">
            {% for province in provinces %}
                <option value="{{ province }}">{{ province }}</option>
            {% endfor %}
        </select>
    </div>
</div>
