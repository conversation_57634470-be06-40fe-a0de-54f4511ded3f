{% extends '@Cat/Event/base_template.html.twig' %}
{% block event_content %}
    {% include '@Cat/Event/Event/header.html.twig' %}
    <div id="event" data-screen="{{ screen }}" data-product-softdelete="{{ product.softdelete }}" data-id="{{ product.id }}" class="row">
        <div class="col-12">
            <h3>Event</h3>
            <div class="row">
                <div class="col-xl-9 col-lg-8">
                    <table class="table table-sm">
                        <tbody>
                        <tr>
                            <td>Titel</td>
                            <td>{{ product.event.title }}</td>
                        </tr>
                        <tr>
                            <td>Beschrijving</td>
                            <td>{{ product.event.description }}</td>
                        </tr>
                        <tr>
                            <td>Instructeur</td>
                            <td>{{ product.event.eventInstructors|map(instructor => instructor.instructor.fullName)|join(', ') }}</td>
                        </tr>
                        <tr>
                            <td>Categorie</td>
                            <td>{{ product.event.category.name }}</td>
                        </tr>
                        <tr>
                            <td>Niveau</td>
                            <td>{{ product.event.level }}</td>
                        </tr>
                        <tr>
                            <td>Winkel</td>
                            <td>{{ product.event.stockLocation.description }}</td>
                        </tr>
                        <tr>
                            <td>Locatie</td>
                            <td>{{ product.location.name }}</td>
                        </tr>
                        <tr>
                            <td>Startdatum</td>
                            <td>{{ product.startDateAndTime|date('d-m-Y H:i:s') }}</td>
                        </tr>
                        <tr>
                            <td>Einddatum</td>
                            <td>{{ product.endDateAndTime|date('d-m-Y H:i:s') }}</td>
                        </tr>
                        <tr>
                            <td>Max aantal deelnemers</td>
                            <td>{{ product.maximumCapacity }}</td>
                        </tr>
                        <tr>
                            <td>Registratie nodig</td>
                            <td>{{ product.needsRegistration == 1 ? 'Ja' : 'Nee' }}</td>
                        </tr>
                        <tr>
                            <td>Betaling nodig</td>
                            <td>{{ product.needsPayment == 1 ? 'Ja' : 'Nee' }}</td>
                        </tr>
                        <tr>
                            <td>Administratie kosten</td>
                            <td>{{ product.administrationCosts == 1 ? 'Ja' : 'Nee' }}</td>
                        </tr>
                        <tr>
                            <td>Event zichtbaar</td>
                            <td>{{ product.event.visible == 1 ? 'Ja' : 'Nee' }}</td>
                        </tr>
                        <tr>
                            <td>Deze datum zichtbaar</td>
                            <td>{{ product.product.visible == 1 ? 'Ja' : 'Nee' }}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-xl-3 col-lg-4">
                    <div class="text-right">
                        <a class="btn btn-secondary" href="{{ path('events_event_copy', {product: product.id}) }}">Nieuwe datum toevoegen</a>
                    </div>
                    {% set products = product.event.products %}
                    {% if products|length > 0 %}
                        <h3>Alle data</h3>
                        <table class="table table-sm">
                            <thead>
                            <tr>
                                <th>Event</th>
                                <th>Locatie</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for otherProduct in products %}
                                <tr>
                                    <td>
                                        {% if otherProduct.id == product.id %}
                                            >
                                            {{ otherProduct.startDateAndTime|date('d-m-Y') }}
                                        {% else %}
                                            <a href="{{ path('events_event_product_view', {product: otherProduct.id}) }}">{{ otherProduct.startDateAndTime|date('d-m-Y') }}</a>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ otherProduct.location.name }}
                                    </td>
                                    <td>
                                        <a class="btn btn-secondary btn-sm product-popup"
                                           href="{{ admin }}/php/artikel_tabs.php?id={{ otherProduct.product.id }}">Admin</a>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    {% endif %}
                </div>
            </div>
            <div class="row">
                <div class="col-12 text-right">
                    <a class="btn btn-sm btn-success" href="{{ event_website_url(product) }}" target="_blank"
                       title="Bekijken op de website">
                        Event bekijken op de website
                    </a>
                    {% if app.user.hasAccess('events-add-events') %}
                        <a class="btn btn-sm btn-success product-popup"
                           href="{{ admin }}/php/artikel_tabs.php?id={{ product.product.id }}"
                           title="Openen in de admin" data-width="1024">
                            Openen in de admin
                        </a>
                        <a class="btn btn-sm btn-success" href="{{ path('events_event_save', {product: product.id}) }}"
                           title="Aanpassen">
                            Dit event aanpassen
                        </a>
                        <a class="btn btn-sm btn-success" href="{{ path('event_log_list', {eventProduct: product.id}) }}" title="Log inzien">{{ 'event_log.view'|trans }}</a>
                    {% endif %}
                </div>
            </div>
            {% if product.needsRegistration %}
                <div class="row">
                    <div class="col-4 p-2">
                        <h4>Inschrijvingen</h4>
                        {% if product.maximumCapacity is not null %}
                        Betaald: {{ product.countReservedRegistrations(true) }}<br />
                        Gereserveerd: {{ product.countReservedRegistrations(false) }}<br />
                        Totaalcapaciteit: {{ product.maximumCapacity }}
                        {% endif %}
                    </div>
                    <div class="col-8 p-2 text-right">
                        {% if app.user.hasAccess('events-add-registration') %}
                            <a href="{{ path('events_registration_search', { product: product.id }) }}"
                               class="btn btn-sm btn-secondary">Toevoegen</a>
                        {% endif %}
                        {% if app.user.hasAccess('events-cancel-registration') %}
                            <a href="{{ path('events_registration_bulk', { type: 'cancel' }) }}"
                               class="btn btn-sm btn-secondary events-bulk post-confirm post-reload">Inschrijvingen
                                annuleren</a>
                        {% endif %}
                        <a href="{{ path('events_registration_bulk', { type: 'message' }) }}"
                           class="btn btn-sm btn-secondary events-bulk post-confirm no-reload">Bericht sturen</a>
                        {% if app.user.hasAccess('events-export') %}
                            <div class="btn-group">
                                <button id="export-options" type="button"
                                        class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown"
                                        aria-haspopup="true" aria-expanded="false">
                                    Exporteren
                                </button>
                                <div class="dropdown-menu" aria-labelledby="export-options">
                                    <a href="{{ path('events_event_export_csv', {'product': product.id}) }}"
                                       class="dropdown-item btn btn-sm btn-secondary">CSV (alleen inschrijvingen)</a>
                                    {% if app.user.hasAccess('events-export') %}
                                    <a href="{{ path('events_event_export_csv', {'product': product.id, 'extended': true}) }}"
                                       class="dropdown-item btn btn-sm btn-secondary">CSV (inclusief orderdetails)</a>
                                    {% endif %}
                                    <a href="{{ path('events_event_export_pdf', {'product': product.id}) }}"
                                       class="dropdown-item btn btn-sm btn-secondary">PDF</a>
                                    {% if app.user.hasAccess('events-export') %}
                                    <a href="{{ path('events_event_export_pdf', {'product': product.id, 'extended': true}) }}"
                                       class="dropdown-item btn btn-sm btn-secondary">PDF (extra details)</a>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="registration-list">
                    <table class="table table-sm">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Status</th>
                            <th>Naam</th>
                            <th>Telefoon</th>
                            <th>Order</th>
                            <th>Acties</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for registration in product.registrations %}
                            <tr>
                                <td>
                                    <label for="registration-id-{{ registration.id }}">
                                        <input type="checkbox" name="registration-ids[]" id="registration-id-{{ registration.id }}" value="{{ registration.id }}">
                                    </label>
                                </td>
                                <td>
                                    {% if registration.isCancelled %}
                                        <i class="fa fa-lg fa-close" title="Geannuleerd"></i>
                                    {% elseif registration.isPaid %}
                                        <i class="fa fa-lg fa-check text-success" title="Betaling akkoord"></i>
                                    {% elseif registration.order is not null and registration.order.isCancelledOrder != true and registration.order.invoiceNumber == 0 %}
                                        <i class="fa fa-lg fa-pause" title="Gereserveerd"></i>
                                    {% endif %}
                                    {% if registration.refundRequested and not registration.isRefunded %}
                                        <i class="fa fa-lg fa-spinner" title="Terugbetaling aangevraagd"></i>
                                    {% endif %}
                                    {% if registration.isRefunded %}
                                        <i class="fa fa-lg fa-undo" title="Terugbetaald"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ registration.getFullName }}
                                </td>
                                <td>
                                    {{ registration.phoneNumber }}
                                </td>
                                <td>
                                    {% if registration.order is not same as (null) %}
                                        <a class="order-popup" href="{{ admin }}/php/bestelling.php?bestelling_id={{ registration.order.id }}"
                                           target="_blank">
                                            {{ registration.order.ordernr }}
                                        </a>
                                        {% if registration.order.isCancelledOrder %}
                                            (geannuleerd)
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if app.user.hasAccess('events-add-registration') %}
                                        <div class="btn-group" role="group">
                                            <button id="registrationOptions" type="button"
                                                    class="btn btn-primary btn-sm dropdown-toggle"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                Opties
                                            </button>
                                            <div class="dropdown-menu" aria-labelledby="registrationOptions">
                                                <a class="dropdown-item"
                                                   href="{{ path('events_registration_save', {'registration': registration.id}) }}">Aanpassen</a>
                                                <a class="dropdown-item post-confirm post-reload with-cancel-reason"
                                                   href="{{ path('events_registration_cancel', {'registration': registration.id}) }}">Annuleren</a>
                                                {% if registration.order is not null %}
                                                    {% if not registration.refundRequested and not registration.isRefunded and registration.canBeRefunded %}
                                                        <a class="dropdown-item post-confirm post-reload"
                                                           href="{{ path('events_registration_refund', {'registration': registration.id}) }}">Terugbetalen</a>
                                                        {% if registration.isCancelled is empty %}
                                                            <a class="dropdown-item post-confirm post-reload with-cancel-reason"
                                                               href="{{ path('events_registration_cancel_and_refund', {'registration': registration.id}) }}">Annuleren
                                                                en terugbetalen</a>
                                                        {% endif %}
                                                    {% endif %}
                                                    <a class="dropdown-item"
                                                       href="{{ path('events_registration_create_from_product_and_order', {'product': product.id, 'order': registration.order.id}) }}">Voeg
                                                        extra registratie toe aan bestelling</a>
                                                {% endif %}
                                                <a class="dropdown-item" href="{{ path('event_log_list', {eventProduct: product.id, registration: registration.id}) }}">Log</a>
                                                {% if registration.pspReference is null %}
                                                    <span class="dropdown-item disabled">Terugbetalen van deze registratie via deze tool is niet mogelijk; dit moet op orderniveau</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block inlinejavascript %}
<script>
    $(document).ready(function() {
        const softdelete = $('#event').data('productSoftdelete');
        const action = softdelete === 1 ? 'terugzetten' : 'verwijderen';
        $('#action-delete').html(action);
        $('#remove-selected').removeClass('disabled');

        $(document).on('click', '#remove-selected', function() {
            swal.fire({
                title: `Weet je zeker dat je dit event wilt ${action}?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: `Ja, ${action}!`,
            }).then(async (result) => {
                if (result.isConfirmed === true) {
                    $('#remove-selected').addClass('disabled');
                    let send = [
                        $('#event').data('id')
                    ];

                    let url = '/events/soft-delete';
                    url += softdelete ? '/undo' : '';
                    $.post(
                        url,
                        { data : JSON.stringify(send) }
                    ).then(async () => {
                        document.location.reload();
                    });

                }
            });
        });
    });
</script>
{% endblock %}
