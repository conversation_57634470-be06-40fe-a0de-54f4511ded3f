{% extends '@Cat/Event/base_template.html.twig' %}
{% block event_content %}
    {% include '@Cat/Event/Event/header.html.twig' %}
    {% include '@Cat/Event/Event/_filters.html.twig' %}

    <table id="datatable-events" data-screen="{{ screen }}" class="table table-striped table-sm datatable">
        <thead>
        <tr>
            <th></th>
            <th>Event</th>
            <th class="datatable-default-sort">Startdatum</th>
            <th>Instructeur</th>
            <th>Locatie</th>
            <th>Prijs</th>
            <th>Inchrijvingen/totaal beschikbaar</th>
            <th class="datatable-disable-sort">Acties</th>
        </tr>
        </thead>
        <tbody>
        </tbody>
    </table>

{% endblock %}

{% block inlinejavascript %}
    <script>
        const overview_url = '/events/products-for-overview';
        let selectsIds = ['instructor', 'location', 'category', 'visible', 'expired', 'province', 'softdelete'];
        const screenIsSoftdelete = $('#datatable-events').data( "screen" ) === 'softdelete';
        const action = screenIsSoftdelete ? 'terugzetten' : 'verwijderen'
        $('#action-delete').html(action);

        // Initialize filters
        let filters = {}
        selectsIds.forEach(val => filters[val] = null);

        // Initialize filters from query parameters
        const url = new URL(document.location);
        const params = url.searchParams;
        selectsIds.forEach(val => {
            let setVal = params.get(val)
            if (setVal !== null && setVal !== '') {
                $(`#${val}`).val(setVal)
                filters[val] = setVal;
            }
        })

        filters['softdelete'] = screenIsSoftdelete ? 1 : 0 ;

        let filtersToQueryString = () => {
            let queryStarted = false;
            let query = '';
            selectsIds.forEach(val => {
                if (filters[val] !== null) {
                    query += (!queryStarted) ? '?' : '&';
                    query += `${val}=${filters[val]}`
                    queryStarted = true;
                }
            })
            return query;
        }

        let reloadTable = (table) => {
            table.ajax.url(overview_url + filtersToQueryString()).load(() => updateOnSelect(table));
        };
        $(function () {
            const table = initDatatable({
                responsive: true,
                pageLength: 50,
                select: {
                    style: 'multi',
                    selector: 'td:first-child'
                },
                rowId: 'id',
                ajax: {
                    url: overview_url + filtersToQueryString(),
                    dataSrc: '',
                },
                columns: [
                    {
                        orderable: false,
                        render: DataTable.render.select(),
                        targets: 0
                    },
                    {
                        render: function (data, type, row) {
                            switch (type) {
                                case 'type':
                                    // To be honest I'm not sure what this is supposed to be...
                                    return 'sort';
                                case 'filter':
                                case 'sort':
                                    return row['title'];
                                case 'display':
                                    let cellContent = `<a href="${row['url']}">${row['title']}</a>`;
                                    if (row['external']) {
                                        cellContent += `<span class="badge badge-info">Extern</span>`;
                                    }
                                    return cellContent;
                                default:
                                    console.error(`Unknown type "${type}" for rendering table cell`);
                                    return '';
                            }
                        }
                    },
                    {
                        data: 'date',
                    },
                    {
                        data: 'instructor',
                    },
                    {
                        data: 'location',
                    },
                    {
                        data: 'price',
                    },
                    {
                        render: function (data, type, row) {
                            switch (type) {
                                case 'type':
                                    // To be honest I'm not sure what this is supposed to be...
                                    return 'sort';
                                case 'filter':
                                case 'sort':
                                    return row['reservedRegistrations'];
                                case 'display':
                                    const max = row['maximumCapacity'] > 0 ? row['maximumCapacity'] : '';
                                    return `${row['reservedRegistrations']}/${max}`;
                            }
                        }
                    },
                    {
                        render: function (data, type, row) {
                            switch (type) {
                                case 'type':
                                    // To be honest I'm not sure what this is supposed to be...
                                    return 'sort';
                                case 'filter':
                                case 'sort':
                                    return null;
                                case 'display':
                                    let cellContent = '';
                                    if (row['editUrl']) {
                                        cellContent += `
                            <div class="btn-group" role="group">
                                        <a class="btn btn-sm btn-success" href="${row['editUrl']}" title="Aanpassen">
                                            <i class="fa fa-lg fa-edit"></i>
                                        </a>
                                        <a class="btn btn-sm btn-secondary" href="${row['copyUrl']}" title="Kopieren">
                                            <i class="fa fa-lg fa-clone"></i>
                                        </a>
                                        <a class="btn btn-sm btn-success" href="${row['websiteUrl']}" target="_blank" title="Bekijken op de website">
                                            <i class="fa fa-lg fa-external-link"></i>
                                        </a>
                            </div>`;
                                    }
                                    if (row['cancelUrl']) {
                                        cellContent += `
                                        <div class="btn-group" role="group">
                                            <a class="btn btn-sm btn-danger post-confirm" href="${row['cancelUrl']}" title="Verwijderen">
                                                <i class="fa fa-lg fa-eraser"></i>
                                            </a>
                                        </div>`;
                                    }
                                    return cellContent;
                                default:
                                    console.error(`Unknown type "${type}" for rendering table cell`);
                                    return '';
                            }
                        }
                    },
                ]
            }, '.datatable');

            $('.dataTables_wrapper').removeClass('form-inline')

            // Register change listeners
            selectsIds.forEach((val) => {
                $(`#${val}`).on('change', function () {
                    filters[val] = this.value === '' ? null : this.value;
                    reloadTable(table);
                    // Also put the filters in the tab url
                    const newURL = new URL(window.location.href);
                    newURL.search = filtersToQueryString();
                    window.history.replaceState({path: newURL.href}, '', newURL.href);
                })
            })

            $(document).on('click','#remove-selected', function () {
                if (this.classList.contains('disabled')) {
                    return;
                }

                const selectedRowsCount = table.rows({selected : true}).data().toArray().length;
                const s = (selectedRowsCount > 1) ? 's' : '';
                const action = screenIsSoftdelete ? 'terugzetten' : 'verwijderen'

                swal.fire({
                    title: `Weet je zeker dat je ${selectedRowsCount} event${s} wilt ${action}?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: `Ja, ${action}!`,
                }).then(async (result) => {
                    const selectedRows = table.rows({selected : true}).data().toArray();

                    if (result.isConfirmed === true) {
                        $('#remove-selected').addClass('disabled');

                        let send = [];
                        for (let row of selectedRows) {
                            send.push(row.id);
                        }

                        let url = '/events/soft-delete';
                        url += screenIsSoftdelete ? '/undo' : '';
                        $.post(
                            url,
                            { data : JSON.stringify(send) }
                        ).then(async () => {
                            reloadTable(table);
                            updateOnSelect(table);
                        });
                    }
                });
            });

            table.on('select deselect change', () => updateOnSelect(table));
        })

        const updateOnSelect = function(table) {
            let selectedRowsCount = table.rows({selected : true}).data().toArray().length;
            selectedRowsCount = selectedRowsCount !== 0 ? selectedRowsCount: '';
            $('#count-delete').html(selectedRowsCount);

            if (selectedRowsCount > 0) {
                $('#remove-selected').removeClass('disabled');
            } else {
                $('#remove-selected').addClass('disabled');
            }

            if (selectedRowsCount > 1) {
                $('#mutiple-delete').removeClass('d-none');
            } else {
                $('#mutiple-delete').addClass('d-none');
            }
        }
    </script>
{% endblock %}
