{% extends webdsign.templates.cat_full %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"/>
    <link rel="stylesheet" href="{{ absolute_url(asset('cat/css/events.css')) }}?{{ version }}"/>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script type="text/javascript" src="{{ absolute_url(asset('cat/js/events.js')) }}?{{ version }}"></script>
{% endblock %}

{% block body %}
    <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
        <a href="{{ path('events_dashboard') }}" class="navbar-brand">Events</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="{{ path('events_event_overview') }}" class="nav-link {{ path_active(['events_event_overview', 'events_event_save', 'events_event_copy']) ? 'active' : '' }}">Events</a>
                </li>
                {% if app.user.hasAccess(constant('Webdsign\\GlobalBundle\\Constant\\EventConstants::RESOURCE_EVENTS')) and app.user.hasAccess(constant('Webdsign\\GlobalBundle\\Constant\\EventConstants::RESOURCE_ADD_EVENTS')) and app.user.hasAccess(constant('Webdsign\\GlobalBundle\\Constant\\EventConstants::RESOURCE_ADD_CATEGORIES')) %}
                <li class="nav-item">
                    <a href="{{ path('events_category_overview') }}" class="nav-link {{ path_active(['events_category_overview', 'events_category_save']) ? 'active' : '' }}">
                        Categorieën
                    </a>
                </li>
                {% endif %}
                <li class="nav-item">
                    <a href="{{ path('events_theme_overview') }}" class="nav-link {{ path_active(['events_theme_overview', 'events_theme_save']) ? 'active' : '' }}">Thema's</a>
                </li>
                <li class="nav-item">
                    <a href="{{ path('events_location_overview') }}" class="nav-link {{ path_active(['events_location_overview', 'events_location_save']) ? 'active' : '' }}">Locaties</a>
                </li>
                <li class="nav-item">
                    <a href="{{ path('events_instructor_overview') }}" class="nav-link {{ path_active(['events_instructor_overview', 'events_instructor_save']) ? 'active' : '' }}">Instructeurs</a>
                </li>
                <li class="nav-item">
                    <a href="{{ path('events_checklist_item_overview') }}" class="nav-link {{ path_active(['events_checklist_item_overview', 'events_checklist_item_save']) ? 'active' : '' }}">Checklistitems</a>
                </li>
                <li class="nav-item">
                    <a href="{{ path('events_reminder_overview') }}" class="nav-link {{ path_active(['events_reminder_overview', 'events_reminder_save']) ? 'active' : '' }}">Reminders</a>
                </li>
                <li class="nav-item">
                    <a href="{{ path('events_softdelete_overview') }}" class="nav-link {{ path_active(['events_softdelete_overview']) ? 'active' : '' }}">Prullenbak</i></a>
                </li>
            </ul>
        </div>
    </nav>
    <div class="container-fluid">
        {% block event_content %}{% endblock %}
    </div>
{% endblock %}
