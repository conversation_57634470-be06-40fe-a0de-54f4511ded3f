{% extends '@Cat/Event/base_template.html.twig' %}
{% block event_content %}
    {% include '@Cat/Event/Theme/header.html.twig' %}
    <div class="row">
        <div class="col-12">
            <table class="table table-striped table-sm datatable">
                <thead>
                    <tr>
                        <th>Thema</th>
                        <th>Tag</th>
                        <th>Acties</th>
                    </tr>
                </thead>
                <tbody>
                    {% for theme in themes %}
                        <tr>
                            <td>
                                <a href="{{ path('events_theme_save', {theme: theme.id}) }}">{{ theme.name }}</a>
                            </td>
                            <td>
                                {{ theme.tag.value ?? '' }}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a class="btn btn-sm btn-success" href="{{ path('events_theme_save', {theme: theme.id}) }}" title="Aanpassen">
                                        <i class="fa fa-lg fa-edit"></i>
                                    </a>
                                </div>
                                <div class="btn-group" role="group">
                                    <a class="btn btn-sm btn-danger post-confirm" href="{{ path('events_theme_delete', {theme: theme.id}) }}" title="Verwijderen">
                                        <i class="fa fa-lg fa-eraser"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}

{% block inlinejavascript %}
    <script>
        $(function () {
            initDatatable({
                responsive: true,
                pageLength: 50
            }, '.datatable');
            $('.dataTables_wrapper').removeClass('form-inline')
        })
    </script>
{% endblock %}
