<?php

declare(strict_types=1);

namespace CatBundle\Command;

use CatBundle\Producer\OutOfStock\OutOfStockProducer;
use CatBundle\Service\CourierListFromOrders;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\CartItem;
use Webdsign\GlobalBundle\Entity\CourierList;
use Webdsign\GlobalBundle\Entity\Info;
use Webdsign\GlobalBundle\Entity\MailContent;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Entity\Parking;
use Webdsign\GlobalBundle\Entity\ParkingRepository;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\PurchaseOrderRepository;
use Webdsign\GlobalBundle\Entity\Stock;
use Webdsign\GlobalBundle\Entity\StockInLocation;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Entity\StockRepository;
use Webdsign\GlobalBundle\Entity\SupplierStock;
use Webdsign\GlobalBundle\Entity\SupplierStockRepository;
use Webdsign\GlobalBundle\Entity\User;
use Webdsign\GlobalBundle\Entity\UserRepository;
use Webdsign\GlobalBundle\Exception\InvalidCourierListProductException;
use Webdsign\GlobalBundle\Exception\InvalidCourierListStateException;
use Webdsign\GlobalBundle\Exception\InvalidCourierListTypeException;

#[AsCommand(
    name: 'cat:courier-list-from-send-later-urk',
    description: 'Create courier list(s) for orders in "Later sturen Urk" parking folder'
)]
class CourierListFromSendLaterUrkCommand extends Command
{
    public function __construct(
        private readonly StockLocationRepository $stockLocationRepository,
        private readonly ParkingRepository $parkingRepository,
        private readonly OrderInfoRepository $orderInfoRepository,
        private readonly PurchaseOrderRepository $purchaseOrderRepository,
        private readonly CourierListFromOrders $courierListFromOrders,
        private readonly SupplierStockRepository $supplierStockRepository,
        private readonly StockRepository $stockRepository,
        private readonly OutOfStockProducer $outOfStockProducer,
        private readonly EntityManagerInterface $entityManager,
        private readonly UserRepository $userRepository,
        string $name = null,
    ) {
        parent::__construct($name);
    }

    /**
     * @throws NonUniqueResultException
     * @throws Exception
     */
    public function execute(InputInterface $input, OutputInterface $output): int
    {
        // Orders in 'Later sturen Urk' map ophalen
        $parking = $this->parkingRepository->findOneById(Parking::SEND_LATER_URK);
        $warehouseUrk = $this->stockLocationRepository->findOneBy(['id' => StockLocation::MAGAZIJN_URK]);

        $output->writeln('Collecting orders from parking folder Send Later Urk');
        $orders = $this->orderInfoRepository->getOrdersByParking($parking);
        $output->writeln(count($orders) . ' orders found');

        foreach ($orders as $order) {
            if ($order->isCancelledOrder() || $order->isFinishedOrder() || $order->getInvoiceNumber() > 0) {
                $this->removeFromParking($order);
                $output->writeln('Removed order #' . $order->getId() . ' from parking');
                continue;
            }

            foreach ($order->getCartItems() as $cartItem) {
                if ($cartItem->getStatus() & CartItem::FLAG_WAS_NOT_IN_STOCK) {
                    // Als het product 2e hands is direct koerierslijst aanmaken
                    if ($cartItem->getProduct()->isOccasion()) {
                        $this->generateCourierList($order, $cartItem);
                        continue;
                    }

                    // Als er geen voorraad is bij een leverancier direct koerierslijst aanmaken
                    /** @var SupplierStock|null $supplierStock */
                    $supplierStock = $this->supplierStockRepository->findByProduct($cartItem->getProduct());
                    if ($supplierStock === null || !$supplierStock->isInStock()) {
                        $this->generateCourierList($order, $cartItem);
                        continue;
                    }

                    // Als er na 1e dag om 10:00 nog geen voorraad is besteld dan koerierslijst maken
                    $openPurchaseOrders = $this->purchaseOrderRepository->getForProduct($cartItem->getProduct());
                    $ordered = $order->getOrderDateTime();
                    $difference = $ordered->diff(new DateTime());
                    if (
                        $difference->d === 1 &&
                        $ordered->format('H') >= 10 &&
                        count($openPurchaseOrders) === 0
                    ) {
                        $this->generateCourierList($order, $cartItem);
                        continue;
                    }

                    // Als er geen inkooporders zijn mag volgende stap worden overgeslagen
                    if (count($openPurchaseOrders) === 0) {
                        continue;
                    }

                    // Is er besteld en na 2e werkdag van bestelling na 15:00 nog niks binnen in magazijn urk, dan koerierslijst maken
                    $stock = $cartItem->getProduct()->getUnsoldStock($warehouseUrk);
                    if (
                        count($openPurchaseOrders) > 0 &&
                        $difference->d === 2 &&
                        $ordered->format('H') >= 15 &&
                        count($stock) <= 0
                    ) {
                        $this->generateCourierList($order, $cartItem);
                    }
                }
            }
        }

        return Command::SUCCESS;
    }

    /**
     * @throws OptimisticLockException
     * @throws InvalidCourierListStateException
     * @throws InvalidCourierListTypeException
     * @throws InvalidCourierListProductException
     * @throws ORMException
     * @throws Exception
     */
    private function generateCourierList(OrderInfo $order, CartItem $cartItem): ?CourierList
    {
        // Winkel met laagste aantal verkopen van product selecteren als fromStockLocation
        $product = $cartItem->getProduct();
        $stockLocations = [];
        $unsoldStock = $product->getUnsoldStock(
            excludedLocations: [StockLocation::MAGAZIJN_URK, StockLocation::WINKEL_URK],
            includedLocations: StockLocation::STORE_IDS
        );
        $availableStockInDates = [];
        $stockInLocations = $product->getStockInLocations();
        foreach ($unsoldStock as $stock) {
            $location = $stock->getLocation();

            /** @var ?StockInLocation $stockInLocation */
            $stockInLocation = $stockInLocations->filter(function (StockInLocation $stockInLocation) use ($location) {
                return $stockInLocation->getStockLocation() === $location;
            })->first();

            // Kijken of product niet al verkocht is
            if (!$stockInLocation instanceof StockInLocation || $stockInLocation->getStock() <= 0) {
                continue;
            }

            $stockLocations[] = $location;
            // Alleen oudste voorraad pushen naar array
            if (!array_key_exists($location->getId(), $availableStockInDates) || $stock->getDateIn() < $availableStockInDates[$location->getId()]) {
                $availableStockInDates[$location->getId()] = $stock->getDateIn();
            }
        }

        if (count($stockLocations) === 0 && !$order->hasSendLaterDeliveryMailSent()) {
            $this->outOfStockProducer->publish(json_encode([
                'order' => $order->getId(),
                'products' => [
                    $product->getId(),
                ],
                'mailContentId' => MailContent::UKNOWN_DELIVERY_DATE_SUPPLIER,
            ]));

            return null;
        }

        $amountOfOrdersPerStockLocation = $this->stockRepository->getAmountOfOrdersPerStockLocationByProduct($stockLocations, $product);
        if (count($amountOfOrdersPerStockLocation) === 0) {
            // Geen historie dus $amountOfOrdersPerStockLocation handmatig vullen met lege data
            foreach ($stockLocations as $stockLocation) {
                $amountOfOrdersPerStockLocation[] = [
                    'allTime' => 0,
                    'location' => $stockLocation->getId(),
                ];
            }
        }

        usort($amountOfOrdersPerStockLocation, function ($a, $b) {
            return $a['allTime'] <=> $b['allTime'];
        });

        // Winkel met laagste aantal verkopen word de 'van' locatie
        $fromStockLocation = $amountOfOrdersPerStockLocation[0]['location'];

        // Checken of er meerdere winkels zijn met laagste aantal verkopen
        $stores = [$amountOfOrdersPerStockLocation[0]];
        foreach ($amountOfOrdersPerStockLocation as $amountOfOrders) {
            if ($stores[0]['allTime'] === $amountOfOrders['allTime'] && $stores[0]['location'] !== $amountOfOrders['location']) {
                $stores[] = $amountOfOrders;
            }
        }

        // Als er meerdere winkels zijn, 'van' locatie veranderen naar waar de oudste voorraad ligt
        $oldestStock = $availableStockInDates[$stores[0]['location']];
        if (count($stores) > 1) {
            foreach ($stores as $store) {
                if ($availableStockInDates[$store['location']] < $oldestStock) {
                    $fromStockLocation = $store['location'];
                }
            }
        }

        $fromStockLocation = $this->stockLocationRepository->findOneBy(['id' => $fromStockLocation]);
        $toStockLocation = $this->stockLocationRepository->findOneBy(['id' => StockLocation::MAGAZIJN_URK]);

        if (!$order->hasSendLaterDeliveryMailSent()) {
            $this->outOfStockProducer->publish(json_encode([
                'order' => $order->getId(),
                'products' => [
                    $product->getId(),
                ],
                'mailContentId' => MailContent::EXPECTED_DELIVERY_DATE,
            ]));
        }

        return $this->courierListFromOrders
            ->setOrders([$order])
            ->setCartItems([$cartItem])
            ->setFromStockLocation($fromStockLocation)
            ->setToStockLocation($toStockLocation)
            ->setAddToExistingList(true)
            ->generate();
    }

    private function removeFromParking(OrderInfo $order): void
    {
        $currentParking = $order->getParking();
        $order->setParking(null);

        $message = '(auto) Bestelling verplaatst van parkeermap ';
        $message .= $currentParking->getDescription() . ' (' . $currentParking->getId() . ')';
        $message .= ' naar niet geparkeerd.';

        $user = $this->userRepository->find(User::SYSTEM_USER_ADMIN);

        $logInfo = new Info();
        $logInfo
            ->setOrder($order)
            ->setUser($user)
            ->setType('bestelling')
            ->setDatetime(new DateTime())
            ->setData($message);
        $this->entityManager->persist($logInfo);
        $this->entityManager->flush();
    }
}
