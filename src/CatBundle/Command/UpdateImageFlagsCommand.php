<?php

declare(strict_types=1);

namespace CatBundle\Command;

use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\ProductImage;

#[AsCommand(
    name: 'cat:update-image-flags',
    description: 'Update flags for product images where no image has a flag set'
)]
class UpdateImageFlagsCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    /**
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Finding images to update...');

        // Use DQL to find images that need updating
        $sql = "SELECT i.id
        FROM cameranu.artikelen_images i
        WHERE (i.flags&1)=0
          AND i.art_id IN (
            SELECT DISTINCT i2.art_id
            FROM cameranu.artikelen_images i2
            GROUP BY i2.art_id
            HAVING SUM(CASE WHEN (i2.flags&1) = 1 THEN 1 ELSE 0 END) = 0
          )
          AND i.pos = (
            SELECT MIN(i3.pos)
            FROM cameranu.artikelen_images i3
            WHERE i3.art_id = i.art_id
          )";

        $stmt = $this->entityManager->getConnection()->prepare($sql);
        $result = $stmt->executeQuery();
        $imagesToUpdate = $result->fetchAllAssociative();
        $count = count($imagesToUpdate);

        $output->writeln(sprintf('Found %d images to update', $count));

        if ($count === 0) {
            return Command::SUCCESS;
        }

        // Update the flags
        $progressBar = new ProgressBar($output, $count);
        $progressBar->start();

        $batchSize = 100;
        $updated = 0;

        foreach (array_chunk($imagesToUpdate, $batchSize) as $batch) {
            $ids = array_column($batch, 'id');
            $idsPlaceholder = implode(',', array_fill(0, count($ids), '?'));

            $updateSql = "UPDATE cameranu.artikelen_images SET flags = flags | ? WHERE id IN ($idsPlaceholder)";

            $stmt = $this->entityManager->getConnection()->prepare($updateSql);
            $params = [ProductImage::FLAG_MAIN, ...$ids];
            $result = $stmt->executeStatement($params);

            $updated += $result;

            $this->entityManager->clear();
            $progressBar->advance(count($batch));
        }

        $progressBar->finish();
        $output->writeln('');
        $output->writeln(sprintf('Successfully updated %d images', $updated));

        return Command::SUCCESS;
    }
}
