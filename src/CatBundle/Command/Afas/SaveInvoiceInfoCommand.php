<?php

declare(strict_types=1);

namespace CatBundle\Command\Afas;

use JsonException;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\ORMException;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\OptimisticLockException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Services\Afas\PeriodicChecker;
use Webdsign\GlobalBundle\Exception\Afas\InvalidLogException;
use Webdsign\GlobalBundle\Exception\Afas\InvalidRequestException;

#[AsCommand(
    name: 'cat:afas-save-invoice-info',
    description: 'Extract invoice info from AFAS to compare in periodic checker'
)]
class SaveInvoiceInfoCommand extends Command
{
    public function __construct(
        private readonly OrderInfoRepository $orderRepository,
        private readonly PeriodicChecker $periodicChecker,
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'full',
                null,
                InputArgument::OPTIONAL,
                'volledig update? anders afgelopen maand'
            )
            ->addOption(
                'fromYear',
                null,
                InputArgument::OPTIONAL,
                '(Her)bereken vanaf januari ingevuld jaar t/m nu'
            );
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws Exception
     * @throws InvalidLogException
     * @throws JsonException
     * @throws InvalidRequestException
     */
    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $full = (bool)$input->getOption('full') ?? false;
        $fromYear = (int)$input->getOption('fromYear') ?? 2023;

        $output->writeln('Start collecting invoices..');
        $currentYear = (int)date('Y');

        if ($full) {
            $years = range($fromYear, $currentYear);

            foreach ($years as $year) {
                $toMonth = 12;
                if ($year === $currentYear) { ///Wanneer het nog dit jaar is doen we tot en met huidige maand.
                    $currentMonth = (int)date('m');
                    $toMonth = $currentMonth;
                }

                $output->writeln('Year: ' . $year);
                $months = range(1, $toMonth);
                foreach ($months as $month) {
                    $output->writeln('Month: ' . $month);
                    $this->handlePeriod($year, $month);
                    $output->writeln('Done..');
                }
            }
        } else {
            $currentMonth = (int)date('m');
            $previousMonth = (int)date('m', strtotime('-1 month'));
            $previousYear = $previousMonth === 12 ? $currentYear - 1 : $currentYear;

            //Vorige maand
            $output->writeln('Year: ' . $previousYear);
            $output->writeln('Month: ' . $previousMonth);
            $this->handlePeriod($previousYear, $previousMonth);
            $output->writeln('Done..');

            //Deze maand
            $output->writeln('Year: ' . $currentYear);
            $output->writeln('Month: ' . $currentMonth);
            $this->handlePeriod($currentYear, $currentMonth);
        }

        return Command::SUCCESS;
    }

    /**
     * @throws OptimisticLockException
     * @throws InvalidRequestException
     * @throws ORMException
     * @throws JsonException
     * @throws Exception
     * @throws InvalidLogException
     */
    private function handlePeriod(int $year, int $month): void
    {
        $insertQueryStart = 'INSERT INTO `cameranu`.`afas_invoice_info` (order_id, invoice_number, year, origin_id, totalInc) VALUES ';
        $insertQueryEnd = ' ON DUPLICATE KEY UPDATE totalInc = VALUES(totalInc)';
        $statement = $insertQueryStart;
        $counter = 0;
        $afasOrders = $this->periodicChecker->findAfasOrdersForPeriod(0, $year, $month);

        $invoiceNumbers = array_keys($afasOrders);
        $info = $this->orderRepository->getInfoForSavingAfasInvoices($invoiceNumbers);
        $indexedInfo = [];
        foreach ($info as $item) {
            $indexedInfo[$item['invoiceNumber']] = $item;
        }

        foreach ($afasOrders as $key => $totalInc) {
            $orderInfo = $indexedInfo[$key] ?? [];
            $order = array_key_exists('id', $orderInfo) ? (string)$orderInfo['id'] : null;
            $origin = array_key_exists('originId', $orderInfo) ? (string)$orderInfo['originId'] : null;

            $values = sprintf('(%d, %d, %d, %d, "%s"),', $order, $key, $year, $origin, $totalInc);
            if ($order === null) {
                $values = sprintf('(NULL, %d, %d, %d, "%s"),', $key, $year, $origin, $totalInc);
            }

            if ($origin === null) {
                $values = sprintf('(%d, %d, %d, NULL, "%s"),', $order, $key, $year, $totalInc);
            }

            if ($order === null && $origin === null) {
                $values = sprintf('(NULL, %d, %d, NULL, "%s"),', $key, $year, $totalInc);
            }

            $statement .= $values;
            $counter++;

            if ($counter % 1000 === 0) {
                $statement = rtrim($statement, ',') . $insertQueryEnd;
                $this->entityManager->getConnection()->executeQuery($statement);
                $statement = $insertQueryStart;
                $this->entityManager->clear();
            }
        }

        if ($statement !== $insertQueryStart) {
            $statement = rtrim($statement, ',') . $insertQueryEnd;
            $this->entityManager->getConnection()->executeQuery($statement);
        }
    }
}
