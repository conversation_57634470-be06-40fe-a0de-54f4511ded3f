<?php

declare(strict_types=1);

namespace CatBundle\Command\Customer;

use League\Csv\Writer;
use Symfony\Component\Console\Helper\ProgressBar;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\CustomerAddress;
use Webdsign\GlobalBundle\Entity\OrderInfo;

#[AsCommand(
    name: 'cat:update-customer-address'
)]
class UpdateCustomerAddressFromOrder extends Command
{
    private const int BATCH_SIZE = 500;
    private Writer $csvWriter;
    private const array CSV_HEADER = [
        'id',
        'time', //before or after command
        'name',
        'firstname',
        'lastnameprefix',
        'lastname',
        'address',
        'city',
        'country',
        'housenr',
        'housenrext',
        'zipcode'
    ];

    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
        parent::__construct();
        // for backup
        $this->csvWriter = Writer::createFromString();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<info>Running update customer adress</info>');

        $count = $this->getAddressesCount();
        $output->writeln('<info>Count addresses:' . $count . '</info>');
        $output->writeln('<info>Looping addresses</info>');
        $progress = new ProgressBar($output, $count);
        $progress->start();

        $this->csvWriter->insertOne(self::CSV_HEADER);

        $updatedCount = 0;
        foreach ($this->getIterableAddresses() as $address) {
            $batch[] = $address;

            if (count($batch) >= self::BATCH_SIZE) {
                $updatedCount += $this->processAddressBatch($batch);
                $batch = [];
            }

            $progress->advance();
        }

        if (!empty($batch)) {
            $updatedCount += $this->processAddressBatch($batch);
        }

        $fileName = 'old_and_new_addresses.csv';
        $fp = fopen(sys_get_temp_dir() . '/' . $fileName, 'wb');
        fwrite($fp, $this->csvWriter->toString());
        fclose($fp);

        $progress->finish();
        $output->writeln('<info>Done updating customer address</info>');
        $output->writeln('<info>Finished. Updated ' . $updatedCount . ' addresses.</info>');

        return Command::SUCCESS;
    }

    private function getAddressesCount(): int
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('count(ca.id) as count')
            ->from(CustomerAddress::class, 'ca')
            ->join('ca.customer', 'c')
            ->where('ca.address = :empty')
            ->setParameter('empty', '');

        return current($queryBuilder->getQuery()->getResult())['count'];
    }

    private function getIterableAddresses(): iterable
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select('ca', 'c')
            ->from(CustomerAddress::class, 'ca')
            ->join('ca.customer', 'c')
            ->where('ca.address = :empty')
            ->setParameter('empty', '')
            ->getQuery();

        return $query->toIterable();
    }

    private function processAddressBatch(array $addresses): int
    {
        $emails = [];

        foreach ($addresses as $address) {
            $emails[] = $address->getCustomer()?->getEmail();
        }

        $emails = array_filter(array_unique($emails));

        if (empty($emails)) {
            return 0;
        }

        $orders = $this->entityManager->createQuery(
            'SELECT b_n
            FROM Webdsign\GlobalBundle\Entity\OrderInfo as b_n
            WHERE b_n.address <> \'\'
            AND b_n.id IN (
                SELECT MAX(b_n2.id)
                FROM Webdsign\GlobalBundle\Entity\OrderInfo as b_n2
                WHERE b_n2.address <> \'\' AND b_n2.email IN (:emails)
                GROUP BY b_n2.email
            )'
        )->setParameter('emails', $emails)
            ->getResult();

        $orderMap = [];
        foreach ($orders as $order) {
            $orderMap[$order->getEmail()] = $order;
        }

        $updated = 0;

        /** @var CustomerAddress $address */
        foreach ($addresses as $address) {
            $email = $address->getCustomer()?->getEmail();

            if ($email && isset($orderMap[$email])) {
                $this->updateAddress($address, $orderMap[$email]);

                $updated++;
            }
        }

        $this->entityManager->flush();
        $this->entityManager->clear();

        return $updated;
    }

    private function updateAddress(CustomerAddress $address, OrderInfo $order): void
    {
        $this->insertAddressInCsv($address, 'before');

        if ($address->getName() === '') {
            $address->setName($order->getName());
        }

        if ($address->getFirstName() === '') {
            $address->setFirstName($order->getFirstName());
        }

        if ($address->getLastName() === '') {
            $address->setLastName($order->getLastName());
        }

        if ($address->getLastNamePrefix() === '') {
            $address->setLastNamePrefix($order->getNameInsertion());
        }

        if ($address->getAddress() === '') {
            $address->setAddress($order->getAddress());
        }

        if ($address->getCity() === '') {
            $address->setCity($order->getCity());
        }

        if ($address->getCountry() === '') {
            $address->setCountry($order->getCountry());
        }

        if ($address->getHousenr() === '') {
            $address->setHousenr($order->getHousenr());
        }

        if ($address->getHousenrext() === '') {
            $address->setHousenrext($order->getHousenrext());
        }

        if ($address->getZipcode() === '') {
            $address->setZipcode($order->getZipcode());
        }

        $this->insertAddressInCsv($address, 'after');
    }

    private function insertAddressInCsv(CustomerAddress $address, string $time): void
    {
        $insert = [
            $address->getId(),
            $time,
            $address->getName(),
            $address->getFirstName(),
            $address->getLastNamePrefix(),
            $address->getLastName(),
            $address->getAddress(),
            $address->getCity(),
            $address->getCountry(),
            $address->getHousenr(),
            $address->getHousenrext(),
            $address->getZipcode(),
        ];

        $this->csvWriter->insertOne($insert);
    }
}
