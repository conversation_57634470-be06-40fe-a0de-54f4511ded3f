<?php

declare(strict_types=1);

namespace CatBundle\Command\OpenAI;

use CatBundle\Producer\AIProductContent\AIProductContentProducer;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\MenuItem;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\SpecsProfile;
use Webdsign\GlobalBundle\Entity\SpecsProfileRepository;

#[AsCommand(
    name: 'cat:open-ai:product-content',
)]
class AIProductContentCommand extends Command
{
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly AIProductContentProducer $productContentProducer,
        private readonly SpecsProfileRepository $specsProfileRepository,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Retrieve all mirrorless cameras which are roughly 500 products and retrieve 500 lenses so we get a total of 1000 products
        $lensesSpecProfile = $this->specsProfileRepository->findOneBy(['id' => SpecsProfile::LENSES_ID]);
        $cameras = $this->productRepository->findByMenuItem(MenuItem::MIRRORLESS_CAMERA_CATEGORY_ID);
        $lenses = $this->productRepository->findBySpecProfile($lensesSpecProfile, true, true, 500);
        $products = array_merge($lenses, $cameras);

        // Place them in the AIProductContent queue
        foreach ($products as $product) {
            $this->productContentProducer->publish(json_encode(['productId' => $product['id']]));
        }

        return Command::SUCCESS;
    }
}
