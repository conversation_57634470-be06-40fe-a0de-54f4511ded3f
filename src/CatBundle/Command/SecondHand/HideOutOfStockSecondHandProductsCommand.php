<?php

declare(strict_types=1);

namespace CatBundle\Command\SecondHand;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\ProductLog;
use Webdsign\GlobalBundle\Entity\ProductRepository;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\UserRepository;

#[AsCommand(
    name: 'cat:hide-outofstock-secondhand-products',
    description: 'Check stock for secondhand products and hide on website if needed'
)]
class HideOutOfStockSecondHandProductsCommand extends Command
{
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly UserRepository $userRepository,
    ) {
        parent::__construct();
    }

    /**
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $secondhandProducts = $this->productRepository->findVisibleOccasionAndDemoProducts();

        $message = 'flags: %s => %s (%s, %s)';
        $systemUser = $this->userRepository->findSystemUser();

        $includedLocations = [
            StockLocation::MAGAZIJN_URK,
            ...StockLocation::STORE_IDS,
        ];

        /**
         * @var Product $secondhandProduct
         */
        foreach ($secondhandProducts as $secondhandProduct) {
            if ($secondhandProduct->getUnsoldStock(includedLocations: $includedLocations)->count() === 0) {
                $oldFlags = $secondhandProduct->getFlags();
                $newFlags = $oldFlags & ~Product::FLAG_VISIBLE;

                if ($oldFlags !== $newFlags) {
                    $secondhandProduct->setFlags($newFlags);

                    $logMessage = sprintf($message, $oldFlags, $newFlags, __CLASS__, __LINE__);
                    $log = new ProductLog();
                    $log->setProduct($secondhandProduct);
                    $log->setUser($systemUser);
                    $log->setAction($logMessage);

                    $this->entityManager->persist($log);
                }
            }
        }

        $this->entityManager->flush();

        return Command::SUCCESS;
    }
}
