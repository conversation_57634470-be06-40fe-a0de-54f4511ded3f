<?php

namespace CatBundle\Command\PowerReviews;

use CatBundle\Service\PowerReviews\Reviews;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use GuzzleHttp\Exception\ClientException;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\Response;
use Webdsign\GlobalBundle\Entity\PowerReview;
use Webdsign\GlobalBundle\Entity\PowerReviewRepository;
use Webdsign\GlobalBundle\Entity\ProductRepository;

#[AsCommand(
    name: 'cat:powerreviews:import-reviews',
    description: 'Import PowerReviews to own database.',
)]
class ImportReviewsCommand extends Command
{
    private const int BATCH_SIZE = 50;

    public function __construct(
        private readonly PowerReviewRepository $powerReviewRepository,
        private readonly Reviews $reviews,
        private readonly EntityManagerInterface $entityManager,
        private readonly ProductRepository $productRepository,
        private readonly LoggerInterface $powerReviewsLogger,
        string $name = null
    ) {
        parent::__construct($name);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Retrieve active and visible products
        $products = $this->productRepository->getProductFeedData(config: ['page_id']);

        $progressBar = new ProgressBar($output, count($products));
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
        $progressBar->start();

        for ($i = 0; $i <= count($products); $i += self::BATCH_SIZE) {
            $persisted = [];
            for ($j = $i; $j < $i + self::BATCH_SIZE; $j++) {
                if (!array_key_exists($j, $products) || in_array($products[$j]['page_id'], $persisted)) {
                    continue;
                }

                // Get all reviews for current product
                $allReviews = [];
                try {
                    $result = $this->reviews->getReviews($products[$j]['page_id']);
                    while ($result['count'] !== 0) {
                        $allReviews = array_merge($allReviews, $result['reviews']);
                        $result = $this->reviews->getReviews($products[$j]['page_id'], $result['next_page']);
                    }
                } catch (ClientException $clientException) {
                    if ($clientException->getResponse()->getStatusCode() !== Response::HTTP_NOT_FOUND) {
                        $this->powerReviewsLogger->warning($clientException->getMessage());
                    }
                } catch (Exception $exception) {
                    $this->powerReviewsLogger->error($exception->getMessage());
                }

                // If no reviews found, skip product
                if (count($allReviews) === 0) {
                    continue;
                }

                // Count all the ratings
                $totalRating = 0;
                foreach ($allReviews as $review) {
                    $totalRating += $review['rating'];
                }

                // Calculate average rating and save to database
                $product = $this->productRepository->findOneBy(['id' => $products[$j]['page_id']]);
                $powerReview = $this->powerReviewRepository->findOneBy(['product' => $product]);

                if (!$powerReview instanceof PowerReview) {
                    $powerReview = new PowerReview();
                }

                $powerReview->setProduct($product);
                $powerReview->setAmount(count($allReviews));
                $powerReview->setRating($totalRating / count($allReviews));

                $this->entityManager->persist($powerReview);
                $persisted[] = $products[$j]['page_id'];
            }

            $this->entityManager->flush();

            $progressBar->advance(self::BATCH_SIZE);
        }

        return Command::SUCCESS;
    }
}
