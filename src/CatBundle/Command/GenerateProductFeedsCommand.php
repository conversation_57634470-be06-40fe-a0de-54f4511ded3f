<?php

declare(strict_types=1);

namespace CatBundle\Command;

use CatBundle\Service\ProductFeed\ProductFeedService;
use Doctrine\DBAL\Driver\Exception;
use League\Flysystem\FilesystemException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Entity\ProductFeed;
use Webdsign\GlobalBundle\Entity\ProductFeedRepository;

#[AsCommand(
    name: 'cat:generate-productfeeds',
    description: 'Generate the product feeds'
)]
class GenerateProductFeedsCommand extends Command
{
    public function __construct(
        private readonly ProductFeedRepository $productFeedRepository,
        private readonly ProductFeedService $productFeedService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'type',
                null,
                InputOption::VALUE_OPTIONAL,
                'Generate feeds for specific type (product, menuItem etc.)',
                'all'
            )
            ->addOption(
                'partner',
                null,
                InputOption::VALUE_OPTIONAL,
                'generate feed for specific partner',
                'all'
            )
        ;
    }

    /**
     * @throws FilesystemException
     * @throws Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $forType = $input->getOption('type');
        $forPartner = $input->getOption('partner');
        $output->writeln('Collecting parties');
        if ($forType !== 'all' && $forPartner !== 'all') {
            $productFeeds = $this->productFeedRepository->findBy([
                'feedType' => $forType,
                'partner' => $forPartner,
            ]);
        } elseif ($forType !== 'all') {
            $productFeeds = $this->productFeedRepository->findBy(['feedType' => $forType]);
        } elseif ($forPartner !== 'all') {
            $productFeeds = $this->productFeedRepository->findBy(['partner' => $forPartner]);
        } else {
            $productFeeds = $this->productFeedRepository->findAll();
        }
        $output->writeln('Parties collected');

        /* @var ProductFeed $productFeed */
        foreach ($productFeeds as $productFeed) {
            $output->writeln('Start generating feed for ' . $productFeed->getPartner());
            switch ($productFeed->getFeedType()) {
                case 'menuItem':
//                    $message = $this->productFeedService->generateMenuItemFeed($productFeed->getHash(), $output);
                    break;
                case 'product':
                default:
                    $message = $this->productFeedService->generateProductFeed($productFeed->getHash(), null, $output);
                    break;
            }
            $output->writeln($message);
        }

        return Command::SUCCESS;
    }
}
