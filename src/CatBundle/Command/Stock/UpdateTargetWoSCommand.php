<?php

declare(strict_types=1);

namespace CatBundle\Command\Stock;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Webdsign\GlobalBundle\Entity\Maingroup;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Entity\Rootgroup;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Entity\Subgroup;

#[AsCommand(
    name: 'cat:stock:update-target-wos',
    description: 'Update targetWoS in StockInLocation based on StockTargetWeeks for active Cameranu products',
)]
class UpdateTargetWoSCommand extends Command
{
    private const BATCH_SIZE = 100;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly StockLocationRepository $stockLocationRepository,
        #[Autowire('%cameranu_parents%')]
        private readonly array $cameranuParents,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'dry-run',
                null,
                InputOption::VALUE_NONE,
                'Show what would be updated without making changes'
            )
            ->addOption(
                'parent',
                'p',
                InputOption::VALUE_REQUIRED,
                'Process only specific parent location'
            )
            ->addOption(
                'batch-size',
                'b',
                InputOption::VALUE_REQUIRED,
                'Batch size for processing',
                self::BATCH_SIZE
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryRun = $input->getOption('dry-run');
        $parentFilter = $input->getOption('parent');
        $batchSize = (int) $input->getOption('batch-size');

        $io->title('Update Target WoS in StockInLocation');

        if ($dryRun) {
            $io->note('Running in dry-run mode - no changes will be made');
        }

        $parents = $parentFilter ? [$parentFilter] : $this->cameranuParents;
        $io->section('Processing parents: ' . implode(', ', $parents));

        $totalUpdated = 0;

        foreach ($parents as $parent) {
            $io->writeln("Processing parent: {$parent}");

            $mainLocations = $this->stockLocationRepository->findMainLocationsByParent($parent);
            if (empty($mainLocations)) {
                $io->warning("No main locations found for parent: {$parent}");
                continue;
            }

            $targetWeeksByMainGroup = $this->getTargetWeeksByMainGroup($parent);
            if (empty($targetWeeksByMainGroup)) {
                $io->warning("No target weeks configuration found for parent: {$parent}");
                continue;
            }

            foreach ($mainLocations as $stockLocation) {
                $io->writeln("  Processing location: {$stockLocation->getDescription()}");

                $updated = $this->updateTargetWoSForLocation($stockLocation->getId(), $targetWeeksByMainGroup, $batchSize, $dryRun, $io);
                $totalUpdated += $updated;

                $io->writeln("    Updated {$updated} records");
            }
        }

        $io->success("Finished processing. Updated {$totalUpdated} records.");

        return Command::SUCCESS;
    }

    private function getTargetWeeksByMainGroup(string $parent): array
    {
        $sql = '
            SELECT wex_maingroup_id, target_weeks
            FROM cameranu.stock_target_weeks
            WHERE parent = :parent
        ';

        $result = $this->entityManager->getConnection()->executeQuery($sql, ['parent' => $parent]);
        $targetWeeks = [];

        foreach ($result->fetchAllAssociative() as $row) {
            $targetWeeks[$row['wex_maingroup_id']] = (int) $row['target_weeks'];
        }

        return $targetWeeks;
    }

    private function updateTargetWoSForLocation(int $stockLocationId, array $targetWeeksByMainGroup, int $batchSize, bool $dryRun, SymfonyStyle $io): int
    {
        $sql = '
            SELECT
                sil.id,
                sil.target_wos,
                wsg.wex_maingroup_id
            FROM
                cameranu.articles_stock_per_location sil
            INNER JOIN
                cameranu.artikelen p ON sil.product_id = p.id
            INNER JOIN
                cameranu.subgroepen sg ON p.subgroep_id = sg.id
            INNER JOIN
                cameranu.hoofdgroepen hg ON sg.hoofdgroep_id = hg.id
            INNER JOIN
                cameranu.rootgroepen rg ON hg.rootgroep_id = rg.id
            INNER JOIN
                cameranu.wex_subgroepen wsg ON sg.wex_subgroup_id = wsg.id
            WHERE
                sil.stock_location_id = :stockLocationId
                AND (p.flags & :productFlags) = :productFlags
                AND (sg.flags & :sgFlags) = :sgFlags
                AND (hg.flags & :hgFlags) = :hgFlags
                AND (rg.flags & :rgFlags) = :rgFlags
                AND sg.wex_subgroup_id IS NOT NULL
                AND wsg.wex_maingroup_id IS NOT NULL
        ';

        $result = $this->entityManager->getConnection()->executeQuery($sql, [
            'stockLocationId' => $stockLocationId,
            'productFlags' => Product::FLAG_SHOW,
            'sgFlags' => Subgroup::FLAG_ACTIVE + Subgroup::FLAG_VISIBLE,
            'hgFlags' => Maingroup::FLAG_ACTIVE + Maingroup::FLAG_VISIBLE + Maingroup::FLAG_CAMERANU,
            'rgFlags' => Rootgroup::FLAG_ACTIVE + Rootgroup::FLAG_VISIBLE + Rootgroup::FLAG_CAMERANU,
        ]);

        $rows = $result->fetchAllAssociative();
        $count = count($rows);

        $progressBar = $io->createProgressBar($count);
        $progressBar->start();

        $updates = [];
        foreach ($rows as $row) {
            $wexMaingroupId = $row['wex_maingroup_id'];
            $currentTargetWoS = $row['target_wos'];
            $targetWeeks = $targetWeeksByMainGroup[$wexMaingroupId] ?? null;

            if ($targetWeeks !== null && $currentTargetWoS !== $targetWeeks) {
                $updates[] = [
                    'id' => $row['id'],
                    'target_wos' => $targetWeeks,
                ];
            }
        }

        if (!empty($updates) && !$dryRun) {
            $totalUpdated = $this->executeBatchUpdates($updates, $batchSize, $progressBar);
        } else {
            $totalUpdated = count($updates);
        }

        $progressBar->finish();

        return $totalUpdated;
    }

    private function executeBatchUpdates(array $updates, int $batchSize, ProgressBar $progressBar): int
    {
        if (empty($updates)) {
            return 0;
        }

        $connection = $this->entityManager->getConnection();
        $totalUpdated = 0;
        $batches = array_chunk($updates, $batchSize);

        foreach ($batches as $batch) {
            $caseStatements = [];
            $ids = [];
            $params = [];

            foreach ($batch as $index => $update) {
                $idParam = "id_{$index}";
                $targetWosParam = "target_wos_{$index}";

                $caseStatements[] = "WHEN id = :{$idParam} THEN :{$targetWosParam}";
                $params[$idParam] = $update['id'];
                $params[$targetWosParam] = $update['target_wos'];
                $ids[] = $update['id'];
            }

            $caseClause = implode(' ', $caseStatements);
            $idPlaceholders = implode(',', array_fill(0, count($ids), '?'));

            $sql = "
                UPDATE cameranu.articles_stock_per_location
                SET target_wos = CASE {$caseClause} END
                WHERE id IN ({$idPlaceholders})
            ";

            $allParams = array_merge($params, $ids);

            $connection->executeStatement($sql, $allParams);

            $count = count($batch);
            $totalUpdated += $count;
            $progressBar->advance($count);
        }

        return $totalUpdated;
    }
}
