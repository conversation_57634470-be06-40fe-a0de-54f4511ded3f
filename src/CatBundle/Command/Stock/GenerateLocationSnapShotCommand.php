<?php

declare(strict_types=1);

namespace CatBundle\Command\Stock;

use CatB<PERSON>le\Service\Stock\StockHelper;
use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Webdsign\GlobalBundle\Services\Telegram;

#[AsCommand(
    name: 'cat:stock_generate_location_snapshot',
    description: 'Generates a stock location snapshot',
)]
class GenerateLocationSnapShotCommand extends Command
{
    public function __construct(
        private readonly StockHelper $stockHelper,
        private readonly Telegram $telegram,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('cat:stock_generate_location_snapshot')
            ->setDescription('Generates a stock location snapshot');
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    public function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $result = $this->stockHelper->generateLocationSnapShop($output);

            if ($result === false) {
                $errorMsg = 'Couldn\'t create StockLocationSnapshot after 5 tries.';
                $output->writeln('<error>' . $errorMsg . '</error>');
                $this->telegram->sendServerMessage($errorMsg);
                return Command::FAILURE;
            }
        } catch (Exception $e) {
            $errorMsg = 'Error ' . $e->getCode() . ': ' . $e->getMessage();
            $this->telegram->sendServerMessage($errorMsg);
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
