<?php

declare(strict_types=1);

namespace CatBundle\Producer\SqueezelyCRMUpdate;

use OldSound\RabbitMqBundle\RabbitMq\Producer;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AbstractConnection;

class SqueezelyCRMUpdateProducer extends Producer
{
    public const QUEUE_NAME = 'squeezelyCRMUpdate';
    public const EXCHANGE_NAME = 'squeezelyCRMUpdate';
    public const QUEUE_OPTIONS = ['name' => 'squeezelyCRMUpdate', 'declare' => false, 'auto_delete' => true];
    public const EXCHANGE_OPTIONS = ['name' => 'squeezelyCRMUpdate', 'type' => 'fanout'];

    public function __construct(AbstractConnection $conn, ?AMQPChannel $ch = null, $consumerTag = null)
    {
        parent::__construct($conn, $ch, $consumerTag);
        self::setQueueOptions(self::QUEUE_OPTIONS);
        self::setExchangeOptions(self::EXCHANGE_OPTIONS);
    }
}
