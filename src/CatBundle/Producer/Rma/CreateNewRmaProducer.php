<?php

declare(strict_types=1);

namespace CatBundle\Producer\Rma;

use OldSound\RabbitMqBundle\RabbitMq\Producer;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AbstractConnection;
use PhpAmqpLib\Exchange\AMQPExchangeType;

class CreateNewRmaProducer extends Producer
{
    public const QUEUE_NAME = 'create_rma';
    public const EXCHANGE_NAME = 'rma';
    public const QUEUE_OPTIONS = [
        'name' => self::QUEUE_NAME,
        'declare' => false,
        'auto_delete' => true
    ];
    public const EXCHANGE_OPTIONS = [
        'name' => self::EXCHANGE_NAME,
        'type' => AMQPExchangeType::FANOUT,
    ];

    public function __construct(AbstractConnection $conn, AMQPChannel $ch = null, $consumerTag = null)
    {
        parent::__construct($conn, $ch, $consumerTag);
        self::setQueueOptions(self::QUEUE_OPTIONS);
        self::setExchangeOptions(self::EXCHANGE_OPTIONS);
    }
}
