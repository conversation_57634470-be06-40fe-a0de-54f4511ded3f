<?php

declare(strict_types=1);

namespace CatBundle\Producer\AIProductContent;

use OldSound\RabbitMqBundle\RabbitMq\Producer;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AbstractConnection;

class AIProductContentProducer extends Producer
{
    public const QUEUE_NAME = 'AIProductContent';
    public const EXCHANGE_NAME = 'AIProductContent';
    public const QUEUE_OPTIONS = ['name' => 'AIProductContent', 'declare' => false, 'auto_delete' => true];
    public const EXCHANGE_OPTIONS = ['name' => 'AIProductContent', 'type' => 'fanout'];

    public function __construct(AbstractConnection $conn, ?AMQPChannel $ch = null, $consumerTag = null)
    {
        parent::__construct($conn, $ch, $consumerTag);
        self::setQueueOptions(self::QUEUE_OPTIONS);
        self::setExchangeOptions(self::EXCHANGE_OPTIONS);
    }
}
