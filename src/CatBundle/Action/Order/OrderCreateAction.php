<?php

declare(strict_types=1);

namespace CatBundle\Action\Order;

use CatBundle\Form\DataObject\SecondHand\TakeStockDataObject;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use Throwable;
use Webdsign\GlobalBundle\DTO\Api\OrderOriginDataObject;
use Webdsign\GlobalBundle\DTO\Api\PaymentMethodDataObject;
use Webdsign\GlobalBundle\DTO\Api\ShippingMethodDataObject;
use Webdsign\GlobalBundle\Entity\AdminLog;
use Webdsign\GlobalBundle\Entity\InstantQuote\Quote;
use Webdsign\GlobalBundle\Entity\InstantQuote\QuoteRepository;
use Webdsign\GlobalBundle\Entity\PaymentMethod;
use Webdsign\GlobalBundle\Entity\ShipmentMethod;
use Webdsign\GlobalBundle\Exception\Api\InvalidOriginException;
use Webdsign\GlobalBundle\Entity\OrderInfoRepository;
use Webdsign\GlobalBundle\Entity\OriginRepository;
use Webdsign\GlobalBundle\Factory\SecondHand\SecondHandOrderFactory;
use Webdsign\GlobalBundle\Services\Api\AbstractManager;
use Webdsign\GlobalBundle\Services\Api\CustomerManager;
use Webdsign\GlobalBundle\Services\Api\OrderManager;
use Webdsign\GlobalBundle\Services\InstantQuote\QuoteManager;

class OrderCreateAction
{
    public function __construct(
        private readonly CustomerManager $customerManager,
        private readonly OrderManager $orderManager,
        private readonly Security $security,
        private readonly OriginRepository $originRepository,
        private readonly OrderInfoRepository $orderInfoRepository,
        private readonly TranslatorInterface $translator,
        private readonly EntityManagerInterface $entityManager,
        private readonly RequestStack $requestStack,
        private readonly SecondHandOrderFactory $secondHandOrderFactory,
        private readonly QuoteRepository $quoteRepository,
        private readonly QuoteManager $quoteManager,
    ) {
    }

    /**
     * @Route("/order/create", name="order_create")
     */
    public function __invoke(Request $request): Response
    {
        $customerManager = $this->customerManager;
        $orderManager = $this->orderManager;
        $user = $this->security->getUser();

        $customerDto = $customerManager->getFromRequest($request);
        $requestData = json_decode($request->getContent(), false, 512, JSON_THROW_ON_ERROR);

        // Is dit een nieuwe klant?
        if (isset($requestData->newCustomer) && $requestData->newCustomer === true) {
            $newCustomerDto = clone $customerDto;

            try {
                $newCustomerDto = $customerManager->save($newCustomerDto);
            } catch (Throwable $e) {
                return new JsonResponse($this->translator->trans($e->getMessage()), Response::HTTP_BAD_REQUEST);
            }

            $customerDto->id = $newCustomerDto->id;
        }

        $orderDto = $orderManager->createFromCustomer($customerDto);
        $orderDto->reference = $requestData->reference ?? '';
        $orderDto->internalComment = $requestData->notes ?? '';

        $orderDto->paymentMethod = new PaymentMethodDataObject();
        $orderDto->paymentMethod->id = PaymentMethod::PIN_CASH;
        $orderDto->shippingMethod = new ShippingMethodDataObject();
        $orderDto->shippingMethod->id = ShipmentMethod::CASH;

        $originRepository = $this->originRepository;
        $origin = $originRepository->findOneBy([
            'id' => $requestData->origin,
        ]);

        if ($origin === null) {
            throw new InvalidOriginException();
        }

        $orderDto->orderOrigin = new OrderOriginDataObject();
        $orderDto->orderOrigin->id = $origin->getId();
        $orderDto->handledBy = $user->getId();

        foreach ($customerDto->addresses as $address) {
            if ($address->type === AbstractManager::ADDRESS_TYPE_INVOICE || $address->type === AbstractManager::ADDRESS_TYPE_SHIPPING || $address->isMainAddress === true) {
                $address->firstName = $customerDto->firstName;
                $address->lastName = $customerDto->lastName;
                $address->prefix = $customerDto->prefix;
                $address->phoneNumber = $customerDto->phoneNumber;

                $orderDto->addresses[] = $address;
            }
        }

        $orderDto = $orderManager->save($orderDto);
        $orderEntity = $this->orderInfoRepository->find($orderDto->id);
        $quote = $request->getSession()->get('instant_quote');
        $logAddition = $quote ? $this->translator->trans('api.order.frontend.for_second_hand') : '';

        $orderLog = new AdminLog();
        $orderLog
            ->setOrderId($orderDto->id)
            ->setType('bestelling')
            ->setText(
                sprintf(
                    $this->translator->trans('api.order.frontend.create_message'),
                    $logAddition,
                    $this->requestStack->getCurrentRequest()->getClientIp()
                )
            )
            ->setCreator($orderEntity->getHandledBy());

        $entityManager = $this->entityManager;
        $entityManager->persist($orderLog);
        $entityManager->flush();

        if ($quote instanceof Quote) {
            $quote = $this->quoteRepository->find($quote->getId());//refresh is necessary

            $secondHandDto = new TakeStockDataObject();
            $secondHandDto->setOrder($orderEntity);
            $secondHandDto->setQuote($quote);
            $this->secondHandOrderFactory->create($secondHandDto);
            $this->quoteManager->setStatus($quote, Quote::STATUS_DONE);
        }

        $request->getSession()->remove('instant_quote');

        return new JsonResponse($orderDto);
    }
}
