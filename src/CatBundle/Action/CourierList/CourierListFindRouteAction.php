<?php

declare(strict_types=1);

namespace CatBundle\Action\CourierList;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Webdsign\GlobalBundle\Entity\CourierList;

class CourierListFindRouteAction extends AbstractController
{
    /**
     * Redirect koerierslijst naar de juiste route
     *
     * @Route("/courier-list/{courierList}", name="courier-list-find-route-wrong", requirements={"courierList"="\d+"})
     * @Route("/courier-list/redirect/{courierList}", name="courier-list-find-route")
     */
    public function __invoke(CourierList $courierList): Response
    {
        switch ($courierList->getState()) {
            case 'open':
                $state = 'edit';
                break;
            case 'scannable':
                $state = 'dispatch';
                break;
            case 'transit':
            case 'transitIssue':
                $state = 'receive';
                break;
            default:
                $state = $courierList->getState();
                break;
        }

        return $this->redirectToRoute('courier-list-' . $state, ['courierList' => $courierList->getId()]);
    }
}
