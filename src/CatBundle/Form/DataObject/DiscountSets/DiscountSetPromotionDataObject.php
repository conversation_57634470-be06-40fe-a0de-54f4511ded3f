<?php

declare(strict_types=1);

namespace CatBundle\Form\DataObject\DiscountSets;

use DateTimeImmutable;
use Webdsign\GlobalBundle\Services\DiscountSet\DiscountSetPromotionType;

class DiscountSetPromotionDataObject
{
    public ?string $name = null;
    public bool $active = false;
    public DateTimeImmutable $activeFrom;
    public DateTimeImmutable $activeTo;
    public DiscountSetPromotionType $type = DiscountSetPromotionType::BLACK_FRIDAY;
    public array $discounts = [0,0,0,0];
    public ?string $title = null;
    public ?string $subtitle = null;

    public function __construct()
    {
        $now = new DateTimeImmutable('now');

        $this->activeFrom = $now->setTime(00, 00, 00);
        $this->activeTo = $now->setTime(23, 59, 59);
    }
}
