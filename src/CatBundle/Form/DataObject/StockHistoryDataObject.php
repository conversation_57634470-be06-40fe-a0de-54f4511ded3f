<?php

declare(strict_types=1);

namespace CatBundle\Form\DataObject;

use DateTime;

class StockHistoryDataObject
{
    /**
     * @var DateTime
     */
    private DateTime $referenceDate;
    /**
     * @var array
     */
    private array $stockLocationParent;
    /**
     * @var array
     */
    private array $stockLocationParents;

    /**
     * @return DateTime
     */
    public function getReferenceDate(): DateTime
    {
        return $this->referenceDate;
    }

    /**
     * @param DateTime $referenceDate
     * @return StockHistoryDataObject
     */
    public function setReferenceDate(DateTime $referenceDate): StockHistoryDataObject
    {
        $this->referenceDate = $referenceDate;
        return $this;
    }

    /**
     * @return array
     */
    public function getStockLocationParent(): array
    {
        return $this->stockLocationParent;
    }

    /**
     * @param array $stockLocationParent
     * @return StockHistoryDataObject
     */
    public function setStockLocationParent(array $stockLocationParent): StockHistoryDataObject
    {
        $this->stockLocationParent = $stockLocationParent;
        return $this;
    }

    /**
     * @return array
     */
    public function getStockLocationParents(): array
    {
        return $this->stockLocationParents;
    }

    /**
     * @param array $stockLocationParents
     * @return StockHistoryDataObject
     */
    public function setStockLocationParents(array $stockLocationParents): StockHistoryDataObject
    {
        $this->stockLocationParents = $stockLocationParents;
        return $this;
    }
}
