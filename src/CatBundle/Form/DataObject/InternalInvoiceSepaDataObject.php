<?php

declare(strict_types=1);

namespace CatBundle\Form\DataObject;

use Webdsign\GlobalBundle\Entity\InternalInvoice;

class InternalInvoiceSepaDataObject
{
    /**
     * @var InternalInvoice[]|null
     */
    public $invoices;

    /**
     * @return InternalInvoice[]|null
     */
    public function getInvoices(): ?array
    {
        return $this->invoices;
    }

    /**
     * @param InternalInvoice[]|null $invoices
     * @return void
     */
    public function setInvoices($invoices): void
    {
        $this->invoices = $invoices;
    }
}
