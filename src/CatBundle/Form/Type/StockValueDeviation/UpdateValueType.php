<?php

declare(strict_types=1);

namespace CatBundle\Form\Type\StockValueDeviation;

use CatBundle\Form\DataObject\StockValueDeviation\UpdateValueDataObject;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Webdsign\GlobalBundle\Entity\Product;
use Webdsign\GlobalBundle\Form\Type\EntityHiddenType;

class UpdateValueType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     * @return void
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('product', EntityHiddenType::class, [
                'class' => Product::class,
            ])
            ->add('value', HiddenType::class)
            ->add('reason', HiddenType::class)
        ;

        $builder->get('value')
            ->addModelTransformer(new CallbackTransformer(
                function (?float $value) {
                    return $value !== null ? (string) $value : $value;
                },
                function (?string $value) {
                    return $value !== null ? (float) str_replace(',', '.', $value) : $value;
                }
            ))
        ;
    }

    /**
     * @param OptionsResolver $resolver
     * @return void
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => UpdateValueDataObject::class,
        ]);
    }
}
