<?php

declare(strict_types=1);

namespace CatBundle\Form\Type;

use CatB<PERSON>le\Form\DataObject\PosPaymentDataObject;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Webdsign\GlobalBundle\Entity\AdyenPosTerminal;
use Webdsign\GlobalBundle\Entity\OrderInfo;
use Webdsign\GlobalBundle\Form\Type\EntityHiddenType;

class PosPaymentType extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     * @return void
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('order', EntityHiddenType::class, [
                'class' => OrderInfo::class,
                'label' => false,
            ])
            ->add('type', ChoiceType::class, [
                'choices'  => [
                    'adyen.pos.type.pin' => 'pin',
                    'adyen.pos.type.qr' => 'qr'
                ],
                'label' => 'adyen.pos.type',
                // 'choices_as_values' => true,
            ])
            ->add('amount', TextType::class, [
                'label' => 'adyen.pos.amount',
                'required' => true,
                'attr' => [
                    'autocomplete' => 'off'
                ],
            ])
            ->add('adyenPosTerminal', EntityType::class, [
                'class' => AdyenPosTerminal::class,
                // 'choices_as_values' => true,
                'choice_translation_domain' => false,
                'label' => 'adyen.pos.pin',
                'required' => true,
                'choice_label' => static function (?AdyenPosTerminal $posTerminal) {
                    return $posTerminal && $posTerminal->getPinTerminal() ?
                        $posTerminal->getPinTerminal()->getDescription() : null;
                },
                'choice_value' => static function (?AdyenPosTerminal $posTerminal) {
                    return $posTerminal ? $posTerminal->getId() : null;
                },
            ])
            ->add('pay', SubmitType::class, [
                'label' => 'adyen.pos.pay',
                'attr' => [
                    'class' => 'btn btn-sm btn-primary',
                ],
            ])
        ;
    }

    /**
     * @param OptionsResolver $resolver
     * @return void
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => PosPaymentDataObject::class,
        ]);
    }
}
