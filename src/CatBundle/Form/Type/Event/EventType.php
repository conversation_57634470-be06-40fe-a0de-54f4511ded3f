<?php

declare(strict_types=1);

namespace CatBundle\Form\Type\Event;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ButtonType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\DTO\Event\EventDataObject;
use Webdsign\GlobalBundle\Entity\Event\Category;
use Webdsign\GlobalBundle\Entity\Event\ChecklistItem;
use Webdsign\GlobalBundle\Entity\Event\ChecklistItemRepository;
use Webdsign\GlobalBundle\Entity\Event\Event;
use Webdsign\GlobalBundle\Entity\Event\EventInstructor;
use Webdsign\GlobalBundle\Entity\Event\EventInstructorRepository;
use Webdsign\GlobalBundle\Entity\Event\Instructor;
use Webdsign\GlobalBundle\Entity\Event\Location;
use Webdsign\GlobalBundle\Entity\Event\LocationRepository;
use Webdsign\GlobalBundle\Entity\Event\Theme;
use Webdsign\GlobalBundle\Entity\Event\ThemeRepository;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;
use Webdsign\GlobalBundle\Form\EventListener\EventChecklistItemListener;

class EventType extends AbstractType
{
    private ChecklistItemRepository $checklistItemRepository;

    public function __construct(ChecklistItemRepository $checklistItemRepository)
    {
        $this->checklistItemRepository = $checklistItemRepository;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add($builder->create('product', FormType::class, ['inherit_data' => true])
                ->add('productVisible', CheckboxType::class, [
                    'label' => 'Deze instantie is zichtbaar op de website',
                    'required' => false,
                ])
                ->add('startDateAndTime', DateTimeType::class, [
                    'widget' => 'single_text',
                    'label' => 'Startdatum en -tijd',
                ])
                ->add('endDateAndTime', DateTimeType::class, [
                    'widget' => 'single_text',
                    'label' => 'Einddatum en -tijd',
                    'constraints' => [
                        new GreaterThan([
                            'propertyPath' => 'parent.all[startDateAndTime].data'
                        ]),
                    ],
                ])
                ->add('location', EntityType::class, [
                    'label' => 'Locatie van het event',
                    'class' => Location::class,
                    'query_builder' => function (LocationRepository $repository) {
                        return $repository->createQueryBuilder('l');
                    },
                    'choice_label' => function (Location $location) {
                        return $location->getName();
                    },
                ])
                ->add('needsRegistration', CheckboxType::class, [
                    'label' => 'Voor dit event is registratie nodig',
                    'required' => false,
                ])
                ->add('needsPayment', CheckboxType::class, [
                    'label' => 'Dit is een betaald event (prijs aanpassen kan in de admin)',
                    'required' => false,
                ])
                ->add('administrationCosts', CheckboxType::class, [
                    'label' => 'Administratie kosten rekenen',
                    'required' => false,
                ])
                ->add('maximumCapacity', NumberType::class, [
                    'label' => 'Aantal plekken',
                    'required' => false,
                ])
                ->add('externalLink', UrlType::class, [
                    'label' => 'Externe link',
                    'required' => false,
                    'default_protocol' => 'https',
                    'attr' => [
                        'placeholder' => 'https://www.eventbrite.nl/...',
                    ],
                ])
                ->add('externalName', TextType::class, [
                    'label' => 'Externe naam',
                    'required' => false,
                    'attr' => [
                        'placeholder' => 'Bijvoorbeeld "Eventbrite"',
                    ],
                ])
                ->add('programListEvent', TextareaType::class, [
                    'label' => 'Programmalijst',
                    'required' => false,
                    'help' => 'Begin met "# Programma". - _9:00_ Tekst',
                ])
            )
            ->add($builder->create('event', FormType::class, ['inherit_data' => true])
                ->add('visible', CheckboxType::class, [
                    'label' => 'Het hele event is zichtbaar op de website',
                    'required' => false,
                ])
                ->add('canGoOffline', CheckboxType::class, [
                    'label' => 'Dit event mag offline indien alle beschikbare datums in het verleden liggen',
                    'required' => false,
                ])
                ->add('title', TextType::class, [
                    'label' => 'Titel',
                ])
                ->add('description', TextareaType::class, [
                    'label' => 'Beschrijving',
                ])
                ->add('metaTitle', TextType::class, [
                    'label' => 'Meta-titel',
                    'required' => false,
                ])
                ->add('metaDescription', TextareaType::class, [
                    'label' => 'Meta-beschrijving',
                    'required' => false,
                ])
                ->add('fillMeta', ButtonType::class, [
                    'label' => 'Meta-tags automatisch invullen',
                ])
                ->add('programDescription', TextareaType::class, [
                    'label' => 'Hoe ziet de dag eruit?',
                    'required' => false,
                ])
                ->add('programList', TextareaType::class, [
                    'label' => 'Programmalijst',
                    'required' => false,
                    'help' => 'Begin met "# Programma". - _9:00_ Tekst',
                ])
                ->add('practicalInformation', TextareaType::class, [
                    'label' => 'Praktische informatie',
                    'required' => false,
                    'help' => 'Vermeld aantal deelnemers workshops. Naar buiten? Vermeld dat je goed ter been moet zijn',
                ])
                ->add('includedList', TextareaType::class, [
                    'label' => 'Inbegrepen',
                    'required' => false,
                    'help' => 'Begin met "# Wat is bij de prijs inbegrepen?". - i:Overig/Check: Koffie en thee. Of: - i:Navigatie/Close: Lunch',
                ])
                ->add('heroImage', TextType::class, [
                    'label' => 'Hero-afbeelding',
                    'required' => false,
                    'help' => 'Formaat 1920 x 700',
                ])
                ->add('thumbnailImage', TextType::class, [
                    'label' => 'Thumbnail-afbeelding',
                    'required' => false,
                    'help' => 'Formaat 410 x 230',
                ])
                ->add('impressionImages', CollectionType::class, [
                    'label' => 'Beeldimpressie',
                    'entry_type' => TextType::class,
                    'allow_add' => true,
                    'allow_delete' => true,
                    'delete_empty' => true,
                    'entry_options' => [
                        'label' => false,
                    ],
                    'attr' => [
                        'class' => 'collection-sortable',
                    ],
                    'required' => false,
                    'help' => 'Formaat 410px breed. Hoogte max 820 hoog',
                ])
                ->add('category', EntityType::class, [
                    'label' => 'Categorie',
                    'class' => Category::class,
                ])
                ->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $formEvent) {
                    /** @var Event $eventEntity */
                    $eventEntity = $formEvent->getData();
                    $form = $formEvent->getForm();

                    $form->add('instructors', EntityType::class, [
                        'class' => Instructor::class,
                        'choice_label' => 'fullName',
                        'multiple' => true,
                        'by_reference' => false,
                        'data' => $eventEntity
                            ? $eventEntity->getEventInstructors()->map(fn($ei) => $ei->getInstructor())
                            : []
                    ]);
                })
                ->add('level', ChoiceType::class, [
                    'label' => 'Niveau',
                    'choices' => EventConstants::VALID_LEVELS,
                    'choice_label' => static function ($choice) {
                        return $choice;
                    },
                ])
                ->add('checklistItems', EntityType::class, [
                    'label' => 'Zelf meenemen',
                    'class' => ChecklistItem::class,
                    'query_builder' => function (ChecklistItemRepository $repository) {
                        return $repository->createQueryBuilder('c');
                    },
                    'choice_label' => function (ChecklistItem $checklistItem) {
                        return $checklistItem->getName();
                    },
                    'multiple' => true,
                    'attr' => [
                        'class' => 'select2-tags',
                    ],
                    'required' => false,
                ])
                ->add('themes', EntityType::class, [
                    'label' => 'Thema\'s',
                    'class' => Theme::class,
                    'query_builder' => function (ThemeRepository $repository) {
                        return $repository->createQueryBuilder('t')->orderBy('t.name');
                    },
                    'choice_label' => function (Theme $theme) {
                        return $theme->getName();
                    },
                    'multiple' => true,
                    'expanded' => true,
                ])
            )
            ->add('save', SubmitType::class, [
                'label' => 'Opslaan',
            ])
            ->addEventSubscriber(new EventChecklistItemListener($this->checklistItemRepository));

        if($builder->getData()?->programList === null){
            $builder->get('event')->remove('programList');
        }
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver
            ->setDefaults([
                'data_class' => EventDataObject::class,
            ]);
    }
}
