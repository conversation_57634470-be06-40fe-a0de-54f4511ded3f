<?php

namespace CatBundle\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Webdsign\GlobalBundle\Entity\CourierList;
use Webdsign\GlobalBundle\Entity\CourierListProduct;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Form\Type\EntityHiddenType;

class StockSearchType extends AbstractType
{
    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'label' => 'search.autocomplete',
            'product' => null,
        ]);
    }

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('courierList', EntityHiddenType::class, [
                'class' => CourierList::class,
            ])
            ->add('courierListProduct', EntityHiddenType::class, [
                'class' => CourierListProduct::class,
            ])
            ->add('stockLocation', EntityHiddenType::class, [
                'class' => StockLocation::class,
            ])
            ->add('query', TextType::class, [
                'attr' => [
                    'class' => 'autocompleter',
                    'minlength' => 1,
                ],
                'label' => $options['label'],
            ]);
    }

    /**
     * @return string
     */
    public function getBlockPrefix(): string
    {
        return 'stocksearchform';
    }
}
