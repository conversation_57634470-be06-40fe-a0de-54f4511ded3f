<?php

namespace CatBundle\Form\Type;

use CatBundle\Form\DataObject\InternalInvoiceRecipientDataObject;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Webdsign\GlobalBundle\Entity\StockLocation;
use Webdsign\GlobalBundle\Entity\StockLocationRepository;

class SaveInternalInvoiceRecipient extends AbstractType
{
    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('stockLocation', EntityType::class, [
                'class' => StockLocation::class,
                // 'choices_as_values' => true,
                'choice_translation_domain' => false,
                'query_builder' => static function (StockLocationRepository $repository) {
                    return $repository->createQueryBuilder('sl')
                        ->orderBy('sl.parent', 'DESC');
                },
                'choice_label' => static function (?StockLocation $stockLocation) {
                    return $stockLocation ? $stockLocation->getDescription() : null;
                },
                'choice_value' => static function (?StockLocation $stockLocation = null) {
                    return $stockLocation ? $stockLocation->getId() : null;
                },
                'required' => true,
                'label' => 'internalinvoice.tolocation',
            ])
            ->add('recipientName', TextType::class, [
                'label' => 'internalinvoice.name',
            ])
            ->add('recipientEmailAddress', TextType::class, [
                'label' => 'internalinvoice.emailaddress',
            ])
            ->add('save', SubmitType::class, [
                'label' => 'core.save',
                'attr' => [
                    'class' => 'btn btn-sm btn-primary',
                ],
            ]);
    }

    /**
     * @param OptionsResolver $resolver
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver
            ->setDefaults([
                'data_class' => InternalInvoiceRecipientDataObject::class,
            ]);
    }
}
