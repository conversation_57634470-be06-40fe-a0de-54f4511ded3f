<?php

declare(strict_types=1);

namespace CatBundle\Form\Type\SecondHand;

use CatBundle\Form\Filter\SecondHand\DoneListFilter;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Webdsign\GlobalBundle\Entity\StockLocation;

class FilterDoneListType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('stockLocation', EntityType::class, [
            'label' => false,
            'class' => StockLocation::class,
            'required' => false,
            'placeholder' => 'second-hand.quote-list.done.filter.placeholder',
            'choices' => $options['stock_locations'],
            'choice_label' => static function (?StockLocation $stockLocation) {
                return $stockLocation?->getDescription();
            },
            'choice_value' => static function (?StockLocation $stockLocation = null) {
                return $stockLocation?->getId();
            },
            'attr' => [
                'class' => 'form-control input-outline-primary',
            ],
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => DoneListFilter::class,
            'method' => 'GET',
            'csrf_protection' => false,
        ]);

        $resolver->setRequired([
            'stock_locations',
        ]);

        $resolver->addAllowedTypes('stock_locations', 'array');
    }
}
