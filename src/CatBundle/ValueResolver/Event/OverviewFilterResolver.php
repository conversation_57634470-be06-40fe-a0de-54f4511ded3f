<?php

declare(strict_types=1);

namespace CatBundle\ValueResolver\Event;

use CatBundle\DTO\Event\OverviewFilter;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsTargetedValueResolver;
use Symfony\Component\HttpKernel\Controller\ValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;
use Webdsign\GlobalBundle\Constant\EventConstants;
use Webdsign\GlobalBundle\Entity\Event\CategoryRepository;
use Webdsign\GlobalBundle\Entity\Event\InstructorRepository;
use Webdsign\GlobalBundle\Entity\Event\LocationRepository;

#[AsTargetedValueResolver('event.overview.filter')]
class OverviewFilterResolver implements ValueResolverInterface
{
    public function __construct(
        private readonly InstructorRepository $instructorRepository,
        private readonly LocationRepository $locationRepository,
        private readonly CategoryRepository $categoryRepository,
    ) {
    }

    public function resolve(Request $request, ArgumentMetadata $argument): iterable
    {
        $instructor = ($id = $request->query->get('instructor')) === null
            ? null
            : $this->instructorRepository->find($id);

        $location = ($id = $request->query->get('location')) === null
            ? null
            : $this->locationRepository->find($id);

        $category = ($name = $request->query->get('category')) === null
            ? null
            : $this->categoryRepository->findOneBy(['name' => $name]);

        $visible = match ($request->query->get('visible')) {
            0, '0' => false,
            1, '1' => true,
            default => null,
        };

        $expired = match ($request->query->get('expired')) {
            0, '0' => false,
            1, '1' => true,
            default => null,
        };

        $province = $request->query->get('province');
        if ($province !== null && !$this->locationRepository->provinceValid($province)) {
            $province = null;
        }

        $softDelete = match ($request->query->get('softdelete')) {
            1, '1' => true,
            default => false,
        };

        return [new OverviewFilter($instructor, $location, $category, $visible, $expired, $province, $softDelete)];
    }
}
