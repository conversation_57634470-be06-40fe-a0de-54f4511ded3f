version: '3.7'

services:
  app:
    container_name: cnat_app
    hostname: cnat_app
    build:
      context: .
      target: app
      args:
        UID: ${UID-1000}
        # To authenticate to private registry either use username / password, or Oauth key/secret / etc
        # Please add Composer Auth JSON Object
        COMPOSER_AUTH: "{}"
        APP_BASE_DIR: ${APP_BASE_DIR-./app}
    restart: always
    environment:
        # For <PERSON><PERSON>buger to work, it needs the docker host ID
        # - in Mac AND Windows, `host.docker.internal` resolve to Docker host IP
        # - in Linux, `**********` is the host IP
        XDEBUG_CLIENT_HOST: ${XDEBUG_CLIENT_HOST-host.docker.internal}
        TZ: Europe/Amsterdam
        COMPOSER_MEMORY_LIMIT: -1
        COMPOSER_ALLOW_SUPERUSER: 1
        XDEBUG_MODE: debug
        PHP_IDE_CONFIG: "serverName=${CAT_HOSTNAME_DOTENV}"
        SERVICETOOL_HOSTNAME_DOTENV: ${SERVICETOOL_HOSTNAME_DOTENV}
        SERVICETOOL_DOMAIN_DOTENV: ${SERVICETOOL_DOMAIN_DOTENV}
        CAT_HOSTNAME_DOTENV: ${CAT_HOSTNAME_DOTENV}
        CAT_DOMAIN_DOTENV: ${CAT_DOMAIN_DOTENV}
        # SSH_AUTH_SOCK: /ssh-agent
    extra_hosts:
        - "host.docker.internal:host-gateway"

  web:
    container_name: cnat_web
    hostname: cnat_web
    build:
      context: .
      target: web
      args:
        UID: ${UID-1000}
        APP_BASE_DIR: ${APP_BASE_DIR:-./app}
    restart: always
    ports:
      - ${WEB_PORT-8080}:8080
      - ${SSL_PORT-8443}:8443
    environment:
      # The Hostname for the FPM (needs to match the hostname for the `app`)
      PHP_FPM_HOST: app
      PHP_FPM_PORT: ${PHP_PORT-9000}
      VIRTUAL_HOST: servicetool.loc,cat.loc,test-tool.loc,cat-test.loc
      VIRTUAL_PORT: 80
      TZ: Europe/Amsterdam
    networks:
      default:
        aliases:
          - servicetool.loc
          - cat.loc
          - test-tool.loc
          - cat-test.loc

  memcached:
    image: memcached:latest
    container_name: cnat_memcached
    hostname: cnat_memcached
    environment:
      - TZ=Europe/Amsterdam
    expose:
      - 11211

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:6.8.13
    container_name: cnat_elasticsearch
    hostname: cnat_elasticsearch
    environment:
      node.name: cnat_elasticsearch
      bootstrap.memory_lock: true
      discovery.type: single-node
      ES_JAVA_OPTS: "-Xms512m -Xmx512m"
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - esdata1:/usr/share/elasticsearch/data
    ports:
      - 9200:9200

  rabbitmq:
    container_name: cnat_rabmq
    hostname: cnat_rabmq
    image: bitnami/rabbitmq:latest
    ports:
      - 15672:15672
      - 5672:5672
    environment:
      - RABBITMQ_PASSWORD=${RABBITMQ_PASSWORD}
      - RABBITMQ_USERNAME=${RABBITMQ_USERNAME}
      - TZ=Europe/Amsterdam

volumes:
  esdata1:
    driver: local

networks:
  default:
    external: true
    name: nginx-proxy

  # database:
  #   image: bitnami/mariadb:latest
  #   restart: always
  #   ports:
  #     - ${DATABASE_PORT-3306}:3306
  #   environment:
  #     MYSQL_DATABASE: ${MYSQL_DATABASE-db}
  #     MARIADB_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD-toor}
  #   healthcheck:
  #     test: ["CMD-SHELL", 'mysql --database=$MYSQL_DATABASE --user=root --password=$MARIADB_ROOT_PASSWORD --execute="SELECT count(table_name) > 0 FROM information_schema.tables;" --skip-column-names -B']
  #     interval: 30s
  #     timeout: 10s
  #     retries: 5
  #   volumes:
  #     - ./docker/mariadb/database:/docker-entrypoint-initdb.d:rw
  #     - ./docker/mariadb/data:/bitnami/mariadb:rw
