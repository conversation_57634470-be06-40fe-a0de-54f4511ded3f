version: "3.9"

services:
  php:
    volumes:
      - app:/var/www/app:rw
      - ./public/build:/var/www/app/public/build
      - ./assets/js:/var/www/app/assets/js

  web:
    volumes:
      - app:/var/www/app:rw
      - ./public/build:/var/www/app/public/build

  node:
    volumes:
      - app:/var/www/app:rw
      - ./node_modules:/var/www/app/node_modules
      - ./assets/js:/var/www/app/assets/js
      - ./public/build:/var/www/app/public/build

volumes:
    app:

x-mutagen:
  sync:
    defaults:
      permissions:
        defaultOwner: "id:1000"
        defaultGroup: "id:1000"
      ignore:
        vcs: true
        paths:
          - 'node_modules'
          - 'assets/js'
          - 'public/build'

    app:
      alpha: "."
      beta: "volume://app"
      mode: "two-way-safe"
      configurationBeta:
        permissions:
          defaultOwner: "id:1000"
          defaultGroup: "id:1000"
