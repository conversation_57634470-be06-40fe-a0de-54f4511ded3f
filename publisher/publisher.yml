---
project:
    name: CameraNU.nl Admin Tool
    repository: ../../cameranu-admin-tool
#    repository: /var/www/app
deployer:
    #ssh_multiplexing gives problems when using OrbStack, you can disable/enable it using this option
    use_ssh_multiplexing: 0
    binary: ../deployer/deployer.phar
    script: deploy
    php: php
php:
    version: 8.3
branches:
    production: master
    acceptance: develop
releases:
    keep: 1
directories:
    root: /var/www/publisher/cnat
    production: /production
    acceptance: /acceptance
    features: /features
    information: /information
urls:
    production: cat.allesnu.int
    acceptance: cat.acc.int
    features: .admin.acc.int
server:
    host: ************
    port: 22
    auth:
        method: Publisher\Remote\Authentication\PublicKey
        username: deploy
        private-key-file: ~/.ssh/id_ed25519
        public-key-file: ~/.ssh/id_ed25519.pub
configurations:
    parser: "Publisher\Parsers\None"
    template: ../../cameranu-admin-tool/.env.local
    path: /current/.env.local
databases:
    feature-prefix: pub_
    copy: false
    copy-from: database
...
