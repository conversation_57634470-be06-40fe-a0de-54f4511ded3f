# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env            contains default values for the dev environment variables needed by the app
#  * .env.local      uncommitted file with local overrides
#  * .env.prod       committed environment-specific defaults
#  * .env.prod.local uncommitted environment-specific overrides
#
##########################################################################################################
# NOTA BENE: We don't currently use a .env.prod file because the values in this file do not consistently #
#            get loaded. All values for prod are defined in the .env.local files on the prod servers.    #
##########################################################################################################
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> docker ###
COMPOSE_PROFILES=dev

WEB_PORT=8080
SSL_PORT=8443

USER_ID=1000
GROUP_ID=1000

XDEBUG_CLIENT_HOST=host.docker.internal
###< docker ###

###> app ###
ADMIN_URL=http://admin.loc
DASHBOARD_URL=http://dashboard.loc
WEBSITE_URL=https://cameranu.loc
API_URL=http://api.loc

# *_HOSTNAME and *_DOMAIN variables suffixed with _DOTENV to prevent server ENV vars overriding them
CAT_HOSTNAME_DOTENV=cat.loc
CAT_URL=https://${CAT_HOSTNAME_DOTENV}
CAT_DOMAIN_DOTENV=${CAT_URL}

SERVICETOOL_HOSTNAME_DOTENV=servicetool.loc
SERVICETOOL_URL=https://${SERVICETOOL_HOSTNAME_DOTENV}
SERVICETOOL_DOMAIN_DOTENV=${SERVICETOOL_URL}

ASSET_URL=${CAT_URL}

MEMCACHE_HOST=cnat_memcached
MEMCACHE_PORT=11211
MEMCACHE_ADMIN_HOST=cna_memcache
MEMCACHE_ADMIN_PORT=11211

RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_API_PORT=15672
RABBITMQ_USERNAME=cat
RABBITMQ_PASSWORD=cat

WEBSITE_CACHE_WARMUP_RESOLVES=

GIT_PATH=

CLANG_TOKEN=

CLANG_WEBHOOK_URL_CUSTOMER=
CLANG_WEBHOOK_URL_ORDER=
CLANG_WEBHOOK_URL_WISHLIST=
CLANG_WEBHOOK_URL_PASSWORD_RESET=
CLANG_WEBHOOK_URL_PASSWORD_RESET_NEW=
CLANG_WEBHOOK_URL_WISHLIST_NEW=
CLANG_WEBHOOK_URL_ACTIVATE_NEWSLETTER=
CLANG_WEBHOOK_URL_LOST_SHOPCART=
CLANG_WEBHOOK_URL_PRODUCT_UPDATE=
CLANG_WEBHOOK_URL_EVENTS_ORDER=
CLANG_WEBHOOK_URL_EVENTS_REMINDER=
CLANG_WEBHOOK_URL_EVENTS_MESSAGE=
CLANG_WEBHOOK_URL_EVENTS_REVIEW=
CLANG_WEBHOOK_URL_TRADE_IN_PROCESS=
CLANG_WEBHOOK_URL_CROSS_SELL=
CLANG_WEBHOOK_URL_PROMOTIONS_PADDINGTON=
CLANG_WEBHOOK_URL_PHOTO_CONTEST=

CLANG_WEBHOOK_TOKEN_CUSTOMER=
CLANG_WEBHOOK_TOKEN_ORDER=
CLANG_WEBHOOK_TOKEN_WISHLIST=
CLANG_WEBHOOK_TOKEN_PASSWORD_RESET=
CLANG_WEBHOOK_TOKEN_PASSWORD_RESET_NEW=
CLANG_WEBHOOK_TOKEN_WISHLIST_NEW=
CLANG_WEBHOOK_TOKEN_ACTIVATE_NEWSLETTER=
CLANG_WEBHOOK_TOKEN_LOST_SHOPCART=
CLANG_WEBHOOK_TOKEN_PRODUCT_UPDATE=
CLANG_WEBHOOK_TOKEN_EVENTS_ORDER=
CLANG_WEBHOOK_TOKEN_EVENTS_REMINDER=
CLANG_WEBHOOK_TOKEN_EVENTS_MESSAGE=
CLANG_WEBHOOK_TOKEN_EVENTS_REVIEW=
CLANG_WEBHOOK_TOKEN_CROSS_SELL=
CLANG_WEBHOOK_TOKEN_TRADE_IN_PROCESS=
CLANG_WEBHOOK_TOKEN_PROMOTIONS_PADDINGTON=
CLANG_WEBHOOK_TOKEN_PHOTO_CONTEST=

CLANG_FTP_HOST=
CLANG_FTP_USERNAME=
CLANG_FTP_PASSWORD=
CLANG_FTP_DIR=ftp/import_files/
CLANG_FTP_EXPORT_DIR=files/

UPLOAD_ATTACHMENT_MOUNTPOINT=spaces
UPLOAD_FTP_HOST=
UPLOAD_FTP_USERNAME=
UPLOAD_FTP_PASSWORD=

PRODUCTFEED_FTP_USERNAME=
PRODUCTFEED_FTP_PASSWORD=
PRODUCTFEED_FTP_DIR=productfeeds_Homedir
SERVICETOOL_EXPORT_FTP_DIR=productfeeds_Homerdir/servicetool

SAYSIMPLE_FTP_HOST=
SAYSIMPLE_FTP_USERNAME=
SAYSIMPLE_FTP_PASSWORD=

SAYSIMPLE_DOMAIN_WHATSAPP=
SAYSIMPLE_DOMAIN_MESSENGER=

WHATSAPP_NUMBER=

EUROPAFOTO_FTP_HOST=
EUROPAFOTO_FTP_USERNAME=
EUROPAFOTO_FTP_PASSWORD=
EUROPAFOTO_FTP_DIRECTORY=Memberfeed

DISNET_API_KEY=
DISNET_API_URL=
DISNET_KIESKEURIG_FEED_URL=
DISNET_KAMERA_EXPRESS_FEED_URL=
DISNET_CAMERANU_FEED_URL=
DISNET_DIFOX_FEED_URL=

UBL_IMAP_PATH=
UBL_EMAIL=
UBL_EMAIL_PASSWORD=

PACKINGSLIP_EMAIL=
PACKINGSLIP_EMAIL_PASSWORD=

FACEBOOK_APP_ID=
FACEBOOK_APP_SECRET=
FACEBOOK_PAGE_ID=
FACEBOOK_ACCESS_TOKEN=

AMAZON_BUCKET=
AMAZON_REGION=eu-central-1
AMAZON_DISTRIBUTION_ID=
AMAZON_KEY=
AMAZON_SECRET=

PRICEUPDATE_FTP_WORKIT_HOST=
PRICEUPDATE_FTP_WORKIT_USERNAME=
PRICEUPDATE_FTP_WORKIT_PASSWORD=
PRICEUPDATE_FTP_WORKIT_DIR=/
PRICEUPDATE_FTP_OMNIA_DIR=/prijsadvies/

TRUNKRS_TOKEN=

POSTNL_TOKEN=
POSTNL_TRACK_AND_TRACE_URL=https://jouw.postnl.nl/track-and-trace/[::barcode::]-[::country::]-[::postalCode::]
POSTNL_POSTALCODECHECK_URL=https://api.postnl.nl/shipment/checkout/v1/postalcodecheck

ROBIN_SPACE=test
ROBIN_AUTHHEADER=
ROBIN_API_KEY=
ROBIN_API_SECRET=
ROBIN_OUTGOING_API_KEY=

PICKR_PARKING_ID=
PICKR_TOKEN=

VENDIRO_API_KEY=
VENDIRO_API_TOKEN=
VENDIRO_API_URI=https://pre-prod-api.vendiro.nl/client/

VENDIRO_CAMERATOOLS_API_KEY=
VENDIRO_CAMERATOOLS_API_TOKEN=
VENDIRO_CAMERATOOLS_API_URI=https://pre-prod-api.vendiro.nl/client/

CAMERANU_SHORTURL_API_URL=https://cameranu.loc

ADYEN_HMAC_KEY=
ADYEN_POS_API_KEY=
ADYEN_POS_PASSPHRASE=
ADYEN_REFUND_API_KEY=
ADYEN_LIVE_URL_PREFIX=

TWEAKWISE_INSTANCE_KEY=
TWEAKWISE_API_KEY=

ZABBIX_HOSTNAME=
ZABBIX_SERVER=
ZABBIX_PORT=
ZABBIX_STUNNEL_HOST=

SPACES_BUCKET=cameranu01
SPACES_ENDPOINT=https://ams3.digitaloceanspaces.com
SPACES_REGION=ams3
SPACES_KEY=
SPACES_SECRET=

SPRAYPAY_WEBHOOK_REFUND_URL=
SPRAYPAY_WEBSHOP_ID=
SPRAYPAY_API_KEY=
SPRAYPAY_REFUND_URL=

OMNIA_FTP_HOST=
OMNIA_FTP_USERNAME=
OMNIA_FTP_PASSWORD=

SQUEEZELY_API_URL=https://api.squeezely.tech
SQUEEZELY_ACCOUNT=
SQUEEZELY_APIKEY=

GA4_API_SECRET=
GA4_MEASUREMENT_ID=
GA4_API_SECRET_LOCAL=
GA4_MEASUREMENT_ID_LOCAL=
GA4_COLLECT_ENDPOINT_LOCAL=metrics.cameranu.nl/collect
GA4_COLLECT_DEBUG_ENDPOINT_LOCAL=metrics.cameranu.nl/debug/collect

INGRAM_MICRO_FTP_HOST=
INGRAM_MICRO_FTP_USERNAME=
INGRAM_MICRO_FTP_PASSWORD=

SIGMA_FTP_HOST=
SIGMA_FTP_USERNAME=
SIGMA_FTP_PASSWORD=
SIGMA_FTP_PORT=0

RINGFOTO_FTP_HOST=
RINGFOTO_FTP_USERNAME=
RINGFOTO_FTP_PASSWORD=
RINGFOTO_FTP_PORT=0

FOTOVIDEORETAIL_FTP_HOST=
FOTOVIDEORETAIL_FTP_USERNAME=
FOTOVIDEORETAIL_FTP_PASSWORD=
FOTOVIDEORETAIL_FTP_PORT=0
FOTOVIDEORETAIL_FTP_PROTOCOL=

ALSO_FTP_HOST=
ALSO_FTP_USERNAME=
ALSO_FTP_PASSWORD=

RESPONSE_BENELUX_FTP_HOST=
RESPONSE_BENELUX_FTP_USERNAME=
RESPONSE_BENELUX_FTP_PASSWORD=

AC_SYSTEMS_FTP_HOST=
AC_SYSTEMS_FTP_USERNAME=
AC_SYSTEMS_FTP_PASSWORD=

HAMA_FTP_HOST=
HAMA_FTP_USERNAME=
HAMA_FTP_PASSWORD=

SERVIX_FTP_HOST=
SERVIX_FTP_USERNAME=
SERVIX_FTP_PASSWORD=
SERVIX_FTP_PORT=0
SERVIX_FTP_PROTOCOL=

NIKON_FTP_HOST=
NIKON_FTP_USERNAME=
NIKON_FTP_PASSWORD=
NIKON_FTP_PORT=0

DIFOX_FTP_HOST=
DIFOX_FTP_USERNAME=
DIFOX_FTP_PASSWORD=
DIFOX_FTP_PORT=0

EIG_FTP_HOST=
EIG_FTP_USERNAME=
EIG_FTP_PASSWORD=

EIG_MAILBOX_USERNAME=
EIG_MAILBOX_SERVER=
EIG_MAILBOX_IMAP_PORT=993
EIG_MAILBOX_CLIENT_ID=
EIG_MAILBOX_CLIENT_SECRET=

AFAS_API_URL=
AFAS_API_TOKEN=

ADCHIEVE_API_URL=
ADCHIEVE_ACCOUNT_ID=
ADCHIEVE_API_KEY=

OAUTH_DEFAULT_SERVER=
OAUTH_DEFAULT_PORT=
OAUTH_DEFAULT_CLIENT_ID=
OAUTH_DEFAULT_CLIENT_SECRET=

POWER_BI_CLIENT_ID=
POWER_BI_CLIENT_SECRET=
POWER_BI_TENANT_ID=
POWER_BI_REST_API_URL=https://api.powerbi.com
POWER_BI_WORKSPACE_ID=
POWER_BI_MIN_STOCK_DATASET_ID=

MOLLIE_PROFILE_DEFAULT_KEY=
MOLLIE_PROFILE_DEFAULT_ID=
MOLLIE_PROFILE_AMSTERDAM_KEY=
MOLLIE_PROFILE_AMSTERDAM_ID=
MOLLIE_PROFILE_ANTWERPEN_KEY=
MOLLIE_PROFILE_ANTWERPEN_ID=
MOLLIE_PROFILE_APELDOORN_KEY=
MOLLIE_PROFILE_APELDOORN_ID=
MOLLIE_PROFILE_EINDHOVEN_KEY=
MOLLIE_PROFILE_EINDHOVEN_ID=
MOLLIE_PROFILE_GRONINGEN_KEY=
MOLLIE_PROFILE_GRONINGEN_ID=
MOLLIE_PROFILE_ROTTERDAM_KEY=
MOLLIE_PROFILE_ROTTERDAM_ID=
MOLLIE_PROFILE_UTRECHT_ID=
MOLLIE_PROFILE_UTRECHT_KEY=

MOLLIE_WEBHOOK_BASE_URL=

CBS_AZURE_ENDPOINT_OUTGOING_CATALOG_URL=
CBS_AZURE_ENDPOINT_OUTGOING_CATALOG_KEY=
CBS_AZURE_ENDPOINT_OUTGOING_ORDER_URL=
CBS_AZURE_ENDPOINT_OUTGOING_ORDER_KEY=

TRANSCONTINENTA_PURCHASE_ORDER_API_CLIENT_ID=
TRANSCONTINENTA_PURCHASE_ORDER_API_CLIENT_SECRET=
TRANSCONTINENTA_PURCHASE_ORDER_API_CLIENT_BASE_URL=
TRANSCONTINENTA_PURCHASE_ORDER_API_CLIENT_KEY=

DOR_USER=
DOR_PASSWORD=
DOR_API_URL=
DOR_STORE_CODE_URK=
DOR_STORE_CODE_AMSTERDAM=
DOR_STORE_CODE_ROTTERDAM=
DOR_STORE_CODE_GRONINGEN=
DOR_STORE_CODE_EINDHOVEN=
DOR_STORE_CODE_APELDOORN=
DOR_STORE_CODE_UTRECHT=

FAKETIME=

USE_FAKE_INVOICE_NUMBERS=true

ELASTIC_FTP_HOST=
ELASTIC_FTP_USERNAME=
ELASTIC_FTP_PASSWORD=

OPEN_AI_API_KEY=

CLOUDFLARE_ACCOUNT_ID=
CLOUDFLARE_API_TOKEN=

DATAFOREST_FTP_HOST=
DATAFOREST_FTP_USERNAME=
DATAFOREST_FTP_PASSWORD=

COMPETITOR_MONITOR_BASE_URI=https://app.competitormonitor.com
COMPETITOR_MONITOR_USERNAME=
COMPETITOR_MONITOR_PASSWORD=

AGICAP_SFTP_HOST=
AGICAP_SFTP_USERNAME=
AGICAP_SFTP_KEY=
AGICAP_SFTP_PORT=

POWERREVIEWS_APIKEY=
POWERREVIEWS_MERCHANTID=
POWERREVIEWS_BASEURL=

POWERREVIEWS_SFTP_HOST=
POWERREVIEWS_SFTP_PORT=
POWERREVIEWS_SFTP_USER=
POWERREVIEWS_SFTP_PASS=

EXPENSIVE_KITS_PERCENTAGE=3

PRICER_PLAZA_SFTP_HOST=
PRICER_PLAZA_SFTP_PORT=22
PRICER_PLAZA_SFTP_USER=
PRICER_PLAZA_SFTP_PRIVATE_KEY=
PRICER_PLAZA_SFTP_DIR=/Multi/
###< app ###

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=e8d8558b8828cc86db39ddd6f6b6185a
###< symfony/framework-bundle ###

###> symfony/mailer ###
#MAILER_DSN=smtp://user:<EMAIL>:port
MAILER_DSN=smtp://mailhog_smtp:1025
###< symfony/mailer ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
# DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=8.0.32&charset=utf8mb4"
# DATABASE_URL="mysql://app:!ChangeMe!@127.0.0.1:3306/app?serverVersion=10.11.2-MariaDB&charset=utf8mb4"
# DATABASE_URL="postgresql://app:!ChangeMe!@127.0.0.1:5432/app?serverVersion=16&charset=utf8"
DATABASE_CAMERANU_HOST=
DATABASE_CAMERANU_PORT=
DATABASE_CAMERANU_NAME=cameranu
DATABASE_CAMERANU_USERNAME=
DATABASE_CAMERANU_PASSWORD=

DATABASE_CAMERANU_URK_HOST=
DATABASE_CAMERANU_URK_PORT=
DATABASE_CAMERANU_URK_NAME=cameranu_urk
DATABASE_CAMERANU_URK_USERNAME=
DATABASE_CAMERANU_URK_PASSWORD=
###< doctrine/doctrine-bundle ###

###> friendsofsymfony/elastica-bundle ###
ELASTICSEARCH_URL=http://elasticsearch:9200/
###< friendsofsymfony/elastica-bundle ###

###> google/apiclient ###
GOOGLE_API_KEY=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_AUTH_CONFIG=%kernel.project_dir%/path/to/file.json
###< google/apiclient ###

###> knplabs/knp-snappy-bundle ###
WKHTMLTOPDF_BINARY=/usr/local/bin/wkhtmltopdf
WKHTMLTOIMAGE_PATH=/usr/local/bin/wkhtmltoimage
###< knplabs/knp-snappy-bundle ###

###> php-amqplib/rabbitmq-bundle ###
RABBITMQ_URL=amqp://${RABBITMQ_USERNAME}:${RABBITMQ_PASSWORD}@${RABBITMQ_HOST}:${RABBITMQ_PORT}
###< php-amqplib/rabbitmq-bundle ###

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

###> symfony/lock ###
# Choose one of the stores below
# postgresql+advisory://db_user:db_password@localhost/db_name
LOCK_DSN=flock
###< symfony/lock ###
