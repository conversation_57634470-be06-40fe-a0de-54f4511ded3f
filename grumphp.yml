grumphp:
    environment:
        variables:
            GRUMPHP_BIN_DIR: "vendor/bin/"
            GRUMPHP_COMPOSER_DIR: "/"
            # Ignore version errors from PHP CS Fixer (it doesn't support PHP 8.2)
            PHP_CS_FIXER_IGNORE_ENV: 1
    hooks_dir: ~
    hooks_preset: local
    git_hook_variables:
        EXEC_GRUMPHP_COMMAND: "docker compose exec php php"
    stop_on_failure: false
    ignore_unstaged_changes: false
    hide_circumvention_tip: false
    process_timeout: 60
    parallel:
        enabled: false
        max_workers: 32
    fixer:
        enabled: true
        fix_by_default: true
    extensions: []
    tasks:
        yamllint:
            ignore_patterns:
                - /^ansible\/(.*)/
                - /^kubernetes\/(.*)/
        composer:
            file: composer.json
        git_blacklist:
            keywords:
                - ' dd('
                - ' die('
                - 'dump('
                - 'die;'
                - 'exit;'
                - 'ini_set'
                - 'error_reporting'
                - 'set_time_limit'
                - 'Auto-generated Migration: Please modify to your needs!'
                - 'this up() migration is auto-generated, please modify it to your needs'
                - 'this down() migration is auto-generated, please modify it to your needs'
        phpcsfixer2:
            allow_risky: false
            cache_file: ~
            config: .php-cs-fixer.dist.php
            rules: []
            using_cache: ~
            config_contains_finder: false
            verbose: true
            diff: false
            triggered_by: ['php']
        phpstan:
            configuration: phpstan.neon
            use_grumphp_paths: true
            memory_limit: "-1"
