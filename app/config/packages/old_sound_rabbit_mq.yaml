old_sound_rabbit_mq:
    connections:
        default:
            url: '%env(RABBITMQ_URL)%'
            lazy: true
            connection_timeout: 3
            read_write_timeout: 3
            heartbeat: 60
            keepalive: true
    producers:
        create_rma:
            connection: default
            class: CatBundle\Producer\Rma\CreateNewRmaProducer
            exchange_options:
                name: rma
                type: fanout
            queue_options:
                name: create_rma
                arguments: { x-dead-letter-exchange: [S, create_rma_dead_letter_ex], x-dead-letter-routing-key: [S, create_rma_dead_letter_qu] }
        create_clang_wish_list:
            connection: default
            class: CatBundle\Producer\Clang\CreateClangWishlistHookProducer
            exchange_options:
                name: clangWishlistHook
                type: fanout
            queue_options:
                name: create_clang_wish_list
                arguments: { x-dead-letter-exchange: [S, create_clang_wish_list_dead_letter_ex], x-dead-letter-routing-key: [S, create_clang_wish_list_dead_letter_qu] }
        create_squeezely_optin:
            connection: default
            exchange_options:
                name: squeezely
                type: fanout
            queue_options:
                name: create_squeezely_optin
                arguments: { x-dead-letter-exchange: [S, create_squeezely_optin_dead_letter_ex], x-dead-letter-routing-key: [S, create_squeezely_optin_dead_letter_qu] }
        create_service_contact_form:
            connection: default
            class: CatBundle\Producer\ServiceContact\CreateServiceContactFormProducer
            exchange_options:
                name: serviceContactForm
                type: fanout
            queue_options:
                name: create_service_contact_form
                arguments: { x-dead-letter-exchange: [S, create_service_contact_form_dead_letter_ex], x-dead-letter-routing-key: [S, create_service_contact_form_dead_letter_qu] }
        create_ticket:
            connection: default
            class: CatBundle\Producer\Ticket\TicketProducer
            exchange_options:
                name: ticket
                type: fanout
            queue_options:
                name: create_ticket
                arguments: { x-dead-letter-exchange: [S, create_ticket_dead_letter_ex], x-dead-letter-routing-key: [S, create_ticket_dead_letter_qu] }
        create_stock_update:
            connection: default
            class: CatBundle\Producer\StockUpdate\StockUpdateProducer
            exchange_options:
                name: stock_update
                type: fanout
            queue_options:
                name: create_stock_update
                arguments: { x-dead-letter-exchange: [S, create_stock_update_dead_letter_ex], x-dead-letter-routing-key: [S, create_stock_update_dead_letter_qu] }
        create_out_of_stock:
            connection: default
            class: CatBundle\Producer\OutOfStock\OutOfStockProducer
            exchange_options:
                name: out_of_stock
                type: fanout
            queue_options:
                name: create_out_of_stock
                arguments: { x-dead-letter-exchange: [S, create_out_of_stock_dead_letter_ex], x-dead-letter-routing-key: [S, create_out_of_stock_dead_letter_qu] }
        create_vendiro:
            connection: default
            class: CatBundle\Producer\Vendiro\VendiroProducer
            exchange_options:
                name: vendiro
                type: fanout
            queue_options:
                name: create_vendiro
                arguments: { x-dead-letter-exchange: [S, create_vendiro_dead_letter_ex], x-dead-letter-routing-key: [S, create_vendiro_dead_letter_qu] }
        create_robin_hook:
            connection: default
            class: CatBundle\Producer\Robin\RobinHookProducer
            exchange_options:
                name: robin_hook
                type: fanout
            queue_options:
                name: create_robin_hook
                arguments: { x-dead-letter-exchange: [S, create_robin_hook_dead_letter_ex], x-dead-letter-routing-key: [S, create_robin_hook_dead_letter_qu] }
        create_stockValueUpdate:
            connection: default
            exchange_options:
                name: create_stockValueUpdate
                type: fanout
            queue_options:
                name: create_stockValueUpdate
                arguments: { x-dead-letter-exchange: [S, create_stockValueUpdate_dead_letter_ex], x-dead-letter-routing-key: [S, create_stockValueUpdate_dead_letter_qu] }
            class: CatBundle\Producer\StockValueUpdate\StockValueUpdateProducer
        productUpdate:
            connection: default
            exchange_options:
                name: productUpdate
                type: fanout
            queue_options:
                name: productUpdate
                arguments: { x-dead-letter-exchange: [S, productUpdate_dead_letter_ex], x-dead-letter-routing-key: [S, productUpdate_dead_letter_qu] }
            class: CatBundle\Producer\ProductUpdate\ProductUpdateProducer
        clangOrderHook:
            connection: default
            exchange_options:
                name: clangOrderHook
                type: fanout
            queue_options:
                name: clangOrderHook
                arguments: { x-dead-letter-exchange: [S, clangOrderHook_dead_letter_ex], x-dead-letter-routing-key: [S, clangOrderHook_dead_letter_qu] }
            class: CatBundle\Producer\ClangOrderHook\ClangOrderHookProducer
        clangMail:
            connection: default
            exchange_options:
                name: clangMail
                type: fanout
            queue_options:
                name: clangMail
                arguments: { x-dead-letter-exchange: [S, clangMail_dead_letter_ex], x-dead-letter-routing-key: [S, clangMail_dead_letter_qu] }
            class: CatBundle\Producer\ClangMail\ClangMailProducer
        squeezelyPostOrder:
            connection: default
            exchange_options:
                name: squeezelyPostOrder
                type: fanout
            queue_options:
                name: squeezelyPostOrder
                arguments: { x-dead-letter-exchange: [S, squeezelyPostOrder_dead_letter_ex], x-dead-letter-routing-key: [S, squeezelyPostOrder_dead_letter_qu] }
            class: CatBundle\Producer\Squeezely\PostOrderProducer
        squeezelyPostReturn:
            connection: default
            exchange_options:
                name: squeezelyPostReturn
                type: fanout
            queue_options:
                name: squeezelyPostReturn
                arguments: { x-dead-letter-exchange: [S, squeezelyPostReturn_dead_letter_ex], x-dead-letter-routing-key: [S, squeezelyPostReturn_dead_letter_qu] }
            class: CatBundle\Producer\Squeezely\PostReturnProducer
        google_analytics_post_return:
            connection: default
            exchange_options:
                name: google_analytics_post_return
                type: fanout
            queue_options:
                name: google_analytics_post_return
                arguments: { x-dead-letter-exchange: [S, google_analytics_post_return_dead_letter_ex], x-dead-letter-routing-key: [S, google_analytics_post_return_dead_letter_qu] }
            class: CatBundle\Producer\GoogleAnalytics\PostReturnProducer
        stockLog:
            connection: default
            exchange_options:
                name: stockLog
                type: fanout
            queue_options:
                name: stockLog
                arguments: { x-dead-letter-exchange: [S, stockLog_dead_letter_ex], x-dead-letter-routing-key: [S, stockLog_dead_letter_qu] }
            class: CatBundle\Producer\StockLog\StockLogProducer
        customer_update:
            connection: default
            exchange_options:
                name: customer_update
                type: fanout
            queue_options:
                name: customer_update
                arguments: { x-dead-letter-exchange: [S, customer_update_dead_letter_ex], x-dead-letter-routing-key: [S, customer_update_dead_letter_qu] }
            class: CatBundle\Producer\CustomerUpdate\CustomerUpdateProducer
        newsletter_subscribe:
            connection: default
            exchange_options:
                name: newsletter_subscribe
                type: fanout
            queue_options:
                name: newsletter_subscribe
                arguments: { x-dead-letter-exchange: [S, newsletter_subscribe_dead_letter_ex], x-dead-letter-routing-key: [S, newsletter_subscribe_dead_letter_qu] }
            class: CatBundle\Producer\NewsletterSubscribe\NewsletterSubscribeProducer
        afas.debtor:
            connection: default
            exchange_options:
                name: afas_debtor
                type: fanout
            queue_options:
                name: afas_debtor
                arguments: { x-dead-letter-exchange: [S, afas_debtor_dead_letter_ex], x-dead-letter-routing-key: [S, afas_debtor_dead_letter_qu] }
            class: CatBundle\Producer\Afas\Debtor\Producer
        afas.order:
            connection: default
            exchange_options:
                name: afas_order
                type: fanout
            queue_options:
                name: afas_order
                arguments: { x-dead-letter-exchange: [S, afas_order_dead_letter_ex], x-dead-letter-routing-key: [S, afas_order_dead_letter_qu] }
            class: CatBundle\Producer\Afas\Order\Producer
        event_messages:
            connection: default
            exchange_options:
                name: event_messages
                type: fanout
            queue_options:
                name: event_messages
                arguments: { x-dead-letter-exchange: [S, event_messages_dead_letter_ex], x-dead-letter-routing-key: [S, event_messages_dead_letter_qu] }
            class: CatBundle\Producer\EventMessages\EventMessagesProducer
        event_refunds:
            connection: default
            exchange_options:
                name: event_refunds
                type: fanout
            queue_options:
                name: event_refunds
                arguments: { x-dead-letter-exchange: [S, event_refunds_dead_letter_ex], x-dead-letter-routing-key: [S, event_refunds_dead_letter_qu] }
            class: CatBundle\Producer\EventRefunds\EventRefundsProducer
        second_hand_update_base_formula:
            connection: default
            exchange_options:
                name: second_hand_update_base_formula
                type: fanout
            queue_options:
                name: second_hand_update_base_formula
                arguments: { x-dead-letter-exchange: [S, second_hand_update_base_formula_dead_letter_ex], x-dead-letter-routing-key: [S, second_hand_update_base_formula_dead_letter_qu] }
            class: CatBundle\Producer\SecondHand\SecondHandUpdateBaseFormulaProducer
        dorRegisterProduct:
            connection: default
            exchange_options:
                name: dorRegisterProduct
                type: fanout
            queue_options:
                name: dorRegisterProduct
                arguments: { x-dead-letter-exchange: [S, dorRegisterProduct_dead_letter_ex], x-dead-letter-routing-key: [S, dorRegisterProduct_dead_letter_qu] }
            class: CatBundle\Producer\Dor\RegisterProductProducer
        dorTransferProduct:
            connection: default
            exchange_options:
                name: dorTransferProduct
                type: fanout
            queue_options:
                name: dorTransferProduct
                arguments: { x-dead-letter-exchange: [S, dorTransferProduct_dead_letter_ex], x-dead-letter-routing-key: [S, dorTransferProduct_dead_letter_qu] }
            class: CatBundle\Producer\Dor\TransferProductProducer
        mollieNotification:
            connection: default
            exchange_options:
                name: mollieNotification
                type: fanout
            queue_options:
                name: mollieNotification
                arguments: { x-dead-letter-exchange: [S, mollieNotification_dead_letter_ex], x-dead-letter-routing-key: [S, mollieNotification_dead_letter_qu] }
            class: CatBundle\Producer\Mollie\MollieNotificationProducer
        clangTradeInHook:
            connection: default
            exchange_options:
                name: clangTradeInHook
                type: fanout
            queue_options:
                name: clangTradeInHook
                arguments: { x-dead-letter-exchange: [S, clangTradeInHook_dead_letter_ex], x-dead-letter-routing-key: [S, clangTradeInHook_dead_letter_qu] }
            class: CatBundle\Producer\ClangTradeInHook\ClangTradeInHookProducer
        dorInternalTransfer:
            connection: default
            exchange_options:
                name: dorInternalTransfer
                type: fanout
            queue_options:
                name: dorInternalTransfer
                arguments: { x-dead-letter-exchange: [S, dorInternalTransfer_dead_letter_ex], x-dead-letter-routing-key: [S, dorInternalTransfer_dead_letter_qu] }
            class: CatBundle\Producer\Dor\InternalTransferProducer
        discountCodeBatch:
            connection: default
            exchange_options:
                name: discountCodeBatch
                type: fanout
            queue_options:
                name: discountCodeBatch
                arguments: { x-dead-letter-exchange: [S, discountCodeBatch_dead_letter_ex], x-dead-letter-routing-key: [S, discountCodeBatch_dead_letter_qu] }
            class: CatBundle\Producer\DiscountCodeBatch\DiscountCodeBatchProducer
        importSecondhandProduct:
            connection: default
            exchange_options:
                name: importSecondhandProduct
                type: fanout
            queue_options:
                name: importSecondhandProduct
                arguments: { x-dead-letter-exchange: [S, importSecondhandProduct_dead_letter_ex], x-dead-letter-routing-key: [S, importSecondhandProduct_dead_letter_qu] }
            class: CatBundle\Producer\ImportSecondhandProduct\ImportSecondhandProductProducer
        promotionsPaddington:
            connection: default
            exchange_options:
                name: promotionsPaddington
                type: fanout
            queue_options:
                name: promotionsPaddington
                arguments: { x-dead-letter-exchange: [S, promotionsPaddington_dead_letter_ex], x-dead-letter-routing-key: [S, promotionsPaddington_dead_letter_qu] }
            class: CatBundle\Producer\PromotionsPaddington\PromotionsPaddingtonProducer
        clangCrossSellWebhook:
            connection: default
            exchange_options:
                name: clangCrossSellWebhook
                type: fanout
            queue_options:
                name: clangCrossSellWebhook
                arguments: { x-dead-letter-exchange: [S, clangCrossSellWebhook_dead_letter_ex], x-dead-letter-routing-key: [S, clangCrossSellWebhook_dead_letter_qu] }
            class: CatBundle\Producer\ClangCrossSellWebhook\ClangCrossSellWebhookProducer
        photoContestCampaignEntryCreate:
            connection: default
            exchange_options:
                name: photoContestCampaignEntryCreate
                type: fanout
            queue_options:
                name: photoContestCampaignEntryCreate
                arguments: { x-dead-letter-exchange: [S, photoContestCampaignEntryCreate_dead_letter_ex], x-dead-letter-routing-key: [S, photoContestCampaignEntryCreate_dead_letter_qu] }
            class: CatBundle\Producer\PhotoContestCampaignEntryCreate\PhotoContestCampaignEntryCreateProducer
        AIProductContent:
            connection: default
            exchange_options:
                name: AIProductContent
                type: fanout
            queue_options:
                name: AIProductContent
                arguments: { x-dead-letter-exchange: [S, AIProductContent_dead_letter_ex], x-dead-letter-routing-key: [S, AIProductContent_dead_letter_qu] }
            class: CatBundle\Producer\AIProductContent\AIProductContentProducer
        squeezelyCRMUpdate:
            connection: default
            exchange_options:
                name: squeezelyCRMUpdate
                type: fanout
            queue_options:
                name: squeezelyCRMUpdate
                arguments: { x-dead-letter-exchange: [S, squeezelyCRMUpdate_dead_letter_ex], x-dead-letter-routing-key: [S, squeezelyCRMUpdate_dead_letter_qu] }
            class: CatBundle\Producer\SqueezelyCRMUpdate\SqueezelyCRMUpdateProducer
    consumers:
        create_rma:
            connection: default
            exchange_options:
                name: rma
                type: fanout
            idle_timeout: 60
            idle_timeout_exit_code: 0
            queue_options:
                name: create_rma
                arguments: { x-dead-letter-exchange: [S, create_rma_dead_letter_ex], x-dead-letter-routing-key: [S, create_rma_dead_letter_qu] }
            callback: consumer.create_rma
        create_rma_dead_letter:
            connection: default
            exchange_options:
                name: create_rma_dead_letter_ex
                type: fanout
            queue_options:
                name: create_rma_dead_letter_qu
            callback: consumer.create_rma_dead_letter
        create_tweakwise_product:
            connection: default
            exchange_options:
                name: tweakwise
                type: fanout
            queue_options:
                name: create_tweakwise_product
                arguments: { x-dead-letter-exchange: [S, create_tweakwise_product_dead_letter_ex], x-dead-letter-routing-key: [S, create_tweakwise_product_dead_letter_qu] }
            callback: consumer.create_tweakwise_product
            qos_options:
                prefetch_size: 0
                prefetch_count: 100
                global: false
        create_tweakwise_product_dead_letter:
            connection: default
            exchange_options:
                name: create_tweakwise_product_dead_letter_ex
                type: fanout
            queue_options:
                name: create_tweakwise_product_dead_letter_qu
            callback: consumer.create_tweakwise_product_dead_letter
        create_clang_wish_list:
            connection: default
            exchange_options:
                name: clangWishlistHook
                type: fanout
            queue_options:
                name: create_clang_wish_list
                arguments: { x-dead-letter-exchange: [S, create_clang_wish_list_dead_letter_ex], x-dead-letter-routing-key: [S, create_clang_wish_list_dead_letter_qu] }
            callback: consumer.create_clang_wish_list
        create_clang_wish_list_dead_letter:
            connection: default
            exchange_options:
                name: create_clang_wish_list_dead_letter_ex
                type: fanout
            queue_options:
                name: create_clang_wish_list_dead_letter_qu
            callback: consumer.create_clang_wish_list_dead_letter
        create_squeezely_optin:
            connection: default
            exchange_options:
                name: squeezely
                type: fanout
            queue_options:
                name: create_squeezely_optin
                arguments: { x-dead-letter-exchange: [S, create_squeezely_optin_dead_letter_ex], x-dead-letter-routing-key: [S, create_squeezely_optin_dead_letter_qu] }
            callback: CatBundle\Consumer\Squeezely\OptinConsumer
            qos_options:
                prefetch_size: 0
                prefetch_count: 100
                global: false
        create_squeezely_optin_dead_letter:
            connection: default
            exchange_options:
                name: create_squeezely_optin_dead_letter_ex
                type: fanout
            queue_options:
                name: create_squeezely_optin_dead_letter_qu
            callback: CatBundle\Consumer\Squeezely\OptinDeadLetter
        create_order_reference:
            connection: default
            exchange_options:
                name: order
                type: fanout
            queue_options:
                name: create_order_reference
                arguments: { x-dead-letter-exchange: [S, create_order_reference_dead_letter_ex], x-dead-letter-routing-key: [S, create_order_reference_dead_letter_qu] }
            callback: consumer.create_order_reference
        create_order_reference_dead_letter:
            connection: default
            exchange_options:
                name: create_order_reference_dead_letter_ex
                type: fanout
            queue_options:
                name: create_order_reference_dead_letter_qu
            callback: consumer.create_order_reference_dead_letter
        create_service_contact_form:
            connection: default
            exchange_options:
                name: serviceContactForm
                type: fanout
            queue_options:
                name: create_service_contact_form
                arguments: { x-dead-letter-exchange: [S, create_service_contact_form_dead_letter_ex], x-dead-letter-routing-key: [S, create_service_contact_form_dead_letter_qu] }
            callback: consumer.create_service_contact_form
        create_service_contact_form_dead_letter:
            connection: default
            exchange_options:
                name: create_service_contact_form_dead_letter_ex
                type: fanout
            queue_options:
                name: create_service_contact_form_dead_letter_qu
            callback: consumer.create_service_contact_form_dead_letter
        create_ticket:
            connection: default
            exchange_options:
                name: ticket
                type: fanout
            queue_options:
                name: create_ticket
                arguments: { x-dead-letter-exchange: [S, create_ticket_dead_letter_ex], x-dead-letter-routing-key: [S, create_ticket_dead_letter_qu] }
            callback: consumer.create_ticket
        create_ticket_dead_letter:
            connection: default
            exchange_options:
                name: create_ticket_dead_letter_ex
                type: fanout
            queue_options:
                name: create_ticket_dead_letter_qu
            callback: consumer.create_ticket_dead_letter
        create_stock_update:
            connection: default
            exchange_options:
                name: stock_update
                type: fanout
            queue_options:
                name: create_stock_update
                arguments: { x-dead-letter-exchange: [S, create_stock_update_dead_letter_ex], x-dead-letter-routing-key: [S, create_stock_update_dead_letter_qu] }
            callback: consumer.create_stock_update
        create_stock_update_dead_letter:
            connection: default
            exchange_options:
                name: create_stock_update_dead_letter_ex
                type: fanout
            queue_options:
                name: create_stock_update_dead_letter_qu
            callback: consumer.create_stock_update_dead_letter
        create_out_of_stock:
            connection: default
            exchange_options:
                name: out_of_stock
                type: fanout
            queue_options:
                name: create_out_of_stock
                arguments: { x-dead-letter-exchange: [S, create_out_of_stock_dead_letter_ex], x-dead-letter-routing-key: [S, create_out_of_stock_dead_letter_qu] }
            callback: consumer.create_out_of_stock
        create_out_of_stock_dead_letter:
            connection: default
            exchange_options:
                name: create_out_of_stock_dead_letter_ex
                type: fanout
            queue_options:
                name: create_out_of_stock_dead_letter_qu
                arguments: { x-dead-letter-exchange: [S, out_of_stock], x-dead-letter-routing-key: [S, create_out_of_stock], x-message-ttl: [I, 300000] }
            callback: consumer.create_out_of_stock_dead_letter
        create_robin_hook:
            connection: default
            exchange_options:
                name: robin_hook
                type: fanout
            queue_options:
                name: create_robin_hook
                arguments: { x-dead-letter-exchange: [S, create_robin_hook_dead_letter_ex], x-dead-letter-routing-key: [S, create_robin_hook_dead_letter_qu] }
            callback: consumer.create_robin_hook
        create_robin_hook_dead_letter:
            connection: default
            exchange_options:
                name: create_robin_hook_dead_letter_ex
                type: fanout
            queue_options:
                name: create_robin_hook_dead_letter_qu
            callback: consumer.create_robin_hook_dead_letter
        create_vendiro:
            connection: default
            exchange_options:
                name: vendiro
                type: fanout
            queue_options:
                name: create_vendiro
                arguments: { x-dead-letter-exchange: [S, create_vendiro_dead_letter_ex], x-dead-letter-routing-key: [S, create_vendiro_dead_letter_qu] }
            callback: consumer.create_vendiro
        create_vendiro_dead_letter:
            connection: default
            exchange_options:
                name: create_vendiro_dead_letter_ex
                type: fanout
            queue_options:
                name: create_vendiro_dead_letter_qu
            callback: consumer.create_vendiro_dead_letter
        create_stockValueUpdate:
            connection: default
            exchange_options:
                name: create_stockValueUpdate
                type: fanout
            queue_options:
                name: create_stockValueUpdate
                arguments: { x-dead-letter-exchange: [S, create_stockValueUpdate_dead_letter_ex], x-dead-letter-routing-key: [S, create_stockValueUpdate_dead_letter_qu] }
            callback: consumer.create_stockValueUpdate
        create_stockValueUpdate_dead_letter:
            connection: default
            exchange_options:
                name: create_stockValueUpdate_dead_letter_ex
                type: fanout
            queue_options:
                name: create_stockValueUpdate_dead_letter_qu
                arguments: { x-dead-letter-exchange: [S, create_stockValueUpdate], x-dead-letter-routing-key: [S, create_stockValueUpdate], x-message-ttl: [I, 120000] }
            callback: consumer.create_stockValueUpdate_dead_letter
        productUpdate:
            connection: default
            exchange_options:
                name: productUpdate
                type: fanout
            queue_options:
                name: productUpdate
                arguments: { x-dead-letter-exchange: [S, productUpdate_dead_letter_ex], x-dead-letter-routing-key: [S, productUpdate_dead_letter_qu] }
            callback: consumer.productUpdate
        productUpdate_dead_letter:
            connection: default
            exchange_options:
                name: productUpdate_dead_letter_ex
                type: fanout
            queue_options:
                name: productUpdate_dead_letter_qu
            callback: consumer.productUpdate_dead_letter
        clangOrderHook:
            connection: default
            exchange_options:
                name: clangOrderHook
                type: fanout
            queue_options:
                name: clangOrderHook
                arguments: { x-dead-letter-exchange: [S, clangOrderHook_dead_letter_ex], x-dead-letter-routing-key: [S, clangOrderHook_dead_letter_qu] }
            callback: consumer.clangOrderHook
        clangOrderHook_dead_letter:
            connection: default
            exchange_options:
                name: clangOrderHook_dead_letter_ex
                type: fanout
            queue_options:
                name: clangOrderHook_dead_letter_qu
            callback: consumer.clangOrderHook_dead_letter
        clangMail:
            connection: default
            exchange_options:
                name: clangMail
                type: fanout
            queue_options:
                name: clangMail
                arguments: { x-dead-letter-exchange: [S, clangMail_dead_letter_ex], x-dead-letter-routing-key: [S, clangMail_dead_letter_qu] }
            callback: consumer.clangMail
        clangMail_dead_letter:
            connection: default
            exchange_options:
                name: clangMail_dead_letter_ex
                type: fanout
            queue_options:
                name: clangMail_dead_letter_qu
                arguments: { x-dead-letter-exchange: [S, clangMail], x-dead-letter-routing-key: [S, clangMail], x-message-ttl: [I, 60000] }
            callback: consumer.clangMail_dead_letter
        squeezelyPostOrder:
            connection: default
            exchange_options:
                name: squeezelyPostOrder
                type: fanout
            queue_options:
                name: squeezelyPostOrder
                arguments: { x-dead-letter-exchange: [S, squeezelyPostOrder_dead_letter_ex], x-dead-letter-routing-key: [S, squeezelyPostOrder_dead_letter_qu] }
            callback: CatBundle\Consumer\Squeezely\PostOrderConsumer
        squeezelyPostOrder_dead_letter:
            connection: default
            exchange_options:
                name: squeezelyPostOrder_dead_letter_ex
                type: fanout
            queue_options:
                name: squeezelyPostOrder_dead_letter_qu
            callback: CatBundle\Consumer\Squeezely\PostOrderDeadLetter
        squeezelyPostReturn:
            connection: default
            exchange_options:
                name: squeezelyPostReturn
                type: fanout
            queue_options:
                name: squeezelyPostReturn
                arguments: { x-dead-letter-exchange: [S, squeezelyPostReturn_dead_letter_ex], x-dead-letter-routing-key: [S, squeezelyPostReturn_dead_letter_qu] }
            callback: CatBundle\Consumer\Squeezely\PostReturnConsumer
        squeezelyPostReturn_dead_letter:
            connection: default
            exchange_options:
                name: squeezelyPostReturn_dead_letter_ex
                type: fanout
            queue_options:
                name: squeezelyPostReturn_dead_letter_qu
            callback: CatBundle\Consumer\Squeezely\PostReturnDeadLetter
        google_analytics_post_return:
            connection: default
            exchange_options:
                name: google_analytics_post_return
                type: fanout
            queue_options:
                name: google_analytics_post_return
                arguments: { x-dead-letter-exchange: [S, google_analytics_post_return_dead_letter_ex], x-dead-letter-routing-key: [S, google_analytics_post_return_dead_letter_qu] }
            callback: consumer.google_analytics_post_return
        google_analytics_post_return_dead_letter:
            connection: default
            exchange_options:
                name: google_analytics_post_return_dead_letter_ex
                type: fanout
            queue_options:
                name: google_analytics_post_return_dead_letter_qu
            callback: consumer.google_analytics_post_return_dead_letter
        stockLog:
            connection: default
            exchange_options:
                name: stockLog
                type: fanout
            queue_options:
                name: stockLog
                arguments: { x-dead-letter-exchange: [S, stockLog_dead_letter_ex], x-dead-letter-routing-key: [S, stockLog_dead_letter_qu] }
            callback: consumer.stockLog
        stockLog_dead_letter:
            connection: default
            exchange_options:
                name: stockLog_dead_letter_ex
                type: fanout
            queue_options:
                name: stockLog_dead_letter_qu
            callback: consumer.stockLog_dead_letter
        customer_update:
            connection: default
            exchange_options:
                name: customer_update
                type: fanout
            queue_options:
                name: customer_update
                arguments: { x-dead-letter-exchange: [S, customer_update_dead_letter_ex], x-dead-letter-routing-key: [S, customer_update_dead_letter_qu] }
            callback: CatBundle\Consumer\CustomerUpdate\CustomerUpdateConsumer
        customer_update_dead_letter:
            connection: default
            exchange_options:
                name: customer_update_dead_letter_ex
                type: fanout
            queue_options:
                name: customer_update_dead_letter_qu
            callback: CatBundle\Consumer\CustomerUpdate\CustomerUpdateDeadLetter
        order_discount_log:
            connection: default
            exchange_options:
                name: order_complete
                type: fanout
            queue_options:
                name: order_discount_log
                arguments: { x-dead-letter-exchange: [S, order_discount_log_dead_letter_ex], x-dead-letter-routing-key: [S, order_discount_log_dead_letter_qu] }
            callback: consumer.order.discount_log
        order_discount_log_dead_letter:
            connection: default
            exchange_options:
                name: order_discount_log_dead_letter_ex
                type: fanout
            queue_options:
                name: order_discount_log_dead_letter_qu
            callback: consumer.order.discount_log_dead_letter
        newsletter_subscribe:
            connection: default
            exchange_options:
                name: newsletter_subscribe
                type: fanout
            queue_options:
                name: newsletter_subscribe
                arguments: { x-dead-letter-exchange: [S, newsletter_subscribe_dead_letter_ex], x-dead-letter-routing-key: [S, newsletter_subscribe_dead_letter_qu] }
            callback: consumer.newsletter_subscribe
        newsletter_subscribe_dead_letter:
            connection: default
            exchange_options:
                name: newsletter_subscribe_dead_letter_ex
                type: fanout
            queue_options:
                name: newsletter_subscribe_dead_letter_qu
            callback: consumer.newsletter_subscribe_dead_letter
        afas.debtor:
            connection: default
            exchange_options:
                name: afas_debtor
                type: fanout
            queue_options:
                name: afas_debtor
                arguments: { x-dead-letter-exchange: [S, afas_debtor_dead_letter_ex], x-dead-letter-routing-key: [S, afas_debtor_dead_letter_qu] }
            callback: consumer.afas.debtor
            qos_options:
                prefetch_size: 0
                prefetch_count: 1
                global: false
        afas.debtor_dead_letter:
            connection: default
            exchange_options:
                name: afas_debtor_dead_letter_ex
                type: fanout
            queue_options:
                name: afas_debtor_dead_letter_qu
            callback: consumer.afas.debtor_dead_letter
        afas.order:
            connection: default
            exchange_options:
                name: afas_order
                type: fanout
            queue_options:
                name: afas_order
                arguments: { x-dead-letter-exchange: [S, afas_order_dead_letter_ex], x-dead-letter-routing-key: [S, afas_order_dead_letter_qu] }
            callback: consumer.afas.order
            qos_options:
                prefetch_size: 0
                prefetch_count: 1
                global: false
        afas.order_dead_letter:
            connection: default
            exchange_options:
                name: afas_order_dead_letter_ex
                type: fanout
            queue_options:
                name: afas_order_dead_letter_qu
            callback: consumer.afas.order_dead_letter
        event_messages:
            connection: default
            exchange_options:
                name: event_messages
                type: fanout
            queue_options:
                name: event_messages
                arguments: { x-dead-letter-exchange: [S, event_messages_dead_letter_ex], x-dead-letter-routing-key: [S, event_messages_dead_letter_qu] }
            callback: consumer.event_messages
            qos_options:
                prefetch_size: 0
                prefetch_count: 1
                global: false
        event_messages_dead_letter:
            connection: default
            exchange_options:
                name: event_messages_dead_letter_ex
                type: fanout
            queue_options:
                name: event_messages_dead_letter_qu
            callback: consumer.event_messages_dead_letter
        event_refunds:
            connection: default
            exchange_options:
                name: event_refunds
                type: fanout
            queue_options:
                name: event_refunds
                arguments: { x-dead-letter-exchange: [S, event_refunds_dead_letter_ex], x-dead-letter-routing-key: [S, event_refunds_dead_letter_qu] }
            callback: consumer.event_refunds
            qos_options:
                prefetch_size: 0
                prefetch_count: 1
                global: false
        event_refunds_dead_letter:
            connection: default
            exchange_options:
                name: event_refunds_dead_letter_ex
                type: fanout
            queue_options:
                name: event_refunds_dead_letter_qu
            callback: consumer.event_refunds_dead_letter
        second_hand_update_base_formula:
            connection: default
            exchange_options:
                name: second_hand_update_base_formula
                type: fanout
            queue_options:
                name: second_hand_update_base_formula
                arguments: { x-dead-letter-exchange: [S, second_hand_update_base_formula_dead_letter_ex], x-dead-letter-routing-key: [S, second_hand_update_base_formula_dead_letter_qu] }
            callback: consumer.second_hand_update_base_formula
        second_hand_update_base_formula_dead_letter:
            connection: default
            exchange_options:
                name: second_hand_update_base_formula_dead_letter_ex
                type: fanout
            queue_options:
                name: second_hand_update_base_formula_dead_letter_qu
            callback: consumer.second_hand_update_base_formula_dead_letter
        mollieNotification:
            connection: default
            exchange_options:
                name: mollieNotification
                type: fanout
            queue_options:
                name: mollieNotification
                arguments: { x-dead-letter-exchange: [S, mollieNotification_dead_letter_ex], x-dead-letter-routing-key: [S, mollieNotification_dead_letter_qu] }
            callback: CatBundle\Consumer\Mollie\MollieNotificationConsumer
        mollieNotification_dead_letter:
            connection: default
            exchange_options:
                name: mollieNotification_dead_letter_ex
                type: fanout
            queue_options:
                name: mollieNotification_dead_letter_qu
            callback: CatBundle\Consumer\Mollie\MollieNotificationDeadLetter
        clangTradeInHook:
            connection: default
            exchange_options:
                name: clangTradeInHook
                type: fanout
            queue_options:
                name: clangTradeInHook
                arguments: { x-dead-letter-exchange: [S, clangTradeInHook_dead_letter_ex], x-dead-letter-routing-key: [S, clangTradeInHook_dead_letter_qu] }
            callback: CatBundle\Consumer\ClangTradeInHook\ClangTradeInHookConsumer
        clangTradeInHook_dead_letter:
            connection: default
            exchange_options:
                name: clangTradeInHook_dead_letter_ex
                type: fanout
            queue_options:
                name: clangTradeInHook_dead_letter_qu
            callback: CatBundle\Consumer\ClangTradeInHook\ClangTradeInHookDeadLetter
        dorRegisterProduct:
            connection: default
            exchange_options:
                name: dorRegisterProduct
                type: fanout
            queue_options:
                name: dorRegisterProduct
                arguments: { x-dead-letter-exchange: [S, dorRegisterProduct_dead_letter_ex], x-dead-letter-routing-key: [S, dorRegisterProduct_dead_letter_qu] }
            callback: CatBundle\Consumer\Dor\RegisterProductConsumer
        dorRegisterProduct_dead_letter:
            connection: default
            exchange_options:
                name: dorRegisterProduct_dead_letter_ex
                type: fanout
            queue_options:
                name: dorRegisterProduct_dead_letter_qu
            callback: CatBundle\Consumer\Dor\RegisterProductDeadLetter
        dorTransferProduct:
            connection: default
            exchange_options:
                name: dorTransferProduct
                type: fanout
            queue_options:
                name: dorTransferProduct
                arguments: { x-dead-letter-exchange: [S, dorTransferProduct_dead_letter_ex], x-dead-letter-routing-key: [S, dorTransferProduct_dead_letter_qu] }
            callback: CatBundle\Consumer\Dor\TransferProductConsumer
        dorTransferProduct_dead_letter:
            connection: default
            exchange_options:
                name: dorTransferProduct_dead_letter_ex
                type: fanout
            queue_options:
                name: dorTransferProduct_dead_letter_qu
            callback: CatBundle\Consumer\Dor\TransferProductDeadLetter
        dorInternalTransfer:
            connection: default
            exchange_options:
                name: dorInternalTransfer
                type: fanout
            queue_options:
                name: dorInternalTransfer
                arguments: { x-dead-letter-exchange: [S, dorInternalTransfer_dead_letter_ex], x-dead-letter-routing-key: [S, dorInternalTransfer_dead_letter_qu] }
            callback: CatBundle\Consumer\Dor\InternalTransferConsumer
        dorInternalTransfer_dead_letter:
            connection: default
            exchange_options:
                name: dorInternalTransfer_dead_letter_ex
                type: fanout
            queue_options:
                name: dorInternalTransfer_dead_letter_qu
            callback: CatBundle\Consumer\Dor\InternalTransferDeadLetter
        discountCodeBatch:
            connection: default
            exchange_options:
                name: discountCodeBatch
                type: fanout
            queue_options:
                name: discountCodeBatch
                arguments: { x-dead-letter-exchange: [S, discountCodeBatch_dead_letter_ex], x-dead-letter-routing-key: [S, discountCodeBatch_dead_letter_qu] }
            callback: CatBundle\Consumer\DiscountCodeBatch\DiscountCodeBatchConsumer
        discountCodeBatch_dead_letter:
            connection: default
            exchange_options:
                name: discountCodeBatch_dead_letter_ex
                type: fanout
            queue_options:
                name: discountCodeBatch_dead_letter_qu
            callback: CatBundle\Consumer\DiscountCodeBatch\DiscountCodeBatchDeadLetter
        importSecondhandProduct:
            connection: default
            exchange_options:
                name: importSecondhandProduct
                type: fanout
            queue_options:
                name: importSecondhandProduct
                arguments: { x-dead-letter-exchange: [S, importSecondhandProduct_dead_letter_ex], x-dead-letter-routing-key: [S, importSecondhandProduct_dead_letter_qu] }
            callback: CatBundle\Consumer\ImportSecondhandProduct\ImportSecondhandProductConsumer
        importSecondhandProduct_dead_letter:
            connection: default
            exchange_options:
                name: importSecondhandProduct_dead_letter_ex
                type: fanout
            queue_options:
                name: importSecondhandProduct_dead_letter_qu
            callback: CatBundle\Consumer\ImportSecondhandProduct\ImportSecondhandProductDeadLetter
        promotionsPaddington:
            connection: default
            exchange_options:
                name: promotionsPaddington
                type: fanout
            queue_options:
                name: promotionsPaddington
                arguments: { x-dead-letter-exchange: [S, promotionsPaddington_dead_letter_ex], x-dead-letter-routing-key: [S, promotionsPaddington_dead_letter_qu] }
            callback: CatBundle\Consumer\PromotionsPaddington\PromotionsPaddingtonConsumer
        promotionsPaddington_dead_letter:
            connection: default
            exchange_options:
                name: promotionsPaddington_dead_letter_ex
                type: fanout
            queue_options:
                name: promotionsPaddington_dead_letter_qu
            callback: CatBundle\Consumer\PromotionsPaddington\PromotionsPaddingtonDeadLetter
        clangCrossSellWebhook:
            connection: default
            exchange_options:
                name: clangCrossSellWebhook
                type: fanout
            queue_options:
                name: clangCrossSellWebhook
                arguments: { x-dead-letter-exchange: [S, clangCrossSellWebhook_dead_letter_ex], x-dead-letter-routing-key: [S, clangCrossSellWebhook_dead_letter_qu] }
            callback: CatBundle\Consumer\ClangCrossSellWebhook\ClangCrossSellWebhookConsumer
        clangCrossSellWebhook_dead_letter:
            connection: default
            exchange_options:
                name: clangCrossSellWebhook_dead_letter_ex
                type: fanout
            queue_options:
                name: clangCrossSellWebhook_dead_letter_qu
            callback: CatBundle\Consumer\ClangCrossSellWebhook\ClangCrossSellWebhookDeadLetter
        photoContestCampaignEntryCreate:
            connection: default
            exchange_options:
                name: photoContestCampaignEntryCreate
                type: fanout
            queue_options:
                name: photoContestCampaignEntryCreate
                arguments: { x-dead-letter-exchange: [S, photoContestCampaignEntryCreate_dead_letter_ex], x-dead-letter-routing-key: [S, photoContestCampaignEntryCreate_dead_letter_qu] }
            callback: CatBundle\Consumer\PhotoContestCampaignEntryCreate\PhotoContestCampaignEntryCreateConsumer
        photoContestCampaignEntryCreate_dead_letter:
            connection: default
            exchange_options:
                name: photoContestCampaignEntryCreate_dead_letter_ex
                type: fanout
            queue_options:
                name: photoContestCampaignEntryCreate_dead_letter_qu
            callback: CatBundle\Consumer\PhotoContestCampaignEntryCreate\PhotoContestCampaignEntryCreateDeadLetter
        squeezelyCRMUpdate:
            connection: default
            exchange_options:
                name: squeezelyCRMUpdate
                type: fanout
            queue_options:
                name: squeezelyCRMUpdate
                arguments: { x-dead-letter-exchange: [S, squeezelyCRMUpdate_dead_letter_ex], x-dead-letter-routing-key: [S, squeezelyCRMUpdate_dead_letter_qu] }
            callback: CatBundle\Consumer\SqueezelyCRMUpdate\SqueezelyCRMUpdateConsumer
        squeezelyCRMUpdate_dead_letter:
            connection: default
            exchange_options:
                name: squeezelyCRMUpdate_dead_letter_ex
                type: fanout
            queue_options:
                name: squeezelyCRMUpdate_dead_letter_qu
            callback: CatBundle\Consumer\SqueezelyCRMUpdate\SqueezelyCRMUpdateDeadLetter
        AIProductContent:
            connection: default
            exchange_options:
                name: AIProductContent
                type: fanout
            queue_options:
                name: AIProductContent
                arguments: { x-dead-letter-exchange: [S, AIProductContent_dead_letter_ex], x-dead-letter-routing-key: [S, AIProductContent_dead_letter_qu] }
            callback: CatBundle\Consumer\AIProductContent\AIProductContentConsumer
        AIProductContent_dead_letter:
            connection: default
            exchange_options:
                name: AIProductContent_dead_letter_ex
                type: fanout
            queue_options:
                name: AIProductContent_dead_letter_qu
            callback: CatBundle\Consumer\AIProductContent\AIProductContentDeadLetter
services:
    consumer.create_stockValueUpdate:
        public: true
        class: CatBundle\Consumer\StockValueUpdate\StockValueUpdateConsumer
        arguments:
            - '@webdsign.telegram'
            - '@product_repository'
            - '@stock.stockvalue_calculation'
    consumer.create_stockValueUpdate_dead_letter:
        public: true
        class: CatBundle\Consumer\StockValueUpdate\StockValueUpdateDeadLetter
    producer.create_stockValueUpdate:
        public: true
        class: CatBundle\Producer\StockValueUpdate\StockValueUpdateProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.productUpdate:
        public: true
        class: CatBundle\Consumer\ProductUpdate\ProductUpdateConsumer
        arguments:
            - '@webdsign.telegram'
            - '@webdsign.product_updater'
            - '@webdsign.clang_web_hook'
    consumer.productUpdate_dead_letter:
        public: true
        class: CatBundle\Consumer\ProductUpdate\ProductUpdateDeadLetter
    producer.productUpdate:
        public: true
        class: CatBundle\Producer\ProductUpdate\ProductUpdateProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.clangOrderHook:
        public: true
        class: CatBundle\Consumer\ClangOrderHook\ClangOrderHookConsumer
        arguments:
            - '@webdsign.telegram'
            - '@webdsign.clang_web_hook'
    consumer.clangOrderHook_dead_letter:
        public: true
        class: CatBundle\Consumer\ClangOrderHook\ClangOrderHookDeadLetter
    producer.clangOrderHook:
        public: true
        class: CatBundle\Producer\ClangOrderHook\ClangOrderHookProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.clangMail:
        public: true
        class: CatBundle\Consumer\ClangMail\ClangMailConsumer
        arguments:
            - '@webdsign.telegram'
            - '@clang_handler'
            - '@webdsign.clang_web_hook'
    consumer.clangMail_dead_letter:
        public: true
        class: CatBundle\Consumer\ClangMail\ClangMailDeadLetter
    producer.clangMail:
        public: true
        class: CatBundle\Producer\ClangMail\ClangMailProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    CatBundle\Producer\Squeezely\OptinProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'
    CatBundle\Producer\Squeezely\PostOrderProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'
    CatBundle\Producer\Squeezely\PostReturnProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'
    consumer.google_analytics_post_return:
        public: true
        class: CatBundle\Consumer\GoogleAnalytics\PostReturnConsumer
        arguments:
            - '@doctrine.orm.entity_manager'
            - '@webdsign.telegram'
            - '@analytics_ecommerce'
    consumer.google_analytics_post_return_dead_letter:
        public: true
        class: CatBundle\Consumer\GoogleAnalytics\PostReturnDeadLetter
    producer.google_analytics_post_return:
        public: true
        class: CatBundle\Producer\GoogleAnalytics\PostReturnProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.stockLog:
        public: true
        class: CatBundle\Consumer\StockLog\StockLogConsumer
        arguments:
            - '@webdsign.telegram'
            - '@stock_log_service'
    consumer.stockLog_dead_letter:
        public: true
        class: CatBundle\Consumer\StockLog\StockLogDeadLetter
    producer.stockLog:
        public: true
        class: CatBundle\Producer\StockLog\StockLogProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    producer.customer_update:
        public: true
        class: CatBundle\Producer\CustomerUpdate\CustomerUpdateProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    producer.order_complete:
        public: ture
        class: CatBundle\Producer\Order\CompleteProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.order.discount_log:
        public: true
        class: CatBundle\Consumer\Order\DiscountLogConsumer
        arguments:
            - '@order_discount_log_stock_service'
            - '@webdsign.telegram'
            - '@order_info_repository'
    consumer.order.discount_log_dead_letter:
        public: true
        class: CatBundle\Consumer\Order\DiscountLogDeadLetter
    consumer.newsletter_subscribe:
        public: true
        class: CatBundle\Consumer\NewsletterSubscribe\NewsletterSubscribeConsumer
        arguments:
            - '@webdsign.telegram'
            - '@webdsign.clang_web_hook'
    consumer.newsletter_subscribe_dead_letter:
        public: true
        class: CatBundle\Consumer\NewsletterSubscribe\NewsletterSubscribeDeadLetter
    producer.newsletter_subscribe:
        public: true
        class: CatBundle\Producer\NewsletterSubscribe\NewsletterSubscribeProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.afas.debtor: '@CatBundle\Consumer\Afas\Debtor\Consumer'
    consumer.afas.debtor_dead_letter:
        public: true
        class: CatBundle\Consumer\Afas\Debtor\DeadLetter
    producer.afas.debtor:
        public: true
        class: CatBundle\Producer\Afas\Debtor\Producer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.afas.order: '@CatBundle\Consumer\Afas\Order\Consumer'
    consumer.afas.order_dead_letter:
        public: true
        class: CatBundle\Consumer\Afas\Order\DeadLetter
    producer.afas.order:
        public: true
        class: CatBundle\Producer\Afas\Order\Producer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.event_messages: '@CatBundle\Consumer\EventMessages\EventMessagesConsumer'
    consumer.event_messages_dead_letter:
        public: true
        class: CatBundle\Consumer\EventMessages\EventMessagesDeadLetter
    producer.event_messages:
        public: true
        class: CatBundle\Producer\EventMessages\EventMessagesProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.event_refunds:
        public: true
        class: CatBundle\Consumer\EventRefunds\EventRefundsConsumer
        arguments:
            - '@webdsign.telegram'
            - '@Webdsign\GlobalBundle\Services\Event\RegistrationManager'
    consumer.event_refunds_dead_letter:
        public: true
        class: CatBundle\Consumer\EventRefunds\EventRefundsDeadLetter
    producer.event_refunds:
        public: true
        class: CatBundle\Producer\EventRefunds\EventRefundsProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    consumer.second_hand_update_base_formula:
        public: true
        class: CatBundle\Consumer\SecondHand\SecondHandUpdateBaseFormulaConsumer
        arguments:
            - '@webdsign.telegram'
            - '@CatBundle\Service\SecondHand\SecondHandPriceManager'
    consumer.second_hand_update_base_formula_dead_letter:
        public: true
        class: CatBundle\Consumer\SecondHand\SecondHandUpdateBaseFormulaDeadLetter
    producer.second_hand_update_base_formula:
        public: true
        class: CatBundle\Producer\SecondHand\SecondHandUpdateBaseFormulaProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
    producer.mollie_notification:
        class: CatBundle\Producer\Mollie\MollieNotificationProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'
when@dev:
    old_sound_rabbit_mq:
        connections:
            default:
                heartbeat: 0
