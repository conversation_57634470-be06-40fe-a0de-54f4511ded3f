# Read the documentation: https://github.com/1up-lab/OneupFlysystemBundle
oneup_flysystem:
    adapters:
        local_adapter:
            local:
                location: '%web_path%/uploads'
        remote_adapter:
            ftp:
                options:
                    host: '%env(UPLOAD_FTP_HOST)%'
                    username: '%env(UPLOAD_FTP_USERNAME)%'
                    password: '%env(UPLOAD_FTP_PASSWORD)%'
                    timeout: 60
                    root: '/'
        temp_adapter:
            local:
                location: '/tmp/images'
        amazon_adapter:
            awss3v3:
                client: s3_client
                bucket: '%env(AMAZON_BUCKET)%'
                prefix: ''
        spaces_adapter:
            awss3v3:
                client: spaces_client
                bucket: '%env(SPACES_BUCKET)%'
                prefix: ''
        saysimple_adapter:
            ftp:
                options:
                    host: '%env(SAYSIMPLE_FTP_HOST)%'
                    username: '%env(SAYSIMPLE_FTP_USERNAME)%'
                    password: '%env(SAYSIMPLE_FTP_PASSWORD)%'
                    root: '/'
        europafoto_adapter:
            ftp:
                options:
                    host: '%env(EUROPAFOTO_FTP_HOST)%'
                    username: '%env(EUROPAFOTO_FTP_USERNAME)%'
                    password: '%env(EUROPAFOTO_FTP_PASSWORD)%'
                    root: '%env(EUROPAFOTO_FTP_DIRECTORY)%'
        productfeed_adapter:
            ftp:
                options:
                    host: '%env(UPLOAD_FTP_HOST)%'
                    username: '%env(PRODUCTFEED_FTP_USERNAME)%'
                    password: '%env(PRODUCTFEED_FTP_PASSWORD)%'
                    root: '%env(PRODUCTFEED_FTP_DIR)%'
        servicetool_export_adapter:
            ftp:
                options:
                    host: '%env(UPLOAD_FTP_HOST)%'
                    username: '%env(PRODUCTFEED_FTP_USERNAME)%'
                    password: '%env(PRODUCTFEED_FTP_PASSWORD)%'
                    root: '%env(SERVICETOOL_EXPORT_FTP_DIR)%'
        clang_adapter:
            ftp:
                options:
                    host: '%env(CLANG_FTP_HOST)%'
                    username: '%env(CLANG_FTP_USERNAME)%'
                    password: '%env(CLANG_FTP_PASSWORD)%'
                    root: '%env(CLANG_FTP_EXPORT_DIR)%'
        omnia_adapter:
            ftp:
                options:
                    host: '%env(OMNIA_FTP_HOST)%'
                    username: '%env(OMNIA_FTP_USERNAME)%'
                    password: '%env(OMNIA_FTP_PASSWORD)%'
                    root: '/'
        eig_adapter:
            sftp:
                options:
                    host: '%env(EIG_FTP_HOST)%'
                    username: '%env(EIG_FTP_USERNAME)%'
                    password: '%env(EIG_FTP_PASSWORD)%'
                    root: '/'
        elastic_adapter:
            sftp:
                options:
                    host: '%env(ELASTIC_FTP_HOST)%'
                    username: '%env(ELASTIC_FTP_USERNAME)%'
                    password: '%env(ELASTIC_FTP_PASSWORD)%'
                    root: '/' # /usr/share/elasticsearch/config on server
        dataforest_adapter:
            sftp:
                options:
                    host: '%env(DATAFOREST_FTP_HOST)%'
                    username: '%env(DATAFOREST_FTP_USERNAME)%'
                    password: '%env(DATAFOREST_FTP_PASSWORD)%'
                    root: '/'
        agicap_adapter:
            sftp:
                options:
                    host: '%env(AGICAP_SFTP_HOST)%'
                    username: '%env(AGICAP_SFTP_USERNAME)%'
                    privateKey: '%env(AGICAP_SFTP_KEY)%'
                    port: '%env(int:AGICAP_SFTP_PORT)%'
                    root: '/cameranu/expected_to_agicap_cashflow/'
        productfeed_local_adapter:
            local:
                location: '%kernel.project_dir%/var/productfeeds'

    filesystems:
        localfilesystem:
            adapter: local_adapter
            mount: local
        remotefilesystem:
            adapter: remote_adapter
            mount: remote
        temp:
            adapter: temp_adapter
            mount: temp
        amazon:
            adapter: amazon_adapter
        spaces:
            adapter: spaces_adapter
            mount: spaces
        saysimplefilesystem:
            adapter: saysimple_adapter
            mount: saysimple
        europafotofilesystem:
            adapter: europafoto_adapter
            mount: europafoto
        productfeed:
            adapter: productfeed_adapter
            mount: productfeed
        servicetool_export:
            adapter: servicetool_export_adapter
            mount: servicetool_export
        clang:
            adapter: clang_adapter
            mount: clang
        omniafilesystem:
            adapter: omnia_adapter
            mount: omnia
        eig_report:
            adapter: eig_adapter
            mount: eig_report
        elastic:
            adapter: elastic_adapter
            mount: elastic
        dataforest:
            adapter: dataforest_adapter
            mount: dataforest
        agicap:
            adapter: agicap_adapter
            mount: agicap
        productfeed_local:
            adapter: productfeed_local_adapter
            mount: productfeed_local

when@dev:
    oneup_flysystem:
        adapters:
            spaces_adapter:
                awss3v3:
                    client: spaces_client
                    bucket: '%env(SPACES_BUCKET)%'
                    prefix: 'Test'
            elastic_adapter:
                local:
                    location: '%kernel.project_dir%/docker/elasticsearch/config/'

        filesystems:
            remotefilesystem:
                adapter: local_adapter
                mount: remote
