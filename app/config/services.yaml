imports:
    - { resource: '@CatBundle/Resources/config/services.yaml' }
    - { resource: '@ContentBundle/Resources/config/services.yaml' }
    - { resource: '@FrontpageBundle/Resources/config/services.yaml' }
    - { resource: '@PriceUpdateBundle/Resources/config/services.yaml' }
    - { resource: '@ServicetoolServiceBundle/Resources/config/services.yaml' }
    - { resource: '@StatisticsBundle/Resources/config/services.yaml' }
    - { resource: '@UblBundle/Resources/config/services.yaml' }
    - { resource: '@WebdsignGlobalBundle/Resources/config/services.yaml' }
    - { resource: '@WebsiteBundle/Resources/config/services.yaml' }
    - { resource: 'aliases.yaml' }

parameters:
    locale: nl

    cat_hostname: '%env(CAT_HOSTNAME_DOTENV)%'
    servicetool_hostname: '%env(SERVICETOOL_HOSTNAME_DOTENV)%'

    web_path: '%kernel.project_dir%/web'
    upload_path: '/uploads/attachments'
    cat_bundle_path: '%kernel.project_dir%/src/CatBundle'
    product_main_image_path: 'https://static.cmra.nu/thumb_160/'

    admin_link: '%env(ADMIN_URL)%'
    servicetool_link: '%env(SERVICETOOL_URL)%'

    invoice_logo: '%web_path%/assets/img/logo_factuur.jpg'
    invoice_rebranding_logo: '%web_path%/assets/img/CNU_HQ_Logo.png'

    paginator_page_range: 5
    paginator_items_per_page: 15

    max_ticket_amount: 10
    rma_status_not_received: 5

    servicepartner_email_addresses:
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'
        - '@@fotovideoretail.nl'
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'

    in_transit_list_email_addresses:
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'
        - '<EMAIL>'

    mountpoints:
        attachments:
            defaultMountpoint: '%env(UPLOAD_ATTACHMENT_MOUNTPOINT)%'
            local:
                base_directory: attachments/
                use_date_subdirectories: false
            remote:
                base_directory: ServicetoolAttachments/
                use_date_subdirectories: true
            spaces:
                base_directory: Attachments/
                use_date_subdirectories: true

    robin_urls:
        -   customers: https://api.robinhq.com/dynamic/customers/
            orders: https://api.robinhq.com/dynamic/orders/
            voip: https://api.robinhq.com/voip/registerevent/

    robin_created_http_expected: 201

    payment_reminder_mail_from_name: 'Wilco de Vries'
    payment_reminder_mail_content_ids:
        - 234
        - 244
        - 484
        - 1885
        - 1895
        - 1905
        - 3024
        - 3057

    cameranu_parents:
        - "cameranu"
        - "cameranu_amsterdam"
        - "cameranu_apeldoorn"
        - "cameranu_groningen"
        - "cameranu_eindhoven"
        - "cameranu_rotterdam"
        - "cameranu_antwerpen"
        - "cameranu_utrecht"

    dor_base_uri: '%env(DOR_API_URL)%'
    dor_store_codes:
        cameranu: '%env(DOR_STORE_CODE_URK)%'
        cameranu_rotterdam: '%env(DOR_STORE_CODE_ROTTERDAM)%'
        cameranu_apeldoorn: '%env(DOR_STORE_CODE_APELDOORN)%'
        cameranu_eindhoven: '%env(DOR_STORE_CODE_EINDHOVEN)%'
        cameranu_groningen: '%env(DOR_STORE_CODE_GRONINGEN)%'
        cameranu_amsterdam: '%env(DOR_STORE_CODE_AMSTERDAM)%'
        cameranu_utrecht: '%env(DOR_STORE_CODE_UTRECHT)%'

    vendiro_parents:
        - 'vendiro'
        - 'vendiro_cameratools'

    cameranu_addresses:
        amsterdam: "amstelveenseweg 288-292"

    internal_invoice_use_fake_invoice_numbers: '%env(bool:USE_FAKE_INVOICE_NUMBERS)%'

    stock_locations:
        cameranu_rotterdam: 109
        cameranu_eindhoven: 101
        cameranu_groningen: 90
        cameranu_amsterdam: 62
        cameranu_apeldoorn: 83
        cameranu: 12 # Urk
        cameranu_antwerpen: 240
        cameranu_utrecht: 260

    stock_locations_couriers:
        cameranu_rotterdam: 123
        cameranu_eindhoven: 121
        cameranu_groningen: 119
        cameranu_amsterdam: 115
        cameranu_apeldoorn: 117
        cameranu: 72 # Urk
        cameranu_antwerpen: 242
        cameranu_utrecht: 262

    courier_list_minimal_stock_ignore_parking:
        62: #Amsterdam
            - 1787 #later afhalen AMS
        83: #Apeldoorn
            - 1842 # later afhalen APL
        90: #Groningen
            - 1900 #later afhalen GRO
        101: #Eindhoven
            - 1782 #later afhalen EHV
        109: #Rotterdam
            - 1997 #later afhalen ROT
        240: # Antwerpen
            - 2205 # Antwerpen later afhalen
        260: # Utrecht
            - 2425 # Utrecht later afhalen

    stock_locations_transit:
        cameranu: '010T'
        cameranu_rotterdam: '060T'
        cameranu_apeldoorn: '030T'
        cameranu_eindhoven: '050T'
        cameranu_groningen: '040T'
        cameranu_amsterdam: '020T'
        cameranu_antwerpen: '090T'
        cameranu_utrecht: '100T'

    cameranu_web_origins:
        - 5 #Website
        - 332 #Amsterdam
        - 343 #Apeldoorn
        - 346 #Groningen
        - 359 #Eindhoven
        - 363 #Rotterdam
        - 369 #Antwerpen
        - 375 #Utrecht

    cameranu_ip_addresses:
        - 127.0.0.1 #Local
        - *************** #CameraNU
        - *************** #CameraNU
        - ************* #Skrepr
        - 0 #Winkels
        # Amsterdam
        - ***********
        # Apeldoorn:
        - *************
        # Eindhoven
        - *************
        # Groningen
        - *************
        # Rotterdam
        - *************
        # Antwerpen
        - *************
        # Utrecht
        #TODO

    cameranu_external_stock_location_codes:
        - '022W'
        - '032W'
        - '042W'
        - '023P'
        - '052W'
        - '062W'
        - '092W'
        - '102W'

    phone_numbers:
        general:
            - '0527-690404'
            - '+31527690404'
        b2b:
            - '0527-200900'
            - '+31527200900'

    email_signatures:
        cameranu:
            signee: 'Inkoopteam'
            company_name: 'Cameranu'
            address: 'Het Spijk 8'
            postcode: '8321 WT'
            city: 'Urk'
            phone_number: '+31527690404'
            fax_number: '+31527690484'
            mobile_number: '+316CAMERANU'
            email_address: '<EMAIL>'
            website: 'www.cameranu.nl'

    app.elastic_index: servicetool

    app.send_from_email: <EMAIL>

    app.system_user_admin: 2325

    app.handling_costs: 8.0

    priceupdate.omnia.report_addresses:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>

    postnl.network_partner_active: 1
    postnl.sandbox: 0
    postnl_token: '%env(POSTNL_TOKEN)%'
    postnl.cameranu.customercode: 'CDVF'
    postnl.cameranu.customernumber: '1106612'

    app.trunkrs_cutofftime: '13:00'

    app.order_divider_cache_key_prefix: OrderDivider-

    app.automatic_ticket_mail_active: 1

    app.robin.debug_connection: false

    app.vat:
        nl:
            high: 21
            low: 9
        fi: 24
        se: 25
        de: 19
        fr: 20
        be: 21
        at: 20
        es: 21
        it: 22
        pt: 23
        lu: 17
        bg: 20
        cz: 21
        dk: 25
        ee: 20
        ie: 21
        gr: 24
        cy: 19
        lv: 21
        lt: 21
        hu: 27
        mt: 18
        pl: 23
        ro: 19
        sl: 22
        sk: 20

    app.access_listener.allow_empty_x_auth_token: false

    app.create_product_queue:
        tweakwise: false
        squeezely: true

    eig_report.sender_address: <EMAIL>

    clang_token: '%env(CLANG_TOKEN)%'

    clang_webhook_tokens:
        customer: '%env(CLANG_WEBHOOK_TOKEN_CUSTOMER)%'
        order: '%env(CLANG_WEBHOOK_TOKEN_ORDER)%'
        wishlist: '%env(CLANG_WEBHOOK_TOKEN_WISHLIST)%'
        passwordReset: '%env(CLANG_WEBHOOK_TOKEN_PASSWORD_RESET)%'
        passwordResetNew: '%env(CLANG_WEBHOOK_TOKEN_PASSWORD_RESET_NEW)%'
        wishlistNew: '%env(CLANG_WEBHOOK_TOKEN_WISHLIST_NEW)%'
        activateNewsletter: '%env(CLANG_WEBHOOK_TOKEN_ACTIVATE_NEWSLETTER)%'
        lostShopcart: '%env(CLANG_WEBHOOK_TOKEN_LOST_SHOPCART)%'
        productUpdate: '%env(CLANG_WEBHOOK_TOKEN_PRODUCT_UPDATE)%'
        eventsOrder: '%env(CLANG_WEBHOOK_TOKEN_EVENTS_ORDER)%'
        eventsReminder: '%env(CLANG_WEBHOOK_TOKEN_EVENTS_REMINDER)%'
        eventsMessage: '%env(CLANG_WEBHOOK_TOKEN_EVENTS_MESSAGE)%'
        eventsReview: '%env(CLANG_WEBHOOK_TOKEN_EVENTS_REVIEW)%'
        tradeInProcess: '%env(CLANG_WEBHOOK_TOKEN_TRADE_IN_PROCESS)%'
        crossSell: '%env(CLANG_WEBHOOK_TOKEN_CROSS_SELL)%'
        promotionsPaddington: '%env(CLANG_WEBHOOK_TOKEN_PROMOTIONS_PADDINGTON)%'
        photoContestCampaignEntryCreate: '%env(CLANG_WEBHOOK_TOKEN_PHOTO_CONTEST)%'

    clang_webhook_urls:
        customer: '%env(CLANG_WEBHOOK_URL_CUSTOMER)%'
        order: '%env(CLANG_WEBHOOK_URL_ORDER)%'
        wishlist: '%env(CLANG_WEBHOOK_URL_WISHLIST)%'
        passwordReset: '%env(CLANG_WEBHOOK_URL_PASSWORD_RESET)%'
        passwordResetNew: '%env(CLANG_WEBHOOK_URL_PASSWORD_RESET_NEW)%'
        wishlistNew: '%env(CLANG_WEBHOOK_URL_WISHLIST_NEW)%'
        activateNewsletter: '%env(CLANG_WEBHOOK_URL_ACTIVATE_NEWSLETTER)%'
        lostShopcart: '%env(CLANG_WEBHOOK_URL_LOST_SHOPCART)%'
        productUpdate: '%env(CLANG_WEBHOOK_URL_PRODUCT_UPDATE)%'
        eventsOrder: '%env(CLANG_WEBHOOK_URL_EVENTS_ORDER)%'
        eventsReminder: '%env(CLANG_WEBHOOK_URL_EVENTS_REMINDER)%'
        eventsMessage: '%env(CLANG_WEBHOOK_URL_EVENTS_MESSAGE)%'
        eventsReview: '%env(CLANG_WEBHOOK_URL_EVENTS_REVIEW)%'
        tradeInProcess: '%env(CLANG_WEBHOOK_URL_TRADE_IN_PROCESS)%'
        crossSell: '%env(CLANG_WEBHOOK_URL_CROSS_SELL)%'
        promotionsPaddington: '%env(CLANG_WEBHOOK_URL_PROMOTIONS_PADDINGTON)%'
        photoContestCampaignEntryCreate: '%env(CLANG_WEBHOOK_URL_PHOTO_CONTEST)%'

    packingslip_email: '%env(PACKINGSLIP_EMAIL)%'
    packingslip_email_pass: '%env(PACKINGSLIP_EMAIL_PASSWORD)%'

    ubl_email: '%env(UBL_EMAIL)%'
    ubl_email_pass: '%env(UBL_EMAIL_PASSWORD)%'

    robin_authenticate:
        -
            api_key: '%env(ROBIN_API_KEY)%'
            api_secret: '%env(ROBIN_API_SECRET)%'
            outgoing_api_key: '%env(ROBIN_OUTGOING_API_KEY)%'

    oauth_default_server: '%env(OAUTH_DEFAULT_SERVER)%'
    oauth_default_port: '%env(OAUTH_DEFAULT_PORT)%'
    oauth_default_client_id: '%env(OAUTH_DEFAULT_CLIENT_ID)%'
    oauth_default_client_secret: '%env(OAUTH_DEFAULT_CLIENT_SECRET)%'

    afas_custom_fields:
        OrderLineDataObject:
            orderNumber: U997EC53645A6E81692384AADA81F2BDB
            productId: U5CD9219D42833ECB8A9AC28C15291B0C
            stockId: UC51BA5EB405D7CF273F37A83DC651A7C
            pspReference: UE6344E317AAC4E9BA76FA4E62C03C1F8
            mollieReference: U86F703095C834A7AB60713C9C0CAE23B

    zabbix.send_triggers: 1
    zabbix.config:
        hostname: '%env(ZABBIX_HOSTNAME)%'
        server: '%env(ZABBIX_SERVER)%'
        port: '%env(ZABBIX_PORT)%'

    mollie.profiles:
        default:
            api_key: '%env(MOLLIE_PROFILE_DEFAULT_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_DEFAULT_ID)%'
        amsterdam:
            api_key: '%env(MOLLIE_PROFILE_AMSTERDAM_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_AMSTERDAM_ID)%'
        antwerpen:
            api_key: '%env(MOLLIE_PROFILE_ANTWERPEN_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_ANTWERPEN_ID)%'
        apeldoorn:
            api_key: '%env(MOLLIE_PROFILE_APELDOORN_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_APELDOORN_ID)%'
        eindhoven:
            api_key: '%env(MOLLIE_PROFILE_EINDHOVEN_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_EINDHOVEN_ID)%'
        groningen:
            api_key: '%env(MOLLIE_PROFILE_GRONINGEN_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_GRONINGEN_ID)%'
        rotterdam:
            api_key: '%env(MOLLIE_PROFILE_ROTTERDAM_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_ROTTERDAM_ID)%'
        utrecht:
            api_key: '%env(MOLLIE_PROFILE_UTRECHT_KEY)%'
            profile_id: '%env(MOLLIE_PROFILE_UTRECHT_ID)%'

    cbs_azure_endpoints:
        outgoing:
            catalog:
                url: '%env(CBS_AZURE_ENDPOINT_OUTGOING_CATALOG_URL)%'
                key: '%env(CBS_AZURE_ENDPOINT_OUTGOING_CATALOG_KEY)%'
            order:
                url: '%env(CBS_AZURE_ENDPOINT_OUTGOING_ORDER_URL)%'
                key: '%env(CBS_AZURE_ENDPOINT_OUTGOING_ORDER_KEY)%'

    transcontinenta_purchase_order_credentials:
        client_key: '%env(TRANSCONTINENTA_PURCHASE_ORDER_API_CLIENT_KEY)%'
        client_secret: '%env(TRANSCONTINENTA_PURCHASE_ORDER_API_CLIENT_SECRET)%'
        api_base_url: '%env(TRANSCONTINENTA_PURCHASE_ORDER_API_CLIENT_BASE_URL)%'

    events:
        administration_costs: '2.95'

    photo_contest:
        entry_file_unused_max_age: 1 hour

    competitor_monitor_account_setups:
        - 974 # Wex Photography - NL

    competitor_monitor_website_id_to_competitor_key:
        6017: !php/const CatBundle\Entity\CompetitorData\Competitor::KEY_KAMERA_EXPRESS
        6018: !php/const CatBundle\Entity\CompetitorData\Competitor::KEY_MPB

    competitor_monitor_cnu_website_ids:
        # Account Setup: 974
        - 6100

    powerreviews_sftp_config:
        host: '%env(POWERREVIEWS_SFTP_HOST)%'
        user: '%env(POWERREVIEWS_SFTP_USER)%'
        pass: '%env(POWERREVIEWS_SFTP_PASS)%'
        port: '%env(int:POWERREVIEWS_SFTP_PORT)%'
        filepath: /data/

    pricer_plaza_sftp_config:
        host: '%env(PRICER_PLAZA_SFTP_HOST)%'
        user: '%env(PRICER_PLAZA_SFTP_USER)%'
        private_key: '%env(PRICER_PLAZA_SFTP_PRIVATE_KEY)%'
        port: '%env(int:PRICER_PLAZA_SFTP_PORT)%'
        filepath: '%env(PRICER_PLAZA_SFTP_DIR)%'

services:
    _defaults:
        autowire: true
        autoconfigure: true
        bind:
            $rootDir: '%kernel.project_dir%'

    # Symfony & dependency aliases
    Symfony\Component\DependencyInjection\ContainerInterface: '@service_container'
    Psr\Container\ContainerInterface: '@service_container'
    Symfony\Contracts\Translation\TranslatorInterface: '@translator.default'
    League\Flysystem\MountManager: '@oneup_flysystem.mount_manager'
    PhpAmqpLib\Connection\AbstractConnection: '@old_sound_rabbit_mq.connection.default'

    # Make session and session.flash_bag available in container for legacy controllers
    Symfony\Component\HttpFoundation\Session\Session:
        factory: [ '@request_stack', 'getSession' ]

    session:
        public: true
        alias: 'Symfony\Component\HttpFoundation\Session\Session'

    Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface:
        factory: [ '@session', 'getFlashBag' ]

    session.flash_bag:
        public: true
        alias: 'Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface'

    # Prevents unused env var warnings
    cat.env_var_stub:
        public: false
        class: Webdsign\GlobalBundle\Services\EnvVarStub
        arguments:
            - '%env(SERVER_NAME)%'
            - '%env(SERVICETOOL_DOMAIN_DOTENV)%'

    Webdsign\GlobalBundle\Command\Abstract\ContainerAwareCommand:
        public: false
        abstract: true
        autoconfigure: false
        calls:
            - [ 'setContainer', [ '@service_container' ] ]

    memcached:
        public: true
        class: Memcached
        calls:
            - [ addServer, [ '%env(MEMCACHE_HOST)%', '%env(MEMCACHE_PORT)%' ] ]

    memcached_admin:
        public: true
        class: Memcached
        calls:
            - [ addServer, [ '%env(MEMCACHE_ADMIN_HOST)%', '%env(MEMCACHE_ADMIN_PORT)%' ] ]

    cat.cache.adapter.doctrine.system:
        parent: 'cache.adapter.memcached'
        tags:
            - { name: 'cache.pool', namespace: 'doctrine-scp-%cat.path_cache_key%' }

    cat.cache.adapter.doctrine.result:
        parent: 'cache.adapter.apcu'
        tags:
            - { name: 'cache.pool', namespace: 'doctrine-rcp-%cat.path_cache_key%' }

    Symfony\Component\HttpFoundation\Session\Storage\Handler\PdoSessionHandler:
        public: true
        arguments:
            - 'mysql:host=%env(DATABASE_CAMERANU_URK_HOST)%;dbname=%env(DATABASE_CAMERANU_URK_NAME)%'
            - { db_username: '%env(DATABASE_CAMERANU_URK_USERNAME)%', db_password: '%env(DATABASE_CAMERANU_URK_PASSWORD)%' }

    session.handler.pdo: '@Symfony\Component\HttpFoundation\Session\Storage\Handler\PdoSessionHandler'

    day_month_year_date_time_normalizer:
        class: CatBundle\Service\Serializer\DayMonthYearDateTimeNormalizer
        public: false
        tags: [ serializer.normalizer ]

    entities_normalizer:
        class: CatBundle\Service\Serializer\EntitiesNormalizer
        public: false
        autowire: true
        tags: [ serializer.normalizer ]

    object_normalizer_serializer:
        class: Symfony\Component\Serializer\Serializer
        arguments:
            - [ "@day_month_year_date_time_normalizer", "@serializer.normalizer.object" ]

    entities_normalizer_serializer:
        class: Symfony\Component\Serializer\Serializer
        arguments:
            - [ "@entities_normalizer", "@serializer.normalizer.object" ]

    CatBundle\Service\Serializer\CustomCourierListListNormalizer:
        arguments:
            - "@object_normalizer_serializer"

    custom_courier_list_list_serializer:
        class: Symfony\Component\Serializer\Serializer
        arguments:
            - [ "@custom_courier_list_list_normalizer", "@day_month_year_date_time_normalizer", "@serializer.normalizer.object" ]

    webdsign.vendiro_api:
        public: true
        class: Webdsign\GlobalBundle\Services\VendiroApi
        arguments:
            $apiKey: '%env(VENDIRO_API_KEY)%'
            $token: '%env(VENDIRO_API_TOKEN)%'
            $uri: '%env(VENDIRO_API_URI)%'

    webdsign.vendiro_cameratools_api:
        public: true
        class: Webdsign\GlobalBundle\Services\VendiroApi
        arguments:
            $apiKey: '%env(VENDIRO_CAMERATOOLS_API_KEY)%'
            $token: '%env(VENDIRO_CAMERATOOLS_API_TOKEN)%'
            $uri: '%env(VENDIRO_CAMERATOOLS_API_URI)%'

    CatBundle\Service\Vendiro\VendiroPost:
        arguments:
            - '@webdsign.vendiro_cameratools_api'

    webdsign.system_user_admin:
        public: true
        class: Webdsign\GlobalBundle\Entity\User
        factory: [ '@user_repository', find ]
        arguments:
            - '%app.system_user_admin%'

    CatBundle\Service\PackageLabelGenerator:
        calls:
            - [ setEntityManager, [ "@doctrine.orm.entity_manager" ] ]
            - [ setTransporters, [ "%transporters%" ] ]
            - [ setSender, [ "%sender%" ] ]
            - [ setDeliverytimes, [ "%deliverytimes%" ] ]
            - [ setConfig, [ "@service_container" ] ]
            - [ setCache, [ "@memcached" ] ]

    CatBundle\Service\PostNL\PackageStatusUpdater:
        calls:
            - [ setPostNL , [ '%transporters%', '%postnl%' ] ]

    CatBundle\Service\AmazonUploader:
        arguments:
            $localFilesystem: "@oneup_flysystem.temp_filesystem"
            $remoteFilesystem: "@oneup_flysystem.amazon_filesystem"
            $credentials: "%webdsign.amazon_credentials%"

    s3_client:
        public: true
        class: Aws\S3\S3Client
        arguments:
            -   version: '2006-03-01' # or 'latest'
                region: '%env(AMAZON_REGION)%'
                credentials:
                    key: '%env(AMAZON_KEY)%'
                    secret: '%env(AMAZON_SECRET)%'
    spaces_client:
        public: true
        class: Aws\S3\S3Client
        arguments:
            -   version: 'latest'
                region: '%env(SPACES_REGION)%'
                endpoint: '%env(SPACES_ENDPOINT)%'
                credentials:
                    key: '%env(SPACES_KEY)%'
                    secret: '%env(SPACES_SECRET)%'

    CatBundle\Service\AttachmentChecker:
        arguments:
            $attachmentPath: '%webdsign.mail_import.attachments_path%'

    Webdsign\GlobalBundle\Services\StockLocationsForAuthenticatedUser:
        public: true

    CatBundle\Service\InternalInvoicePdfGenerator:
        arguments:
            $fileSystem: '@oneup_flysystem.spaces_filesystem'

    CatBundle\Service\InternalInvoiceMailer:
        arguments:
            $config: '%internal_invoices_config%'

    CatBundle\Service\InternalInvoiceForCourierLists:
        arguments:
            $stockLocationCourierIds: '%stock_locations_couriers%'

    CatBundle\Service\CourierListStockTotalValue:
        arguments:
            $cache: '@memcached'

    CatBundle\Service\CourierListToOrder:
        arguments:
            $catUrl: '%env(CAT_URL)%'

    CatBundle\Service\CourierListOrderColli:
        arguments:
            $cache: '@memcached_admin'

    CatBundle\Service\CourierListFromMinimalStock:
        arguments:
            $ignoreParking: '%courier_list_minimal_stock_ignore_parking%'

    CatBundle\Command\ExportSalesForCriteo:
        arguments:
            $mailInfo: '%webdsign.criteo_sales_export%'

    CatBundle\Command\AnalyticsImportCSVCommand:
        tags:
            - { name: console.command }
        arguments:
            $mailInfo: '%webdsign.analytics_import_csv%'
            $ipAddresses: '%cameranu_ip_addresses%'
            $origins: '%cameranu_web_origins%'
            $view_id: '%webdsign.analytics_view_id%'

    CatBundle\Command\ClangCustomerPreferencesImportCommand:
        arguments:
            $options:
                clang_ftp_host: '%env(CLANG_FTP_HOST)%'
                clang_ftp_username: '%env(CLANG_FTP_USERNAME)%'
                clang_ftp_password: '%env(CLANG_FTP_PASSWORD)%'
                clang_ftp_location: '%env(CLANG_FTP_DIR)%'

    CatBundle\Service\OrderChecker:
        arguments:
            $deliveryNames: '%webdsign.order_checker_origin_names%'
            $recipients: '%webdsign.order_checker_recipients%'
            $sender: '%webdsign.infomail%'
            $adminLink: '%env(ADMIN_URL)%'

    CatBundle\Service\Export\EuropafotoExportStockFeed:
        arguments:
            $europaFotoFeedFileName: '%webdsign.europafoto_stock_feed_filename%'
            $supplierCollectionId: '%webdsign.europafoto_stock_feed_supplier_collection_id%'
            $fileSystem: '@oneup_flysystem.europafotofilesystem_filesystem'

    CatBundle\Service\GoogleAnalytics\Ga4Service:
        arguments:
            $apiSecret: '%env(GA4_API_SECRET)%'
            $measurementId: '%env(GA4_MEASUREMENT_ID)%'
            $apiSecretLocal: '%env(GA4_API_SECRET_LOCAL)%'
            $measurementIdLocal: '%env(GA4_MEASUREMENT_ID_LOCAL)%'
            $collectEndpointLocal: '%env(GA4_COLLECT_ENDPOINT_LOCAL)%'
            $collectDebugEndpointLocal: '%env(GA4_COLLECT_DEBUG_ENDPOINT_LOCAL)%'

    CatBundle\Command\AnalyticsUpdateCommand:
        arguments:
            $ipAddresses: '%cameranu_ip_addresses%'
            $origins: '%cameranu_web_origins%'

    CatBundle\Service\Adyen\StandardPaymentNotification:
        arguments:
            $configuration: '%adyen_configuration%'

    CatBundle\Service\Adyen\PosPayment:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_POS_API_KEY)%'

    CatBundle\Service\Adyen\PosTerminals:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_POS_API_KEY)%'

    CatBundle\Service\Adyen\Refund:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_REFUND_API_KEY)%'

    CatBundle\Service\CourierListToTransit:
        arguments:
            $stockLocationsTransit: '%stock_locations_transit%'

    CatBundle\Service\ClaimsAndFeesReportGenerator:
        arguments:
            $fileSystem: '@oneup_flysystem.spaces_filesystem'

    CatBundle\Service\Adyen\Capture:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_POS_API_KEY)%'

    CatBundle\Service\Adyen\Cancel:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_POS_API_KEY)%'

    CatBundle\Service\ProductFeed\ProductFeedService:
        arguments:
            $imgPath: '%webdsign.img_path%'
            $fileSystem: '@oneup_flysystem.productfeed_local_filesystem'
            $websiteUrl: '%env(WEBSITE_URL)%'
            $remoteDirectory: 'Feeds'
            $powerReviewsSFTP: '%powerreviews_sftp_config%'

    CatBundle\Service\Product\PriceExporter:
        arguments:
            $sftpConfig: '%pricer_plaza_sftp_config%'

    CatBundle\Command\OrdersAutomaticCancellationCommand:
        arguments:
            $ipAddresses: '%cameranu_ip_addresses%'
            $origins: '%cameranu_web_origins%'

    CatBundle\Consumer\OutOfStockConsumer:
        arguments:
            $cameranuParents: '%cameranu_parents%'
            $automaticTicketMailActive: '%app.automatic_ticket_mail_active%'

    CatBundle\Command\DisnetDifoxFeedCommand:
        arguments:
            $difoxUrl: '%env(DISNET_DIFOX_FEED_URL)%'

    CatBundle\Service\Export\ClangExporter:
        arguments:
            - '@oneup_flysystem.clang_filesystem'

    CatBundle\Command\BackOrdersCheckCommand:
        arguments:
            $cameraNUAddresses: '%cameranu_addresses%'

    CatBundle\Service\ProductUrlService:
        arguments:
            $cameranuUrl: '%env(WEBSITE_URL)%'

    CatBundle\Command\ExportBusinessCustomersCommand:
        arguments:
            $businessEmail: '%webdsign.businessmail%'
            $infoMail: '%webdsign.infomail%'

    CatBundle\Service\Export\ServicetoolExporter:
        arguments:
            $fileSystem: '@oneup_flysystem.spaces_filesystem'
            $remoteDirectory: 'Exports'

    producer.create_rma:
        public: true
        class: CatBundle\Producer\Rma\CreateNewRmaProducer
        arguments:
            - '@old_sound_rabbit_mq.connection.default'

    Webdsign\GlobalBundle\Services\RMA\HandleRmaReply:
        arguments:
            $postNlTrackAndTraceUrl: '%env(POSTNL_TRACK_AND_TRACE_URL)%'

    CatBundle\Service\Filter\InternalInvoiceFilter:
        arguments:
            - [ 'mixed_stock', 'courier_list', 'rma', 'credit', 'stock_transfer' ]
            - urk: [ '12', '22' ]
              amsterdam: [ '62' ]
              apeldoorn: [ '83' ]
              groningen: [ '90' ]
              eindhoven: [ '101' ]
              rotterdam: [ '109' ]
              antwerpen: [ '240' ]
              utrecht: [ '260' ]
            - [ 'created', 'invoice' ]

    CatBundle\Consumer\Tweakwise\CreateProduct:
        calls:
            - [ setHttpProductFeed, [ '@app.http_product_feed' ] ]
            - [ setTweakwiseApi, [ '@app.tweakwise_api' ] ]
            - [ setZabbixSender, [ '@app.zabbix_sender' ] ]
            - [ setProductRepository, [ '@product_repository' ] ]
            - [ setTelegramHandler, [ '@webdsign.telegram' ] ]
            - [ setSqueezelyPostProduct, [ '@squeezely.post_product' ] ]
            - [ setConfig, [ '%app.create_product_queue%' ] ]

    CatBundle\Service\ProductFeed\HttpProductFeedService:
        public: true
        parent: CatBundle\Service\ProductFeed\ProductFeedService

    CatBundle\Service\TweakwiseApi:
        calls:
            - [ setInstanceKey, [ '%env(TWEAKWISE_INSTANCE_KEY)%' ] ]
            - [ setApiKey, [ '%env(TWEAKWISE_API_KEY)%' ] ]

    CatBundle\Service\Tweakwise\PageBlockNavigation:
        arguments:
            $instanceKey: '%env(TWEAKWISE_INSTANCE_KEY)%'

    CatBundle\Service\Shipment\ShipmentGenerator:
        arguments:
            $catUrl: '%env(CAT_URL)%'
            $shipmentGeneratorDecorator: '@shipment_generator_decorator_postnl'

    CatBundle\Service\Shipment\ShipmentGeneratorDecoratorPostNL:
        arguments:
            $cache: '@memcached'
            $postNLNetworkPartnerActive: '%postnl.network_partner_active%'

    CatBundle\Producer\Clang\CreateClangWishlistHookProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'

    CatBundle\Consumer\ServiceContact\CreateServiceContactFormConsumer:
        calls:
            - [ setMailer, [ "@mailer.mailer" ] ]
            - [ setTelegramHandler, [ '@webdsign.telegram' ] ]
            - [ setEntityManager, [ '@doctrine.orm.entity_manager' ] ]
            - [ setSendFromEmail, [ '%app.send_from_email%' ] ]

    CatBundle\Producer\ServiceContact\CreateServiceContactFormProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'

    CatBundle\Service\SprayPay\Handler:
        arguments:
            $webshopId: '%env(SPRAYPAY_WEBSHOP_ID)%'
            $chargebackNoticiationUrl: '%env(SPRAYPAY_WEBHOOK_REFUND_URL)%'
            $apiKey: '%env(SPRAYPAY_API_KEY)%'
            $spraypayRefundUrl: '%env(SPRAYPAY_REFUND_URL)%'

    CatBundle\Service\PackingSlip\ScanHelper:
        arguments:
            $adminLink: '%env(ADMIN_URL)%'

    CatBundle\Controller\AdditionalContributions\AddAdditionalContribution:
        arguments:
            $paginatedFinderInterface: '@fos_elastica.finder.app_customer'
            $vatOptions: '%app.vat%'

    CatBundle\Controller\AdditionalContributions\EditAdditionalContribution:
        arguments:
            $paginatedFinderInterface: '@fos_elastica.finder.app_customer'
            $vatOptions: '%app.vat%'

    CatBundle\Command\WrongRefundCreditAccountCSVCommand:
        arguments:
            $mailInfo: '%webdsign.adyen_wrong_refund_mail_info%'
            $adyenConfiguration: '%adyen_configuration%'

    # cloudstorage controllers
    CatBundle\Controller\CloudStorage\Download:
        tags: [ controller.service_arguments ]
        arguments:
            $fileSystem: '@oneup_flysystem.spaces_filesystem'

    CatBundle\Controller\CloudStorage\Upload:
        tags: [ controller.service_arguments ]
        arguments:
            $fileSystem: '@oneup_flysystem.spaces_filesystem'

    CatBundle\Command\ImportOmniaCommand:
        arguments:
            $fileSystem: '@oneup_flysystem.omniafilesystem_filesystem'

    CatBundle\Service\Adyen\PosByReference:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_POS_API_KEY)%'

    CatBundle\Service\Adyen\NexoHelper:
        arguments:
            $passPhrase: '%env(ADYEN_POS_PASSPHRASE)%'

    CatBundle\Service\Adyen\PaymentMethods:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_POS_API_KEY)%'

    CatBundle\Service\ClaimsAndFeesHelper:
        arguments:
            $config: '%webdsign.claims_and_fees%'

    CatBundle\Service\RabbitMq\Client:
        arguments:
            $host: '%env(RABBITMQ_HOST)%'
            $user: '%env(RABBITMQ_USERNAME)%'
            $port: '%env(RABBITMQ_PORT)%'
            $pass: '%env(RABBITMQ_PASSWORD)%'

    CatBundle\Command\Zabbix\ImportRabbitMqData:
        arguments:
            $zabbixHost: '%env(ZABBIX_STUNNEL_HOST)%'

    CatBundle\Consumer\Ticket\TicketConsumer:
        calls:
            - [ setTicketHelper, [ '@ticket_helper' ] ]
            - [ setTelegramHandler, [ '@webdsign.telegram' ] ]

    CatBundle\Producer\Ticket\TicketProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'

    CatBundle\Consumer\StockUpdate\StockUpdateConsumer:
        calls:
            - [ setStockUpdater, [ '@webdsign.stock_updater' ] ]
            - [ setTelegramHandler, [ '@webdsign.telegram' ] ]

    CatBundle\Producer\StockUpdate\StockUpdateProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'

    CatBundle\Consumer\OutOfStock\OutOfStockConsumer:
        calls:
            - [ setConsumer, [ '@out_of_stock_consumer' ] ]
            - [ setTelegramHandler, [ '@webdsign.telegram' ] ]

    CatBundle\Producer\OutOfStock\OutOfStockProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'

    CatBundle\Consumer\Robin\RobinHookConsumer:
        calls:
            - [ setTelegramHandler, [ '@webdsign.telegram' ] ]
            - [ setRobinHookHandler, [ '@robin.webhook.handler' ] ]

    CatBundle\Producer\Robin\RobinHookProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'

    Servicetool\ServiceBundle\Services\RobinPost:
        arguments:
            $robinUrls: '%robin_urls%'
            $authentication: '%robin_authenticate%'
            $expectedHttp: '%robin_created_http_expected%'
            $debugConnection: '%app.robin.debug_connection%'

    CatBundle\Consumer\Vendiro\VendiroConsumer:
        calls:
            - [ setVendiroPost, [ '@webdsign.vendiro_post' ] ]
            - [ setTelegramHandler, [ '@webdsign.telegram' ] ]

    CatBundle\Producer\Vendiro\VendiroProducer:
        arguments:
            $conn: '@old_sound_rabbit_mq.connection.default'

    CatBundle\Service\Customer\DataValidationService:
        arguments:
            $postnlPostalcodecheckUrl: '%env(POSTNL_POSTALCODECHECK_URL)%'
            $postnlToken: '%env(POSTNL_TOKEN)%'
            $cache: '@memcached'

    CatBundle\Service\CourierList\CourierListCancel:
        arguments:
            $stockLocationsTransit: '%stock_locations_transit%'

    CatBundle\Service\CourierList\Issue\Handler:
        arguments:
            $stockLocationsTransit: '%stock_locations_transit%'

    CatBundle\Service\PowerReviews\Client:
        arguments:
            $merchantId: '%env(POWERREVIEWS_MERCHANTID)%'
            $baseUrl: '%env(POWERREVIEWS_BASEURL)%'
            $clientId: '%env(POWERREVIEWS_CLIENT_ID)%'
            $clientSecret: '%env(POWERREVIEWS_CLIENT_SECRET)%'

    CatBundle\Service\PowerReviews\Reviews:
        parent: CatBundle\Service\PowerReviews\Client
        arguments:
            $merchantId: '%env(POWERREVIEWS_MERCHANTID)%'
            $baseUrl: '%env(POWERREVIEWS_BASEURL)%'
            $clientId: '%env(POWERREVIEWS_CLIENT_ID)%'
            $clientSecret: '%env(POWERREVIEWS_CLIENT_SECRET)%'

    gedmo.listener.timestampable:
        public: true
        class: Gedmo\Timestampable\TimestampableListener
        tags:
            - { name: doctrine.event_subscriber, connection: cameranu_urk }
        calls:
            - [ setAnnotationReader, [ '@annotation_reader' ] ]

    monolog.logger.postnl:
        class: Symfony\Bridge\Monolog\Logger
        arguments:
            - 'postnl'

    CatBundle\Controller\Damage\ShowSlipController:
        public: true
        arguments:
            $logo: '%invoice_logo%'
            $logoRebranding: '%invoice_rebranding_logo%'

    CatBundle\Service\ClangHandler:
        public: true
        arguments:
            $clangToken: '%env(CLANG_TOKEN)%'

    CatBundle\Service\AdditionalContributionService:
        arguments:
            $infoMail: '%webdsign.infomail%'
            $invoiceUrl: '%webdsign.invoice_url%'
            $vatOptions: '%app.vat%'

    CatBundle\Command\CheckDuplicateInvoiceNumbers:
        arguments:
            $recipients: '%webdsign.duplicate_invoice_number_checker_recipients%'
            $sender: '%webdsign.root_mail_cameranu%'

    Webdsign\GlobalBundle\Services\PurchaseOrder\OrderMailer:
        arguments:
            $emailSignatures: '%email_signatures%'

    Webdsign\GlobalBundle\Services\Api\SupplierFeedManager:
        arguments:
            $fileSystem: '@oneup_flysystem.spaces_filesystem'

    Webdsign\GlobalBundle\ArgumentResolver\SupplierFeedDTOResolver:
        tags:
            - { name: controller.targeted_value_resolver }

    Webdsign\GlobalBundle\ArgumentResolver\ApiFilterArgumentResolver:
        tags:
            - { name: controller.argument_value_resolver, priority: 50 }

    Webdsign\GlobalBundle\ArgumentResolver\ApiOptionsArgumentResolver:
        tags:
            - { name: controller.argument_value_resolver, priority: 50 }

    CatBundle\Service\Product\ProductFinder:
        arguments:
            $finder: '@fos_elastica.finder.app_product_simple'

    CatBundle\Service\Stock\StockFinder:
        public: true
        arguments:
            $finder: '@fos_elastica.finder.app_stock'

    CatBundle\Service\Product\SecondHandProductFinder:
        public: true
        arguments:
            $finder: '@fos_elastica.finder.app_product'

    CatBundle\Service\SecondHand\SecondHandProductFinder:
        public: true
        arguments:
            $finder: '@fos_elastica.finder.app_product'

    Webdsign\GlobalBundle\Factory\SecondHand\SecondHandOrderFactory:
        public: true
        arguments:
            $translator: '@translator.default'

    Webdsign\GlobalBundle\Factory\SecondHand\SecondHandStockFactory:
        public: true
        arguments:
            $translator: '@translator.default'

    CatBundle\Service\SecondHand\SecondHandPriceManager:
        public: true
        arguments:
            $systemUser: '@webdsign.system_user_admin'

    CatBundle\Controller\MainGroup\Accessory\CreateAction:
        public: true
        arguments:
            $translator: '@translator.default'
            $router: '@router'

    CatBundle\Controller\MainGroup\Accessory\DeleteAction:
        public: true
        arguments:
            $translator: '@translator.default'

    CatBundle\Service\Adyen\PaymentInvitationGenerator:
        arguments:
            $websiteUrl: '%env(WEBSITE_URL)%'

    CatBundle\Service\Adyen\PosDisplayQR:
        arguments:
            $configuration: '%adyen_configuration%'
            $apiKey: '%env(ADYEN_POS_API_KEY)%'

    lock.store.flock_store:
        class: Symfony\Component\Lock\Store\FlockStore

    lock.lock_factory:
        class: Symfony\Component\Lock\LockFactory
        arguments:
            - '@lock.store.flock_store'

    CatBundle\Event\Console\ConsoleListener:
        arguments:
            - '@lock.lock_factory'
        tags:
            - { name: kernel.event_listener, event: console.command, method: onConsoleCommand }
            - { name: kernel.event_listener, event: console.terminate, method: onConsoleCommandTerminate }

    Webdsign\GlobalBundle\Controller\AbstractWebdsignController:
        public: false
        calls:
            - [ 'setContainer', [ '@service_container' ] ]
        arguments:
            - '@http_kernel'
            - '@translator.default'
            - '@Webdsign\GlobalBundle\Logger'
            - '@security.untracked_token_storage'
            - '@security.authorization_checker'
            - '@security.csrf.token_manager'
            - '@Doctrine\Persistence\ManagerRegistry'
            - '@knp_paginator'
            - '@twig'
            - '@router'
            - '@request_stack'
            - '@form.factory'
            - '@serializer'

    CatBundle\Command\IncomingProductsCommand:
        arguments:
            $adminLink: '%env(ADMIN_URL)%'

    CatBundle\Command\EIGReport\TransferAttachmentsCommand:
        tags: [ name: console.command ]
        arguments:
            $mailbox: '@cat.mailbox.eig_report'
            $filesystem: '@oneup_flysystem.eig_report_filesystem'

    CatBundle\Service\EIGReport\AttachmentTransporter:
        arguments:
            $targetDirectories:
                - { target: '/NL/Purchase/NL_Purchase_%%date%%', property: 'filename', regex: '/purchase/i' }
                - { target: '/NL/Stock/NL_Stock_%%date%%', property: 'filename', regex: '/stock/i' }
                - { target: '/NL/NL_Sales_%%date%%', property: 'filename', regex: '/sales/i' }
            $senderAddress: '%eig_report.sender_address%'

    cat.mailbox.eig_report:
        class: Webdsign\GlobalBundle\Mail\Mailbox
        arguments:
            $server: '%env(EIG_MAILBOX_SERVER)%'
            $port: '%env(int:EIG_MAILBOX_IMAP_PORT)%'
            $username: '%env(EIG_MAILBOX_USERNAME)%'
            $password: ''
            $addImapSetting: true
            $oauth: true
            $oauthClientId: '%env(EIG_MAILBOX_CLIENT_ID)%'
            $oauthClientSecret: '%env(EIG_MAILBOX_CLIENT_SECRET)%'

    ## Afas services
    Webdsign\GlobalBundle\Services\Afas\ApiClient:
        arguments:
            $url: '%env(AFAS_API_URL)%'
            $token: '%env(AFAS_API_TOKEN)%'

    Webdsign\GlobalBundle\Services\Afas\OrderManager:
        arguments:
            $customFields: '%afas_custom_fields%'

    Webdsign\GlobalBundle\Form\DataTransformer\Afas\OrderTransformer:
        arguments:
            $vatOptions: '%app.vat%'

    ## Adchieve services
    Webdsign\GlobalBundle\Services\Adchieve\ApiClient:
        arguments:
            $url: '%env(ADCHIEVE_API_URL)%'
            $accountId: '%env(ADCHIEVE_ACCOUNT_ID)%'
            $apiKey: '%env(ADCHIEVE_API_KEY)%'

    Webdsign\GlobalBundle\Services\ProductManager:
        arguments:
            $vatPercentages: '%app.vat%'

    # event controllers
    Webdsign\GlobalBundle\ArgumentResolver\EventDataObjectResolver:
        tags:
            - { name: controller.argument_value_resolver, priority: 50 }

    Webdsign\GlobalBundle\ArgumentResolver\RegistrationDataObjectResolver:
        tags:
            - { name: controller.argument_value_resolver, priority: 50 }

    CatBundle\Service\PowerBI\Client:
        arguments:
            $clientId: '%env(POWER_BI_CLIENT_ID)%'
            $clientSecret: '%env(POWER_BI_CLIENT_SECRET)%'
            $tenantId: '%env(POWER_BI_TENANT_ID)%'
            $workspaceId: '%env(POWER_BI_WORKSPACE_ID)%'
            $baseUri: '%env(POWER_BI_REST_API_URL)%'
            $cache: '@memcached'

    CatBundle\Service\Product\ProductImageManager:
        arguments:
            $environment: '%env(APP_ENV)%'

    CatBundle\Controller\SecondHand\Action\Save:
        arguments:
            $cache: '@memcached'

    cat.mollie.service_locator.base_handler:
        class: Symfony\Component\DependencyInjection\ServiceLocator
        arguments:
            -
                doctrine.orm.entity_manager: '@doctrine.orm.entity_manager'
                monolog.logger.mollie: '@monolog.logger.mollie'
                CatBundle\Service\PSP\LogManager: '@CatBundle\Service\PSP\LogManager'
                lock.lock_factory: '@lock.lock_factory'
                CatBundle\Service\Monolog\LogContextStore: '@CatBundle\Service\Monolog\LogContextStore'

    Webdsign\GlobalBundle\Services\Elastic\SynonymsService:
        arguments:
            $elasticFilesystem: '@oneup_flysystem.elastic_filesystem'

    CatBundle\Service\OpenAI\AssistantFactory:
        arguments:
            $apiKey: '%env(OPEN_AI_API_KEY)%'

    CatBundle\Service\OpenAI\FaqWriter:
        lazy: true
        factory: [ '@CatBundle\Service\OpenAI\AssistantFactory', 'getAssistant' ]
        arguments:
            $assistantName: 'CatBundle\Service\OpenAI\FaqWriter'

    CatBundle\Service\OpenAI\ContentWriter:
        lazy: true
        factory: [ '@CatBundle\Service\OpenAI\AssistantFactory', 'getAssistant' ]
        arguments:
            $assistantName: 'CatBundle\Service\OpenAI\ContentWriter'

    CatBundle\Service\OpenAI\ContentRewriter:
        lazy: true
        factory: [ '@CatBundle\Service\OpenAI\AssistantFactory', 'getAssistant' ]
        arguments:
            $assistantName: 'CatBundle\Service\OpenAI\ContentRewriter'

    CatBundle\Service\OpenAI\SpecWriter:
        lazy: true
        factory: [ '@CatBundle\Service\OpenAI\AssistantFactory', 'getAssistant' ]
        arguments:
            $assistantName: 'CatBundle\Service\OpenAI\SpecWriter'

    CatBundle\Service\OpenAI\ProductImagesDescriber:
        lazy: true
        factory: [ '@CatBundle\Service\OpenAI\AssistantFactory', 'getAssistant' ]
        arguments:
            $assistantName: 'CatBundle\Service\OpenAI\ProductImagesDescriber'

    CatBundle\Service\OpenAI\ProductsSorter:
        lazy: true
        factory: [ '@CatBundle\Service\OpenAI\AssistantFactory', 'getAssistant' ]
        arguments:
            $assistantName: 'CatBundle\Service\OpenAI\ProductsSorter'

    cat.monolog.formatter.multiline:
        class: Monolog\Formatter\LineFormatter
        arguments:
            $allowInlineLineBreaks: true
