{"name": "moment", "license": "MIT", "main": "moment.js", "ignore": ["**/.*", "benchmarks", "bower_components", "meteor", "node_modules", "scripts", "tasks", "test", "component.json", "composer.json", "CONTRIBUTING.md", "ender.js", "Gruntfile.js", "Moment.js.nuspec", "package.js", "package.json", "ISSUE_TEMPLATE.md", "typing-tests", "min/tests.js"], "homepage": "https://github.com/moment/moment", "version": "2.20.1", "_release": "2.20.1", "_resolution": {"type": "version", "tag": "2.20.1", "commit": "1bef83c699e577b29d5c775647cef3f43309a8f9"}, "_source": "https://github.com/moment/moment.git", "_target": ">=2.9.0", "_originalSource": "moment"}