var ticketOpened = false;

/**
 * Verzamelbak voor document.ready
 */
$(function () {

    /**
     * Fix replay checkboxes
     */
    $('#saveticketreply_wait').prop('checked', false);
    $('#saveticketreply_close').prop('checked', false);

    /**
     * Fix menu header bij meerdere regels
     */
    var doubleHeaderFix = function() {
        if ($('#sub-header').height() > 40) {
            $('#main').addClass('double-header');
        } else {
            $('#main').removeClass('double-header');
        }
    };
    doubleHeaderFix();

    /**
     * Filteren op log niveau
     */
    $(document).on('click', ".webdsignLogLevel", function() {
        var level = $(this).data("level");
        $(".webdsignLog li").each(function (){
            if($(this).data('level') < level) {
                $(this).hide();
            } else {
                $(this).show();
            }
        });
        return false;
    });

    /**
     * Ticketheader vastclippen aan menubalk als er gescrolld wordt.
     */
    $(window).scroll(function () {
        var isOverflowing = false;
        var header = $('#affix-header');
        var entryCard = $(".entry-card");
        var hasDoubleHeader = $('.double-header').length !== 0;

        if (entryCard[0]) {
            var topPosEntryCard = entryCard[0].getBoundingClientRect().top + $(window)['scrollTop']() - (hasDoubleHeader?111:91);
            var bottomPosEntryCard = entryCard[0].getBoundingClientRect().bottom + $(window)['scrollTop']() - (header.height()*2);

            if($(window).scrollTop() < topPosEntryCard || $(window).scrollTop() > bottomPosEntryCard) {
                isOverflowing = true;
            }
        }

        var customerInfo = $('#affix-customerInfo');

        if ($(window).scrollTop() > (hasDoubleHeader?10:30) && isOverflowing === false) {
            var headerWidth = header.parent().width();
            header.addClass('selfAffix').css('width', headerWidth).next().addClass('selfAffixPadding');

            var row = customerInfo.closest('.row');
            row.css('height', row.height());

            customerInfo.css('width', customerInfo.parent().width()).addClass('selfAffix');

        } else {
            header.removeClass('selfAffix').next().removeClass('selfAffixPadding');
            customerInfo.removeClass('selfAffix').css('left', 'auto').css('width', 'autocalc(33.333333% + 30px)');
        }
    });

    let collapseStates = JSON.parse(localStorage.getItem('collapseStates')) || {};

    $("#affix-customerInfo").find(".collapse").on('shown.bs.collapse hidden.bs.collapse', function() {
        let triggerElement = $(this).parent().find('[data-target="#' + this.id + '"]');
        collapseStates[this.id] = !$(this).hasClass('in');
        if (collapseStates[this.id]) {
            triggerElement.removeClass('zmdi-hc-rotate-180');
        } else {
            triggerElement.addClass('zmdi-hc-rotate-180');
        }
        localStorage.setItem('collapseStates', JSON.stringify(collapseStates));
    });

    // initiele collapse state instellen
    $.each(collapseStates, function(elementId, mustCollapse) {
        if (mustCollapse) {
            $('#' + elementId).collapse();
        }
    });

    $(window).resize(function () {
        $(window).scroll();
        doubleHeaderFix();
    });

    /**
     * Verander links met de popup class in popups.
     */
    $(document).on('click', "a[class$='-popup'],a[class*='-popup ']", function() {
        var height = 700;
        var width = 800;
        if ($(this).attr('data-width')) {
            width = $(this).data("width");
        }
        if ($(this).attr('data-height')) {
            height = $(this).data("height");
        }
        window.open($(this).attr("href"),$(this).attr("class"),'resizable=yes, scrollbars=yes, titlebar=yes, height='+height+',width='+width);
        return false;
    });

    /**
     * Maak een javascript filter input.
     */
    $(document).on('keyup', ".filter-box", function () {
        var $this = $(this);
        var value = $this.val().toString();
        var searchStr = value.toLowerCase();
        var targetClass = $this.data("target");
        var searchUrl = $this.data('search-url');
        var searchMe;
        if (searchUrl) {
            $this.next().find('.overlay').removeClass('hidden');
            $.ajax({
                url: searchUrl + value,
                dataType: "html",
                method: "POST",
                success: function (data) {
                    $('.ticket-list .items').html(data);
                }
            }).done(function () {
                $this.next().find('.overlay').addClass('hidden');
            });
        } else {
            $("." + targetClass).each(function () {
                if ($(this).attr('data-search')) {
                    searchMe = $(this).data("search").toString();
                } else {
                    searchMe = $(this).html();
                }

                if (searchMe.toLowerCase().indexOf(searchStr) > -1) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        }
    });

    $(document).on('click', ".show-notification", function(e) {
        e.preventDefault();
        var nIcons = $(this).attr('data-icon');
        var nType = $(this).attr('data-type');
        var nTitle = $(this).attr('data-title');
        var nMessage = $(this).attr('data-message');

        notify(nIcons, nType, nTitle, nMessage);
    });

    $('body').on('click', '.popup-confirm', function(){
        var link = $(this);
        Swal.fire({
            title: Translator.trans('js.sure'),
            text: Translator.trans('js.cant.undo'),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: 'DD6B55',
            confirmButtonText: Translator.trans('js.yes'),
            cancelButtonText: Translator.trans('js.cancel')
        }).then(function (result) {
            if (result.isConfirmed) {
                if (link.attr('href')) {
                    window.location.href = link.attr('href');
                } else {
                    if (link.prop('tagName') == 'BUTTON') {
                        link.removeClass('popup-confirm'); // als we deze niet weghalen blijven we in een loop hangen...
                        link.click();
                        link.addClass('popup-confirm'); // voor het geval er iets mis gaat met de submit...
                    } else {
                        link.closest('form').submit();
                    }
                }
            }
        });
        return false;
    });

    $(document).on('click', 'a[data-question]', function(e) {
        e.preventDefault();

        var link = $(this);
        Swal.fire({
            title: link.data('question'),
            icon: 'success',
            showCancelButton: true,
            confirmButtonColor: '#DD6B55',
            confirmButtonText: Translator.trans('js.yes'),
            cancelButtonText: Translator.trans('js.no')
        }).then(function (result) {
            if (result.isConfirmed) {
                if (link.data('question-true')) {
                    window.location.href = link.data('question-true');
                    return;
                }
            } else if (link.data('question-false')) {
                window.location.href = link.data('question-false');
                return;
            }
            window.location.href = link.attr('href');
        });
        return false;
    });

    /**
     * Modals laten werken voor alle ajax requests, met kleur
     */
    $(document).on('hidden.bs.modal', '#ajaxModal', function () {
        $(this).removeData('bs.modal');
        var target = $(this).data('target');
        $(target).removeAttr('data-modal-color');
    });

    /**
     * Registreer de source van het event
     */
    $(document).on('show.bs.modal', '#ajaxModal', function (event) {
        $('#ajaxModal').data('related-target', event.relatedTarget);
    });

    /**
     * Als de data voor een modal geladen gaan we kijken of we
     * een formulier moeten vullen vanuit localstorage.
     */
    $(document).on('loaded.bs.modal', '#ajaxModal', function () {
        $('.modalMinified').removeClass('modalMinified');
        var modal = $('#ajaxModal');
        fetchLocalFormData(modal.find('.modal-content'));

        // Voor een ticket in een modal binnen een ticket moet er nog wat extra gebeuren...
        if (modal.data('related-target') && $(modal.data('related-target')).data('ticket-modal')) {
            showExpandReply();
            hideReplyContacts();
            initTicketPendingDatePicker();
        }
    });

    $(document).on('click', '.minifyModalButton', function () {
        var modal = $(this).closest('.modal');
        var modalContent = modal.find('.modal-content').first().clone();

        modal.modal('hide');
        modalContent.find('.minifyModalButton').remove();
        modalContent.addClass('draggableModal');
        modalContent.css('position', 'absolute');
        modalContent.css('right', '0');

        $('#main').append(modalContent);
        //Zodat resizen niet naar links schiet
        modalContent.css('left', modalContent.position().left);

        modalContent.draggable({
                handle: '.modal-header',
                containment: 'html',
                drag: function(event, ui) {
                    ui.position.top = event.pageY-115;
                    if(ui.position.top < 0) { ui.position.top = 0; }
                }
            }
        );
        modalContent.resizable({
            //containment: "body",
            handles: 'e, w'
        });
    });

    $(document).on('focusin', '.draggableModal', function () {
        $('.draggableModal').css('z-index', 7);
        $(this).css('z-index', 8);

    });

    $(document).on('click', '.draggableModal .close', function () {
        $(this).closest('.draggableModal').remove();

    });

    /*
     $(document).on('click', '.showMinifiedModal', function () {
     $('.modalMinified').removeClass('modalMinified');
     $(this).addClass('hidden');
     });*/



    $(document).on('click', 'a[data-target-color]', function(){
        var color = $(this).data('target-color');
        var target = $(this).data('target');
        $(target).attr('data-modal-color', color);
    });

    /**
     * Alle rijen tegelijk selecteren.
     */
    $(document).on('click', '.check-all, .selectable-toggle', function() {
        var allCheckboxes;

        if($(this).hasClass('check-all')) {
            allCheckboxes = $(this).parents('.selectable').find('td input[type="checkbox"]');
            allCheckboxes.prop('checked', $(this).prop('checked'));
        } else if($(this).hasClass('selectable-toggle')) {
            allCheckboxes = $('div.selectable').find('.lv-body div.checkbox input[type="checkbox"]');
            allCheckboxes.prop('checked', $(this).data('check'));
        }

        if(typeof(allCheckboxes) != 'undefined') {
            allCheckboxes.trigger('change', [false]);
            selectableCheckboxHandling();
        }
    });

    /**
     * Een rij selecteren na een klik op de tr.
     */
    $(document).on('click', 'table.selectable tbody tr.row-select, .selectable .list-row .row-select', function(e) {
        if (e.target.type !== 'checkbox' && e.target.tagName.toLowerCase() !== 'a') {
            $(this).find('td input[type="checkbox"], div.checkbox input[type="checkbox"]').prop('checked', !$(this).find('td input[type="checkbox"], div.checkbox input[type="checkbox"]').prop('checked')).trigger('change');
        }
    });

    /**
     * Rijen selecteren met de shifttoets.
     */
    $(".selectable").mousedown(function (e) {
        if(!$(e.target).parents('form').length) {
            if (e.ctrlKey || e.shiftKey) {
                e.preventDefault();
            }
        }
    });

    var shiftPreviousIndex = false;

    $(document).on('click', 'table.selectable tbody tr, .selectable .list-row', function(e) {
        var currentIndex = $('table.selectable tbody tr, .selectable .list-row').index(this);

        if(e.shiftKey) {
            if(shiftPreviousIndex !== false) {
                var currentChecked = $(this).find('input[type="checkbox"]').prop('checked');
                if(currentIndex > shiftPreviousIndex) currentIndex += 1;

                var selectIndexes = [currentIndex, shiftPreviousIndex].sort(function(a,b){return a - b});

                $(this).parents('tbody, .lv-body').find('tr, .list-row').slice(selectIndexes[0], selectIndexes[1]).find('input[type="checkbox"],input[type="radio"]').prop('checked', currentChecked).trigger('change');
            }
        }
        shiftPreviousIndex = currentIndex;

        if($(e.target).is('input')) {
            e.stopPropagation();
        }
    });

    /**
     * Rijen een extra class meegeven als een checkbox aangezet wordt.
     */
    $(document).on('change', '.selectable td input[type="checkbox"], .selectable .lv-body input[type="checkbox"]', function(e, triggerHandling) {
        if($(this).data('id') !== undefined) {
            if (typeof(triggerHandling) == 'undefined') {
                triggerHandling = true;
            }

            var action = false;

            if (this.checked) {
                action = 'checked';
                $(this).parents('tr').addClass('highlighted');
                $(this).parents('.list-row').addClass('highlighted');
            } else {
                action = 'unchecked';
                $(this).parents('tr').removeClass('highlighted');
                $(this).parents('.list-row').removeClass('highlighted');
            }

            // Andere gebruikers laten zien dat we dit item aan- of uitvinken.
            if (typeof notifyType != 'undefined' && wsSession != false && action != false) {
                var entryId = $(this).attr('data-id');

                wsSession.publish('webdsign/update/' + notifyType, [{
                    action: action,
                    entryId: entryId
                }]);
            }

            if (triggerHandling) {
                selectableCheckboxHandling();
            }
        }
    });

    /**
     * Zit er meer tekst in een reply dan we zien? Dan het openklapicoontje laten zien.
     */
    var showExpandReply;

    (showExpandReply = function() {
        $('.lv-item.message-reply .lv-small').each(function(key, element) {
            if(isOverflowed(element)) {
                $(element).parents('.media-body').find('.expand-reply').removeClass('hide');
            }
        });
        $(".hiddenReplies").addClass("hidden").removeClass("invisible");
    })();

    /**
     * Klap een reply open.
     */
    $(document).on('click', '.message-reply.internal, .message-reply.reply, .message-reply.customerReply, .message-reply.failedReply', function(e) {
        var expandReply = $(this).find('.expand-reply');

        if(!expandReply.hasClass('hide')) {
            var icon = expandReply.find('.zmdi');
            var text = expandReply.parents('.media-body').find('.lv-small');
            if(icon.hasClass('zmdi-chevron-down')) {
                text.addClass('expanded');
                icon.removeClass('zmdi-chevron-down').addClass('zmdi-chevron-up');
            } else {
                var offset = $(this).offset();
                if(e.pageY - offset.top < 30) {
                    text.removeClass('expanded');
                    icon.removeClass('zmdi-chevron-up').addClass('zmdi-chevron-down');
                }
            }
        }
    });

    /**
     * Reply bij een bericht pas laten zien bij een klik.
     */
    $(document).on('click', '.reply-form', function() {
        $(this).addClass('open');
        $(this).find('.reply-closed').addClass('hidden');
        $(this).find('.reply-open').removeClass('hidden');
    });

    /**
     * En ook weer verbergen als buiten de lijst met replies geklikt wordt.
     */
    $(document).on('mouseup', function(e) {
        if (!$(e.target).closest(".listview, .select2-container--open, .sweet-overlay, .sweet-alert").length) {
            $('.reply-form').removeClass('open');
            $('.reply-closed').removeClass('hidden');
            $('.reply-open').addClass('hidden');
        }
    });

    /**
     * De balk met selectie-acties verbergen.
     */
    $(document).on('click', 'nav.selectable .hide-selectable', function() {
        $('nav.selectable').removeClass('slideIn');

        var allCheckboxes = $('.selectable').find('input[type="checkbox"]');
        allCheckboxes.prop('checked', false);

        $('.selectable .highlighted').removeClass('highlighted');
    });

    $(document).on('click', 'a.ajax', function(e) {
        var element = $(this);
        var url = element.attr('href');
        var executeFunction = element.data('function');
        var options = element.data('options');

        $.ajax({
            url: url,
            dataType: "json",
            method: "POST",
            success: function (data) {
                if(typeof window[executeFunction] == 'function') {
                    window[executeFunction](data, element);
                    $('body>.tooltip').remove();
                }
            }
        });

        e.preventDefault();
        return false;
    });

    /**
     * Actie uitvoeren vanuit de balk met selectie-acties.
     */
    $(document).on('click', 'nav.selectable .selectable-action', function() {
        if(typeof $(this).data('popover') !== 'undefined') {
            return;
        }

        var element = $(this);
        var executeFunction = $(this).data('function');
        var options = $(this).data('options');
        var allCheckboxes = $('.selectable').find('td input[type="checkbox"]:checked, .lv-body input[type="checkbox"]:checked');

        if(typeof window[executeFunction] == 'function') {
            /**
             * Functie uitvoeren met de geselecteerde rijen.
             * Als dat goed gaat de checkboxes weer uitzetten.
             */

            if(typeof options.confirm != 'undefined') {
                Swal.fire({
                    title: Translator.trans('js.sure'),
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: Translator.trans('js.oke'),
                    cancelButtonText: Translator.trans('js.cancel')
                }).then(function (result) {
                    if (result.isConfirmed) {
                        window[executeFunction](allCheckboxes, options, element);
                    }
                });
            } else {
                window[executeFunction](allCheckboxes, options, element);
            }
        }
    });

    var selectableHasPopover = $('.has-popover>.selectable-action');

    selectableHasPopover.each(function() {
        if($(this).data('popoverContent') !== 'undefined') {
            var popoverContent = $(this).siblings('.selectable-popover-content');
            $(this).data('popoverContent', popoverContent);
        }
    });

    selectableHasPopover.popover({
        content: function() {
            return $(this).data('popoverContent');
        }
    });

    selectableHasPopover.on('hide.bs.popover', function() {

    });

    selectableHasPopover.on('show.bs.popover', function() {
        if($(this).data('popoverContent') !== 'undefined') {
            $(this).data('popoverContent').removeClass('hide');
        }
    });

    /**
     * Volledige ticket of e-mail openen vanuit een lijst na het klikken op de row.
     */
    $(document).on('click', '.lv-item.list-row .media-body', function(e) {
        if(!$(e.target).is('input')) {
            var openEntry = $(this).find('.open-entry');
            if(openEntry.length > 0) {
                openEntry.first().trigger('click');
            }

            var openRma = $(this).find('.open-rma');
            if(openRma.length > 0) {
                window.location = $(".open-rma").first().attr('href');
            }
        } else {
            e.stopPropagation();
            return true;
        }
    });

    var markAsReadTimers = [];

    /**
     * Volledige ticket of e-mail openen vanuit een lijst.
     */
    $(document).on('click', '.lv-item .open-entry', function(e) {
        e.preventDefault();
        var $this = $(this);

        var customerAddAction = $('.customer-add-action');
        customerAddAction.removeData(['ticket-id', 'mail-id']);

        if($this.data('ticket-id')) {
            if($this.attr('data-no-customer')) {
                customerAddAction.data('ticket-id', $this.data('ticket-id'));
                entryIsOpen = true;
            } else {
                $('.ticket-add-action').data('customer-id', $this.data('customer-id'));
                customerEntryIsOpen = true;
            }
        }
        if($this.data('mail-id')) {
            customerAddAction.data('mail-id', $this.data('mail-id'));
            entryIsOpen = true;
        }


        var row = $(this).parents('.lv-item.list-row');
        var entryId = row.find('div.checkbox input[type="checkbox"]').data('id');
        var cardContainer = $('<div class="entry-card"></div>');
        var card = $('<div class="card z-depth-2" data-entry='+entryId+'></div>');
        var entry;
        var list = $(this).parents('.lv-body');
        var markAsRead = list.data('mark-as-read');

        // Eerst alle andere entries weer dichtklappen.
        $('div.entry-card').remove();
        $('.lv-item.list-row').show();

        // Andere gebruikers laten zien dat we dit item niet meer befkijken.
        if(typeof notifyType != 'undefined' && wsSession != false) {
            wsSession.publish('webdsign/update/' + notifyType, [{
                action: 'closing'
            }]);
        }

        var scrollTo = 90;

        $.get($(this).attr('href'), function(data) {

            // Eerst alle andere entries weer dichtklappen
            $('div.entry-card').remove();
            $('.lv-item.list-row').show();

            // Onze code geeft altijd een <section> terug met een ajaxrequest.
            entry = $($(data)[2]);
            script = "<script>" + $($(data)[4]).html() + "</script>";

            card.html(entry.html() + script);
            cardContainer.append(card);
            row.after(cardContainer);
            row.hide();

            // Een paar standaardacties uitvoeren die normaal bij het laden
            // van de pagina uitgevoerd worden.
            showExpandReply();
            hideReplyContacts();
            initTicketPendingDatePicker();

            // Markeren als gelezen als dat nodig is.
            if(markAsRead !== 'undefined' && row.hasClass('unread')) {
                var timer = setTimeout(function() {
                    $.get(markAsRead, {id: entryId}, function(data) {
                        row.removeClass('unread');
                        row.addClass('read');
                    });
                }, 2500);
                markAsReadTimers.push(timer);
            }

            // Naar de bovenkant van het element scrollen.
            $('html, body').animate({
                scrollTop: cardContainer.offset().top - scrollTo
            }, 0);

            // Andere gebruikers laten zien dat we dit item bekijken.
            if(typeof notifyType != 'undefined' && wsSession != false) {
                wsSession.publish('webdsign/update/' + notifyType, [{
                    action: 'reading',
                    entryId: entryId
                }]);

                ticketOpened = true;
            }

            fetchLocalFormData(card);
        }).always(function() {
            $this.data('requestRunning', false);
        });

        return false;
    });

    /**
     * En ook weer verbergen als buiten de lijst met entries geklikt wordt.
     */

    $(document).on('mouseup', function(e) {
        if (!$(e.target).closest(".listview, .dropdown.open, .select2-container, .sweet-overlay, .sweet-alert, .action-menu").length && (e.target != $('html').get(0))) {
            $('div.entry-card').remove();
            $('.lv-item.list-row').show();

            //Entry uit met delay omdat je bij klikken op dingen deze al sluit en de check dus niet doorkomt.
            setTimeout(function() {
                entryIsOpen = false;
                customerEntryIsOpen = false;
            }, 5);

            // Markeren als gelezen stoppen als er nog timers lopen.
            $.grep(markAsReadTimers, function(timer) {
                clearTimeout(timer);
                return false;
            });

            // Andere gebruikers laten zien dat we dit item niet meer bekijken.
            if(typeof notifyType != 'undefined' && wsSession != false && ticketOpened == true) {
                wsSession.publish('webdsign/update/' + notifyType, [{
                    action: 'closing'
                }]);

                ticketOpened = false;
            }
        }
    });

    /**
     * Notities openen vanuit een lijst.
     */
    $(document).on('mouseover', '.lv-item .notes', function(e) {
        e.preventDefault();
        var $this = $(this);

        // We willen maar een event tegelijk verwerken.
        if($this.data('requestRunning')) {
            return false;
        }

        if($this.data('hasPopover')) {
            $this.popover('show');
        } else {
            $this.data('requestRunning', true);
            $.get($(this).data('url'), function (data) {
                // Onze code geeft altijd een <section> terug met een ajaxrequest.
                var html = $($(data)[2]).html() + "<script>" + $($(data)[4]).html() + "</script>";
                $this.popover({
                    animation: false,
                    content: html,
                    html: true,
                    placement: 'left',
                    trigger: 'manual',
                    container: 'body'
                }).popover('show');
                $this.data('hasPopover', true);
            }).always(function () {
                $this.data('requestRunning', false);
            });
        }
        return false;
    });

    $(document).on('mouseout', '.lv-item .notes', function(e) {
        e.preventDefault();
        var $this = $(this);

        // We willen maar een event tegelijk verwerken.
        if($this.data('requestRunning')) {
            return false;
        }

        if($this.data('hasPopover')) {
            $this.popover('hide');
        }
        return false;
    });

    /**
     * Formulier in dropdownmenu voor o.a. het toewijzen van een ticket.
     */
    $(document).on('click', '.dropdown-menu .fg-line', function(e) {
        e.stopPropagation();
    });

    /**
     * De keep-open class zorgt ervoor dat een dropdown alleen dicht kan bij een klik
     * op een button of een link.
     */
    $(document).on('shown.bs.dropdown', '.dropdown.keep-open', function() {
        $(this).data('closable', false);
    });

    $(document).on('mousedown', '.dropdown.keep-open button, .dropdown.keep-open a', function() {
        var parent = $(this).parents('.dropdown.keep-open');
        parent.data('closable', true);
    });

    $(document).on('hide.bs.dropdown', '.dropdown.keep-open', function() {
        return $(this).data('closable');
    });

    $(document).on('click', '.lvh-filter-trigger', function(e) {
        e.preventDefault();
        var $lvHeaderAlt = $(this).closest('.lv-header-alt');
        var filter = $lvHeaderAlt.find('.lvh-filter');
        $lvHeaderAlt.css('min-height', '104px');
        filter.fadeIn(300);
    });

    $(document).on('click', '.lvh-assignnew-trigger', function(e) {
        e.preventDefault();
        var filter = $(this).closest('.lv-header-alt').find('.lvh-assignnew');
        filter.fadeIn(300);
    });

    $(document).on('click', '.lvh-extra-bar-close', function(e) {
        e.preventDefault();
        var filter = $(this).parent('.lvh-extra-bar');
        var $lvHeaderAlt = $(this).closest('.lv-header-alt');
        $lvHeaderAlt.css('min-height', '63px');
        filter.fadeOut(300);
    });

    $(document).on('submit', '.filter-form', function(e) {
        e.preventDefault();
        var form = $(this);
        var formname = $(this).data('formname');
        var formData = form.serializeObject();
        var allInputs = form.find('input, textarea, select').not(':input[type=button], :input[type=submit], :input[type=reset]');
        var url = window.location.pathname + window.location.search;
        var filterUrl = url;
        var setKey;
        var setValue;
        var regex;
        var matches;

        if(typeof formData[formname] == 'undefined') {
            console.error('formname is niet gezet voor dit formulier');
            return false;
        }

        if (form.data('otherUrl')) {
            filterUrl = form.data('otherUrl');
        }

        allInputs.each(function() {
            if(typeof $(this).attr('name') == 'undefined') {
                return;
            }

            setValue = '';
            regex = new RegExp(formname + '\\[([a-zA-Z0-9]+)\\]', "gi");
            matches = regex.exec($(this).attr('name'));

            if(matches !== null) {
                setKey = matches[1];

                if(typeof formData[formname][setKey] != 'undefined') {
                    setValue = formData[formname][setKey];
                    if (Array.isArray(setValue)) {
                        setValue = setValue.join();
                    }
                }

                filterUrl = updateURLParameter(filterUrl, setKey, setValue, true);
            }
        });

        if(url != filterUrl) {
            // De pagina parameter moet ook een reset krijgen na het filteren.
            filterUrl = updateURLParameter(filterUrl, 'page', '', true);
            window.location = filterUrl;
        }
    });

    /**
     * Action menu ( Plus Onderin ) functies
     */
    $(document).on({
        mouseenter: function () {
            $(".action-extra").addClass("is-visible");
        },
        mouseleave: function () {
            $(".action-extra").removeClass("is-visible");

        }
    }, ".action-menu");

    $(document).on({
        mouseenter: function () {
            $(this).find(".hover").removeClass("hidden");
            $(this).find(".no-hover").addClass("hidden");
        },
        mouseleave: function () {
            $(this).find(".no-hover").removeClass("hidden");
            $(this).find(".hover").addClass("hidden");
        }
    }, ".icon-switch");

    /**
     * Logout
     */
    $(document).on('click', ".logout", function(e) {
        var link = $(this);
        $.get(link.data("open-tickets"), '', function(data) {
            var tickets = parseInt(data.tickets);
            if(tickets == 0) {
                window.location.href = link.attr('href');
            } else {
                Swal.fire({
                    title: Translator.trans('js.logout.tickets.title'),
                    text: Translator.trans('js.logout.tickets.open', { 'count' : tickets}),
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: Translator.trans('js.logout.yes'),
                    cancelButtonText: Translator.trans('js.logout.no')
                }).then(function (result) {
                    if (result.isConfirmed) {
                        window.location.href = link.attr('href');
                    } else {
                        window.location.href = link.data('check-tickets');
                    }
                });
                return false;
            }
        });
        e.preventDefault();
        return false;
    });


    $(document).on('keyup', '.ajaxUpdater', function(e){
        if(e.which == 13) {
            runAjaxUpdate($(this));
        }
        e.preventDefault();
        return false;
    });


    /**
     * Als het toewijsformulier gebruikt wordt om meerdere mails of tickets
     * toe te wijzen moeten we de id's handmatig zetten.
     */
    $(document).on('submit', '.popover form.ticketAssign', function(e) {
        var form = $(this);
        var ticketIdField = form.find('#assignticket_id');
        var allCheckboxes = $('.selectable').find('input[type="checkbox"]:checked');

        var ticketIds = allCheckboxes.map(function() {
            return $(this).data('id');
        }).get().join(',');

        ticketIdField.val(ticketIds);
    });

    /**
     * Submitten van een formulier via ajax
     */
    $(document).on('submit', 'form.ajaxSubmit', function(e) {
        var form = $(this);
        var formData = form.serializeObject();
        var url = form.attr('action');
        var executeFunction = form.data('function');

        $.ajax({
            url: url,
            dataType: "json",
            method: "POST",
            data: formData,
            success: function (data) {
                if(typeof window[executeFunction] == 'function') {
                    window[executeFunction](data, form);
                }
            }
        });

        // Eventuele popovers weer dichtdoen
        $(this).closest('.dropdown.keep-open.open').removeClass('open');
        $('.has-popover>.selectable-action').popover('hide');

        e.preventDefault();
        return false;
    });

    /**
     * Iets afhandelen door op het vinkje te klikken.
     */
    $(document).on('click', '.complete-entry', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var ticketUrl = $(this).data('ticket-url');
        var ticketName = $(this).data('ticket-name');

        var closeTicket = function() {
            if(currentRoute == 'ticketShow' || currentRoute == 'mailRead') {
                window.location = url;
            } else {
                $.get(url, function(data) {
                    fadeRowsAndRefresh(data, ticketUrl, ticketName);
                });
            }
        };

        if ($(this).closest('.row').find(".hoverBlock[data-tag-category='rma']").length) {
            confirmRmaClosure(closeTicket);
        } else {
            closeTicket();
        }
    });

    $(document).on('change', 'input[name*=close]', function(e) {
        if($(this).prop("checked") && e.originalEvent
            && $(this).closest("form").closest(".row").find(".hoverBlock[data-tag-category='rma']").length) {

            var checkBox = $(this);
            confirmRmaClosure(null, function() {
                checkBox.prop("checked", false);
            });
        }
    });

    $(document).on('change', '#rma_tags', function() {
        var tag = $(this);
        if (tag.val() !== null) {
            var url = tag.parents('[data-status-url]').data('status-url').replace('!ID!', tag.val().join(','));
            $.get(url).done(function (data) {
                var rmaStatus = $('#rma_status');
                var selectedValue = rmaStatus.val();
                rmaStatus.find('option').remove();
                rmaStatus.append($('<option></option>'));
                for(var i=0; i<data.length; i++) {
                    rmaStatus.append($('<option></option>').val(data[i].id).html(data[i].text));
                }
                rmaStatus.val(selectedValue).trigger('change');
            });
        }
    });

    $(document).on('click', '.lv-item.message-reply[data-replyid] .lv-title a[href*=ticketfromreply]', function(e) {
        e.preventDefault();

        var url = $(this).attr('href');
        var selectedReply = $(this).closest('[data-replyid]');
        var newerReplies = selectedReply.add(selectedReply.nextAll('.lv-item.message-reply[data-replyid]'));

        if (selectedReply.closest('.hiddenReplies').length) {
            newerReplies = newerReplies.add(selectedReply.closest('.hiddenReplies').parent().nextAll('.lv-item.message-reply[data-replyid]'));
        }

        newerReplies.each(function() {
            $(this).find(".lv-title").prepend('<span class="pull-right"><div class="checkbox checkbox-inline p-l-5"><label class="p-r-5 p-l-20"><input type="checkbox" class="ticketfromreply" value="'+$(this).data("replyid")+'"><i class="input-helper"></i></label></div></span>');
        });
        selectedReply.find(".ticketfromreply")
            .prop('checked', true)
            .prop('disabled', true)
            .parent()
            .popover({
                'title': Translator.trans('js.service.ticket.reply.convert.title'),
                'content': '<p>' + Translator.trans('js.service.ticket.reply.convert.body') + '</p><div class="m-t-10 text-center"><button class="btn btn-primary" id="convertRepliesToTicket">' + Translator.trans('js.service.ticket.reply.convert.button') + '</button></div>',
                'html': true,
                'container': 'body',
                'placement': 'right',
                'trigger': 'manual'
            })
            .popover('show');

        $("#convertRepliesToTicket").on('click', function() {
            $(this).prop("disabled", true);

            var ids = [];
            $(".ticketfromreply:checked").each(function(){
                ids.push(this.value);
            });

            window.location = url + '?replies=' + ids.join(',');
        });

        // verwijder alle knopjes om van een reply een ticket te maken...
        $('.lv-item.message-reply[data-replyid] .lv-title a[href*=ticketfromreply]').remove();
        // eventuele tooltip die is blijven staan verbergen
        $('.tooltip').hide();
    });

    function confirmRmaClosure(confirmCallback, cancelCallback) {
        Swal.fire({
            title: Translator.trans('js.sure'),
            text: Translator.trans('js.sure.completeRma'),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#DD6B55',
            confirmButtonText: Translator.trans('js.oke'),
            cancelButtonText: Translator.trans('js.cancel')
        }).then(function (result) {
            if (result.isConfirmed && confirmCallback) {
                confirmCallback();
            } else if (!result.isConfirmed && cancelCallback) {
                cancelCallback();
            }
        });
    }

    $(document).on('click', '#addProductToRma', function(e) {
        e.preventDefault();
        var productsContainer = $("#rmaProductContainer");

        // zoek een beschikbaar ID voor het nieuwe product
        var lastIndex = 0;
        productsContainer.children("div").each(function() {
            lastIndex = Math.max(lastIndex, this.id.substring(this.id.lastIndexOf('_')+1));
        });

        var newProduct = $(productsContainer.data('new-item').replace(/__name__/g, lastIndex + 1));
        $(this).before(newProduct);
        updateRmaProductContainer();
    });

    $(document).on('click', '.deleteProductFromRma', function(e) {
        e.preventDefault();
        $(this).parent().remove();
        updateRmaProductContainer();
    });

    $(document).on('mouseover', '.deleteProductFromRma', function() {
        $(this).parent().addClass('alert-danger');
    });
    $(document).on('mouseout', '.deleteProductFromRma', function() {
        $(this).parent().removeClass('alert-danger');
    });

    $(document).on('change', '#rma_tags', function() {
        updateRmaProductContainer();
    });

    $(document).on('click', '#ticketstatistics-everybody', function(e) {
        e.preventDefault();

        var form = $(this).closest('form');
        form.data('otherUrl', $(this).data('url'));
        form.find('#ticketstatisticsfilterform_user').prop('disabled', true); // userId niet mee nemen
        form.submit();
    });

    /**
     * Autocomplete arrow navigation
     */
    $(document).keydown(function(e) {
        if ($(".autocompleter-results ul:visible:first").length) {
            var results = $(".autocompleter-results ul:visible:first");

            if (e.keyCode === 40 || e.keyCode === 38) { //down / up arrow keys
                if(results.find('.selected').length) {
                    var selected = results.find('.selected');
                    selected.removeClass('selected');
                    if (e.keyCode === 40) {
                        var next = selected.next();
                        if(next.hasClass("search-header")) { next = next.next() }
                    } else {
                        var next = selected.prev();
                        if(next.hasClass("search-header")) { next = next.prev() }
                    }
                    if(next.is('li')) {
                        next.addClass('selected');
                    } else {
                        if (e.keyCode === 40) {
                            var first = results.find("li").first();
                            if(first.hasClass("search-header")) { first = first.next() }
                            first.addClass('selected');
                        } else {
                            results.find("li").last().addClass('selected');
                        }
                    }
                } else {
                    var first = results.find("li").first();
                    if(first.hasClass("search-header")) { first = first.next() }
                    first.addClass('selected');
                }
                e.preventDefault();
                return false;
            }
            if (e.keyCode === 13) { //enter
                if(results.find('.selected').find('a').length) {
                    window.location = results.find('.selected').find('a').attr('href');
                }
                e.preventDefault();
                return false;
            }
        }

    });

    $(document).on('mouseover', '.autocompleter-results ul li', function(e) {
        if(!$(this).hasClass("search-header")) {
            $(".autocompleter-results ul .selected").removeClass("selected");
            $(this).addClass("selected");
        }
    });


    /** Peter fix **/
    if ($('.high-contrast-test').css('color') !== 'rgb(0, 0, 0)') {
        $('html').addClass('high-contrast-mode');
    }

    fetchLocalFormData();
    generateInputFields();

    $('.add-address-book').each(function() {
        addAddressBook($(this));
    });

    $(document).on('click', '.address-book-button', function() {
        var select = $(this).siblings('.address-book-select');

        select.select2('open');
        if(select.data('select2') !== undefined) {
            select.select2('open');
        }

        return false;
    });

    $(document).on('change', '.address-book-select', function() {
        var select = $(this).parent().parent().find('#saveticketreply_contactEmailAddress');

        if(select.data('select2') !== undefined) {
            var newValue = $(this).val();
            //var newText = $(this).find('option:selected').text();
            var newText = newValue;
            var newOption = $('<option></option>');
            var currentValues = [];

            newOption.val(newValue);
            newOption.text(newText);
            select.append(newOption);
            currentValues.push(newValue);

            select.select2('val', currentValues);
        }
    });

    // Formulierelementen opslaan in localstorage.
    $(document).on('change summernote.change', 'form[data-persist] input, form[data-persist] textarea, form[data-persist] select', function(e) {
        if(triggerPersistChange === true) {
            var form = $(this).parents('form');
            var element = $(this);
            var elementId = element.attr('id');
            var persistId = form.data('persist');
            var persistFromLastReply = form.data('persist-lastreply');
            var storeValue = null;

            var storedItem = false;
            try {
                storedItem = localStorage.getItem(persistId);
            } catch(e) {
                if(e.name == "NS_ERROR_FILE_CORRUPTED") {
                    alert("It looks like your browser storage has been corrupted. Please remove webappsstore.sqlite and restart your browser.");
                }
            }

            // We willen de lastReply onthouden, zodat we geen nieuwere data overschrijven
            if (!storedItem) {
                storedItem = {'__lastReply': persistFromLastReply};
            } else {
                storedItem = JSON.parse(storedItem);
                if (!storedItem['__lastReply'] || storedItem['__lastReply'] < persistFromLastReply) {
                    storedItem = {'__lastReply': persistFromLastReply};
                }
            }

            if(element.is(':checkbox')) {
                storeValue = element.is(':checked');
            } else {
                storeValue = element.val();
            }

            storedItem[elementId] = storeValue;
            try {
                localStorage.setItem(persistId, JSON.stringify(storedItem));
            } catch(e) {
                if(e.name == "NS_ERROR_FILE_CORRUPTED") {
                    alert("It looks like your browser storage has been corrupted. Please remove webappsstore.sqlite and restart your browser.");
                }
            }
        }
    });

    if ($(".orderAdd .order-popup").length === 0) {
        $("#rmaOrderHelp").removeClass("hidden");
    }

    // Localstorage weer legen voor een formulier na een submit.
    $(document).on('submit', 'form[data-persist]', function() {
        var form = $(this);
        var persistId = form.data('persist');

        try {
            localStorage.removeItem(persistId);
        } catch(e) {
            if(e.name === "NS_ERROR_FILE_CORRUPTED") {
                alert("It looks like your browser storage has been corrupted. Please remove webappsstore.sqlite and restart your browser.");
            }
        }
    });

    $(document).on('click', '.reply-form #saveticketreply_close', function() {
        $('#saveticketreply_wait').prop('checked', false);
        $('.ticket-reply-wait-days').hide();
    });

    $(document).on('click', '.reply-form #saveticketreply_wait', function(e) {
        $('#saveticketreply_close').prop('checked', false);

        if (e.target['checked'] === true) {
            $('.ticket-reply-wait-days').show();
        } else {
            $('.ticket-reply-wait-days').hide();
        }
    });

    $(document).on('click', '.reply-form #submitAndClose', function() {
        $('#saveticketreply_close').prop('checked', true);
        $('#saveticketreply_wait').prop('checked', false);
        $('#saveticketreply_save').click();
        return false;
    });

    $(document).on('click', '.reply-form #submitAndWaitThree', function() {
        $('#saveticketreply_close').prop('checked', false);
        $('#saveticketreply_wait').prop('checked', true);
        $('#saveticketreply_waitDays').val(3);
        $('#saveticketreply_save').click();
        return false;
    });

    $(document).on('click', '.reply-form #submitAndWaitSeven', function() {
        $('#saveticketreply_close').prop('checked', false);
        $('#saveticketreply_wait').prop('checked', true);
        $('#saveticketreply_waitDays').val(7);
        $('#saveticketreply_save').click();
        return false;
    });

    $(document).on('click', '.reply-form #submitAndWaitFourteen', function() {
        $('#saveticketreply_close').prop('checked', false);
        $('#saveticketreply_wait').prop('checked', true);
        $('#saveticketreply_waitDays').val(14);
        $('#saveticketreply_save').click();
        return false;
    });

    $(document).on('click', '.reply-form #submitAndWaitTwentyOne', function() {
        $('#saveticketreply_close').prop('checked', false);
        $('#saveticketreply_wait').prop('checked', true);
        $('#saveticketreply_waitDays').val(21);
        $('#saveticketreply_save').click();
        return false;
    });

    $(document).on('click', '.reply-form #submitAndReset', function() {
        $('#saveticketreply_close').prop('checked', false);
        $('#saveticketreply_wait').prop('checked', false);
        $('#saveticketreply_save').click();
        return false;
    });

    var rmaScan = $('#rmasearchform_query');
    if (rmaScan.length) {
        rmaScan.get(0).focus();
    }

    $(document).trigger('ajaxComplete');
});

//Doe een ajax update op een element
function runAjaxUpdate(element) {
    if (element.val() != '') {
        $.get(element.data("fetch-url") + "/" + element.val(), function (data) {
            $(element.data("target")).replaceWith(data);
        });
    }
}

/**
 * Editable boxes
 */
$(document).ajaxComplete(function() {
    generateInputFields();
    if ($('[data-toggle="tooltip"]')[0]) {
        $('[data-toggle="tooltip"]').tooltip();
    }

    if ($('[data-toggle="popover"]')[0]) {
        $('[data-toggle="popover"]').popover();
    }

    $('.add-address-book').each(function() {
        addAddressBook($(this));
    });

    if ($('.color-picker')[0]) {
        $('.color-picker').each(function(){
            $('.color-picker').each(function(){
                var colorOutput = $(this).closest('.cp-container').find('.cp-value');
                $(this).farbtastic(colorOutput);
            });
        });
    }
    // Date pickers
    if ($('.webdsign-date-time-picker')[0]) {
        $('.webdsign-date-time-picker').datetimepicker({
            format: 'DD-MM-YYYY LT'
        });
    }
    if ($('.webdsign-time-picker')[0]) {
        $('.webdsign-time-picker').datetimepicker({
            format: 'LT'
        });
    }
    if ($('.webdsign-date-picker')[0]) {
        $('.webdsign-date-picker').datetimepicker({
            format: 'DD-MM-YYYY'
        });
    }
    updateRmaProductContainer();
});

/*
 * checkbox fix in de link modal van summernote.
 */
$(document).on('click',  'button[data-name="link"]', function(){
    if($('.note-link-dialog').hasClass('in')) {
        var checkbox = $('.note-link-dialog').find('.checkbox label');
        checkbox.append('<i class="input-helper"></i>');
    }
});

$(document).on('click', '.printTicket', function(){
    window.print();
});

$.fn.modal.Constructor.prototype.enforceFocus = function() {}; //Zodat select2's in modals werken: https://github.com/select2/select2/issues/1436

$(function () {
    // Handmatig de sorting in de URL aanpassen.
    //
    // $(document).on('change', '#ticketfilterform_assignees', function() {
    //     var defaultSort = $('#ticket-default-sort');
    //     var sortHref = defaultSort.attr('href');
    //     var history = window.history;
    //     var currentUrl = window.location.href;
    //
    //     if($(this).val() === null) {
    //         defaultSort.attr('href', sortHref.replace('f.id%2B', ''));
    //         if(typeof(history) != 'undefined' && history.pushState) {
    //             history.replaceState({url:currentUrl}, document.title, currentUrl.replace('f.id%2B', ''));
    //         }
    //     } else if(sortHref.indexOf('f.id%2B') === -1) {
    //         defaultSort.attr('href', sortHref.replace('sort=', 'sort=f.id%2B'));
    //         history.replaceState({url:currentUrl}, document.title, currentUrl.replace('sort=', 'sort=f.id%2B'));
    //     }
    // });

    //ticketMerge
    /**
     * Vanuit de inbox van een mail een ticket maken.
     */
    $(document).on('click', '.add-as-ticket', function() {
        var url = $(this).attr('href');

        $.get(url, function(data) {
            createdTicket(data);
        });

        return false;
    });
    $(document).on('click', '.addOrderFromText', function(e) {
        var input = $('.addOrderInput')
        input.val($(this).data('id'));
        runAjaxUpdate(input);
        return false;
        e.preventDefault();
    });


    /** Ticket toevoegen aan klant **/
    $(document).on('click', '.customer-add-action', function() {
        var url = $(this).data('link-customer');
        if($(this).data('ticket-id')) {
            url = $(this).data('link-ticket').replace("replace", $(this).data('ticket-id'));
        }
        if($(this).data('mail-id')) {
            url = $(this).data('link-mail').replace("replace", $(this).data('mail-id'));
        }

        $('#ajaxModal').modal({'remote' : url});
        return false;
    });

    /** Klant toevoegen aan ticket **/
    $(document).on('click', '.ticket-add-action', function(e) {

        var url = $(this).data('link-ticket');
        if(customerEntryIsOpen == true) {
            url = $(this).data('link-customer').replace("replace", $(this).data('customer-id'));
        }

        $('#ajaxModal').modal({'remote' : url});
        e.preventDefault();
        return false;
    });

    /**
     *  Verborgen ticket replies laten zien als er geklikt wordt
     */
    $(document).on('click', '.hiddenRepliesHolder', function() {
        $('.tooltip').tooltip('destroy');
        $(".hiddenRepliesCounter").remove();
        $(".hiddenRepliesHolder").removeClass('hiddenRepliesHolder');
        $(".hiddenReplies").removeClass("hidden");
        return false;
    });

    $(document).on('click', '.tagAddShow', function() {
        $(".tagAdd").removeClass("hidden");
        $(this).addClass("hidden");
        $('.tagAdd select').select2('open');
        return false;
    });
    $(document).on('click', '.tagAddHide', function() {
        $(".tagAdd").addClass("hidden");
        $(".tagAddShow").removeClass("hidden");
        return false;
    });

    /**
     * Simpele client-side check voor geldigheid e-mailadres
     */
    var contactEmailAddressSelector = "#saveticketreply_contactEmailAddress";
    var contactEmailAddressOptionsSelector = ".select2-container--open .select2-results";
    $(document).on("select2:selecting", contactEmailAddressSelector, function(event) {
        var address = event.params.args.data.id;
        var emailPattern = /^[+a-z0-9._-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;
        if (!emailPattern.test(address))
        {
            $(contactEmailAddressOptionsSelector).tooltip({
                "title": "Ongeldig e-mailadres: " + address,
                "placement" : "right",
                "container" : "body",
                "trigger": "manual"
            });
            $(contactEmailAddressOptionsSelector).tooltip("show");
            return false;
        }
    });
    $(document).on("select2:closing", contactEmailAddressSelector, function() {
        $(contactEmailAddressOptionsSelector).tooltip("hide");
    });
    $(document).on("change", contactEmailAddressSelector, function() {
        var replyType = $('#saveticketreply_replyType');
        var history = $('#saveticketreply_history');
        var values = $(contactEmailAddressSelector).val();
        history.prop("disabled", false).closest(".checkbox").removeClass("disabled hidden");
        if (values && values.length > 1) {
            history.prop("disabled", true).closest(".checkbox").addClass("disabled hidden");
            if (replyType.val() != 'reply') {
                replyType.data('dontUpdate', true).val('reply').trigger('change');
            }
        } else if(values && values.length == 1) {
            var customerEmail = replyType.closest('.reply-form').data('customer-email');
            var servicePartnerEmail = replyType.closest('.reply-form').data('servicepartner-email');

            if (customerEmail && values[0] == customerEmail && replyType.val() != 'reply') {
                replyType.data('dontUpdate', true).val('reply').trigger('change');
            } else if (servicePartnerEmail && values[0] == servicePartnerEmail && replyType.val() != 'replyServicePartner') {
                replyType.data('dontUpdate', true).val('replyServicePartner').trigger('change');
            }
        }

        var warningListElement = $('#customerWarningAddress');
        warningListElement.hide();
        var warningList = warningListElement.data('list');
        if (values && values.length > 0 && warningList && warningList.length > 0) {
            for (var warningIndex=0; warningIndex<warningList.length; ++warningIndex) {
                var warningItem = warningList[warningIndex];
                for (var emailIndex = 0; emailIndex < values.length; ++emailIndex) {
                    if (warningItem.email === values[emailIndex]) {
                        var warningTextElement = warningListElement.find('[data-warningText]');
                        warningTextElement.html(warningTextElement.data('warningtext').replace('[EMAIL]', warningItem.email));
                        var description = warningItem.description ? warningItem.description : warningTextElement.data('warningDescription');
                        warningListElement.find('i').prop('title', description).tooltip('fixTitle');
                        warningListElement.show();
                    }
                }
            }
        }
    });
    $(document).on('click', '#customerWarningAddress', function() {
        $(this).find('i').tooltip('toggle');
    });
    $(contactEmailAddressSelector).change();
    $(document).on('submit', 'form', function(e) {
        var form = $(this);
        if (form.find('#customerWarningAddress:visible').length) {
            e.preventDefault();
            return false;
        }
    });

    /**
     * Als een element het check-refresh data-attribuut heeft wordt er
     * om een te bepalen tijd een request gedaan naar een url waarbij een lijst
     * met id's teruggegeven wordt. Als deze id's niet matchen met de id's op de pagina
     * wordt een melding getoond.
     */
    $('[data-check-refresh]').each(function() {
        var url = $(this).data('check-refresh');
        var interval = parseInt($(this).data('check-refresh-interval'));
        var refreshAlertShown = false;

        if(url == 'self' || url == '') {
            url = window.location.href;
        }

        if(interval == '' || isNaN(interval)) {
            interval = 10;
        }

        setInterval(function() {
            $.get(url, {checkRefresh: true},function(data) {
                if(typeof data.ids != 'undefined') {
                    var ids = data.ids;
                    $.each(ids, function(i,v) {
                        var element = $('[data-id="'+v+'"]');
                        if(element.length === 0 && refreshAlertShown === false && currentlyEditingTicket === false) {
                            var updateAlert = $('<div class="alert alert-info alert-dismissible" data-refresh-alert="true" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>' + Translator.trans('js.changed.page.title') + '. <strong><a href="javascript:location.reload()">' + Translator.trans('js.changed.page.text') + '</a></strong></div>');
                            $('#content .container').prepend(updateAlert);
                            refreshAlertShown = true;
                        }
                    });
                }

                currentlyEditingTicket = false;
            });
        }, interval * 1000 * 60);
    });

    $(document).on('click', '.assign-user .activeUser', function() {
        var userId = $(this).data('user-id');
        var select = $(this).parents('.dropdown-form').find(':input[name="assignticket[user]"]');
        if(select.length === 1) {
            select.val(userId);
            select.parents('form').submit();
        }
    });

    $(document).on('click', '.quickTag', function() {
        var tagId = $(this).data('tag-id');
        var formElement = $(this).parents('.dropdown-form').find('input[data-assigntype="tag"]');
        if(formElement.length === 1) {
            formElement.val(tagId);
            formElement.parents('form').removeClass('ajaxSubmit').submit();
        }
    });

    $(document).on('click' , '#assignToTag', function() {
        $(this)
            .addClass("hidden")
            .siblings()
            .removeClass("hidden")
    });

    hideReplyContacts();
});

$(document).on('change', '#saveticketreply_replyType', function(event) {
    if (event.originalEvent) {
        hideReplyContacts(true);
    } else {
        hideReplyContacts();
    }
});

/**
 * Verberg de lijst met e-mailadressen bij het antwoorden op een ticket als dat nodig is.
 */
var hideReplyContacts;

(hideReplyContacts = function(isUserUpdate) {
    var replyType = $('#saveticketreply_replyType');
    if(replyType.length) {
        if(replyType.val() == 'internal') {
            $('#saveticketreply_from').parents('.toggle-internal').addClass('hidden');
            $('#saveticketreply_assignTag').parents('.toggle-internal').removeClass('hidden');
            $('#saveticketreply_contactEmailAddress').parents('.form-group').addClass('hidden');
            $('#saveticketreply_signature').parents('.toggle-internal').addClass('hidden');
            $('#saveticketreply_history').parents('.form-group').addClass('hidden');
            $('#saveticketreply_invoice').parents('.form-group').addClass('hidden');
            $('#saveticketreply_returnLabel').parents('.form-group').addClass('hidden');
            $('#saveticketreply_returnLabelFree').parents('.form-group').addClass('hidden');
            $('#saveticketreply_customerContact').parents('.form-group').removeClass('hidden');
            $('#saveticketreply_save').html(Translator.trans('js.save'));
        } else {
            $('#saveticketreply_from').parents('.toggle-internal').removeClass('hidden');
            $('#saveticketreply_assignTag').parents('.toggle-internal').addClass('hidden');
            $('#saveticketreply_contactEmailAddress').parents('.form-group').removeClass('hidden');
            $('#saveticketreply_signature').parents('.toggle-internal').removeClass('hidden');
            $('#saveticketreply_history').parents('.form-group').removeClass('hidden');
            $('#saveticketreply_invoice').parents('.form-group').removeClass('hidden');
            $('#saveticketreply_returnLabel').parents('.form-group').removeClass('hidden');
            $('#saveticketreply_returnLabelFree').parents('.form-group').removeClass('hidden');
            $('#saveticketreply_customerContact').parents('.form-group').addClass('hidden');
            $('#saveticketreply_close').parents('.form-group').removeClass('hidden');
            $('#saveticketreply_save').html(Translator.trans('js.send'));

            if (isUserUpdate) {
                if (replyType.data('dontUpdate')) {
                    replyType.data('dontUpdate', false);
                } else if (replyType.val() == 'replyServicePartner') {
                    var servicepartnerEmail = replyType.closest('.reply-form').data('servicepartner-email');
                    if (servicepartnerEmail) {
                        $("#saveticketreply_contactEmailAddress").val(servicepartnerEmail).trigger('change');
                        $('.saveticketreply_sendAsCNU').show();
                    } else {
                        Swal.fire({
                            title: Translator.trans('js.service.ticket.noservicepartner.title'),
                            text: Translator.trans('js.service.ticket.noservicepartner.text'),
                            icon: 'warning'
                        });
                    }
                } else if (replyType.val() == 'reply') {
                    var customerEmail = replyType.closest('.reply-form').data('customer-email');
                    if (customerEmail) {
                        $("#saveticketreply_contactEmailAddress").val(customerEmail).trigger('change');
                        $('#saveticketreply_sendAsCNU').prop('checked',false);
                        $('.saveticketreply_sendAsCNU').hide();
                    }
                }
            }
        }
    }
})();

/**
 * Huidige ticket updaten als er iets aan aangepast wordt.
 */
var currentlyEditingTicket = false;
$(document).on('click', '.currently-editing, #saveticket_save, #assignticket_assign, .complete-entry, .ajax, #saveticketreply_save, .activeUser', function() {
    currentlyEditingTicket = true;
});

/**
 * Ticket naar "open" zodra deze wordt toegewezen aan een ander
 */
$(function(){
    $(document).on('change', '#saveticket_assignee', function() {
        var stateField = $('#saveticket_state');

        if (stateField.val() != 'open') {
            stateField.val('open').parent().addClass('bg-warning');
            window.setTimeout(function () {
                $('#saveticket_state').parent().removeClass('bg-warning');
            }, 1500);
        }
    });
});

$(document).on("hidden.bs.modal", ".attachmentModal", function(){
    $(this).find('.modal-content').html('');
    $(this).removeData('bs.modal');
    if($('#ajaxModal').hasClass('in')){
        $('#ajaxModal').show();
    }
});
$(document).on("shown.bs.modal", ".attachmentModal", function(){
    if($('#ajaxModal').hasClass('in')){
        $('#ajaxModal').hide();
    }
});
/**
 * Kijk of er een ticket is opengeklapt
 */
var entryIsOpen = false;
var customerEntryIsOpen = false;


function updateRmaProductContainer() {
    var productsContainer = $("#rmaProductContainer");
    if (productsContainer.length) {

        var allowMultipleProducts = true;
        // create ticket+rma scherm heeft optie op tags te selecteren, controleer hier op de "retour" tag
        var rmaTags = $("#rma_tags");
        if (rmaTags.length) {
            var retourTagValue = rmaTags.find("option").filter(function(){ return $(this).text().toLowerCase().indexOf("retour") !== -1; }).val();
            if (null !== rmaTags.val() && rmaTags.val().indexOf("" + retourTagValue) !== -1) {
                $("#addProductToRma").show();
            } else {
                // geen retour tag geselecteerd, dus ook geen meerdere producten toestaan (sorry, maar die verwijder ik gewoon!)
                $("#addProductToRma").hide();
                allowMultipleProducts = false;
                productsContainer.children("div").each(function(index, element){
                    if (index > 0) {
                        element.remove();
                    }
                });
            }
        }

        // fix eventuele delete buttons
        productsContainer.find('.deleteProductFromRma').remove();
        if (productsContainer.children("div").length > 1) {
            productsContainer.children("div").each(function () {
                $(this).prepend($(productsContainer.data('delete-button')));
            });
        }

        // verberg de add-button als er een order is gekoppeld en meerdere producten toegestaan
        if (allowMultipleProducts) {
            var optionCount = productsContainer.find("select:first option").length;
            if (optionCount > 0 && productsContainer.children("div").length >= optionCount) {
                $("#addProductToRma").hide();
            } else {
                $("#addProductToRma").show();
            }
        }
    }
}
