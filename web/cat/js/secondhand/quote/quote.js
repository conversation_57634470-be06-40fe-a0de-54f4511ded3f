var quoteTotal = document.getElementById('quote-total-value');
var quotePotentialTotal = document.getElementById('quote-potential-total-value');
var quoteTotalValue = parseFloat(document.getElementById('quote-total-value-input').value);
var quotePotentialTotalValue = parseFloat(document.getElementById('quote-potential-total-value-input').value);
var missingAccessoryTotal = parseFloat(document.getElementById('quote-potential-missing-accessory-total').value);
var payoutModifier = 0.0;
if (document.getElementById('quote_payoutChoice')) {
    payoutModifier = parseFloat(document.getElementById('quote_payoutChoice').getAttribute('data-payout-reduction-modifier'));
} else {
    payoutModifier = parseFloat(document.getElementById('payout-reduction-modifier').getAttribute('data-modifier'));
}

function changeQuoteTotal() {
    var calculatedQuoteTotalValue = quoteTotalValue;
    var calculatedQuotePotentialTotalValue = quotePotentialTotalValue;

    var payoutChoice = document.querySelector('input[name="quote[payoutChoice]"]:checked');

    // TODO: This piece of code could be made a lot smarter, but for now its fine.
    if (!payoutChoice || (payoutChoice && payoutChoice.value !== 'debit')) {
        // If this changes, also check the code/calculation on:
        // Quote/Panels/Products.html.twig line 23
        // product-question.js line 61
        // Only calculate when base is not zero, else the calculation doesn't work
        if (calculatedQuoteTotalValue > 0) {
            calculatedQuoteTotalValue = (calculatedQuoteTotalValue + missingAccessoryTotal) / payoutModifier - missingAccessoryTotal;
        }
    } else {
        calculatedQuotePotentialTotalValue = (calculatedQuotePotentialTotalValue + missingAccessoryTotal) * payoutModifier - missingAccessoryTotal;
    }

    quoteTotal.innerText = new Intl.NumberFormat(
        'nl-NL', {
            style: 'currency',
            currency: 'EUR',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }
    ).format(Math.round(calculatedQuoteTotalValue));

    quotePotentialTotal.innerText = new Intl.NumberFormat(
        'nl-NL', {
            style: 'currency',
            currency: 'EUR',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }
    ).format(Math.round(calculatedQuotePotentialTotalValue));
}

function saveQuote(validate = false)
{
    // Add products to submit request
    var quoteProducts = [];
    $('.product-card-body .customer-contact-panel').each(function () {
        quoteProducts.push({
            'quoteProductId': $(this).data('quote-product-id'),
            'productId': $(this).data('product-id'),
            'isIntakableAsSecondHand': !$(this).hasClass('not-intakeable')
        });
    });

    $('#quote_products').val(JSON.stringify(quoteProducts));

    if (!validate || validateQuote()) {
        $('form[name="quote"]').submit();
    }
}

if (quoteTotal) {
    changeQuoteTotal();
    $(document).on('change', 'input[name="quote[payoutChoice]"]', changeQuoteTotal);
    $(document).on('change', '#quote_customerType', changeQuoteTotal);
}

function validateQuote()
{
    let intakeableProducts = $('.product-card-body .customer-contact-panel').not('.not-intakeable');
    let productsValid = intakeableProducts.length;
    let productsReceived = intakeableProducts.length;

    if (intakeableProducts.length === 0) {
        return true;
    }

    $(intakeableProducts).each(function () {
        if ($(this).find('.product-has-definitive-grade').length === 0) {
            productsValid = false;
            return false;
        }

        if ($(this).find('.product-is-not-received').length !== 0) {
            productsReceived = false;
        }
    });

    if (!productsValid) {
        alert('Je hebt nog niet voor alle producten de vragen ingevuld');
        return false;
    }

    if (!productsReceived) {
        alert('Je hebt nog niet alle producten op ontvangen gezet');
        return false;
    }

    if (!$('#quote_customer').val() > 0) {
        alert('Er is nog geen klant gekoppeld!');
        return false;
    }

    return true;
}

$(document).on('click', '.search-list-item', function () {

    var productId = $(this).data('id');
    var quoteId = $('#quote_id').val();
    var isNotIntakable = $(this).hasClass('not-intake-able');

    $.ajax({
        url: '/api/instant-quote/quote/' + quoteId + '/add-product/' + productId,
        dataType: "json",
        method: "POST",
        success: function (data) {
            var newProduct = $('#newProductTemplate').clone();
            newProduct.find('.product-name').text(data.productName);

            newProduct.find('.judge-quote-product').attr('href', '/instant-quote/quote/product/' + data.id);
            newProduct.find('.receive-quote-product').attr('data-quote-product-receive-url', '/instant-quote/quote/product/' + data.id + '/receive');
            newProduct.find('.remove-quote-product').attr('data-quote-product-delete-url', '/instant-quote/quote/product/' + data.id + '/delete');

            if (data.status === 'receive') {
                newProduct.find('.receive-quote-product').removeClass('d-none');
            } else if (data.status === 'judge') {
                newProduct.find('.judge-quote-product').removeClass('d-none');
            }

            // Append new product to the list
            $('.product-card-body').append(newProduct.html());

            // Close product search
            $('.autocompleter-results').css('display', 'none');

            if(isNotIntakable === true) {
                window.location.reload();
                return;
            }

            // Redirect to product questions
            window.location.replace('/instant-quote/quote/product/' + data.id);
        }
    });
});

$(document).on('click', '.receive-quote-product', function () {
    if (confirm('Weet je zeker dat je dit product op ontvangen wil zetten?')) {
        var parentElement = $(this).parent();
        fetch($(this).attr('data-quote-product-receive-url'), {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            method: 'GET'
        })
        .then((response) => response.json())
        .then((json) => {
            if (json.success) {
                parentElement.find('.receive-quote-product').addClass('d-none');
                parentElement.find('.judge-quote-product').removeClass('d-none');
                parentElement.find('.product-is-not-received').remove();
            }
        });
    }
});

$(document).on('click', '.remove-quote-product-request', function () {
    if (confirm('Weet je zeker dat je deze product aanvraag wilt verwijderen?')) {
        fetch($(this).attr('data-quote-product-request-delete-url'), {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            method: 'GET'
        })
        .then((response) => response.json())
        .then((json) => {
            if (json.success) {
                $(this).closest('.product-request-panel').remove();
            }
        });
    }
});

$(document).on('click', '.return-quote-product', function () {
    if (confirm('Weet je zeker dat je dit product op geretourneerd wilt zetten?')) {
        var parentElement = $(this).parent();

        fetch($(this).attr('data-quote-product-return-url'), {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            method: 'GET'
        })
        .then((response) => response.json())
        .then((json) => {
            if (json.success) {
                parentElement.find('.return-quote-product').addClass('d-none');
                parentElement.find('.quote-product-returned').removeClass('d-none');
            }
        });
    }
});

$(document).on('click', '.recycle-quote-product', function () {
    if (confirm('Weet je zeker dat je dit product op gerecycled wilt zetten?')) {
        var parentElement = $(this).parent();

        fetch($(this).attr('data-quote-product-recycle-url'), {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            method: 'GET'
        })
        .then((response) => response.json())
        .then((json) => {
            if (json.success) {
                parentElement.find('.recycle-quote-product').addClass('d-none');
                parentElement.find('.quote-product-recycled').removeClass('d-none');
            }
        });
    }
});

$(document).on('click', '.remove-quote-product', function () {
    if (confirm('Weet je zeker dat je dit product wil verwijderen?')) {
        fetch($(this).attr('data-quote-product-delete-url'), {
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            },
            method: 'GET'
        })
        .then((response) => response.json())
        .then((json) => {
            if (json.success) {
                $(this).closest('.customer-contact-panel').remove();
            }
        });
    }
});

$(document).on('change', '#customer_agreed', function() {
    var payoutChoice = document.querySelector('input[name="quote[payoutChoice]"]:checked');
    if ($(this).is(':checked') && payoutChoice === null) {
        alert('Je hebt nog geen uitbetaling type gekozen. Let op! na maken keuze eerst op opslaan klikken.');
        $(this).prop('checked', false);
    } else if ($('#quote_payoutChoice').data('payout-choice-is-saved') === false) {
        alert('Eerst opslaan a.u.b.');
        $(this).prop('checked', false);
    }

    $('.toggle-agreed').prop('disabled', !$(this).is(':checked'));
});

$(document).on('click', '#btn-customer-agreed', function() {
    window.location.href = $(this).data('path');
});

$(document).on('click', '#quote_save', function(e) {
    e.preventDefault();
    saveQuote(false);
});

$(document).on('click', '#send_quote', function(e) {
    e.preventDefault();

    if (confirm('Weet je zeker dat je de offerte wilt versturen?')) {
        $('#quote_sendOffer').val(true);
        saveQuote(true);
    }
});

$(document).on('click', '#quote_status_open', function(e) {
    e.preventDefault();

    if (confirm('Weet je zeker dat je dit concept om wil zetten?')) {
        $('#quote_setStatusOpen').val(true);
        saveQuote(true);
    }
});

$(document).on('click', '.add-product', function (e) {
    var searchForm = $('.product-card-body > .d-none');
    searchForm.removeClass('d-none');

    $(this).parent().removeClass('text-center');
    $(this).replaceWith(searchForm);
});

$(document).on('click', '.btn-send-provisional-quote', function () {
    $('.loader').show();
});

$(document).on('click', '.dropdown-questions', function (){
    $(this).parents('.customer-contact-panel').find('.product-answers').toggleClass('d-none');
    $(this).find('.bi').toggleClass('bi-chevron-down').toggleClass('bi-chevron-up');
});
