$(document).ready(function() {
    $('#overview input[name="select-all"]').change(function () {
        $('#overview input[name="select-all-group"]').prop('checked', $(this).is(':checked')).trigger('change');
    });

    $('#overview input[name="select-all-group"]').change(function () {
        $(this).closest('tr').nextUntil('tr.group').find('input[name="select"]').prop('checked', $(this).is(':checked'));
    });

    $('#overview button[name="export"]').click(function() {
        var list = $('input[data-id]:is(:checked)');
        var ids = [];
        $(list).each(function() {
            ids.push($(this).data('id'));
        });

        $.post('/supplier-backorders/export/', JSON.stringify(ids), function(data) {
            var downloadLink = document.createElement("a");
            var fileData = ['\ufeff'+data];

            var blobObject = new Blob(fileData,{
                type: "text/csv;charset=utf-8;"
            });

            var url = URL.createObjectURL(blobObject);
            downloadLink.href = url;
            downloadLink.download = "leveranciers-besteld.csv";

            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        });
    });

    $('#overview input[name="check-date"], input[name="delivery-date"]').change(function() {
        var ids = [];
        if ($(this).closest('tr').hasClass('group')) {
            $(this).closest('tr').nextUntil('tr.group').find('input[name="select"]').each(function() {
                ids.push($(this).data('id'));
            });
        }

        var data = {
            'type': $(this).attr('name'),
            'date': $(this).val(),
            'id': $(this).closest('tr').find('input[name="select"]').data('id'),
            'ids': ids
        };

        $.post('/supplier-backorders/', JSON.stringify(data), function () {
            if (ids.length) {
                window.location.reload(true);
            }
        });
    });

    $('#overview button.filter').click(function() {
        var interval = $('#overview select[name="date-filter"]').find(':selected').val();
        var supplier = $('#overview select[name="supplier-filter"]').find(':selected').val();
        var sort = $('#overview select[name="sort"]').find(':selected').val();
        var deliveryDate = $('#overview input[name="delivery-date-filter"]').val();
        var eigCategory = $('#overview select[name="eig-category-filter"]').find(':selected').val();

        var url = '/supplier-backorders';
        url += '/' + (interval ?? 0);
        url += '/' + (supplier ??= null);
        url += '/' + (sort ??= null);
        url += '/' + (eigCategory ??= null);
        url += '/' + (deliveryDate ??= null);

        window.location.href = url;
    });

    $('#detail input[name="select-all"]').change(function () {
        $('input[name="select"]').prop('checked', $(this).is(':checked')).trigger('change');
    });

    $('#detail input[name="select"]').change(function () {
        if ($(this).is(':checked')) {
            $('input[name="delivery-date-all"], input[name="check-date-all"]').show();
            $('span.delivery-date, span.check-date').hide();
        } else if (!$('input[type="checkbox"]:checked').length) {
            $('input[name="delivery-date-all"], input[name="check-date-all"]').hide();
            $('span.delivery-date, span.check-date').show();
        }
    });

    $('#detail input[name="delivery-date-all"], input[name="check-date-all"]').change(function () {
        var list = $('input[data-id]:is(:checked)');
        var ids = [];
        $(list).each(function() {
            ids.push($(this).data('id'));
        });

        var data = {
            checkDate: $('#detail input[name="check-date-all"]').val(),
            deliveryDate: $('#detail input[name="delivery-date-all"]').val(),
            ids: ids
        };

        $.post('/supplier-backorders/' + referenceId, JSON.stringify(data), function(data) {
            window.location.reload(true);
        });
    });
});
