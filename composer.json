{"name": "skrepr/cat", "description": "CameraNU Admin Tool", "license": "proprietary", "type": "project", "_comment": ["composer-dependency-analyzer comments:", "   beberlei/doctrineextensions for doctrine.orm.entity_managers.cameranu_urk.dql", "   league/flysystem* deps are in the require-dev of the flysystem bundle, we need them in production", "   phpdocumentor/type-resolver for https://symfony.com/doc/6.4/controller.html#mapping-request-payload", "   phpstan/phpdoc-parser for https://symfony.com/doc/6.4/controller.html#mapping-request-payload", "   scienta/doctrine-json-functions for doctrine.orm.entity_managers.cameranu_urk.dql.string_functions", "   symfony/dotenv for dotenv loading...", "   symfony/error-handler in bin/console, possibly to require-dev?", "   symfony/messenger for message things...", "   symfony/monolog-bridge in services.monolog.logger.postnl (services.yaml)", "   symfony/notifier for app/config/packages/notifier.yaml things", "try to remove doctrine/annotations when going to attributes"], "require": {"php": "~8.3.0", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-ftp": "*", "ext-gd": "*", "ext-iconv": "*", "ext-imap": "*", "ext-json": "*", "ext-memcached": "*", "ext-mysqli": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-pdo": "*", "ext-simplexml": "*", "ext-sockets": "*", "ext-xmlreader": "*", "ext-zip": "*", "ext-zlib": "*", "ext-xsl": "*", "94noni/highcharts-bundle": "^2.2", "abcaeffchen/sepa-utilities": "^1.1", "abcaeffchen/sephpa": "^1.3", "adyen/php-api-library": "^14.0", "beberlei/doctrineextensions": "^1.2", "br33f/php-ga4-mp": "^0.1.0", "brick/postcode": "^0.2.8", "cocur/slugify": "^4.5.1", "doctrine/annotations": "^2.0.1", "doctrine/collections": "^2.1", "doctrine/dbal": "^3.6", "doctrine/doctrine-bundle": "^2.10", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/event-manager": "^2.0", "doctrine/migrations": "^3.8", "doctrine/orm": "^2.15", "doctrine/persistence": "^3.2", "erkens/fpdf-barcode": "^2.0", "ezyang/htmlpurifier": "^4.11", "facebook/php-ads-sdk": "^18.0", "firstred/postnl-api-php": "^2.0", "friendsofsymfony/elastica-bundle": "^6.3", "friendsofsymfony/jsrouting-bundle": "^3.2", "gedmo/doctrine-extensions": "^3.11", "ggergo/sqlindexhintbundle": "^1.1", "giggsey/libphonenumber-for-php": "^8.13", "google/apiclient": "^2.0", "google/apiclient-services": "~0.336.0", "guzzlehttp/guzzle": "^7.7", "guzzlehttp/psr7": "^2.6", "jms/serializer": "^3.29", "jms/serializer-bundle": "^5.3", "knplabs/knp-components": "^4.3", "knplabs/knp-paginator-bundle": "^6.2", "knplabs/knp-snappy": "^1.5", "knplabs/knp-snappy-bundle": "^1.6", "knplabs/knp-time-bundle": "^2.4", "league/csv": "^9.8", "league/flysystem": "^3.24", "league/flysystem-aws-s3-v3": "^3.15", "league/flysystem-ftp": "^3.15", "league/flysystem-sftp-v3": "^3.15", "league/oauth2-client": "^2.7", "mollie/mollie-api-php": "^2.65", "nesbot/carbon": "^2.24", "nette/php-generator": "^4.1", "oneup/flysystem-bundle": "^4.8", "php-amqplib/php-amqplib": "^3.5", "php-amqplib/rabbitmq-bundle": "^2.11", "php-imap/php-imap": "^3.0", "phpdocumentor/type-resolver": "^1.8", "phpoffice/phpspreadsheet": "^1.18", "phpseclib/phpseclib": "^3.0", "phpstan/phpdoc-parser": "^1.29", "picqer/php-barcode-generator": "^2.0", "portphp/csv": "^1.1", "psr/cache": "^3.0", "psr/container": "^2.0", "psr/http-message": "^1.1", "psr/log": "^3.0", "ramsey/uuid": "^4.7", "ruflin/elastica": "^7.3", "scienta/doctrine-json-functions": "^4.4", "skrepr/clang-api": "^1.22", "skrepr/tweakwise": "v2.0.1", "stof/doctrine-extensions-bundle": "^1.3", "symfony/config": "^6.4", "symfony/console": "^6.4", "symfony/dependency-injection": "^6.4", "symfony/doctrine-bridge": "^6.4", "symfony/dom-crawler": "^6.4", "symfony/dotenv": "^6.4", "symfony/error-handler": "^6.4", "symfony/event-dispatcher": "^6.4", "symfony/expression-language": "^6.4", "symfony/finder": "^6.4", "symfony/form": "^6.4", "symfony/framework-bundle": "^6.4", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.4", "symfony/lock": "^6.4", "symfony/mailer": "^6.4", "symfony/messenger": "^6.4", "symfony/mime": "^6.4", "symfony/monolog-bridge": "^6.4", "symfony/monolog-bundle": "^3.8", "symfony/notifier": "^6.4", "symfony/options-resolver": "^6.4", "symfony/password-hasher": "^6.4", "symfony/process": "^6.4", "symfony/routing": "^6.4", "symfony/security-bundle": "^6.4", "symfony/security-core": "^6.4", "symfony/security-csrf": "^6.4", "symfony/security-http": "^6.4", "symfony/serializer": "^6.4", "symfony/stopwatch": "^6.4", "symfony/translation": "^6.4", "symfony/translation-contracts": "^3.4", "symfony/twig-bundle": "^6.4", "symfony/validator": "^6.4", "symfony/webpack-encore-bundle": "^2.0", "symfony/yaml": "^6.4", "thenetworg/oauth2-azure": "^2.1", "tivie/php-os-detector": "^1.1", "twig/extra-bundle": "^3.9", "twig/html-extra": "^3.8", "twig/twig": "^3.6", "webklex/php-imap": "^5.5", "willdurand/js-translation-bundle": "^5.0", "symfony/css-selector": "^7.1", "openai-php/client": "^0.10.1", "jfcherng/php-diff": "^6.16", "twig/intl-extra": "^3.21"}, "require-dev": {"ergebnis/composer-normalize": "*", "lissonpsantos2/wkhtmltopdf-amd64": "^1.0", "roave/security-advisories": "dev-latest", "shipmonk/composer-dependency-analyser": "^1.2", "squizlabs/php_codesniffer": "^3.0", "symfony/debug-bundle": "^6.3", "symfony/maker-bundle": "^1.49", "symfony/phpunit-bridge": "^6.3", "symfony/var-dumper": "^6.4", "symfony/web-profiler-bundle": "^6.3", "theofidry/psysh-bundle": "^4.5", "rector/rector": "^2.0"}, "conflict": {"symfony/symfony": "*"}, "repositories": [{"type": "vcs", "url": "ssh://**************/skrepr/cameranu-clang.git"}, {"type": "vcs", "url": "**************:skrepr/cameranu-tweakwise.git"}], "autoload": {"psr-4": {"": "src/"}, "files": ["app/AppKernel.php", "app/AppCache.php"]}, "config": {"allow-plugins": {"ergebnis/composer-normalize": true, "php-http/discovery": true}}, "extra": {"public-dir": "web/", "symfony": {"allow-contrib": false, "require": "6.3.*"}, "symfony-app-dir": "app", "symfony-assets-install": "relative", "symfony-bin-dir": "bin", "symfony-tests-dir": "tests", "symfony-var-dir": "var", "symfony-web-dir": "web"}}