version: '3.7'

services:
  app:
    build:
      context: .
      target: app-test
      args:
        UID: ${UID-1000}
        # To authenticate to private registry either use username / password, or <PERSON><PERSON><PERSON> key/secret / etc
        # Please add Composer Auth JSON Object
        COMPOSER_AUTH: "{}"
        APP_BASE_DIR: ${APP_BASE_DIR:-./app}
    environment:
      DATABASE_URL: "mysql://root:database@127.0.0.1:3306/database?serverVersion=5.7"
    restart: always
    extra_hosts:
        - "host.docker.internal:host-gateway"
    depends_on:
      database:
        condition: service_healthy

  web:
    build:
      context: .
      target: web
      args:
        UID: ${UID-1000}
        APP_BASE_DIR: ${APP_BASE_DIR:-./app}
    restart: always
    environment:
      # The Hostname for the FPM (needs to match the hostname for the `app`)
      PHP_FPM_HOST: app
    depends_on:
      app:
        condition: service_healthy

  database:
    image: bitnami/mariadb:latest
    restart: always
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE-db}
      MARIADB_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD-toor}
    healthcheck:
      test: ["CMD-SHELL", 'mysql --database=$MYSQL_DATABASE --user=root --password=$MARIADB_ROOT_PASSWORD --execute="SELECT count(table_name) > 0 FROM information_schema.tables;" --skip-column-names -B']
      interval: 30s
      timeout: 10s
      retries: 5

